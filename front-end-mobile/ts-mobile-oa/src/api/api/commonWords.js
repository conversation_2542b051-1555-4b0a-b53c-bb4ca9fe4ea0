import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取常用语列表**/
  getOfficalDictionList(datas) {
    return request.post(
      `${apiConfig.oa()}/employee/officaldiction/list`,
      datas,
      {
        custom: {
          showLoading: true
        }
      }
    );
  },
  /**@desc 常用语操作(置顶/编辑/默认填充)**/
  confirmOfficalDiction(type, datas) {
    return request.post(
      `${apiConfig.oa()}/employee/officaldiction/${type}`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
