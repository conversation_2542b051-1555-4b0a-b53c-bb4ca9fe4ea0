import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取危急值列表**/
  getCriticalValueList(datas) {
    return request.post(
      `${apiConfig.external()}/criticalValue/getCriticalValueList`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 获取危急值详情**/
  getCriticalValueDetails(datas) {
    return request.post(
      `${apiConfig.external()}/criticalValue/getCriticalValue`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 更新危急值状态**/
  updateCriticalValueStstus(datas) {
    return request.post(
      `${apiConfig.external()}/criticalValue/updateCriticalValueStstus`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  //确认处理结果
  handleCriticalValue(datas) {
    return request.post(
      `${apiConfig.external()}/criticalValue/handleCriticalValue`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
