import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取未读邮件**/
  getNoreadEmailList(datas) {
    return request.get(
      `${apiConfig.information()}/emailInternal/getNoreadEmailList`,
      {
        params: datas,
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 获取邮件详情**/
  getEmailDetails(datas) {
    return request.get(
      `${apiConfig.information()}/emailInternal/getEmailDetails`,
      {
        params: datas,
        custom: {
          showLoading: true
        }
      }
    );
  },
  /**@desc 发送外部邮件**/
  sendEmailOut(datas) {
    return request.post(
      `${apiConfig.information()}/emailInternal/sendEmailOut`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 发送内部邮件**/
  sendEmailInternal(datas, loadingText) {
    return request.post(
      `${apiConfig.information()}/emailInternal/sendEmailInternal`,
      datas,
      {
        custom: {
          showLoading: true,
          loadingText: loadingText
        },
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 获取邮件统计数**/
  getEmailNum(datas) {
    return request.post(
      `${apiConfig.information()}/emailInternal/selectMainEmailList`,
      datas,
      {
        custom: {
          showLoading: true,
          loadingText: ''
        }
      }
    );
  },
  /**@desc 邮件操作（删除、彻底删除）**/
  confirmEmail(datas) {
    return request.post(
      `${apiConfig.information()}/emailInternal/operateMail`,
      datas,
      {
        custom: {
          showLoading: true,
          loadingText: ''
        }
      }
    );
  },
  /**@desc 获取邮件列表**/
  getEmailList(datas) {
    return request.get(`${apiConfig.information()}/emailInternal/list`, {
      params: datas
    });
  },
  /**@desc 设置邮件全部已读**/
  setEmailAllRead() {
    return request.post(
      `${apiConfig.information()}/emailInternal/operateAllRead`
    );
  }
};
