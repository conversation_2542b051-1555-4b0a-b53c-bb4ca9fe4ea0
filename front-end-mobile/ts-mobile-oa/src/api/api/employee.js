import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 登录**/
  login(datas) {
    return request.post(`${apiConfig.sso()}/login`, datas, {
      custom: {
        showLoading: true,
        loadingText: '正在登录...'
      },
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 退出登录**/
  signOut(datas) {
    return request.get(`${apiConfig.information()}/cp/weixin/loginOut`, {
      params: datas,
      custom: {
        showLoading: true
      }
    });
  },
  /**@desc 获取人员列表**/
  getEmployeeList(datas) {
    return request.get(`${apiConfig.oa()}/employee/getEmployeeList`, {
      params: datas,
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 获取群组**/
  getOrgGroupList(datas) {
    return request.get(
      `${apiConfig.basics()}/employee/orgGroup/getOrgGroupList`,
      {
        params: datas,
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        },
        custom: {
          showLoading: true,
          loadingText: '加载中...'
        }
      }
    );
  },
  /**@desc 获取群组类别**/
  getOrgGroupClassList(datas) {
    return request.post(
      `${apiConfig.basics()}/employee/orgGroupClass/list`,
      datas
    );
  },
  /**@desc 群组操作(新增、编辑、删除)**/
  confirmOrgGroup(type, datas) {
    return request.post(
      `${apiConfig.basics()}/employee/orgGroup/${type}`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 群组操作(新增、编辑、删除)**/
  getAllOrgList(data) {
    return request.post(
      `${apiConfig.basics()}/organization/getAllOrgList`,
      data
    );
  },
  /**@desc 获取科室列表**/
  getDeptList(datas) {
    return request.get(`${apiConfig.system()}/dept/deptlist`, {
      params: datas,
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 获取人员信息**/
  getPersonalInformationSettings(datas) {
    return request.get(
      `${apiConfig.oa()}/employee/personalInformationSettings`,
      {
        params: datas
      }
    );
  },
  /**@desc 获取人员信息详尽版 */
  getPersonalInformationAllDetailSettings(id) {
    return request.post(
      `${apiConfig.basics()}/cusotmEmployee/findByIdAndDetails/${id}`
    );
  },
  /**@desc 获取人员信息详尽版 档案改版 新*/
  customEmployeeBaseFindDetailsById(id, params) {
    return request.post(
      `${apiConfig.basics()}/api/customEmployeeBase/findDetailsById/${id}`,
      params
    );
  },

  /**@desc 暂存记录 */
  pendingGetstorage(id) {
    return request.get(`${apiConfig.basics()}/cusotmEmployee/getstorage/${id}`);
  },

  /**@desc 暂存记录 档案改版 新*/
  customEmployeeBaseGetStorage(params) {
    return request.get(
      `${apiConfig.basics()}/api/customEmployeeBase/getStorage`,
      params
    );
  },

  /**@desc 流程记录人 */
  cusotmEmployeeGetEmployeeTask(id) {
    return request.get(
      `${apiConfig.basics()}/cusotmEmployee/getEmployeeTask/${id}`
    );
  },
  /**@desc 流程记录人 档案改版 新*/
  customEmployeeBaseGetEmployeeTask(employeeNo) {
    return request.get(
      `${apiConfig.basics()}/api/customEmployeeBase/getEmployeeTask/${employeeNo}`
    );
  },
  /**@desc 暂存 */
  pendingStorage(datas) {
    return request.post(`${apiConfig.basics()}/cusotmEmployee/storage`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 暂存 档案改版 新*/
  customEmployeeBaseStorage(datas) {
    return request.post(
      `${apiConfig.basics()}/api/customEmployeeBase/storage`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  //获取用户个人信息状态
  employeeReviewStatus(employeeId) {
    return request.get(
      `${apiConfig.basics()}/employee/reviewStatus/${employeeId}`
    );
  },
  //获取用户个人信息状态 档案改版 新
  customEmployeeBaseInApproval() {
    return request.get(
      `${apiConfig.basics()}/api/customEmployeeBase/inApproval`
    );
  },

  /**@desc 获取人员栏目查看权限 */
  byPersonalIdentity(id) {
    return request.get(
      `${apiConfig.basics()}/api/columnAuthority/byPersonalIdentity/${id}`
    );
  },
  /**@desc 获取人员栏目必填权限 */
  columnAuthorityRequired(id) {
    return request.get(
      `${apiConfig.basics()}/api/columnAuthorityRequired/byPersonalIdentity/${id}`
    );
  },
  /**@desc 获取人员信息编辑内容 */
  getPersonalInformationEditSettings(data) {
    return request.post(`${apiConfig.basics()}/employeeField/getList`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 获取人员信息编辑内容 档案改版 新*/
  customEmployeeGroupGetDetailList(params) {
    return request.post(
      `${apiConfig.basics()}/api/customEmployeeGroup/getDetailList`,
      params
    );
  },
  /**@desc 获取技术档案数据 */
  getJsdaList(employeeId) {
    return request.get(`${apiConfig.basics()}/employee/jsdaList/${employeeId}`);
  },
  /**@desc 获取数据字典**/
  getDicApi(data) {
    return request.get(
      `${apiConfig.basics()}/dictItem/getDictItemByTypeCode?typeCode=${data}`
    );
  },
  /**@desc 获取tree Name */
  jobtitleBasicGet(data) {
    return request.post(`${apiConfig.basics()}/jobtitleBasic/get`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 编辑人员信息**/
  editEmployee(datas) {
    return request.post(`${apiConfig.oa()}/employee/updateEmployee`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 获取职务列表**/
  getDutyList(datas) {
    return request.post(`${apiConfig.oa()}/employee/duty/list`, datas);
  },
  /**@desc 获取个人信息**/
  getMyEmployeeDetail(datas) {
    return request.get(`${apiConfig.basics()}/employee/getMyEmployeeDetail`, {
      params: datas,
      custom: {
        showLoading: true
      }
    });
  },
  /**@desc 修改个人信息**/
  updateUserConfig(datas) {
    return request.post(
      `${apiConfig.basics()}/employee/updateUserConfig`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 修改密码**/
  editPassWord(datas) {
    return request.post(`${apiConfig.basics()}/user/chgpwd`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 更新我的信息**/
  updateMyUser(datas) {
    return request.post(`${apiConfig.basics()}/employee/updateMyUser`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 获取个人信息**/
  getPersonInfoByPrimaryKey(datas) {
    return request.post(`${apiConfig.oa()}/employee/selectByPrimaryKey`, datas);
  },
  /**@desc 获取个人信息**/
  getEmpByUserCode(datas) {
    return request.get(`${apiConfig.basics()}/employee/selectEmpByUserCode`, {
      params: datas
    });
  },
  /**@desc 获取签名信息并初始化jdk**/
  getWxJsapiSignature(datas) {
    return request.get(
      `${apiConfig.information()}/notice/getWxJsapiSignature`,
      {
        params: datas
      }
    );
  },
  /**@desc 获取工作台个人菜单**/
  getWorkBenchMenuPermissions(datas) {
    return request.get(`${apiConfig.system()}/menu/getfmenus`, {
      params: datas
    });
  },
  /**@desc 获取首页列表**/
  getIndexMessageRemindList() {
    return request.get(
      `${apiConfig.information()}/information/getMessageRemindList`
    );
  },
  /**@desc 获取个人设置**/
  getAllGlobalSetting(data) {
    return request.get(
      `${apiConfig.basics()}/globalSetting/getAllGlobalSetting`
    );
  },
  /**@desc 获取兼职职务数据 */
  getUserInfoEditPartTimePositionData(data) {
    return request.post(`${apiConfig.basics()}/position/list`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 获取人员编辑选项数据 */
  getUserInfoEditGetOptionsData(data) {
    return request.post(`${apiConfig.basics()}/post/getShowList`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 获取薪资选项数据 */
  getUserInfoEditSalaryLevelTypeData() {
    return request.post(
      `${apiConfig.hrms()}/dict/combobox/salary_level_category`
    );
  },
  /**@desc 获取薪资等级数据 */
  getUserInfoEditSalaryLevelData(data) {
    return request.post(`${apiConfig.hrms()}/salaryLevel/combobox`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 获取工作选项数据 */
  getUserInfoEditJobData() {
    return request.post(`${apiConfig.basics()}/jobtitleBasic/getJobtitleTree`);
  },
  /**@desc 获取平铺的组织机构数据 */
  getOneLineDeptListData() {
    return request.post(`${apiConfig.basics()}/organization/getOrgAllList`);
  },
  /**@desc 编辑个人信息-新版-详尽版 */
  handleEditUserInfoAsPcType(data) {
    return request.post(`${apiConfig.basics()}/cusotmEmployee/save`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 编辑个人信息-新版-详尽版 档案改版 新*/
  customEmployeeBaseSave(data) {
    return request.post(
      `${apiConfig.basics()}/api/customEmployeeBase/save`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
