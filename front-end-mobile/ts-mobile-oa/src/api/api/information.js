import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取信息发布数据**/
  getInformationDatas(datas) {
    return request.get(
      `${apiConfig.information()}/information/selectInformationContent`,
      {
        params: datas,
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 获取信息发布文件**/
  getInformationFiles(datas) {
    return request.get(
      `${apiConfig.information()}/informationAccessory/selectAccessoryByInformationId`,
      {
        params: datas
      }
    );
  },
  /**@desc 获取栏目选项**/
  getInformationChannel() {
    return request.get(
      `${apiConfig.information()}/informationChannel/getInformationChannelData`,
      {
        params: {
          id: 1
        },
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 提交会议申请**/
  saveInformationApply(datas) {
    return request.post(`${apiConfig.information()}/information/save`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 获取信息类型**/
  getInfoCountByChannel(datas) {
    return request.get(
      `${apiConfig.information()}/information/selectInfoCountByChannel`,
      {
        params: datas,
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 获取信息列表**/
  getInformationList(datas) {
    return request.get(`${apiConfig.information()}/information/list`, {
      params: datas,
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 设置信息全部已读**/
  setInformationAllRead() {
    return request.post(`${apiConfig.information()}/information/setAllRead`);
  },
  /**@desc 获取信息统计数**/
  getInfomationNumber(datas) {
    return request.get(
      `${apiConfig.information()}/information/selectInfomationNumber`,
      {
        params: datas,
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 插入阅读记录**/
  mergeInsertInformationBrowser(datas) {
    return request.post(
      `${apiConfig.information()}/information/mergeInsertInformationBrowser`,
      datas
    );
  },
  /**@desc 获取阅读量**/
  getInformationReaderSituation(datas) {
    return request.get(
      `${apiConfig.information()}/information/getReaderSituation`,
      {
        params: datas,
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 获取是否收藏**/
  getInformationIsCollect(datas) {
    return request.get(
      `${apiConfig.information()}/informationCollect/selectIsCollect`,
      {
        params: datas,
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 信息操作(收藏、取消收藏)**/
  confirmInformationCollect(type, datas) {
    return request.post(
      `${apiConfig.information()}/informationCollect/${type}`,
      datas
    );
  },
  /**@desc 信息操作**/
  operateInformation(datas) {
    return request.post(
      `${apiConfig.information()}/information/operateInformation`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 信息操作删除**/
  deleteInformation(datas) {
    return request.post(
      `${apiConfig.information()}/information/deletedById`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 查询已读人员列表**/
  getInformationReaderList(datas) {
    return request.get(`${apiConfig.information()}/information/getReaderList`, {
      params: datas,
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 查询未读信息列表**/
  getInfomationNoreadData(datas) {
    return request.get(
      `${apiConfig.information()}/information/getInfomationNoreadData`,
      {
        params: datas,
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
