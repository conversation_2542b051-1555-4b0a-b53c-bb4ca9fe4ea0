import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取任务登记**/
  superviseRegister(params) {
    return request.get(`${apiConfig.oa()}/api/superviseRegister/list`, {
      params,
      custom: {
        showLoading: true
      }
    });
  },

  /**@desc 获取任务类型**/
  getSuperviseTypeList(params) {
    return request.get(
      `${apiConfig.oa()}/api/superviseType/getSuperviseTypeList`,
      {
        params,
        custom: {
          showLoading: true
        }
      }
    );
  },

  /**@desc 保存任务登记 */
  saveTaskRegistrtion(datas) {
    return request.post(`${apiConfig.oa()}/api/superviseRegister/save`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 获取反馈列表 */
  feedbackDetails(datas) {
    return request.post(
      `${apiConfig.oa()}/api/superviseRegister/feedbackDetails`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 更新任务登记 */
  updateTaskRegistrtion(datas) {
    return request.post(
      `${apiConfig.oa()}/api/superviseRegister/update`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 督办详情 */
  getTaskDetail(params) {
    return request.get(`${apiConfig.oa()}/api/superviseRegister/${params.id}`, {
      custom: {
        showLoading: true
      }
    });
  },

  /**@desc 操作记录 */
  getSuperviseLogsList(params) {
    return request.get(
      `${apiConfig.oa()}/api/superviseLogs/getSuperviseLogsList`,
      {
        params,
        custom: {
          showLoading: true
        }
      }
    );
  },

  /**@desc 催办 */
  saveUrgentTask(datas) {
    return request.post(
      `${apiConfig.oa()}/api/superviseRegister/urgent`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 转办 */
  saveTransferTask(datas) {
    return request.post(
      `${apiConfig.oa()}/api/superviseRegister/transfer`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 批示 */
  saveApproveTask(datas) {
    return request.post(
      `${apiConfig.oa()}/api/superviseRegister/approve`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 撤销 */
  saveCancelTask(datas) {
    return request.post(
      `${apiConfig.oa()}/api/superviseRegister/cancel`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 验收 */
  saveCheckTask(datas) {
    return request.post(
      `${apiConfig.oa()}/api/superviseRegister/check`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 删除任务 */
  deleteTask(datas) {
    return request.post(
      `${apiConfig.oa()}/api/superviseRegister/delete/${datas.id}`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 终止 */
  saveCloseTask(datas) {
    return request.post(
      `${apiConfig.oa()}/api/superviseRegister/close`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 延期 */
  saveDelayTask(datas) {
    return request.post(
      `${apiConfig.oa()}/api/superviseRegister/delay`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 办理 */
  saveHandleTask(datas) {
    return request.post(
      `${apiConfig.oa()}/api/superviseRegister/handle`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 反馈 */
  saveFeedbackTask(datas) {
    return request.post(
      `${apiConfig.oa()}/api/superviseRegister/feedback`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
