import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取数据字典**/
  getDataByDataLibrary(data) {
    return request.get(
      `${apiConfig.basics()}/dictItem/getDictItemByTypeCode?typeCode=${data}`
    );
  },
  /**@desc 获取日程列表**/
  getMyTrainTableDataList(params) {
    return request.post(
      `${apiConfig.hrms()}/train/record/getDataList?` + params,
      null,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 获取培训计划详情 */
  getTrainPlanDetailData(id) {
    return request.get(`${apiConfig.hrms()}/train/plan/${id}`);
  },
  /**@desc 签到签退 */
  handleTrainPlanSignInOrOut(params) {
    return request.post(`${apiConfig.hrms()}/train/record/signin?${params}`);
  },
  /**@desc 获取积分榜数据 */
  getTrainingAccumulatePointsData(params) {
    return request.get(`${apiConfig.hrms()}/train/record/ranking`, {
      params,
      custom: {
        showLoading: true
      }
    });
  },
  /**@desc 获取培训评论列表内容 */
  getTrainingItemCommentDataList(params) {
    return request.get(`${apiConfig.hrms()}/api/traincomment/list`, {
      params,
      custom: {
        showLoading: true
      }
    });
  },
  /**@desc 评论点赞 */
  handleExpressingPointForTrainingComment(data) {
    return request.post(
      `${apiConfig.hrms()}/trainpraise/trainpraise/save`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 回复 */
  handleSaveTrainingCommentData(data) {
    return request.post(`${apiConfig.hrms()}/api/traincomment/save`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 评分 */
  handleSaveTrainingScoreData(data) {
    return request.post(`${apiConfig.hrms()}/api/trainscore/save`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 获取评分数据 */
  getSelfTrainingItemCommentData(trainPlanId) {
    return request.get(`${apiConfig.hrms()}/api/trainscore/${trainPlanId}`);
  }
};
