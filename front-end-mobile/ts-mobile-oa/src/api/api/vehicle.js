import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取车辆预约列表**/
  getVehicleAppointmentTableData(params) {
    return request.get(`${apiConfig.oa()}/api/vehicleApply/list`, {
      params,
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  /**@desc 查询用车时间是否被占用**/
  getIsOccupy(data) {
    return request.post(
      `${apiConfig.oa()}/api/vehicleApply/getIsOccupy`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 获取我的出/还车列表 */
  getVehicleUsageTableData(params) {
    return request.get(
      `${apiConfig.oa()}/api/vehicleApply/getOutAndRetuenList`,
      {
        params
      }
    );
  },

  /**@desc 车辆预约**/
  vehicleApplySave(data) {
    return request.post(`${apiConfig.oa()}/api/vehicleApply/save`, data, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  vehicleInfoSeeDetails(id) {
    return request.get(`${apiConfig.oa()}/api/vehicleInfo/${id}`);
  },

  /**@desc 获取车辆预约详情 */
  getVehicleAppointmentDetail(id) {
    return request.get(`${apiConfig.oa()}/api/vehicleApply/${id}`);
  },

  /**@desc 获取我的审批表格数据 */
  getVehicleApprovalTableData(params) {
    return request.get(`${apiConfig.oa()}/api/vehicleApply/getApprovalList`, {
      params
    });
  },

  /**@desc 获取我的审批表格数据 */
  getVehicleApprovalTableData(params) {
    return request.get(`${apiConfig.oa()}/api/vehicleApply/getApprovalList`, {
      params
    });
  },

  /**@desc 获取车辆列表**/
  vehicleInfoList(params) {
    return request.get(`${apiConfig.oa()}/api/vehicleInfo/list`, params, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  /**@desc 车辆不分页**/
  getVehicleInfoList(data) {
    return request.post(
      `${apiConfig.oa()}/api/vehicleInfo/getVehicleInfoList`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 查询司机不分页**/
  getVehicleDriverList(data) {
    return request.post(
      `${apiConfig.oa()}/api/vehicleDriver/getVehicleDriverList`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 油卡列表**/
  getVehicleOilList(data) {
    return request.post(
      `${apiConfig.oa()}/api/vehicleOil/getVehicleOilList`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 审批通过 */
  handleVehicleApprovalPass(data) {
    return request.post(`${apiConfig.oa()}/api/vehicleApply/pass`, data, {
      header: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    });
  },
  /**@desc 审批不通过 */
  handleVehicleApprovalReject(data) {
    return request.post(`${apiConfig.oa()}/api/vehicleApply/reject`, data, {
      header: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    });
  },
  /**@desc 派车 */
  vehicleApplyDispatch(data) {
    return request.post(`${apiConfig.oa()}/api/vehicleApply/dispatch`, data, {
      header: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    });
  },
  /**@desc 出车 */
  handleOutVehicle(data) {
    return request.post(`${apiConfig.oa()}/api/vehicleApply/outVehicle`, data, {
      header: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    });
  },
  /**@desc 还车 */
  handleReturnVehicle(data) {
    return request.post(
      `${apiConfig.oa()}/api/vehicleApply/returnVehicle`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      }
    );
  }
};
