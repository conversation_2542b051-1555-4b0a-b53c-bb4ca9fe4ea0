import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取版本列表**/
  getVersionList(datas) {
    return request.post(`${apiConfig.basics()}/version/list`, datas);
  },
  /**@desc 获取版本信息**/
  getWxVersionDetailsByVersionId(datas) {
    return request.get(
      `${apiConfig.basics()}/version/getWxVersionDetailsByVersionId`,
      {
        params: datas,
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
