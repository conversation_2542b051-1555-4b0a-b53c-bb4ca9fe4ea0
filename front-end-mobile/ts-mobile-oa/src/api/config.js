// let indexConfig = window[process.env.VUE_APP_BASE_URL];
let indexConfig = [
  {
    matchPath: '*',
    config: {
      sso: { base: '', url: '/user' },
      resource: { base: '', url: '/ts-resource' },
      form: { base: '', url: '/ts-form' },
      external: { base: '', url: '/ts-external' },
      basics: { base: '', url: '/ts-basics-bottom' },
      information: { base: '', url: '/ts-information' },
      oa: { base: '', url: '/ts-oa' },
      workflow: { base: '', url: '/ts-workflow' },
      worksheet: { base: '', url: '/ts-worksheet' },
      system: { base: '', url: '/ts-system' },
      document: { base: '', url: '/ts-document' },
      preview: { base: '', url: '/ts-preview' },
      hrms: { base: '', url: '/ts-hrms' }
    }
  }
];

let mode = 0;
let config = {};
let _config = {};
if (process.env.NODE_ENV == 'development') {
  config = indexConfig[mode].config;
} else {
  let configArr = indexConfig.filter(item => {
    if (item.matchPath == '*') {
      return true;
    } else {
      if (location.href.indexOf(item.matchPath) != -1) {
        return true;
      }
    }
    return false;
  });
  config = configArr[0].config;
}
for (let key in config) {
  _config[key] = function() {
    if (Object.prototype.toString.call(config[key]) == '[object Object]') {
      //如果是对象 则进行处理
      if (config[key].base) {
        return `${config[key].base || ''}${config[key].url || ''}`;
      }
      return `${config.base || ''}${config[key].base || ''}${config[key].url ||
        ''}`;
    } else if (
      Object.prototype.toString.call(config[key]) == '[object String]'
    ) {
      return config[key];
    }
  };
  _config[`_${key}`] = config[key];
}

export const apiConfig = _config;
