// #ifdef H5
if (window.__POWERED_BY_QIANKUN__) {
  if (process.env.NODE_ENV === 'development') {
    __webpack_public_path__ = `//localhost:${process.env.VUE_APP_PORT}${process.env.BASE_URL}`;
  } else {
  }
}
// #endif

import Vue from 'vue';
import App from './App';

App.mpType = 'app';
let instance = null;

Vue.config.productionTip = false;

//状态管理
import store from './store';
Vue.prototype.$store = store;

//引入头部导航栏组件
import pageHead from './components/page-head/page-head.vue';
Vue.component('page-head', pageHead);

//相关基础配置
import config from './common/js/setting.js';
Vue.prototype.$config = config;

//公共方法
import common from './common/js/common.js';
Vue.prototype.$common = common;

//全局过滤器
import filters from './common/js/filters.js';
for (let key in filters) {
  Vue.filter(key, filters[key]);
}
//引入request
// import request from 'common/js/request.js';
// Vue.prototype.$request = request;
import ajax from './api/index.js';
Vue.use(ajax);
import $api from './api/ajax.js';
Vue.prototype.$api = $api;

//dayjs插件
import dayjs from 'dayjs';
Vue.prototype.$dayjs = dayjs;

import oaModule from './common/js/oaModule.js';
Vue.prototype.$oaModule = oaModule;

import downloadFile from './common/js/downloadFile.js';
Vue.prototype.$downloadFile = downloadFile;

//uview-ui
import trasenuView from '@trasen-oa/trasen-uview-ui';
Vue.use(trasenuView);

import baseConfig from './config/config.js';
Object.keys(baseConfig).forEach(item => {
  Vue.prototype[`$${item}`] = baseConfig[item];
});

Vue.mixin({
  created: function() {
    document.title = `${this.$store.state.globalSetting.webTitle ||
      '综合协同办公平台'}`;
  }
});

function render(props = {}) {
  let base = '';
  if (window.__POWERED_BY_QIANKUN__) {
    base = props.data.activeRule || '/';
  } else {
    base = document.querySelector('base').href || '/';
    base = base.replace(/^https?:\/\/[^\/]+/, '');
  }
  window.__uniConfig.router.base = base;
  Vue.prototype.$__uniConfig = {
    mode: 'history',
    base
  };
  const router = Vue.prototype.qiankunRouter();
  instance = new Vue({
    router,
    store,
    ...App
  });
  instance.$mount();
}
// #ifdef H5
if (!window.__POWERED_BY_QIANKUN__) {
  Vue.prototype.qiankunParentNode = document.body;
  render();
}
// #endif

//非h5
// #ifndef H5
instance = new Vue({
  store,
  ...App
});
instance.$mount();
// #endif

export async function bootstrap(props) {}
export async function mount(props) {
  Vue.prototype.qiankunParentNode = document.getElementById(
    process.env.VUE_APP_CONTAINER
  ).parentNode;
  Object.keys(props.fn).forEach(method => {
    Vue.prototype[`$${method}`] = props.fn[method];
  });
  // 设置通讯
  let storeState = props.fn.storeInfo();
  store.state.token = storeState.common.token; //主动获取用户信息
  store.state.empcode = storeState.common.empCode; //主动获取用户信息
  store.state.username = (storeState.common.userInfo || {}).employeeName; //主动获取用户信息
  store.state.globalSetting = storeState.common.globalSetting; //主动获取系统配置
  store.state.userInfo = storeState.common.userInfo;
  store.state.systemCustomCode = storeState.common.systemCustomCode;
  store.state.personalSortData = storeState.common.personalSortData;
  Vue.prototype.$onGlobalStateChange = props.onGlobalStateChange;
  Vue.prototype.$setGlobalState = props.setGlobalState;
  render(props);
}
export async function update(props) {}
export async function unmount() {
  instance.$destroy();
  instance.$el.innerHTML = '';
  instance = null;
}
