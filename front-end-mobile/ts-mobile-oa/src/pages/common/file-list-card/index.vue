<template>
  <view class="file_list_card">
    <base-item
      left-text="相关资料"
      :right-style="{ color: '#333333', fontSize: '28rpx' }"
    >
      <template v-slot:right>
        <view class="icon_box" v-show="!readOnly">
          <form
            class="addBox oa-icon oa-icon-plus-circle"
            ref="fileinput"
          ></form>
          <view v-show="readOnly" class="mask_layer"></view>
        </view>
      </template>
    </base-item>
    <base-item
      v-for="file in fileList"
      :key="file.id"
      :left-text="file.originalName"
      :left-style="{ color: '#666666', flex: '1' }"
      @click="handleActionFile(file)"
      style="background: #fff;"
    />
    <bottom-menu
      ref="bottomMenu"
      :actions="bottomMenuActions"
      @preview="handlePreview"
      @download="handleDownload"
      @delete="handleDelete"
    />
  </view>
</template>

<script>
import BaseItem from '@/components/base-item/base-item.vue';
import BottomMenu from '@/components/bottom-menu/bottom-menu.vue';
import Base64 from '@/common/js/base64.min.js';
export default {
  components: { BaseItem, BottomMenu },
  props: {
    accessoryId: {
      type: String
    },
    readOnly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: [],
      bottomMenuActions: [
        { label: '预览', emitName: 'preview' },
        { label: '下载', emitName: 'download' },
        { label: '删除', emitName: 'delete' }
      ]
    };
  },
  methods: {
    async getFileList() {
      try {
        // 附件列表
        const fileList = await this.ajax.getFileAttachmentByBusinessIdGET({
          businessId: this.accessoryId
        });
        this.fileList = fileList.object || [];
      } catch (error) {}
    },
    initUploadFile() {
      if (this.$refs.fileinput) {
        let input = document.createElement('input');
        input.style.width = '100%';
        input.type = 'file'; //添加file类型
        // input.accept='.pdf' //限制只能上传PDF文件
        input.style.height = '100%';
        input.style.position = 'absolute';
        input.style.top = '0';
        input.style.right = '0';
        input.style.opacity = '0';
        input.style.overflow = 'hidden'; //防止注意input 元素溢出
        input.id = 'file';
        input.multiple = 'multiple'; //安卓浏览器不兼容（除QQ浏览器）
        input.onchange = event => {
          let files = event.target.files;
          this.uploadFile(files, input);
        };
        this.$refs.fileinput.$el.appendChild(input);
      }
    },
    //上传文件
    uploadFile(files, inputDom) {
      //创建对象实例
      let formData = new FormData();
      //追加文件数据
      for (let i = 0; i < files.length; i++) {
        formData.append('file', files[i]);
      }
      //上传单个
      let xhr = new XMLHttpRequest();
      let requestUrl = `${this.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/upload?moduleName=oa&businessId=${this.accessoryId}`;
      xhr.open('POST', requestUrl, true);
      xhr.upload.addEventListener(
        'progress',
        function(event) {
          if (event.lengthComputable) {
            let percent = Math.ceil((event.loaded * 100) / event.total) + '%';
            uni.showLoading({
              title: `上传中(${percent})`
            });
          }
        },
        false
      );
      xhr.ontimeout = function() {
        // xhr请求超时事件处理
        uni.showToast({
          title: '请求超时',
          icon: 'none'
        });
      };
      xhr.onreadystatechange = async () => {
        if (xhr.readyState === 4 && xhr.status === 200) {
          //上传成功
          let res = JSON.parse(xhr.responseText);
          if (res.statusCode === 302 || res.statusCode === 21000) {
            this.$store.dispatch('common/goToLogin');
          } else if (res.statusCode != 200) {
            uni.showModal({
              title: '提示',
              showCancel: false,
              confirmColor: '#005BAC',
              content: res.message
            });
          } else {
            uni.showToast({
              title: '上传成功',
              icon: 'none'
            });
            inputDom.value = '';
            const fileList = await this.ajax.getFileAttachmentByBusinessIdGET({
              businessId: this.accessoryId
            });
            this.fileList = fileList.object || [];
          }
        }
      };
      xhr.send(formData);
    },
    handleActionFile(row) {
      this.$refs.bottomMenu.show(row);
    },
    // 文件预览
    handlePreview(row) {
      let filePath = `${
        this.$config.ENABLE_FILE_PREVIEW
          ? this.$config.DOCUMENT_BASE_HOST
          : this.$config.BASE_HOST
      }/ts-basics-bottom/fileAttachment/downloadFile/${row.fileId ||
        row.id}?fullfilename=${row.id}.${row.fileExtension}&token=${
        this.token
      }`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      } else {
        this.$downloadFile.downloadFile(filePath);
      }
    },
    // 文件下载
    handleDownload(row) {
      let filePath = `${
        this.$config.BASE_HOST
      }/ts-basics-bottom/fileAttachment/downloadFile/${row.fileId ||
        row.id}?fullfilename=${row.id}.${row.fileExtension}&token=${
        this.token
      }`;
      this.$downloadFile.downloadFile(filePath);
    },
    // 删除附件
    async handleDelete(row) {
      try {
        await this.ajax.deleteFileId({
          fileid: row.id
        });
        uni.showToast({
          title: '删除成功！'
        });
        const idx = this.fileList.findIndex(e => {
          return e.id == row.id;
        });
        this.fileList.splice(idx, 1);
      } catch (error) {}
    }
  },
  mounted() {
    this.initUploadFile();
  },
  watch: {
    accessoryId: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.getFileList();
        }
      },
      immediate: true,
      deep: true
    },
    readOnly: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.bottomMenuActions = [
            { label: '预览', emitName: 'preview' },
            { label: '下载', emitName: 'download' }
          ];
        } else {
          this.bottomMenuActions = [
            { label: '预览', emitName: 'preview' },
            { label: '下载', emitName: 'download' },
            { label: '删除', emitName: 'delete' }
          ];
        }
      },
      immediate: true,
      deep: true
    }
  }
};
</script>
<style lang="scss" scoped>
.file_list_card {
  .icon_box {
    display: flex;
    align-items: center;
    position: relative;
    .mask_layer {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      background: white;
    }
  }
  .addBox {
    font-size: 36rpx;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>
