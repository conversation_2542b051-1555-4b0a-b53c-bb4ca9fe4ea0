<template>
  <view class="main_agenda">
    <u-navbar title="议程编辑"> </u-navbar>
    <view class="main_agenda_form">
      <u-form
        :label-width="190"
        :model="form"
        ref="uForm"
        :error-type="errorType"
      >
        <u-form-item label="主题" :required="true" prop="agenda">
          <u-input
            input-align="right"
            trim
            v-model="form.agenda"
            placeholder="请输入主题"
          />
        </u-form-item>
        <u-form-item label="负责人" :required="true" prop="functionary">
          <u-input
            input-align="right"
            trim
            v-model="form.functionary"
            placeholder="请输入负责人"
          />
        </u-form-item>
        <u-form-item label="议程内容" label-position="top">
          <u-input
            trim
            type="textarea"
            v-model="form.content"
            placeholder="请输入议程内容"
          />
        </u-form-item>
      </u-form>
    </view>
    <view class="booking_details_footer">
      <view
        class="booking_details_footer_btn"
        style="color: #999999;"
        v-if="dataSource.type === 'EDIT'"
        @click="handleDelete"
      >
        删除
      </view>
      <view
        style="color: #005bac;"
        class="booking_details_footer_btn"
        @click="submit"
      >
        保存
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dataSource: { type: 'ADD', data: {} },
      formLabelWidth: '190',
      form: {
        agenda: '',
        functionary: '',
        content: ''
      },
      rules: {
        agenda: [
          {
            required: true,
            message: '请输入主题',
            trigger: ['change', 'blur']
          }
        ],
        functionary: [
          {
            required: true,
            message: '请输入负责人',
            trigger: ['change', 'blur']
          }
        ]
      },
      errorType: ['toast']
    };
  },
  onLoad() {
    this.dataSource = JSON.parse(uni.getStorageSync('mainAgendaData'));
    this.form = Object.assign(this.form, this.dataSource.data || {});
  },
  mounted() {
    this.$refs.uForm.setRules(this.rules);
  },
  methods: {
    submit() {
      this.$refs.uForm.validate(valid => {
        if (valid) {
          uni.$emit('mainAgenda', {
            type: this.dataSource.type === 'ADD' ? 'add' : 'edit',
            data: JSON.parse(JSON.stringify(this.form))
          });
          uni.navigateBack();
        }
      });
    },
    handleDelete() {
      uni.$emit('mainAgenda', { type: 'delete' });
      uni.navigateBack();
    }
  }
};
</script>
<style lang="scss" scoped>
.main_agenda {
  height: 100%;

  background: white;
  display: flex;
  flex-direction: column;
  .main_agenda_form {
    padding: 0 30rpx;
    flex: 1;
    overflow-y: auto;
  }
  .booking_details_footer {
    margin-top: 20rpx;
    height: 88rpx;
    background: #ffffff;
    box-shadow: 0px -2px 4px 0px rgba(182, 182, 182, 0.5);
    // position: absolute;
    // bottom: 0;
    // left: 0;
    // right: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: 32rpx;
    font-weight: 400;
    .booking_details_footer_btn {
      flex: 1;
      text-align: center;
    }
  }
}
.right_slot {
  padding-right: 30rpx;
  font-size: 28rpx;
  color: #333333;
}
</style>
