<template>
  <view
    class="ts-content"
    style="background-color: #fff; background-position-x: -60rpx;"
  >
    <page-head title="值班查看" @clickLeft="returnBack"></page-head>

    <view class="content_top_month">
      <view class="top_text" @click="showPicker('date')">
        {{ `${dateStr}年` }}</view
      >
      <uni-icons
        type="arrowdown"
        :size="30"
        class="uni-icon-wrapper"
        color="#333"
        @click="showPicker('date')"
      ></uni-icons>

      <view class="tips_text">点击电话图标即可拨打该人电话</view>
    </view>

    <fixed-table
      id="DutyViewTable"
      :data="dutyViewList"
      :header="columns"
      :fixed="true"
      :firstFixed="true"
      :border="true"
      :stripe="true"
      :showActions="true"
    />

    <date-picker
      startDate="2000"
      mode="date"
      :value="dateStr"
      @confirm="onConfirm"
      ref="date"
      fields="month"
      :disabled-after="false"
    ></date-picker>
  </view>
</template>

<script>
let handleCallPhone = val => {
  if (val && val.includes('-')) {
    let phone = val.split('-')[1];

    let phoneReg = new RegExp(/^1[3-9][0-9]{9}$/g); //手机号校验规则
    if (phoneReg.test(phone)) {
      window.location.href = 'tel:' + phone;
    }
  }
};
import { mapState } from 'vuex';
import datePicker from '@/components/picker/date-picker.vue';
import FixedTable from '@/components/fixed-table/fixed-table.vue';
export default {
  components: {
    datePicker,
    FixedTable
  },
  data() {
    return {
      dateStr: '',
      fromPage: '',
      dutyViewList: [],
      columns: [
        {
          prop: 'reportDate',
          title: '值班日期',
          width: 70,
          click: val => {}
        },
        {
          prop: 'weekdayame',
          title: '星期',
          width: 55,
          click: val => {}
        },
        {
          prop: 'zbld',
          title: '值班领导',
          phoneIcon: true,
          width: 70,
          formatter: val => {
            if (val && val.includes('-')) {
              return val.split('-')[0];
            }
            return val;
          },
          click: val => {
            handleCallPhone(val);
          }
        },
        {
          prop: 'xzzb',
          title: '行政值班',
          phoneIcon: true,
          width: 70,
          formatter: val => {
            if (val && val.includes('-')) {
              return val.split('-')[0];
            }
            return val;
          },
          click: val => {
            handleCallPhone(val);
          }
        },
        {
          prop: 'sdzb',
          title: '水电值班',
          phoneIcon: true,
          width: 70,
          formatter: val => {
            if (val && val.includes('-')) {
              return val.split('-')[0];
            }
            return val;
          },
          click: val => {
            handleCallPhone(val);
          }
        },
        {
          prop: 'jskzb',
          title: '精神科值班',
          phoneIcon: true,
          width: 85,
          formatter: val => {
            if (val && val.includes('-')) {
              return val.split('-')[0];
            }
            return val;
          },
          click: val => {
            handleCallPhone(val);
          }
        },
        {
          prop: 'jszb',
          title: '精四值班',
          phoneIcon: true,
          width: 70,
          formatter: val => {
            if (val && val.includes('-')) {
              return val.split('-')[0];
            }
            return val;
          },
          click: val => {
            handleCallPhone(val);
          }
        },
        {
          prop: 'lyzb',
          title: '老一值班',
          phoneIcon: true,
          width: 70,
          formatter: val => {
            if (val && val.includes('-')) {
              return val.split('-')[0];
            }
            return val;
          },
          click: val => {
            handleCallPhone(val);
          }
        },
        {
          prop: 'lszb',
          title: '老三值班',
          phoneIcon: true,
          width: 70,
          formatter: val => {
            if (val && val.includes('-')) {
              return val.split('-')[0];
            }
            return val;
          },
          click: val => {
            handleCallPhone(val);
          }
        },
        {
          prop: 'ysexzb',
          title: '医生二线值班',
          phoneIcon: true,
          width: 110,
          formatter: val => {
            if (val && val.includes('-')) {
              return val.split('-')[0];
            }
            return val;
          },
          click: val => {
            handleCallPhone(val);
          }
        },
        {
          prop: 'hszexzb',
          title: '护士长二线值班',
          phoneIcon: true,
          width: 120,
          formatter: val => {
            if (val && val.includes('-')) {
              return val.split('-')[0];
            }
            return val;
          },
          click: val => {
            handleCallPhone(val);
          }
        },
        {
          prop: 'zbsj',
          title: '值班司机',
          phoneIcon: true,
          width: 70,
          formatter: val => {
            if (val && val.includes('-')) {
              return val.split('-')[0];
            }
            return val;
          },
          click: val => {
            handleCallPhone(val);
          }
        }
      ]
    };
  },
  computed: {
    ...mapState(['empcode', 'username', 'globalSetting'])
  },
  async onLoad(opt) {
    this.fromPage = opt.fromPage;

    this.dateStr = this.$dayjs().format('YYYY-MM');
    this.getMonthDutyList();
  },
  methods: {
    addZero(num) {
      var num = num - 0;
      return num > 9 ? num : '0' + num;
    },

    async getMonthDutyList() {
      let nowDate = this.addZero(new Date().getDate());
      let employeeName = this.$store.state.userInfo.employeeName;

      await this.ajax
        .getAllByReportDate(this.dateStr)
        .then(res => {
          if (res.success && res.statusCode === 200) {
            this.dutyViewList = res.object || [];
            this.dutyViewList.forEach(item => {
              if (item.reportDate == nowDate) item.className = 'high-light';
            });
          } else {
            uni.showToast({
              title: '获取失败,请联系管理员',
              icon: 'none'
            });
          }

          setTimeout(() => {
            const DutyViewTable = document.getElementById('DutyViewTable');
            const tds = DutyViewTable.getElementsByClassName('item');

            [...tds].forEach(ele => {
              if (ele.innerText === employeeName) ele.style.color = 'red';
            });
          }, 300);
        })
        .catch(() => {});
    },
    showPicker(type) {
      this.$refs[type].show();
    },
    async onConfirm(e) {
      this.dateStr = e.result;
      this.getMonthDutyList();
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/${this.fromPage}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  ::v-deep .high-light {
    background: rgb(255, 153, 0) !important;
    color: #fff;
  }

  .content_top_month {
    width: 100%;
    height: 44px;
    line-height: 44px;
    padding: 0 20rpx;
    background-color: rgba(0, 91, 172, 0.1);
    box-sizing: border-box;
    display: flex;
    align-items: center;
    .top_text {
      display: inline-block;
      margin-right: 10rpx;
      font-size: 32rpx;
      color: #333333;
      box-sizing: border-box;
    }
    .tips_text {
      display: inline-block;
      margin-left: 60rpx;
      font-size: 24rpx;
      color: #333333;
      box-sizing: border-box;
      color: #4a99d7;
    }
  }

  ::v-deep .fixed-table {
    height: calc(100vh - 44px - 44px) !important;
    table {
      width: 250%;
    }
  }
}
</style>
