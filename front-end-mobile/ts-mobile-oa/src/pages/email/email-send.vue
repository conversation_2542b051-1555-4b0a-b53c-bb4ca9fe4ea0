<template>
  <view class="ts-content">
    <page-head
      title="发邮件"
      @clickLeft="returnBack"
      right-text="发送"
      @clickRight="sendEmail"
    ></page-head>
    <view class="row_data">
      <text class="label">收件人：</text>
      <view class="uni-input">{{ email.toName }}</view>
      <text
        class="add_icon oa-icon oa-icon-tianjiachaosong"
        @click="choosePerson"
      ></text>
    </view>
    <view class="row_data">
      <text class="label">抄送：</text>
      <view class="uni-input">{{ email.ccName }}</view>
      <text
        @click="chooseCcPerson"
        class="add_icon oa-icon oa-icon-tianjiachaosong"
      ></text>
    </view>
    <view class="row_data">
      <text class="label">密送：</text>
      <view class="uni-input">{{ email.bccName }}</view>
      <text
        @click="chooseBccPerson"
        class="add_icon oa-icon oa-icon-tianjiachaosong"
      ></text>
    </view>
    <view class="row_data">
      <text class="label">主题：</text>
      <input class="row_input" v-model="email.subject" />
      <text @click="chooseFile" class="file_icon oa-icon oa-icon-fujian"></text>
    </view>
    <view>
      <textarea
        maxlength="-1"
        v-model="email.content"
        placeholder="请输入正文"
        class="textarea"
      />
    </view>
    <view class="opt-row">
      <view v-if="isShow">
        <view class="mask" v-if="isShowFile" @click="isShowFileList"></view>
        <view class="attachment">
          <view
            class="file_icon oa-icon oa-icon-fujian"
            @click="isShowFileList"
          >
            {{ fileCount }}个附件</view
          >
          <transition name="slide-fade">
            <view class="attachment_list" v-show="isShowFile">
              <view
                class="attachment_item"
                v-for="(item, index) in attachmentList"
                :key="index"
              >
                <view
                  class="attachment_item_content"
                  @click="previewFile(item.fileId, item.fileRealName)"
                >
                  <text
                    class="oa-icon"
                    :class="
                      'oa-icon-' + $oaModule.formatFileType(item.fileType)
                    "
                  ></text>
                  <view class="attachment_item_info">
                    <text class="original_name">{{ item.fileName }}</text>
                    <text class="file_size">{{
                      item.fileSize | fileSizeFilter
                    }}</text>
                  </view>
                </view>
                <view
                  class="oa-icon oa-icon-xiazai down_load"
                  @tap.stop="downloadFile(item.fileId, item.fileRealName)"
                >
                </view>
                <text
                  class="oa-icon oa-icon-guanbi delete_file"
                  @click.stop="deleteFile(item.fileId)"
                ></text>
              </view>
            </view>
          </transition>
        </view>
      </view>
    </view>
    <uni-popup ref="showpopup" :type="popupType">
      <view class="draft">
        <view class="option_wrap">
          <view class="option option_row" @click="noSaveDraft">
            <text class="no_save_draft">不保存草稿</text>
          </view>
          <view class="option option_row" @click="saveDraft">
            <text class="save_draft">保存草稿</text>
          </view>
        </view>
        <view class="option" @click="cancel"><text>取消</text></view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { chooseImage, chooseFile } from '../../common/js/uploadImg.js';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import Base64 from '@/common/js/base64.min.js';
export default {
  components: {
    uniPopup
  },
  data() {
    return {
      fromPage: '',
      popupType: 'bottom',
      email: {
        id: '',
        toId: '',
        toName: '',
        ccId: '',
        ccName: '',
        subject: '',
        content: '',
        uploadedFile: '',
        saveToOutbox: 'on',
        sendToWx: 'on'
      },
      isShow: false,
      isShowFile: false,
      attachmentList: [],
      fileCount: 0,
      personlist: [],
      personCclist: [],
      personBccList: [],
      statusId: null,
      index: 0,
      fromPersonCode: ''
    };
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    if (undefined != opt.index) this.index = opt.index;
    if (undefined != opt.index && null != opt.statusId) {
      this.ajax
        .getEmailDetails({
          statusId: opt.statusId,
          from: phone
        })
        .then(res => {
          let data = res.object;
          this.email.id = data.id;
          this.email.toId = data.toId;
          this.email.toName = data.toName;
          this.email.ccId = data.ccId;
          this.email.ccName = data.ccName;
          this.email.subject = data.subject;
          this.email.content = data.content;
          if (null != data.toEmpList && data.toEmpList.length > 0) {
            this.personlist = data.toEmpList.map(item => {
              return {
                name: item.empName,
                id: item.empCode,
                empFirstName: item.empName.substring(item.empName.length - 2),
                empHeadImg: item.empHeadImg,
                empDeptName: item.empDeptName,
                empDutyName: item.empDutyName,
                empSex: item.empSex,
                choose: true
              };
            });
          }
          if (null != data.ccEmpList && data.ccEmpList.length > 0) {
            this.personCclist = data.ccEmpList.map(item => {
              return {
                name: item.empName,
                id: item.empCode,
                empFirstName: item.empName.substring(item.empName.length - 2),
                empHeadImg: item.empHeadImg,
                empDeptName: item.empDeptName,
                empDutyName: item.empDutyName,
                empSex: item.empSex,
                choose: true
              };
            });
          }
          if (null != data.bccEmplist && data.bccEmplist.length > 0) {
            this.personBcclist = data.bccEmplist.map(item => {
              return {
                name: item.empName,
                id: item.empCode,
                empFirstName: item.empName.substring(item.empName.length - 2),
                empHeadImg: item.empHeadImg,
                empDeptName: item.empDeptName,
                empDutyName: item.empDutyName,
                empSex: item.empSex,
                choose: true
              };
            });
          }
          if (null != data.attachmentList && data.attachmentList.length > 0) {
            this.isShow = true;
            this.attachmentList = data.attachmentList.map(e => {
              let nameList = fileName[index].split('.');
              return {
                fileId: e.id,
                fileName: e.originalName,
                filePath: e.filePath,
                fileSize: e.fileSize,
                fileType:
                  nameList.length > 1 ? nameList[nameList.length - 1] : ''
              };
            });
            this.fileCount = this.attachmentList.length;
          }
        });
    } else if (undefined != opt.usercode) {
      this.fromPersonCode = opt.usercode;
      this.email.toId = opt.usercode;
      let username = opt.username;
      this.email.toName = username;
      this.personlist = JSON.parse(uni.getStorageSync('person_list'));
    }
  },
  methods: {
    choosePerson() {
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        this.personlist = data;
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
        this.email.toId = '';
        this.email.toName = '';
        this.personlist.forEach(e => {
          this.email.toId += e.id + ',';
          this.email.toName += e.name + ',';
        });
        this.email.toId = this.email.toId.substring(
          0,
          this.email.toId.length - 1
        );
        this.email.toName = this.email.toName.substring(
          0,
          this.email.toName.length - 1
        );
      });
      uni.setStorageSync('person_list', JSON.stringify(this.personlist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=checkBox'
      });
    },
    chooseCcPerson() {
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        this.personCclist = data;
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
        this.email.ccId = '';
        this.email.ccName = '';
        this.personCclist.forEach(e => {
          this.email.ccId += e.id + ',';
          this.email.ccName += e.name + ',';
        });
        this.email.ccId = this.email.ccId.substring(
          0,
          this.email.ccId.length - 1
        );
        this.email.ccName = this.email.ccName.substring(
          0,
          this.email.ccName.length - 1
        );
      });
      uni.setStorageSync('person_list', JSON.stringify(this.personCclist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=checkBox'
      });
    },
    chooseBccPerson() {
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        this.personBccList = data;
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
        this.email.bccId = '';
        this.email.bccName = '';
        this.personBccList.forEach(e => {
          this.email.bccId += e.id + ',';
          this.email.bccName += e.name + ',';
        });
        this.email.bccId = this.email.bccId.substring(
          0,
          this.email.bccId.length - 1
        );
        this.$set(
          this.email,
          'bccName',
          this.email.bccName.substring(0, this.email.bccName.length - 1)
        );
        this.$forceUpdate();
      });
      uni.setStorageSync('person_list', JSON.stringify(this.personBccList));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=checkBox'
      });
    },
    chooseFile() {
      chooseFile({
        limitNum: 9, //数量
        showLoading: true,
        hideLoading: true,
        loadingTitle: '上传中...',
        uploadFileUrl: `${this.$config.BASE_HOST}/ts-document/attachment/fileUpload?module=email`, //服务器地址
        fileKeyName: 'file', //参数
        success: res => {
          let resVal = JSON.parse(res),
            nameList = resVal.object[0].fileName.split('.');
          resVal.object[0].fileType =
            nameList.length > 1 ? nameList[nameList.length - 1] : '';
          this.email.uploadedFile += resVal.object[0].fileId + ',';
          this.attachmentList.push(resVal.object[0]);
          this.fileCount = this.attachmentList.length;
          this.isShow = true;
        }
      });
    },
    sendEmail() {
      let data = this.email;
      //发送邮件需判断必填项
      if (null == data.toName || '' == data.toName) {
        uni.showToast({
          title: '收件人不能为空',
          duration: 2000,
          icon: 'none'
        });
        return;
      } else if (null == data.subject || '' == data.subject) {
        uni.showToast({
          title: '请输入主题',
          duration: 2000,
          icon: 'none'
        });
        return;
      } else if (null == data.content || '' == data.content) {
        uni.showToast({
          title: '请输入正文',
          duration: 2000,
          icon: 'none'
        });
        return;
      }
      this.sendRequest(true);
    },
    //保存草稿
    saveDraft() {
      this.sendRequest(false);
    },
    //不保存草稿
    noSaveDraft() {
      if (this.index == 0) {
        uni.navigateTo({
          url: `/pages/email/email-list?fromPage=${this.fromPage}`
        });
      } else if (this.index == 5) {
        uni.removeStorageSync('person_list');
        uni.navigateTo({
          url: `/pages/addressBook/personal-info?usercode=${this.fromPersonCode}&fromPage=${this.fromPage}`
        });
      } else {
        uni.navigateTo({
          url: `/pages/email/email-box-list?folderId=${this.index}&fromPage=${this.fromPage}`
        });
      }
    },
    cancel() {
      this.$nextTick(() => {
        this.$refs['showpopup'].close();
      });
    },
    /**
     * @func
     * @desc 提交邮件
     * param {Boolean} isSendEmail = [true|false] 发送邮件|保存邮件
     */
    sendRequest(isSendEmail) {
      let data = this.email;
      data.isDraft = isSendEmail ? '0' : '1';
      this.attachmentList.forEach(e => {
        data.uploadedFile += e.fileId + ',';
      });
      this.ajax
        .sendEmailInternal(data, isSendEmail ? '正在发送' : '正在保存草稿')
        .then(res => {
          uni.navigateTo({
            url: `/pages/email/email-box-list?folderId=${
              isSendEmail ? '2' : '3'
            }&fromPage=${this.fromPage}`
          });
        });
    },
    isShowFileList() {
      this.isShowFile = !this.isShowFile;
    },
    //删除附件
    deleteFile(id) {
      let fileList = this.email.uploadedFile.split(',');
      this.attachmentList.some((item, i) => {
        if (item.fileId == id) {
          fileList.splice(i, 1);
          this.attachmentList.splice(i, 1);
          return true;
        }
      });
      this.email.uploadedFile = fileList.join(',');
      this.fileCount = this.attachmentList.length;
      if (this.fileCount == 0) {
        this.isShowFile = false;
        this.isShow = false;
      }
    },
    //查看附件详情
    previewFile(id, fileName) {
      let filePath = `${this.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    },
    downloadFile(id, fileName) {
      let filePath = `${this.$config.BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      this.$downloadFile.downloadFile(filePath);
    },
    //显示弹出层
    togglePopup(type) {
      this.popupType = type;
      this.$nextTick(() => {
        this.$refs['showpopup'].open();
      });
    },
    //关闭弹出层
    popupConfirm() {
      this.$refs['showpopup'].close();
    },
    returnBack() {
      if (
        this.attachmentList.length == 0 &&
        this.email.toName == '' &&
        this.email.ccName == '' &&
        this.email.subject == '' &&
        this.email.content == ''
      ) {
        this.noSaveDraft();
        return;
      }
      this.togglePopup('bottom');
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  background: #ffffff;
  .row_data {
    font-size: 32rpx;
    border-bottom: 1px solid #efefef;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 0 10rpx;
    .label {
      font-size: 32rpx;
      color: #333333;
      padding: 20rpx;
    }
    .uni-input {
      overflow: auto;
    }
    .row_input {
      flex: 1;
      font-size: 32rpx;
      padding: 20rpx 0;
    }

    .file_icon,
    .add_icon {
      color: #10b47f;
      font-size: 40rpx;
      padding: 20rpx;
      line-height: 1;
    }
    .add_icon {
      color: #005bac;
    }
  }
  .textarea {
    font-size: 32rpx;
    padding: 20rpx 30rpx;
    height: 500rpx !important;
    width: 100%;
    box-sizing: border-box;
  }
  .opt-row {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    box-shadow: 0 -1px 6px #ddd;
    .mask {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #333333;
      opacity: 0.2;
      z-index: 9;
    }
    .attachment {
      z-index: 10;
      background-color: #ffffff;
      position: relative;
      height: 100%;
      .file_icon {
        color: #10b47f;
        font-size: 28rpx;
        text-align: center;
        height: 80rpx;
        line-height: 80rpx;
        z-index: 10;
        background-color: #ffffff;
        position: relative;
      }
      .attachment_list {
        background-color: #ffffff;
        z-index: 10;
        position: relative;
        height: 100%;
        padding: 10rpx 20rpx 20rpx;
        .attachment_item {
          text-decoration: none;
          display: block;
          font-size: 28rpx;
          color: #333333;
          padding: 6rpx 20rpx;
          border: 1px solid #dddddd;
          border-radius: 5px;
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;
          .attachment_item_content {
            flex: 1;
            display: flex;
            align-items: center;
          }
          .oa-icon {
            font-size: 40rpx;
            margin-right: 20rpx;
            color: $theme-color;
          }
          .attachment_item_info {
            flex: 1;
            .original_name {
              font-size: 28rpx;
              color: #333333;
              margin-right: 20rpx;
            }
            .file_size {
              color: #999999;
              font-size: 24rpx;
            }
          }
          .down_load {
            flex-shrink: 0;
            margin-left: 8px;
          }
        }
      }
    }
  }
  .draft {
    text-align: center;
    background-color: #f2f2f2;
    .option {
      padding: 30rpx 20rpx;
      background-color: #ffffff;
      font-size: 32rpx;
      color: #333333;
    }
    .option_wrap {
      margin-bottom: 16rpx;
      .option_row {
        position: relative;
        &:first-child::after {
          position: absolute;
          bottom: 0;
          right: 0;
          left: 0;
          height: 1px;
          content: '';
          -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
          background-color: #eeeeee;
        }
        .no_save_draft {
          color: #ff0000;
        }
      }
    }
  }
}
</style>
