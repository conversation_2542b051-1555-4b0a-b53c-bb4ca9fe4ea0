<template>
  <view class="ts-content">
    <page-head title="科室文档" @clickLeft="returnBack"></page-head>
    <view v-if="directoryIndexes.length > 1" class="directory_indexes">
      <view
        v-for="(item, index) in directoryIndexes"
        :key="index"
        class="indexes_item"
        @click="showIndexes(item.title, item.id, item.level, index)"
      >
        <text
          class="indexes_item_text"
          :class="index < directoryIndexes.length - 1 ? 'pre_item_text' : ''"
          >{{ item.title }}</text
        >
        <uni-icons
          v-if="index < directoryIndexes.length - 1"
          :size="30"
          class="uni-icon-wrapper"
          color="#bbb"
          type="arrowright"
        />
      </view>
    </view>
    <uni-list>
      <uni-list-item
        v-for="item in directoryList"
        :key="item.id"
        :title="item.name"
        thumb="oa-icon-wenjianjia"
        iconStyleStr="font-size:48rpx;"
        :note="item.uploadTime ? item.uploadTime : item.createDate"
        @click="checkFolderList(item.name, item.id, item.channelLevel)"
      ></uni-list-item>
      <uni-list-item
        v-for="item in fileList"
        :key="item.id"
        :title="item.name"
        :thumb="'oa-icon-' + $oaModule.formatFileType(item.type)"
        iconStyleStr="font-size:48rpx;"
        :showArrow="false"
        :note="item.uploadTime ? item.uploadTime : item.createDate"
        @click="showDetail(item.id, item.saveName)"
      ></uni-list-item>
    </uni-list>

    <uni-popup ref="popup" type="bottom">
      <view
        class="popup-action-item"
        @click="previewFile(actionItem.id, actionItem.fileName)"
      >
        预览
      </view>
      <view
        class="popup-action-item"
        @click="downloadFile(actionItem.id, actionItem.fileName)"
      >
        下载
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import Base64 from '@/common/js/base64.min.js';
export default {
  data() {
    return {
      directoryIndexes: [
        {
          title: '全部',
          level: 1,
          parentid: ''
        }
      ],
      directoryList: [],
      fileList: [],

      actionItem: {}
    };
  },
  computed: {
    ...mapState(['empcode'])
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.getFolderList('', 1);
  },
  methods: {
    /**
     * 获取文件夹中的信息
     * @param {string} id - 文件夹id
     * @param {string} level - 当前文件夹的层级
     * @return
     */
    getFolderList(id, level) {
      this.ajax
        .getFolderList({
          userCode: this.empcode,
          id: id,
          channelLevel: level
        })
        .then(res => {
          let data = res.object;
          this.directoryList = data.channelList;
          this.fileList = data.fileList ? data.fileList : [];
        });
    },
    /**
     * 点击文件夹获取文件夹中的信息
     * @param {string} name - 文件夹名称
     * @param {string} id - 文件夹id
     * @param {string} level - 当前文件夹的层级
     * @return
     */
    checkFolderList(name, id, level) {
      let nextLevle = Number(level) + 1;
      this.directoryIndexes.push({
        title: name,
        level: nextLevle,
        id: id
      });
      this.getFolderList(id, nextLevle);
    },
    /**
     * 点击文件夹获取文件夹中的信息
     * @param {string} name - 文件夹名称
     * @param {string} id - 文件夹id
     * @param {string} level - 当前文件夹的层级
     * @param {string} index - 当前文件夹的索引
     * @return
     */
    showIndexes(name, id, level, index) {
      this.directoryIndexes.splice(index + 1);
      this.getFolderList(id, level);
    },
    showDetail(id, fileName) {
      this.$refs.popup.open();
      this.actionItem = {
        id,
        fileName
      };
    },
    previewFile(id, fileName) {
      this.$refs.popup.close();
      let filePath = `${this.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    },
    downloadFile(id, fileName) {
      this.$refs.popup.close();
      let filePath = `${this.$config.BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      this.$downloadFile.downloadFile(filePath);
    },
    returnBack() {
      uni.redirectTo({
        url: `/pages/file/file?fromPage=${this.fromPage}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.directory_indexes {
  padding: 10rpx 30rpx;
  background-color: #ffffff;
  .indexes_item {
    display: inline;
    .indexes_item_text {
      color: #333333;
    }
    .pre_item_text {
      color: #005bac;
    }
  }
}
.uni-list::before {
  height: 1px;
}
/deep/ .uni-transition {
  background-color: transparent;
}
.popup-action-item {
  background-color: #fff;
  text-align: center;
  margin: 8px 16px;
  border-radius: 4px;
  line-height: 40px;
}
</style>
