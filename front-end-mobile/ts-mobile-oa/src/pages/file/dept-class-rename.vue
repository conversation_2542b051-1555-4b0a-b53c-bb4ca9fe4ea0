<template>
  <view class="ts-content" v-if="showContent">
    <page-head
      title="修改分类"
      rightIcon=""
      rightText=""
      @clickLeft="returnBack"
    ></page-head>
    <base-form
      class="base-form"
      ref="baseForm"
      :form-list="formList"
      :form-data.sync="form"
      :rules="rules"
      :showSubmitButton="false"
    ></base-form>
    <view class="action-content">
      <view class="action-item" @click="returnBack">关闭</view>
      <view class="action-item submit-btn" @click="submit()">保存</view>
    </view>
  </view>
</template>

<script>
import BaseForm from '@/components/base-form/base-form.vue';
export default {
  components: { BaseForm },
  data() {
    return {
      showContent: false,
      formList: [
        {
          title: '上级分类',
          prop: 'pname',
          propVal: 'parentid',
          type: 'select',
          mode: 'dept',
          selectMode: 'scoll',
          chooseType: 'radio',
          placeholder: '请选择所属目录',
          name: '目录选择',
          getListType: 'scollSearch',
          searchParams: [{ name: 'scope', value: 'pub' }],
          searchApi: 'documentChannelList',
          disabled: true,
          required: true,
          changeCallback: this.handleRepairManDeptChange
        },
        {
          title: '目录名称',
          prop: 'name',
          type: 'text',
          placeholder: '请填写新目录名称',
          maxlength: 70,
          required: true
        },
        {
          title: '文档容量(MB)',
          prop: 'channelSize',
          type: 'number',
          placeholder: '请填写文档容量',
          maxlength: 70,
          required: true
        }
      ],
      form: {
        parentid: '',
        pname: '',
        name: '',
        pub: 'pub'
      },
      rules: {
        channelId: [
          {
            required: true,
            message: '请选择所属目录',
            trigger: ''
          }
        ]
      }
    };
  },
  async onLoad(opt) {
    await this.documentChannelSelectById(opt.id);
    this.form.pub = 'pub';
    this.showContent = true;
  },
  methods: {
    async documentChannelSelectById(id) {
      let res = await this.ajax.documentChannelSelectById({ id });
      this.form = res.object || {};
    },
    handleRepairManDeptChange(data = [], props) {
      let prop = '',
        propVal = '';
      if (data.length) {
        prop = data[0].name;
        propVal = data[0].id;
      }
      this.$set(this.form, props.prop, prop);
      this.$set(this.form, props.propVal, propVal);
      this.$refs.baseForm.personLabel = {};
    },
    async submit() {
      const result = await this.$refs.baseForm.validate();
      if (!result) return;
      if (this.form.parentid == this.form.id) {
        uni.showToast({
          title: '上级分类不能选本身',
          duration: 2000,
          icon: 'none'
        });
        return;
      }
      let data = {
        parentid: this.form.parentid,
        pname: this.form.pname,
        name: this.form.name,
        channelSize: this.form.channelSize,
        id: this.form.id
      };
      const res = await this.ajax.documentChannelUpdate(data);
      if (!res.success) {
        uni.showToast({
          title: res.message || '操作失败!',
          duration: 2000,
          icon: 'none'
        });
        return;
      }
      uni.showToast({
        title: '操作成功',
        duration: 1000,
        icon: 'none'
      });
      setTimeout(() => {
        this.$set(this, 'form', {});
        this.returnBack();
      }, 500);
    },
    //返回上一层
    returnBack() {
      this.form = this.$options.data().form;
      uni.navigateBack({
        delta: 1
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  /deep/ .base-form {
    height: calc(100% - 84px);
    overflow: auto;
    .u-form-item--left__content__label {
      font-size: 28rpx;
    }
    .u-switch {
      &.u-switch--on {
        background: $theme-color !important;
      }
    }
  }
  .action-content {
    position: absolute;
    bottom: 0;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    box-shadow: 0 -1px 6px #ccc;
    width: 100%;
    height: 40px;
    .action-item {
      flex: 1;
      text-align: center;
      color: $uni-text-color-grey;
      position: relative;
      &:not(:last-child)::after {
        content: ' ';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 15px;
        border-right: 1px solid #eee;
      }
      &.submit-btn {
        color: $theme-color;
      }
    }
  }
}
</style>
