<template>
  <view class="ts-content">
    <page-head title="文档" @clickLeft="returnBack"></page-head>
    <uni-list v-if="showContent">
      <uni-list-item
        v-for="(item, index) in fileList"
        :key="index"
        :title="item.title"
        :number="item.number"
        :thumb="item.thumb"
        @click="goDetailPage(item.path)"
        :iconStyleStr="item.iconStyleStr"
      ></uni-list-item>
    </uni-list>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import uniList from '@/components/uni-list/uni-list.vue';
import uniListItem from '@/components/uni-list-item/uni-list-item.vue';

export default {
  components: {
    uniList,
    uniListItem
  },
  data() {
    return {
      showContent: false,
      fromPage: '',
      fileList: [
        {
          title: '科室文档',
          index: '0',
          number: null,
          thumb: 'oa-icon-keshiwendang',
          iconStyleStr: 'color: rgb(249, 170, 93);font-size:60rpx',
          path: '/pages/file/new-department-file'
        },
        {
          title: '个人文档',
          index: '1',
          number: null,
          thumb: 'oa-icon-gerenwendang',
          iconStyleStr: 'color: rgb(123, 184, 114);font-size:60rpx',
          path: '/pages/file/new-personal-file'
        }
      ],
      showBadge: false
    };
  },
  computed: {
    ...mapState(['empcode'])
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.getFileNum();
  },
  methods: {
    getFileNum() {
      this.ajax
        .getDocumentNumber({
          userCode: this.empcode
        })
        .then(res => {
          let data = res.object;
          this.fileList[0].number = data['科室文档'];
          this.fileList[1].number = data['个人文档'];
          this.showContent = true;
        });
    },
    goDetailPage(path) {
      uni.navigateTo({
        url: `${path}?fromPage=${this.fromPage}`
      });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/${this.fromPage}`
      });
    }
  }
};
</script>

<style></style>
