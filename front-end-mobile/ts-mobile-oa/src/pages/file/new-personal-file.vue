<template>
  <view class="ts-content" v-if="showContent">
    <page-head
      title="个人文档"
      @clickLeft="returnBack"
      right-text="新建"
      @clickRight="handleAdd"
    ></page-head>
    <view class="search-box">
      <view class="search-form">
        <uni-search-bar
          radius="100"
          bgColor="#FFFFFF"
          cancelButton="none"
          searchBgColor="#eeeeee"
          borderColor="transparent"
          @confirm="search"
        ></uni-search-bar>
      </view>
      <view class="search-sift" @tap="listoverView">
        <text
          v-if="isListOverivew"
          class="search-sift-icon oa-icon oa-icon-wenjianjia"
        ></text>
        <text v-else class="search-sift-icon oa-icon oa-icon-liebiao"></text>
      </view>
      <view class="search-sift" @tap="handleScreen">
        <text class="search-sift-icon oa-icon oa-icon-shaixuan"></text>
      </view>
    </view>
    <view v-if="!isListOverivew" class="directory_indexes">
      <view
        v-for="(item, index) in historyList"
        :key="index"
        class="indexes_item"
        @click="showIndexes(index)"
      >
        <text
          class="indexes_item_text"
          :class="index < historyList.length - 1 ? 'pre_item_text' : ''"
          >{{ item.name }}</text
        >
        <uni-icons
          v-if="index < historyList.length - 1"
          :size="30"
          class="uni-icon-wrapper"
          color="#bbb"
          type="arrowright"
        />
      </view>
    </view>
    <scroll-view class="swiper-head" :scroll-x="true" :show-scrollbar="false">
      <view
        v-for="(tab, index) in tabBars"
        class="uni-tab-item"
        :key="index"
        :data-current="index"
        @click="ontabtap"
      >
        <view :class="tabIndex === index ? 'uni-tab-item-title-active' : ''">
          <text class="uni-tab-item-title">{{ tab.name }}</text>
          <text
            class="uni-tab-item-num"
            v-if="tab.total != null && tab.total != 0"
            >{{ tab.total >= 100 ? '99+' : tab.total }}</text
          >
        </view>
      </view>
    </scroll-view>
    <swiper
      :current="tabIndex"
      class="swiper-box"
      :duration="300"
      @change="ontabchange"
    >
      <swiper-item
        class="swiper-item"
        v-for="(item, index) in tabBars"
        :key="index"
      >
        <mescroll
          :ref="'mescroll' + index"
          :mescrollIndex="index"
          :down="item.downOption"
          :isHasPage="isListOverivew ? true : false"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view class="contact_list" v-if="isListOverivew">
            <view
              class="contact_item"
              v-for="(row, rowIndex) in item['list']"
              :key="rowIndex"
              @tap="toDetail(row)"
            >
              <view
                @tap.stop="chooseItem(row)"
                v-if="tabIndex != 2 || (tabIndex == 2 && row.type != 'channel')"
              >
                <uni-icons
                  :type="row.choose ? 'checkbox-filled' : 'circle'"
                  :color="row.choose ? '#005BAC' : '#aaa'"
                  size="48"
                  style="margin-right: 10rpx;"
                />
              </view>
              <view class="contact_item_img">
                <text
                  class="oa-icon"
                  :class="
                    'oa-icon-' + $oaModule.formatFileType(row.fileExtension)
                  "
                  style="color: rgb(66, 146, 246);font-size:50rpx"
                ></text>
              </view>
              <view class="user_info">
                <view class="subject">
                  <text class="docTitle">
                    {{ row.originalName }}
                  </text>
                </view>
                <view class="subject">
                  <text class="fileSize">{{
                    formatFileSize(row.fileSize)
                  }}</text>
                  <text class="docTime">{{ row.createDate }}</text>
                  <text class="createUserName">{{ row.createUserName }}</text>
                </view>
                <view class="subject">
                  <text class="deptAllName">{{ row.folderName }}</text>
                </view>
              </view>
            </view>
          </view>
          <view class="view_list" v-else>
            <view
              class="view_item"
              v-for="(row, rowIndex) in item['list']"
              :key="rowIndex"
              @tap="floderDetail(row)"
            >
              <view
                @tap.stop="chooseItem(row)"
                v-if="tabIndex != 2 || (tabIndex == 2 && row.type != 'channel')"
              >
                <uni-icons
                  class="check"
                  :type="row.choose ? 'checkbox-filled' : 'circle'"
                  :color="row.choose ? '#005BAC' : '#aaa'"
                  size="48"
                  style="margin-right: 20rpx;"
                />
              </view>
              <image
                class="floder"
                src="@/static/img/folder.png"
                v-if="row.type == 'channel'"
              />
              <image
                class="floder"
                src="@/static/img/docx.png"
                v-else-if="
                  row.fileExtension == 'doc' || row.fileExtension == 'docx'
                "
              />
              <image
                class="floder"
                src="@/static/img/zip.png"
                v-else-if="
                  row.fileExtension == 'zip' ||
                    row.fileExtension == 'war' ||
                    row.fileExtension == 'rar' ||
                    row.fileExtension == '7z'
                "
              />
              <image
                class="floder"
                src="@/static/img/pdf.png"
                v-else-if="row.fileExtension == 'pdf'"
              />
              <image
                class="floder"
                src="@/static/img/ppt.png"
                v-else-if="row.fileExtension == 'ppt'"
              />
              <image
                class="floder"
                src="@/static/img/txt.png"
                v-else-if="row.fileExtension == 'txt'"
              />
              <image
                class="floder"
                src="@/static/img/xlsx.png"
                v-else-if="
                  row.fileExtension == 'xlsx' || row.fileExtension == 'xls'
                "
              />
              <image
                class="floder"
                src="@/static/img/image.png"
                v-else-if="'jpg,jpg,png,gif'.indexOf(row.fileExtension) > -1"
              />
              <image class="floder" src="@/static/img/other.png" v-else />
              <view class="name">
                <text class="tiName">{{ row.name }}</text>
                <text class="num" v-if="row.type == 'channel'"
                  >({{ row.filenumbes }})</text
                >
              </view>
              <view class="share-info" v-if="tabIndex == 2">
                {{ row.sharetoUserName || row.sharetoDeptName }}
              </view>
            </view>
          </view>
        </mescroll>
      </swiper-item>
    </swiper>
    <view class="bottom">
      <text class="choose">已选：{{ this.selectList.length }}个</text>
      <text class="button" @click="chooseBtn">操作</text>
    </view>
    <uni-drawer :visible="showRight" mode="right" @close="closeDrawer()">
      <view class="option-wrap">
        <view class="option-tap">
          <view class="option-title">文档状态</view>
          <view class="option-list">
            <text
              class="option-item"
              :class="status == item.status ? 'selected' : ''"
              :data-status-id="item.status"
              v-for="(item, index) in statusList"
              :key="index"
              @tap="chooseStatus"
              >{{ item.statusName }}</text
            >
          </view>
        </view>
      </view>
    </uni-drawer>
    <uni-popup ref="uniPopup" type="bottom" class="uniPopupActionsheet">
      <view class="actionsheet">
        <view
          v-for="(item, index) in listBtn"
          :key="index"
          @tap="selectConfirm(item)"
          class="selectItem"
        >
          <text>{{ item.label }}</text>
        </view>
      </view>
      <view class="actionsheet">
        <view
          v-for="(item, index) in subListBtn"
          :key="index"
          @tap="selectConfirm(item)"
          class="selectItem"
        >
          <text>{{ item.label }}</text>
        </view>
      </view>
    </uni-popup>
    <uni-popup
      ref="uniPopupRename"
      top-height="45%"
      left-height="5%"
      right-height="5%"
      class="uniPopupRename"
      background-color="#fff"
    >
      <base-form
        class="base-form"
        ref="baseForm"
        :form-list="formList"
        :form-data.sync="form"
        :rules="rules"
        :showSubmitButton="false"
      ></base-form>
      <view class="action-content">
        <view class="action-item" @click="closeRename">关闭</view>
        <view class="action-item submit-btn" @click="submitRename()">保存</view>
      </view>
    </uni-popup>
    <uni-popup
      ref="uniPopupMigration"
      top-height="45%"
      left-height="5%"
      right-height="5%"
      class="uniPopupRename"
      background-color="#fff"
    >
      <base-form
        class="base-form"
        ref="baseForm1"
        :form-list="formList1"
        :form-data.sync="form1"
        :rules="rules1"
        :showSubmitButton="false"
      ></base-form>
      <view class="action-content">
        <view class="action-item" @click="closeMigration">关闭</view>
        <view class="action-item submit-btn" @click="submitMigration()">
          保存
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="uniPopupFile" type="bottom" class="uniPopupActionsheet">
      <view class="actionsheet">
        <view class="selectItem" @tap="previewFile">
          <text>预览</text>
        </view>
        <view class="selectItem" @tap="downloadFile">
          <text>下载</text>
        </view>
      </view>
      <view class="actionsheet">
        <view class="selectItem" @tap="closeFile">
          <text>取消</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import BaseForm from '@/components/base-form/base-form.vue';
import Base64 from '@/common/js/base64.min.js';
import { mapState } from 'vuex';
export default {
  components: { mescroll, BaseForm },
  data() {
    return {
      showRight: false,
      isListOverivew: false,
      showContent: false,
      keywords: '',
      status: '',
      statusList: [],
      dataList: [],
      historyList: [{ id: '', name: '文档分类' }],
      selectList: [],
      buttonList: [
        { value: 'share', label: '分享', channelId: '0' },
        { value: 'delete', label: '删除', channelId: '0,1' },
        { value: 'migration', label: '移动', channelId: '0' },
        { value: 'clear', label: '清空', channelId: '3' },
        { value: 'return', label: '还原', channelId: '3' },
        { value: 'shareCancel', label: '撤回', channelId: '2' }, //我的分享
        { value: 'close', label: '取消', isSub: true, channelId: '0,1,2,3' }
      ],
      floderList: [
        { value: 'editClass', label: '修改', channelId: '0' },
        { value: 'deleteClass', label: '删除', channelId: '0' },
        { value: 'shareClass', label: '分享', channelId: '0' },
        { value: 'close', label: '取消', isSub: true, channelId: '0,3' }
      ],
      file: {},
      formList: [
        {
          title: '文件名',
          prop: 'newOriginalName',
          type: 'text',
          placeholder: '请填写文件名',
          maxlength: 70,
          required: true
        }
      ],
      form: {
        newOriginalName: ''
      },
      rules: {
        channelId: [
          {
            required: true,
            message: '请填写文件名',
            trigger: ''
          }
        ]
      },
      formList1: [
        {
          title: '目标目录',
          prop: 'folderName',
          propVal: 'folderId',
          type: 'select',
          mode: 'dept',
          selectMode: 'scoll',
          chooseType: 'radio',
          placeholder: '请选择所属目录',
          name: '目录选择',
          getListType: 'scollSearch',
          searchParams: [{ name: 'scope', value: 'personal' }],
          searchApi: 'documentChannelList',
          required: true,
          changeCallback: this.handleRepairManDeptChange
        }
      ],
      form1: {
        folderId: '',
        folderName: ''
      },
      rules1: {
        folderId: [
          {
            required: true,
            message: '请选择目标目录',
            trigger: ''
          }
        ]
      },
      tabBars: [
        {
          name: '文档库',
          api: 'getMyAttachment',
          viewApi: 'getMyDocumentView',
          tabCode: 1,
          downOption: false,
          isInit: false,
          total: null,
          list: []
        },
        {
          name: '分享给我',
          api: 'getShareAttachment',
          viewApi: 'getMyDocumentView',
          tabCode: 2,
          downOption: false,
          isInit: false,
          total: null,
          list: []
        },
        {
          name: '我的分享',
          api: 'getMyShareAttachment',
          viewApi: 'getMyDocumentView',
          tabCode: 5,
          downOption: false,
          isInit: false,
          total: null,
          list: []
        },
        {
          name: '回收站',
          api: 'getRecoveryAttachment',
          viewApi: 'getMyDocumentView',
          tabCode: 3,
          downOption: false,
          isInit: false,
          total: null,
          list: []
        }
      ],
      tabIndex: 0 //当前选中的tab索引值，从0计数
    };
  },
  computed: {
    ...mapState(['empcode']),
    listBtn() {
      if (this.isListOverivew) {
        let list = this.buttonList.filter(
          e => e.channelId.indexOf(this.tabIndex) > -1 && !e.isSub
        );
        if (this.selectList.length == 1 && this.tabIndex == '0') {
          list.unshift({ value: 'rename', label: '重命名' });
        }
        return list;
      } else {
        let index = this.selectList.findIndex(e => e.type == 'channel');
        if (index < 0) {
          let list = this.buttonList.filter(
            e => e.channelId.indexOf(this.tabIndex) > -1 && !e.isSub
          );
          if (this.selectList.length == 1 && this.tabIndex == '0') {
            list.unshift({ value: 'rename', label: '重命名' });
          }
          return list;
        } else {
          let list = this.floderList.filter(
            e => e.channelId.indexOf(this.tabIndex) > -1 && !e.isSub
          );
          return list;
        }
      }
    },
    subListBtn() {
      if (this.isListOverivew) {
        return this.buttonList.filter(
          e => e.channelId.indexOf(this.tabIndex) > -1 && e.isSub
        );
      } else {
        let index = this.selectList.findIndex(e => e.type == 'channel');
        if (index < 0) {
          return this.buttonList.filter(
            e => e.channelId.indexOf(this.tabIndex) > -1 && e.isSub
          );
        } else {
          return this.floderList.filter(
            e => e.channelId.indexOf(this.tabIndex) > -1 && e.isSub
          );
        }
      }
    }
  },
  watch: {
    isListOverivew: {
      handler() {
        this.getAllTypeNum();
      },
      immediate: true
    }
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.getFileExtension();
    if (opt.index >= 0 && opt.tabIndex != 'undefined')
      this.tabIndex = Number(opt.tabIndex);
    if (opt.isListOverivew && opt.isListOverivew != 'undefined')
      this.isListOverivew = opt.isListOverivew == 'true' ? true : false;
    this.tabBars.map((item, index) => {
      if (this.tabIndex == index) {
        item.downOption = true;
        item.isInit = true;
      } else {
        item.downOption = false;
        item.isInit = false;
      }
    });
    this.showContent = true;
  },
  methods: {
    // 打开抽屉
    handleScreen() {
      this.showRight = true;
    },
    // 关闭抽屉
    closeDrawer() {
      this.showRight = false;
    },
    async getAllTypeNum() {
      let res = await this.ajax.getMyDocumentNumbers({ type: 2 }); //type: 1科室文档统计 2个人文档统计
      this.tabBars[0]['total'] = res.object.wdNumbers;
      this.tabBars[1]['total'] = res.object.shareToNumbers;
      this.tabBars[2]['total'] = res.object.shareNumbers;
      this.tabBars[3]['total'] = res.object.hsNumbers;
    },
    // 预览
    previewFile() {
      this.$refs.uniPopupFile.close();
      let _self = this,
        filePath = `${_self.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${this.file.id}?fullfilename=${this.file.fileName}&source=mobile`;
      if (_self.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            _self.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    },
    downloadFile() {
      let filePath = `${this.$config.BASE_HOST}/ts-document/attachment/downloadFile/${this.file.id}?fullfilename=${this.file.fileName}&source=mobile`;
      this.$downloadFile.downloadFile(filePath);
    },
    closeFile() {
      this.$refs.uniPopupFile.close();
    },
    getFileExtension() {
      this.ajax.getFileExtension().then(res => {
        if (res.success) {
          this.statusList = res.object.map(e => {
            return {
              status: e.name,
              statusName: e.name
            };
          });
          this.statusList.unshift({ status: '', statusName: '全部' });
        }
      });
    },
    // 操作前验证
    chooseBtn() {
      if (this.selectList.length == 0) {
        uni.showToast({
          icon: 'none',
          title: `请选择需要操作的数据`
        });
        return;
      }
      this.$refs.uniPopup.open();
    },
    // 主动下拉刷新
    resetMescroll() {
      this.selectList = [];
      this.datasInit('', this.tabIndex);
      this.$refs[`mescroll${this.tabIndex}`][0].downCallback();
      this.getAllTypeNum();
    },
    // 操作判断
    selectConfirm(e) {
      let status = e.value;
      switch (status) {
        case 'share':
          this.choosePerson('share');
          break;
        case 'delete':
          this.uniModel('myDeleted', '删除');
          break;
        case 'clear':
          this.uniModel('thorough', '清空');
          break;
        case 'return':
          this.uniModel('reduction', '还原');
          break;
        case 'rename':
          this.form.newOriginalName = '';
          this.$refs.uniPopupRename.open();
          break;
        case 'migration':
          this.form1.personal = 'personal';
          this.$refs.uniPopupMigration.open();
          break;
        case 'deleteClass':
          let _this = this;
          uni.showModal({
            title: '提示',
            content: `您确定【删除】该分类及下面所有文件？`,
            confirmText: '取消',
            cancelText: '确定',
            confirmColor: '#005BAC',
            success: function(res) {
              if (res.cancel) _this.handleClassDel();
            }
          });
          break;
        case 'editClass':
          uni.navigateTo({
            url: `/pages/file/class-rename?id=${this.selectList[0].id}`
          });
          break;
        case 'shareClass':
          this.choosePerson('shareClass');
          break;
        case 'shareCancel':
          //我的分享撤回
          this.uniModel('', '撤回', 'cancleMyDocumentShare');
          break;
        case 'close':
          this.$refs.uniPopup.close();
          break;
        default:
          break;
      }
      this.$refs.uniPopup.close();
    },
    // 删除分类
    handleClassDel() {
      this.ajax
        .documentChannelDelete({ id: this.selectList[0].id, scope: 'pub' })
        .then(res => {
          if (res.success) {
            uni.showModal({
              icon: 'none',
              content: '【删除】成功'
            });
            this.selectList = [];
            this.resetMescroll();
            return;
          } else {
            uni.showModal({
              icon: 'none',
              content: res.message || '【删除】失败'
            });
          }
        });
    },
    // 统一的uni提示
    uniModel(method, label, APIFunc) {
      if (method)
        uni.showModal({
          title: '提示',
          content: `您确定【${label}】选中的数据？`,
          confirmText: '取消',
          cancelText: '确定',
          confirmColor: '#005BAC',
          success: res => {
            if (res.cancel) this.handleBatchOperate(method, label);
          }
        });
      else this.handleBatchShareOperate(label, APIFunc);
    },
    // 批量作废 删除 清空 还原 归档
    handleBatchOperate(method, label) {
      let param = {
        attachmentIdList: this.selectList.map(e => e.id),
        method
      };
      this.ajax.operationAttachment(param).then(res => {
        if (res.success) {
          this.selectList = [];
          uni.showToast({
            icon: 'none',
            title: `【${label}】成功`
          });
          this.resetMescroll();
          return;
        }
        uni.showToast({
          icon: 'none',
          title: `【${label}】失败`
        });
      });
    },
    //批量撤回我的分享
    handleBatchShareOperate(label, func) {
      let param = this.selectList.map(e => {
        return {
          id: e.id
        };
      });
      this.ajax[func](param).then(res => {
        if (res.success) {
          this.selectList = [];
          uni.showToast({
            icon: 'none',
            title: `【${label}】成功`
          });
          this.resetMescroll();
          return;
        }
        uni.showToast({
          icon: 'none',
          title: `【${label}】失败`
        });
      });
    },
    closeRename() {
      this.$refs.uniPopupRename.close();
    },
    async submitRename() {
      const result = await this.$refs.baseForm.validate();
      if (!result) return;
      let data = {
        id: this.selectList[0].id,
        originalName: this.form.newOriginalName
      };
      const res = await this.ajax.attachmentRename(data);
      this.$refs.uniPopupRename.close();
      if (!res.success) {
        uni.showToast({
          title: res.message || '操作失败!',
          duration: 2000,
          icon: 'none'
        });
        return;
      }
      uni.showToast({
        title: '操作成功',
        duration: 1000,
        icon: 'none'
      });
      setTimeout(() => {
        this.$set(this, 'form', {});
        this.resetMescroll();
      }, 500);
    },
    closeMigration() {
      this.form = {};
      this.$refs.uniPopupMigration.close();
    },
    async submitMigration() {
      const result = await this.$refs.baseForm1.validate();
      if (!result) return;
      let data = this.selectList.map(e => {
        return {
          folderId: this.form1.folderId,
          id: e.id,
          method: 'move'
        };
      });
      const res = await this.ajax.bacthMoveDocument(data);
      this.$refs.uniPopupMigration.close();
      if (!res.success) {
        uni.showToast({
          title: res.message || '移动失败!',
          duration: 2000,
          icon: 'none'
        });
        return;
      }
      this.selectList = [];
      uni.showToast({
        title: '移动成功',
        duration: 1000,
        icon: 'none'
      });
      setTimeout(() => {
        this.$set(this, 'form', {});
        this.resetMescroll();
      }, 500);
    },
    // 切换视图
    listoverView() {
      this.selectList = [];
      this.isListOverivew = !this.isListOverivew;
      this.resetMescroll();
    },
    // 新增文档
    handleAdd() {
      let url = `/pages/file/add-file?fromPage=${this.fromPage}`;
      if (!this.isListOverivew && this.historyList.length > 1) {
        let channel = this.historyList[this.historyList.length - 1];
        url = url + `&channelId=${channel.id}&channelName=${channel.name}`;
      }
      url += `&tabIndex=${this.tabIndex}&isListOverivew=${this.isListOverivew}`;
      uni.redirectTo({ url });
    },
    // 文档详情
    toDetail(item) {
      this.file = item;
      this.$refs.uniPopupFile.open();
    },
    // 搜索
    search(e) {
      this.keywords = e ? e.value : '';
      this.resetMescroll();
    },
    //选择人员
    choosePerson(key) {
      uni.setStorageSync('person_list', JSON.stringify([]));
      let personPageParams = {
        title: '选择人员',
        personInfoProp: {
          name: 'empName',
          describe: 'empDeptName',
          describeConcatSymbol: '-',
          key: 'empCode',
          sex: 'empSex',
          headImg: 'headImg'
        }
      };
      personPageParams = { ...personPageParams };
      uni.setStorageSync('personPageParams', JSON.stringify(personPageParams));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('trasenPerson', data => {
        let API = this.ajax.bacthShareDocument;
        let param = null;
        if (key == 'share') {
          param = this.selectList.map(e => {
            return {
              id: e.id,
              sharetoUserName: data.map(i => i.empName).join(','),
              sharetoUser: data.map(i => i.empCode).join(','),
              sharetoDept: ''
            };
          });
        } else {
          API = this.ajax.attachmentShareChannel;
          param = {
            folderId: this.selectList[0].id,
            sharetoUserName: data.map(i => i.empName).join(','),
            sharetoUser: data.map(i => i.empCode).join(','),
            sharetoDept: ''
          };
        }
        API(param).then(res => {
          if (res.success) {
            this.selectList = [];
            uni.showToast({
              icon: 'none',
              title: `【分享】成功`
            });
            this.resetMescroll();
            return;
          }
          uni.showToast({
            icon: 'none',
            title: `【分享】失败`
          });
        });
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('trasenPerson');
      });
      uni.navigateTo({
        url: `/pages/choose-person/choose-person?chooseType=checkbox&searchType=fullApiPost`
      });
    },
    // 格式化文档大小
    formatFileSize(fileSize) {
      let fileSizes = Number(fileSize);
      let KB = (fileSizes / 1024).toFixed(2);
      let MB = (fileSizes / 1024 / 1024).toFixed(2);
      if (KB < 1024) {
        return KB + 'KB';
      } else {
        return MB + 'MB';
      }
    },
    // 切换查询状态
    chooseStatus(e) {
      this.status = e.currentTarget.dataset.statusId;
      this.resetMescroll();
    },
    // 选择元素
    chooseItem(item) {
      item.choose = !item.choose;
      let index = this.tabIndex;
      let list = [];
      if (this.isListOverivew) {
        list = this.tabBars[index]['list'].filter(e => e.choose);
      } else {
        if (item.type == 'channel') {
          this.tabBars[index]['list'].forEach(e => {
            if (e.channelId != item.channelId) e.choose = false;
          });
          list = this.tabBars[index]['list'].filter(e => e.choose);
        } else {
          this.tabBars[index]['list'].forEach(e => {
            if (e.type == 'channel') e.choose = false;
          });
          list = this.tabBars[index]['list'].filter(e => e.choose);
        }
      }
      this.selectList = list.map(e => {
        return {
          id: e.id || e.channelId,
          type: e.type || 'document'
        };
      });
    },
    // 分类或者文档
    floderDetail(item) {
      if (item.type == 'channel') {
        this.historyList.push({
          id: item.channelId,
          name: item.name
        });
        this.resetMescroll();
      } else {
        item.id = item.channelId;
        item.fileName = item.originalName;
        this.toDetail(item);
      }
    },
    handleRepairManDeptChange(data = [], props) {
      let prop = '',
        propVal = '';
      if (data.length) {
        prop = data[0].name;
        propVal = data[0].id;
      }
      this.$set(this.form1, props.prop, prop);
      this.$set(this.form1, props.propVal, propVal);
      this.$refs.baseForm1.personLabel = {};
    },
    // 文件夹视图下的分类导航
    showIndexes(index) {
      if (index + 1 == this.historyList.length) return;
      this.historyList.splice(index + 1);
      this.resetMescroll();
    },
    //tab点解切换
    ontabtap(e) {
      this.selectList = [];
      this.tabBars[this.tabIndex]['list'].forEach(e => (e.choose = false));
      let index = e.target.dataset.current || e.currentTarget.dataset.current;
      this.switchTab(Number(index));
    },
    //tab滑动切换
    async ontabchange(e) {
      this.selectList = [];
      this.tabBars[this.tabIndex]['list'].forEach(e => (e.choose = false));
      let index = e.target.current || e.detail.current;
      await this.switchTab(Number(index));
    },
    async switchTab(index) {
      this.tabBars[index]['isInit'] = true;
      this.tabBars[index]['list'] = [];
      await this.$refs[`mescroll${index}`][0].downCallback();
      this.tabIndex = index;
    },
    getListData(page, successCallback, errorCallback, keywords, index) {
      if (!this.isListOverivew && this.tabBars[index]['list'].length > 0) {
        successCallback([], 0);
      }
      let API = null;
      let param = {
        pageSize: page.size,
        pageNo: page.num,
        originalName: this.keywords,
        fileExtension: this.status,
        sidx: 'UPLOAD_TIME',
        sord: 'desc'
      };
      if (this.isListOverivew) {
        if (this.tabIndex == '1') {
          API = this.ajax.getShareAttachment;
          param.sidx = 'create_date';
          param.sord = 'desc';
        }
        if (this.tabIndex == '2' || this.tabIndex == '3') {
          API = this.ajax.getRecoveryAttachment;
          param.sidx = 'create_date';
          param.sord = 'desc';
        }
        API = this.ajax[this.tabBars[index].api];
      } else {
        API = this.ajax[this.tabBars[index].viewApi];
        param = {
          index: this.tabBars[index].tabCode,
          channelId: this.historyList[this.historyList.length - 1].id,
          scope: 'personal'
        };
        if (this.keywords) {
          param.originalName = this.keywords;
        }
        if (this.status) {
          param.fileExtension = this.status;
        }
      }
      API(param)
        .then(res => {
          let rows = res.rows || res.object;
          rows.forEach(e => (e.choose = false));
          successCallback(rows, res.totalCount || res.object.length);
        })
        .catch(e => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      this.tabBars[index]['list'] = this.tabBars[index]['list'].concat(rows);
    },
    datasInit(keywords, index) {
      this.tabBars[index]['list'] = [];
    },
    returnBack() {
      uni.redirectTo({
        url: `/pages/file/file?fromPage=${this.fromPage}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .search-box {
    display: flex;
    align-items: center;
    background-color: #eeeeee;
    .search-form {
      flex: 1;
    }
    .search-sift {
      font-size: 28rpx;
      color: #666666;
      padding-right: 16rpx;
      .search-sift-icon {
        font-size: 44rpx !important;
        margin-right: 24rpx;
        color: #666666;
      }
    }
  }
  .directory_indexes {
    padding: 10rpx 30rpx;
    background-color: #ffffff;
    .indexes_item {
      display: inline;
      .indexes_item_text {
        color: #333333;
      }
      .pre_item_text {
        color: #005bac;
      }
    }
  }
  .swiper-head {
    position: relative;
    width: 750rpx;
    height: 80rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    white-space: nowrap;
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    &::after {
      bottom: 0;
    }
    .uni-tab-item-title-active {
      color: $theme-color;
      border-bottom: 2px solid $theme-color;
    }
    .uni-tab-item {
      display: inline-block;
      flex-wrap: nowrap;
      width: 21%;
      margin: 0 2%;
      box-sizing: border-box;
      text-align: center;
      .uni-tab-item-title,
      .uni-tab-item-num {
        color: #666;
        font-size: 30rpx;
        height: 100%;
        line-height: 2.5;
        flex-wrap: nowrap;
        box-sizing: border-box;
        /* #ifndef APP-PLUS */
        white-space: nowrap;
        /* #endif */
      }
      .uni-tab-item-num {
        font-size: 28rpx;
        color: #fff;
        background-color: #f59a23;
        border-radius: 40rpx;
        padding: 0 10rpx;
      }
    }
  }
  .swiper-box {
    flex: 1;
    .swiper-item {
      flex: 1;
      flex-direction: row;
    }
  }
  /deep/ .scroll-wrap {
    flex: 1;
    position: relative;
    margin-bottom: 80rpx;
  }
  .mescroll-uni-warp {
    height: calc(100% - 80rpx);
  }
  .contact_list {
    .contact_item {
      padding: 20rpx 30rpx;
      display: flex;
      align-items: center;
      background-color: #ffffff;
      position: relative;
      &::after {
        position: absolute;
        bottom: 0;
        right: 0;
        left: 30rpx;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #eeeeee;
      }
      &:last-child::after {
        height: 0;
      }
      .user_info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 1;
        .subject {
          font-size: 28rpx;
          color: #999;
          display: flex;
          justify-content: space-between;
          .left {
            flex: 1;
            display: flex;
            font-size: 28rpx;
          }
          .deptAllName,
          .docTitle {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
          }
          .deptName {
            margin-left: 4rpx;
          }
          .docTitle {
            color: #295cf9;
          }
          .statusName {
            &.normal {
              color: #333;
            }
            &.zuofei {
              color: #a30014;
            }
            &.guoqi {
              color: #f59a32;
            }
          }
        }
      }
      .contact_item_img {
        width: 80rpx;
        height: 80rpx;
        margin-right: 10rpx;
        border-radius: 100%;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(#4699f6, 0.2);
      }
      .subjectTitle {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }
  }
  .view_list {
    display: flex;
    flex-wrap: wrap;
    background-color: #ffffff;
    .view_item {
      width: calc(33% - 16rpx);
      border: 2rpx solid #eee;
      border-radius: 20rpx;
      margin: 8rpx;
      padding: 10rpx 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      overflow: hidden;
      .check {
        position: absolute;
        right: -15rpx;
        top: 0rpx;
      }
      .floder {
        height: 136rpx;
        width: 136rpx;
      }
      .name {
        border-top: 2rpx solid #eee;
        width: 100%;
        padding: 12rpx;
        text-align: center;
        font-size: 28rpx;
        display: flex;
        justify-content: center;
        .tiName {
          flex: 1;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          word-break: break-all;
          text-overflow: ellipsis;
        }
        .num {
          color: #295cf9;
        }
      }
      .share-info {
        position: absolute;
        top: 0;
        left: 0;
        max-width: calc(100% - 68rpx);
        font-size: 10px;
        background-color: rgba(#295cf9, 0.2);
        color: #295cf9;
        padding: 0 8rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .option-wrap {
    height: 100%;
    overflow: auto;
    .option-tap {
      padding: 0 20rpx;
      .option-title {
        height: 56rpx;
        line-height: 56rpx;
        font-size: 28rpx;
        margin: 10rpx 0;
        color: #666;
      }
      .option-item {
        border-radius: 8rpx;
        line-height: 70rpx;
        height: 70rpx;
        width: 30%;
        margin: 0 3% 20rpx 0;
        text-align: center;
        background: #ffffff;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 28rpx;
        border: 1px solid #ddd;
        box-sizing: border-box;
        color: #333333;
      }
      .selected {
        background-color: #005bac;
        border-color: #005bac;
        color: #ffffff;
      }
      .divider {
        width: auto;
        border: 0;
        box-sizing: border-box;
      }
    }
    .btn-tap {
      margin: 80rpx 0;
      padding: 0 20rpx;
      text-align: right;
      .btn-item {
        border-radius: 8rpx;
        line-height: 70rpx;
        height: 70rpx;
        width: 30%;
        text-align: center;
        background: #ffffff;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 28rpx;
        border: 1px solid #ddd;
        border-right: 0;
        box-sizing: border-box;
        color: #333333;
      }
      .cancle-btn {
        border-bottom-right-radius: 0;
        border-top-right-radius: 0;
      }
      .them-btn {
        border-left: 0;
        border: 1px solid #005bac;
        background-color: #005bac;
        color: #ffffff;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
        margin-right: 4%;
      }
    }
  }
  .bottom {
    position: fixed;
    bottom: 0;
    background: #a9befd;
    width: 100%;
    padding: 10rpx 30rpx;
    display: flex;
    justify-content: space-between;
    .choose {
      line-height: 66rpx;
    }
    .button {
      background: #295cf9;
      color: #fff;
      font-size: 28rpx;
      padding: 10rpx 26rpx;
      border-radius: 8rpx;
    }
  }
  .actionsheet {
    margin: 16rpx;
    border-radius: 4px;
    overflow: hidden;
    .selectItem {
      line-height: 80rpx;
      font-size: 28rpx;
      text-align: center;
      color: $theme-color;
      border-bottom: 1rpx solid #eee;
      background: #fff;
    }
    &:last-child {
      font-weight: bold;
    }
  }
  /deep/.uniPopupActionsheet .uni-transition {
    background-color: transparent;
  }
  /deep/ .uniPopupRename {
    .uni-transition:last-child {
      background-color: #fff;
      border-radius: 10rpx;
      align-items: normal !important;
    }
    .uni-popup__wrapper-box {
      padding: 12rpx 0;
      position: relative;
      .base-form {
        height: 100px;
        overflow: auto;
        .u-form-item--left__content__label {
          font-size: 28rpx;
        }
        .u-switch {
          &.u-switch--on {
            background: $theme-color !important;
          }
        }
      }
      .action-content {
        position: absolute;
        bottom: 0;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: space-around;
        background-color: #fff;
        box-shadow: 0 -1px 6px #ccc;
        width: 100%;
        height: 40px;
        .action-item {
          flex: 1;
          text-align: center;
          color: $uni-text-color-grey;
          position: relative;
          &:not(:last-child)::after {
            content: ' ';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 1px;
            height: 15px;
            border-right: 1px solid #eee;
          }
          &.submit-btn {
            color: $theme-color;
          }
        }
      }
    }
  }
}
</style>
