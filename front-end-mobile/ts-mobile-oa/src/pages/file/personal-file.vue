<template>
  <view class="ts-content">
    <page-head title="个人文档" @clickLeft="returnBack">
      <view slot="right">
        <form
          v-show="tabsAcitve == 0"
          class="addBox oa-icon oa-icon-shangchuan"
          ref="fileinput"
        />
      </view>
    </page-head>

    <u-tabs :list="list" :current="tabsAcitve" @change="handleChangeTabs" />

    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :searchInput="true"
        @getDatas="getFileList"
        @setDatas="setFileList"
        @datasInit="datasInit"
      >
        <uni-swipe-action class="swipeAction">
          <uni-swipe-action-item
            v-for="(item, index) in fileList"
            :auto-close="false"
            :show="item.isOpened"
            :options="options"
            :key="item.id"
            :itemIndex="index"
            @change="swipeChange($event, index)"
            @click="swipeClick(index, $event)"
            @choose="showDetail(item.id, item.fileName)"
          >
            <view class="file_item">
              <view class="file_item_icon">
                <text
                  class="oa-icon"
                  :class="
                    'oa-icon-' + $oaModule.formatFileType(item.fileExtension)
                  "
                  style="font-size:48rpx;"
                ></text>
              </view>
              <view class="file_item_content">
                <view class="file_item_content_title_container top">
                  <text class="file_item_content_title">
                    {{ item.originalName }}
                  </text>
                  <text class="file_item_content_title create-info">
                    {{ item.createDeptName }} {{ item.createUserName }}
                  </text>
                </view>
                <view class="file_item_content_note_container">
                  <text class="file_item_content_note">
                    {{ item.fileSize | fileSizeFilter }}
                  </text>
                  <text class="file_item_content_note">
                    {{ item.uploadTime ? item.uploadTime : item.createDate }}
                  </text>
                </view>
              </view>
            </view>
          </uni-swipe-action-item>
        </uni-swipe-action>
      </mescroll>
    </view>
    <input-prompt
      v-if="cancelShow"
      type="text"
      :title="titleText"
      :value="inputVal"
      :name="fieldName"
      placeholder="请输入文件名"
      @confirm="confirm"
      @cancel="cancel"
    ></input-prompt>

    <uni-popup ref="popup" type="bottom">
      <view
        class="popup-action-item"
        @click="previewFile(actionItem.id, actionItem.fileName)"
      >
        预览
      </view>
      <view
        class="popup-action-item"
        @click="downloadFile(actionItem.id, actionItem.fileName)"
      >
        下载
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import inputPrompt from '@/components/input-prompt/input-prompt.vue';
import Base64 from '@/common/js/base64.min.js';
export default {
  components: {
    mescroll,
    inputPrompt
  },
  data() {
    return {
      fromPage: '',
      fileList: [],
      options: [],
      cancelShow: false,
      inputVal: '',
      fieldName: '',
      titleText: '',
      currentIndex: null,
      actionItem: {},

      list: [{ name: '我的文档' }, { name: '分享给我的' }],
      tabsAcitve: 0
    };
  },
  watch: {
    tabsAcitve: {
      handler(val) {
        if (val === 0) {
          this.options = [
            {
              text: '重命名',
              style: {
                backgroundColor: 'rgb(254,156,1)'
              }
            },
            {
              text: '删除',
              style: {
                backgroundColor: 'rgb(255,58,49)'
              }
            }
          ];
        } else {
          this.options = [];
        }

        this.$nextTick(() => {
          this.datasInit();
          this.$refs.mescroll.mescroll.resetUpScroll();
        });
      },
      immediate: true
    }
  },
  computed: {
    ...mapState(['empcode'])
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
  },
  mounted() {
    // #ifdef H5
    let input = document.createElement('input');
    input.style.width = '100%';
    input.type = 'file'; //添加file类型
    // input.accept='.pdf' //限制只能上传PDF文件
    input.style.height = '100%';
    input.style.position = 'absolute';
    input.style.top = '0';
    input.style.right = '0';
    input.style.opacity = '0';
    input.style.overflow = 'hidden'; //防止注意input 元素溢出
    input.id = 'file';
    input.multiple = 'multiple'; //安卓浏览器不兼容（除QQ浏览器）
    input.onchange = event => {
      let files = event.target.files;
      this.uploadFile(files, input);
    };
    this.$refs.fileinput.$el.appendChild(input);
    // #endif
  },
  methods: {
    handleChangeTabs(val) {
      this.tabsAcitve = val;
    },
    //获取文件列表
    async getFileList(page, successCallback, errorCallback, keywords) {
      let API =
        this.tabsAcitve == 0
          ? this.ajax.getWXMyAttachment
          : this.ajax.getShareAttachment;
      await API({
        uploadUser: this.empcode,
        originalName: keywords,
        pageNo: page.num,
        pageSize: page.size
      })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setFileList(row) {
      for (let i = 0; i < row.length; i++) {
        row[i].isOpened = false;
      }
      this.fileList = this.fileList.concat(row);
    },
    datasInit() {
      this.fileList = [];
    },

    /**@desc 显示下载或者预览 */
    showDetail(id, fileName) {
      // this.$refs.popup.open();
      // this.actionItem = {
      //   id,
      //   fileName
      // };

      this.previewFile(id, fileName);
    },

    // 预览
    previewFile(id, fileName) {
      this.$refs.popup.close();
      let filePath = `${this.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    },

    // 下载
    downloadFile(id, fileName) {
      this.$refs.popup.close();
      let filePath = `${this.$config.BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      this.$downloadFile.downloadFile(filePath);
    },
    swipeChange(e, vm) {
      this.fileList.forEach((item, index) => {
        if (vm === index) {
          this.$set(this.fileList[index], 'isOpened', e);
          return;
        } else {
          if (e) this.$set(this.fileList[index], 'isOpened', false);
        }
      });
    },

    swipeClick(index, e) {
      switch (e.index) {
        case 0: //重命名
          this.cancelShow = true;
          let originalNameArr = this.fileList[index].originalName.split('.');
          originalNameArr.splice(originalNameArr.length - 1, 1);
          this.inputVal = originalNameArr.join('.');
          this.fieldName = index.toString();
          this.titleText = '修改文件名';
          break;
        case 1: //删除
          uni.showModal({
            title: '提示',
            content: '您确定删除改文件？',
            confirmText: '取消',
            cancelText: '确定',
            confirmColor: '#005BAC',
            success: res => {
              if (res.cancel) {
                this.deleteFile(index);
              }
            }
          });
          break;
      }
    },

    //弹出层确定事件
    confirm(nameVal, pholderVal, formVal) {
      if (!formVal) {
        uni.showToast({
          icon: 'none',
          title: pholderVal
        });
        return;
      }
      this.cancelShow = false;
      this.editFileName(nameVal, formVal);
    },

    //弹出层取消事件
    cancel() {
      this.cancelShow = false;
      this.fieldName = '';
      this.inputVal = '';
    },

    //重命名
    editFileName(index, val) {
      this.ajax
        .editFileName({
          id: this.fileList[index].id,
          newOriginalName: `${val}.${this.fileList[index].fileExtension}`,
          originalName: `${val}.${this.fileList[index].fileExtension}`
        })
        .then(res => {
          this.$set(this.fileList[Number(index)], 'isOpened', false);
          this.$set(
            this.fileList[Number(index)],
            'originalName',
            `${val}.${this.fileList[index].fileExtension}`
          );
          uni.showToast({
            title: '修改成功',
            icon: 'none'
          });
        });
    },

    //删除文件
    deleteFile(index) {
      this.ajax
        .deleteFile({
          attachmentIdList: [this.fileList[index].id],
          method: 'myDeleted'
        })
        .then(res => {
          this.fileList.splice(index, 1);
          uni.showToast({
            title: '删除成功',
            icon: 'none'
          });
        });
    },

    //上传文件
    uploadFile(files, inputDom) {
      //创建对象实例
      let formData = new FormData();
      //追加文件数据
      for (let i = 0; i < files.length; i++) {
        formData.append('file', files[i]);
      }
      //上传单个
      let xhr = new XMLHttpRequest();
      let requestUrl = `${this.$config.BASE_HOST}/ts-document/attachment/fileUpload?module=personal`;
      xhr.open('POST', requestUrl, true);
      xhr.upload.addEventListener(
        'progress',
        function(event) {
          if (event.lengthComputable) {
            let percent = Math.ceil((event.loaded * 100) / event.total) + '%';
            uni.showLoading({
              title: `上传中(${percent})`
            });
          }
        },
        false
      );
      xhr.ontimeout = function() {
        // xhr请求超时事件处理
        uni.showToast({
          title: '请求超时',
          icon: 'none'
        });
      };
      xhr.onreadystatechange = () => {
        if (xhr.readyState === 4 && xhr.status === 200) {
          //上传成功
          let res = JSON.parse(xhr.responseText);
          if (res.statusCode === 302 || res.statusCode === 21000) {
            this.$store.dispatch('goToLogin');
          } else if (res.statusCode != 200) {
            uni.showModal({
              title: '提示',
              showCancel: false,
              confirmColor: '#005BAC',
              content: res.message
            });
          } else {
            uni.showToast({
              title: '上传成功',
              icon: 'none'
            });
            inputDom.value = '';
            this.$refs['mescroll'].downCallback();
          }
        }
      };
      xhr.send(formData);
    },

    //返回
    returnBack() {
      uni.redirectTo({
        url: `/pages/file/file?fromPage=${this.fromPage}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  .mescroll-content {
    position: absolute;
    width: 100%;
    height: calc(100% - 92px);
    top: 92px;
    bottom: 0;
  }
  .file_input_wrap {
    // display: none;
  }
  .addBox {
    position: relative;
    font-size: 56rpx;
    color: #005bac;
  }
  .file_item {
    width: 100%;
    display: flex;
    background-color: #ffffff;
    position: relative;
    flex-direction: row;
    padding: 22rpx 30rpx;
    position: relative;
    justify-content: space-between;
    align-items: center;
    .file_item_icon {
      margin-right: 20rpx;
      line-height: 1;
    }
    .file_item_content {
      display: flex;
      flex: 1;
      overflow: hidden;
      flex-direction: column;
      color: #333333;
      .file_item_content_title_container {
        &.top {
          display: flex;
          justify-content: space-between;
          .create-info {
            font-size: 28rpx !important;
          }
        }
        line-height: 1.3;
        .file_item_content_title {
          font-size: 32rpx;
          color: #333333;
          overflow: hidden;
        }
      }
      .file_item_content_note_container {
        line-height: 1;
        .file_item_content_note {
          color: #999;
          font-size: 24rpx;
          overflow: hidden;
          padding-right: 10px;
        }
      }
    }
  }
}
/deep/ .uni-transition {
  background-color: transparent;
}
.popup-action-item {
  background-color: #fff;
  text-align: center;
  margin: 8px 16px;
  border-radius: 4px;
  line-height: 40px;
}
</style>
