<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="会议助手"></page-head>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :searchInput="false"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view
          class="contact_item"
          v-for="(item, index) in dataList"
          :key="index"
          :data-item-id="item.id"
        >
          <view class="contact_item_time">{{
            item.START_TIME | indexTimeFilter
          }}</view>
          <view class="contact_item_info">
            <view class="contact_item_top">
              <text class="contact_item_title">参会确认</text>
            </view>
            <view class="contact_item_content">
              <view class="contact_item_text"
                >收到一条会议邀请，请您确认是否参与会议！</view
              >
              <view class="contact_item_subtext">主题：{{ item.MOTIF }}</view>
              <view class="contact_item_subtext"
                >时间：{{ item.START_TIME }}-{{
                  item.END_TIME.split(' ')[1]
                }}</view
              >
              <view class="contact_item_subtext"
                >地点：{{ item.LOCATION }} {{ item.NAME }}</view
              >
            </view>
            <view class="contact_item_bottom">
              <view class="item_bottom" @click="confirmMeeting(item.signinId)"
                >参会</view
              >
              <view class="item_bottom" @click="cancelMeeting(item.signinId)"
                >请假</view
              >
            </view>
          </view>
        </view>
      </mescroll>
    </view>
    <input-prompt
      v-if="cancelShow"
      type="text"
      title="请假理由"
      name=""
      placeholder="请输入请假理由"
      @confirm="confirm"
      @cancel="cancel"
    ></input-prompt>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  computed: {
    ...mapState(['empcode'])
  },
  data() {
    return {
      dataList: [], //列表数据
      cancelShow: false,
      itemId: ''
    };
  },
  watch: {
    $route: {
      handler: function() {
        this.dataList = [];
        this.$refs['mescroll'].downCallback();
      },
      // 深度观察监听
      deep: true
    }
  },
  methods: {
    async getListData(page, successCallback, errorCallback, keywords) {
      await this.ajax
        .getMyBoardroomMeeting({
          userCode: this.empcode
        })
        .then(res => {
          let rows = res.object;
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(row) {
      this.dataList = this.dataList.concat(row);
    },
    datasInit() {
      this.dataList = [];
    },
    confirmMeeting(itemId) {
      let data = {
        id: itemId,
        isAffirm: '1'
      };
      this.confirmBoardroomMeeting(data).then(res => {
        if (res.success) {
          uni.showToast({
            title: '发送回执成功',
            duration: 2000,
            icon: 'none'
          });
        } else {
          uni.showToast({
            title: res.message,
            duration: 2000,
            icon: 'none'
          });
        }
      });
    },
    cancelMeeting(itemId) {
      this.cancelShow = true;
      this.itemId = itemId;
    },
    confirm(nameVal, pholderVal, formVal) {
      if (!formVal) {
        uni.showToast({
          icon: 'none',
          title: pholderVal
        });
        return;
      }
      let data = {
        id: this.itemId,
        isAffirm: '2',
        signinStatus: '2',
        reasonleave: formVal
      };
      this.ajax.confirmBoardroomMeeting(data).then(res => {
        if (res.success) {
          uni.showToast({
            title: '请假成功',
            duration: 2000,
            icon: 'none'
          });
        } else {
          uni.showToast({
            title: res.message,
            duration: 2000,
            icon: 'none'
          });
        }
      });
      this.cancelShow = false;
    },
    cancel() {
      this.cancelShow = false;
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/index'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  .mescroll-content {
    position: absolute;
    width: 100%;
    top: 44px;
    bottom: 0;
    .contact_item_time {
      font-size: 24rpx;
      color: #999;
      text-align: center;
      margin-top: 20rpx;
    }
    .contact_item_info {
      background-color: #ffffff;
      margin: 30rpx 22rpx;
      border-radius: 16rpx;
      .contact_item_top {
        display: flex;
        padding: 22rpx 30rpx 0;
        .contact_item_title {
          flex: 1;
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
      .contact_item_content {
        padding: 22rpx 30rpx;
        font-size: 28rpx;
        .contact_item_text {
          color: #333333;
          font-size: 28rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }
        .contact_item_subtext {
          color: #666666;
          font-size: 28rpx;
        }
      }
      .contact_item_bottom {
        text-align: right;
        padding: 22rpx 30rpx;
        position: relative;
        font-size: 28rpx;
        color: #333;
        display: flex;
        justify-content: space-between;
        &::after {
          position: absolute;
          content: '';
          top: 0;
          height: 1px;
          background-color: #eee;
          left: 30rpx;
          right: 30rpx;
          transform: scaleY(0.5);
        }
        .item_bottom {
          flex: 1;
          text-align: center;
          position: relative;
          &:first-child::after {
            width: 0;
          }
          &::after {
            position: absolute;
            content: '';
            top: 0;
            bottom: 0;
            left: 0;
            width: 1px;
            background-color: #eee;
            transform: scaleY(0.5);
          }
        }
      }
    }
  }
}
</style>
