<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="邮箱助手"></page-head>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :searchInput="false"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view
          class="contact_item"
          v-for="(item, index) in dataList"
          :key="index"
        >
          <view class="contact_item_time">{{
            item.createDate | indexTimeFilter
          }}</view>
          <view
            class="contact_item_info"
            :data-item-id="item.statusId"
            @tap="chooseItem"
          >
            <view class="contact_item_top">
              <text class="contact_item_title">{{ item.subject }}</text>
            </view>
            <view class="contact_item_content">
              <rich-text class="infoContent" :nodes="item.content"></rich-text>
            </view>
            <view class="contact_item_bottom">阅读详情</view>
          </view>
        </view>
      </mescroll>
    </view>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  data() {
    return {
      dataList: [] //列表数据
    };
  },
  watch: {
    $route: {
      handler: function() {
        this.dataList = [];
        this.$refs['mescroll'].downCallback();
      },
      // 深度观察监听
      deep: true
    }
  },
  methods: {
    async getListData(page, successCallback, errorCallback, keywords) {
      await this.ajax
        .getNoreadEmailList({
          source: 'mobile'
        })
        .then(res => {
          let rows = res.object;
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(row) {
      this.dataList = this.dataList.concat(row);
    },
    datasInit() {
      this.dataList = [];
    },
    chooseItem(e) {
      let data = e.currentTarget.dataset;
      uni.navigateTo({
        url: `/pages/email/email-details?statusId=${data.itemId}&folderId=1&fromPage=index`
      });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/index'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  .mescroll-content {
    position: absolute;
    width: 100%;
    top: 44px;
    bottom: 0;
    .contact_item_time {
      font-size: 24rpx;
      color: #999;
      text-align: center;
      margin-top: 20rpx;
    }
    .contact_item_info {
      background-color: #ffffff;
      margin: 30rpx 22rpx;
      border-radius: 16rpx;
      .contact_item_top {
        display: flex;
        padding: 22rpx 30rpx 0;
        .contact_item_title {
          flex: 1;
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
      .contact_item_content {
        padding: 22rpx 30rpx;
        font-size: 28rpx;
        .infoContent {
          color: #757575;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }
      }
      .contact_item_bottom {
        text-align: right;
        padding: 22rpx 30rpx;
        position: relative;
        font-size: 28rpx;
        color: #333;
        &::after {
          position: absolute;
          content: '';
          top: 0;
          height: 1px;
          background-color: #eee;
          left: 30rpx;
          right: 30rpx;
          transform: scaleY(0.5);
        }
      }
    }
  }
}
</style>
