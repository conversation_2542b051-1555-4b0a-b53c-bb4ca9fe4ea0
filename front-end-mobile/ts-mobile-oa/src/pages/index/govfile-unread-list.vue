<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="收文助手"></page-head>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :searchInput="false"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view
          class="contact_item"
          v-for="(item, index) in dataList"
          :key="index"
        >
          <view class="contact_item_time">{{
            item.createDate | indexTimeFilter
          }}</view>
          <view class="contact_item_info" @tap="chooseItem(item)">
            <view class="contact_item_top">
              <text class="contact_item_title">{{ item.draftUnit }}</text>
            </view>
            <view class="contact_item_row">
              <text class="contact_item_content">{{ item.fileTitle }}</text>
            </view>
            <view class="contact_item_bottom">阅读详情</view>
          </view>
        </view>
      </mescroll>
    </view>
    <uni-popup type="bottom" ref="popup">
      <view class="popup-content-box">
        <view class="popup-content-title">{{ checkFileTitle }}</view>
        <view
          class="file-item"
          v-for="item in checkFileList"
          :key="item.fileId"
          @click="openFile(item)"
        >
          <text
            class="oa-icon"
            :class="'oa-icon-' + $oaModule.formatFileType(item.fileExtension)"
          ></text>
          <text>{{ item.originalName || '未知' }}</text>
          <text
            class="oa-icon oa-icon-xiazai down_load"
            @click.stop="downloadFile(item)"
          >
          </text>
        </view>
      </view>
    </uni-popup>
    <file-operation-selection
      ref="actionPopup"
      @preview="handleActionedFile"
      @download="handleActionedFile"
    ></file-operation-selection>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import Base64 from '@/common/js/base64.min.js';
export default {
  components: {
    mescroll
  },
  data() {
    return {
      dataList: [], //列表数据
      checkFileTitle: '',
      checkFileList: []
    };
  },
  methods: {
    async getListData(page, successCallback, errorCallback, keywords) {
      await this.ajax
        .getMySendfileList({
          isRead: 0,
          index: 5,
          pageSize: page.size,
          pageNo: page.num
        })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(row) {
      this.dataList = this.dataList.concat(row);
    },
    datasInit() {
      this.dataList = [];
    },
    chooseItem(row) {
      if (row.fileId && row.fileId.indexOf(',') !== -1) {
        this.showPopup(row);
      } else {
        this.$refs.actionPopup.open({
          id: row.fileId,
          fileName: row.fileName,
          rowId: row.id
        });
      }
    },
    async handleActionedFile(row) {
      if (this.tabIndex == 0) await this.changFileStatus(row.rowId);
    },
    async openFile(row) {
      let filePath = `${this.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${row.fileId}?fullfilename=${row.fileName}&source=mobile`;
      await this.changFileStatus(row.id);
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(
            filePath
          )}&path=/pages/index/govfile-unread-list`
        });
      } else {
        this.$downloadFile.downloadFile(filePath);
      }
    },
    async downloadFile(row) {
      let filePath = `${this.$config.BASE_HOST}/ts-document/attachment/downloadFile/${row.fileId}?fullfilename=${row.fileName}&source=mobile`;
      await this.changFileStatus(row.id);
      this.$downloadFile.downloadFile(filePath);
    },
    showPopup(row) {
      this.checkFileTitle = row.fileTitle;
      let fileIdList = row.fileId.split(','),
        originalNameList = row.originalName.split(','),
        fileNameList = row.fileName.split(','),
        fileExtensionList = row.fileExtension.split(',');
      this.checkFileList = fileIdList.map((item, index) => {
        return {
          id: row.id,
          fileId: item,
          fileName: fileNameList[index],
          originalName: originalNameList[index],
          fileExtension: fileExtensionList[index]
        };
      });
      this.$refs['popup'].open();
    },
    async changFileStatus(id) {
      await this.ajax.confirmMySendFile({
        isRead: 1,
        id: id
      });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/index'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  .mescroll-content {
    position: absolute;
    width: 100%;
    top: 44px;
    bottom: 0;
    .contact_item_time {
      font-size: 24rpx;
      color: #999;
      text-align: center;
      margin-top: 20rpx;
    }
    .contact_item_info {
      background-color: #ffffff;
      margin: 30rpx 22rpx;
      border-radius: 16rpx;
      .contact_item_top {
        display: flex;
        padding: 22rpx 30rpx 0;
        .contact_item_title {
          flex: 1;
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
      .contact_item_row {
        padding: 22rpx 30rpx;
        .contact_item_content {
          font-size: 28rpx;
          color: #666;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
      .contact_item_bottom {
        text-align: right;
        padding: 22rpx 30rpx;
        position: relative;
        font-size: 28rpx;
        color: #333;
        &::after {
          position: absolute;
          content: '';
          top: 0;
          height: 1px;
          background-color: #eee;
          left: 30rpx;
          right: 30rpx;
          transform: scaleY(0.5);
        }
      }
    }
  }
  .popup-content-box {
    background-color: #fff;
    .popup-content-title {
      padding: 20rpx 30rpx;
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
      border-bottom: 1px solid #ddd;
    }
  }
  .file-item {
    text-decoration: none;
    color: #333;
    background-color: #fff;
    display: flex;
    align-items: center;
    padding: 16rpx 30rpx;
    font-size: 28rpx;
    position: relative;
    // display: flex;
    // align-items: center;
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 30rpx;
      bottom: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &:last-child::after {
      height: 0;
    }
    text:nth-child(2) {
      flex: 1;
    }
    text:not(:nth-child(2)) {
      flex-shrink: 0;
    }
    .down_load {
      color: $theme-color;
    }
    .oa-icon {
      font-size: 40rpx;
      margin-right: 10rpx;
    }
  }
}
</style>
