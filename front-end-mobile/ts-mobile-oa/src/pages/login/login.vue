<template>
  <view class="ts-content" v-if="showContent">
    <view
      class="login-top"
      :style="{
        backgroundImage: 'url(' + globalSetting.loginPageBackground + ')',
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat'
      }"
    >
      <view class="login-top_logo-banner" v-if="globalSetting.loginTopLogo">
        <image
          class="login-top_logo-img"
          :src="globalSetting.loginTopLogo"
          mode="aspectFit"
        >
        </image>
      </view>
    </view>
    <view class="login-wrap">
      <view class="login-title">综合管理平台</view>
      <template>
        <uni-forms
          ref="form"
          :rules="rules"
          :modelValue="loginFormData"
          :border="true"
        >
          <uni-forms-item
            name="account"
            :required="true"
            :showRequiredIcon="false"
            :showMessage="false"
          >
            <text class="form-icon oa-icon oa-icon-wo" slot="leftIcon"> </text>
            <uni-easyinput
              v-model="loginFormData.account"
              :inputBorder="false"
              :focus="true"
              placeholder="请输入账号"
              @confirm="submit('form')"
            />
          </uni-forms-item>
          <uni-forms-item
            name="password"
            :required="true"
            :showRequiredIcon="false"
            :showMessage="false"
          >
            <text class="form-icon oa-icon oa-icon-suo" slot="leftIcon"> </text>
            <uni-easyinput
              type="password"
              v-model="loginFormData.password"
              :inputBorder="false"
              placeholder="请输入密码"
              @confirm="submit('form')"
            />
          </uni-forms-item>
        </uni-forms>
      </template>
      <button class="submit-btn" type="primary" @click="submit('form')">
        登录
      </button>
    </view>
    <view class="login-copyright">
      <view
        class="login-copyright-text"
        @click="
          globalSetting.recordLinkUrl
            ? jumpPage(encodeURIComponent(globalSetting.recordLinkUrl))
            : ''
        "
      >
        {{ globalSetting.recordNumber }}
      </view>
      <view
        class="login-copyright-text"
        @click="jumpPage(encodeURIComponent('http://www.trasen.cn'))"
      >
        综合协同办公平台{{ globalSetting.originalContent }}
      </view>
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
export default {
  data() {
    return {
      showContent: false,
      loginFormData: {
        account: '',
        password: ''
      },
      // 校验规则
      rules: {
        account: {
          rules: [
            {
              required: true,
              errorMessage: '请输入账号'
            }
          ]
        },
        password: {
          rules: [
            {
              required: true,
              errorMessage: '请输入密码'
            }
          ]
        }
      }
    };
  },
  computed: {
    ...mapState(['hasbind', 'usercode', 'password', 'globalSetting'])
  },
  onLoad() {
    this.showContent = true;
    if (this.hasbind && this.usercode && this.password) {
      this.loginFormData.account = this.usercode; //usercode实际为账号，empcode为工号
      this.loginFormData.password = this.password;
      this.loginTrasen();
    }
  },
  methods: {
    ...mapMutations(['changeState']),

    submit(ref) {
      this.$refs[ref]
        .validate()
        .then(res => {
          this.loginTrasen();
        })
        .catch(err => {
          this.$common.toast(err[0].errorMessage);
        });
    },
    //登录
    async loginTrasen() {
      await this.ajax
        .login({
          usercode: this.loginFormData.account,
          password: this.loginFormData.password,
          remarkid: `${location.origin}${location.pathname}#/page/index/index?encrypt=0`
        })
        .then(async res => {
          let data = res.object;
          uni.setStorageSync('_oa_token', data.token);
          uni.setStorageSync('_oa_usercode', data.usercode);
          let level = this.$common.pwdCheckStrong(
              this.loginFormData.password,
              this.globalSetting.passwordLength
            ),
            passwordRuleLength =
              (
                this.globalSetting.passwordRule &&
                this.globalSetting.passwordRule.split(',')
              ).length || 1;
          //弱密码校验
          if (this.globalSetting.remindPassword && level < passwordRuleLength) {
            this.$common.confirm('密码强度较低', '提示', '前往修改').then(e => {
              if (e) {
                uni.navigateTo({
                  url: '/pages/personalCenter/setting/password?fromPage=login'
                });
              }
            });
          } else await this.getPersonalInfo(data);
        });
    },
    //获取个人信息,查询用户是否已完善信息
    async getPersonalInfo(data) {
      await this.ajax
        .getPersonalInformationSettings({
          userCode: data.usercode
        })
        .then(res => {
          let obj = res.object;
          if (
            this.$config.ENABLE_IMPROVE_PERSON_INFO &&
            (!obj.empIdcard || !obj.empPhone)
          ) {
            uni.navigateTo({
              url: `/pages/personalCenter/settingPersonal/improve-information`
            });
          } else this.storeUserInfo(obj);
        });
    },
    //存储用户信息并跳转到首页
    storeUserInfo(data) {
      let userInfo = {
        empid: data.id,
        empcode: data.empCode,	//员工工号
        username: data.empName,
        empsex: data.empSex,
        password: this.loginFormData.password,
        usercode: data.userAccounts,  //员工账号
        deptname: data.empDeptName,
        deptid: data.empDeptId,
        dutyname: data.empDutyName,
        headimg: data.empHeadImg,
        hasbind: true
      };
      uni.setStorageSync('_oa_user_key', JSON.stringify(userInfo));
      //更新状态
      if (!this.hasbind) {
        this.changeState(userInfo);
        //绑定用户
        location.href = `${this.$config.BASE_HOST}/ts-information/oa/bindUser?userAccounts=${this.loginFormData.account}`;
      } else {
        this.changeState(userInfo);
      }
      let _prePage = uni.getStorageSync('_prePage');
      if (_prePage) {
        uni.removeStorageSync('_prePage');
        this.$nextTick(() => {
          uni.reLaunch({
            url: _prePage
          });
        });
      } else {
        uni.switchTab({
          url: '/pages/index/index'
        });
      }
    },
    jumpPage(href) {
      // #ifdef H5
      window.open(decodeURIComponent(href));
      // #endif
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  background-color: #ffffff;
  height: 100%;
}
.login-top {
  background: url(../../static/img/loginBg.png);
  height: 406rpx;
  position: relative;
}
.login-top_logo-banner {
  height: 140rpx;
  background: rgba(0, 0, 0, 0.3);
  position: absolute;
  bottom: 0;
  width: 100%;
  left: 0;
  padding: 12rpx 0;
  box-sizing: border-box;
  text-align: center;
}
.login-top_logo-img {
  height: 100%;
  width: 100%;
}
.login-wrap {
  margin: 70rpx 90rpx 0;
}
.login-title {
  color: #666666;
  font-size: 32rpx;
  font-weight: 600;
}
.form-icon {
  font-size: 44rpx;
  color: #999;
  margin-right: 30rpx;
}
.submit-btn {
  margin-top: 80rpx;
  font-size: 36rpx;
  height: 88rpx;
  border-radius: 44rpx;
  background-color: $theme-color;
}
.login-copyright {
  position: absolute;
  bottom: 0;
  margin-bottom: 20rpx;
  left: 0;
  width: 100%;
}
.login-copyright-text {
  color: #999999;
  font-size: 20rpx;
  text-align: center;
}
</style>
