<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="会议列表" />
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :searchInput="true"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <!-- 数据列表 -->
        <view
          class="contact_item"
          v-for="item in dataList"
          :key="item.id"
          :data-item-id="item.id"
          :data-item-subject="item.motif"
          @tap="chooseItem"
        >
          <view class="contact_item_title">
            {{ item.motif }}
            <text
              class="contact_item_status status_wait"
              v-if="item.meetingStatus == 0"
              >待开始</text
            >
            <text
              class="contact_item_status status_on"
              v-else-if="item.meetingStatus == 1"
              >进行中</text
            >
            <text
              class="contact_item_status status_end"
              v-else-if="item.meetingStatus == -1"
              >已结束</text
            >
          </view>
          <view class="contact_item_time">
            <text class="oa-icon oa-icon-shijian contact_item_icon"></text>
            {{
              item.startTime.substring(0, 16) +
                '-' +
                item.endTime.substring(11, 16)
            }}
          </view>
          <view class="contact_item_address">
            <text class="oa-icon oa-icon-didian contact_item_icon"></text>
            {{ item.location + '-' + item.name }}
          </view>
        </view>
        <!-- 数据列表 end-->
      </mescroll>
    </view>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  data() {
    return {
      dataList: [], //列表数据
      type: ''
    };
  },
  onLoad(opt) {
    this.type = opt.type;
  },
  methods: {
    async getListData(page, successCallback, errorCallback, keywords) {
      await this.ajax
        .getMeetingConsult({
          pageSize: page.size,
          pageNo: page.num,
          boardroom_search: keywords,
          sord: 'desc',
          sidx: 'CREATE_DATE'
        })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(row) {
      this.dataList = this.dataList.concat(row);
    },
    datasInit() {
      this.dataList = [];
    },
    chooseItem(e) {
      let data = e.currentTarget.dataset;
      uni.navigateTo({
        url: `/pages/${this.type}?id=${data.itemId}&subject=${data.itemSubject}`
      });
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  .mescroll-content {
    position: absolute;
    width: 100%;
    top: 44px;
    bottom: 0;
    .contact_item {
      padding: 22rpx 30rpx;
      background-color: #ffffff;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        height: 1px;
        background-color: #eee;
        left: 30rpx;
        right: 0;
      }
      &:last-child::after {
        height: 0;
      }
      .contact_item_title {
        font-size: 32rpx;
        color: #333333;
        font-weight: bold;
        overflow: hidden;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .contact_item_status {
          font-size: 24rpx;
          font-weight: normal;
        }
        .status_wait {
          color: #3aad73;
        }
        .status_on {
          color: #005bac;
        }
        .status_end {
          color: #999;
        }
      }
      .contact_item_time,
      .contact_item_address {
        font-size: 28rpx;
        color: #666;
        overflow: hidden;
        .contact_item_icon {
          color: #f59a23;
          padding-right: 10rpx;
          font-size: 28rpx;
        }
      }
    }
  }
}
</style>
