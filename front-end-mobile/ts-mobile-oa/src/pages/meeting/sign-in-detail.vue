<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" :title="title + '统计'"></page-head>
    <view class="contact" v-if="showContent">
      <view class="contact_top">
        <view class="contact_top_title">
          <text class="title">{{ title }}情况</text>
          <text class="title_right" v-if="status == 0"
            >应到 {{ dueNumber }}</text
          >
        </view>
        <view class="contact_top_subject">
          {{ meetingSubject }}
        </view>
      </view>
      <view class="contact_center tab_wrap">
        <view
          class="tab_item"
          :class="signType == 'signIn' ? 'active' : ''"
          data-type="signIn"
          @tap="switchType"
        >
          <text class="tab_item_text"
            >已{{
              title + ' ' + (status == 0 ? signInNumber : leaveSignedNumber)
            }}</text
          >
        </view>
        <view
          class="tab_item"
          :class="signType == 'notSign' ? 'active' : ''"
          data-type="notSign"
          @tap="switchType"
        >
          <text class="tab_item_text"
            >未{{
              title + ' ' + (status == 0 ? notSignNumber : noLeaveSignedNumber)
            }}</text
          >
        </view>
      </view>
      <view class="contact_bottom">
        <view v-if="status == 0">
          <view v-if="signType == 'signIn'" class="tap_wrap">
            <view class="tap_item">
              <view class="item_num">{{ bePresentNumber }}</view>
              <view class="item_name purpleFont">出席</view>
            </view>
            <view class="tap_item">
              <view class="item_num">{{ rankNumber }}</view>
              <view class="item_name purpleFont">列席</view>
            </view>
            <view class="tap_item">
              <view class="item_num">{{ lateNumber }}</view>
              <view class="item_name orgFont">迟到</view>
            </view>
          </view>
          <view v-else class="tap_wrap">
            <view class="tap_item">
              <view class="item_num">{{ absentNumber }}</view>
              <view class="item_name orgFont">缺席</view>
            </view>
            <view class="tap_item">
              <view class="item_num">{{ leaveNumber }}</view>
              <view class="item_name orgFont">请假</view>
            </view>
          </view>
        </view>
        <view class="list_wrap">
          <view class="view_cell" v-for="(item, index) in list" :key="index">
            <image
              class="iconImg"
              v-if="item.empHeadImg ? true : false"
              :src="$config.BASE_HOST + item.empHeadImg"
              mode="aspectFill"
            ></image>
            <view
              v-else
              class="iconImg"
              :class="item.empSex == 0 ? 'sexMan' : 'sexWoman'"
            >
              {{
                item.signinUsername.substring(item.signinUsername.length - 2)
              }}
            </view>
            <view class="userInfo">
              <view class="userInfo_title">
                <text class="name">{{ item.signinUsername }}</text>
                <text class="orgFont warning" v-if="item.signinStatus == 2"
                  >请假</text
                >
                <text class="orgFont warning" v-else-if="item.signinStatus == 3"
                  >迟到</text
                >
              </view>
              <view class="userInfo_description">
                <text class="dept"
                  >{{ item.signinUserdept }}&nbsp;&nbsp;{{
                    item.empDutyName
                  }}</text
                >
                <text class="time">{{
                  status == 0 ? item.signinTime : item.signinOutTime
                }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showContent: false,
      status: 0, //0 :'签到', 1: '签退'
      signType: 'signIn', //signIn :'签到' ,notSign: '未签到'
      meetingId: '',
      meetingSubject: '',
      signInNumber: 0,
      notSignNumber: 0,
      dueNumber: 0, //应到
      bePresentNumber: 0, //出席
      rankNumber: 0, //列席
      lateNumber: 0, //迟到
      leaveNumber: 0, //请假
      absentNumber: 0,
      list: [],
      signedList: [], //已签到人员列表
      noSignedList: [], //未签到和请假人员列表
      leaveSignedList: [], //已签退人员列表
      noLeaveSignedList: [] //未签退人员列表
    };
  },
  onLoad(opt) {
    this.title = opt.status == 0 ? '签到' : '签退';
    this.meetingSubject = opt.subject;
    this.meetingId = opt.id;
    this.status = opt.status;
    this.getData(opt.id);
  },
  methods: {
    async getData() {
      await this.ajax
        .getSignedDetailsList({
          applyId: this.meetingId
        })
        .then(res => {
          let data = res.object;
          this.dueNumber = data.dueNumber; //应到
          this.bePresentNumber = data.bePresentNumber; //出席
          this.rankNumber = data.rankNumber; //列席
          this.lateNumber = data.lateNumber; //迟到
          this.leaveNumber = data.leaveNumber; //请假
          this.signInNumber = data.signedList.length; //签到
          this.notSignNumber = data.noSignedList.length; //未签到
          this.leaveSignedNumber = data.leaveSignedList.length; //签退
          this.noLeaveSignedNumber = data.noLeaveSignedList.length; //未签退
          this.signedList = data.signedList; //签到列表
          this.noSignedList = data.noSignedList; //未签到列表
          this.leaveSignedList = data.leaveSignedList; //签退列表
          this.noLeaveSignedList = data.noLeaveSignedList; //未签退列表
          this.setListData();
          this.showContent = true;
        });
    },
    setListData() {
      if (this.status == 0 && this.signType == 'signIn') {
        this.list = this.signedList;
      } else if (this.status == 0 && this.signType == 'notSign') {
        this.list = this.noSignedList;
      } else if (this.status == 1 && this.signType == 'signIn') {
        this.list = this.leaveSignedList;
      } else if (this.status == 1 && this.signType == 'notSign') {
        this.list = this.noLeaveSignedList;
      }
    },
    switchType(e) {
      let data = e.currentTarget.dataset;
      this.signType = data.type;
      this.setListData();
    },
    //返回上一层
    returnBack() {
      uni.redirectTo({
        url: `/pages/meeting/sign-in-list?id=${this.meetingId}&subject=${this.meetingSubject}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  .purpleFont {
    color: #8080ff;
  }
  .orgFont {
    color: #f59a23;
  }
  .contact {
    flex: 1;
    display: flex;
    flex-direction: column;
    .contact_top {
      padding: 22rpx 30rpx;
      .contact_top_title {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        overflow: hidden;
        color: #333333;
        .title {
          flex: 1;
          font-size: 32rpx;
          font-weight: bold;
        }
        .title_right {
          font-size: 28rpx;
        }
      }
      .contact_top_subject {
        font-size: 28rpx;
      }
    }
    .tab_wrap {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      font-weight: bold;
      background-color: #ffffff;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        transform: scaleY(0.5);
        background-color: #eee;
      }
      .tab_item {
        flex: 1;
        text-align: center;
        .tab_item_text {
          padding: 16rpx;
          position: relative;
          display: inline-block;
        }
      }
      .tab_item.active {
        position: relative;
        .tab_item_text {
          &::after {
            position: absolute;
            content: '';
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background-color: #005bac;
          }
        }
      }
    }
    .contact_bottom {
      background-color: #ffffff;
      display: flex;
      flex-direction: column;
      flex: 1;
      .tap_wrap {
        display: flex;
        position: relative;
        &::after {
          position: absolute;
          content: '';
          bottom: 0;
          left: 0;
          right: 0;
          height: 1px;
          transform: scaleY(0.5);
          background-color: #eee;
        }
        .tap_item {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          padding: 20rpx 0;
          color: #666;
          font-size: 32rpx;
          font-weight: bold;
          .item_num {
            font-size: 36rpx;
          }
        }
      }
      .list_wrap {
        flex: 1;
        background-color: #ffffff;
        overflow-y: scroll;
        position: relative;
        .view_cell {
          padding: 22rpx 30rpx;
          display: flex;
          align-items: center;
          background-color: #ffffff;
          position: relative;
          &::after {
            position: absolute;
            content: '';
            bottom: 0;
            height: 1px;
            background-color: #eee;
            left: 30rpx;
            right: 0;
          }
          &:last-child::after {
            height: 0;
          }
          .userInfo {
            display: flex;
            flex-direction: column;
            justify-content: center;
            flex: 1;
            color: #333333;
            .userInfo_title {
              color: #333333;
              .name {
                padding-right: 10rpx;
                font-size: 32rpx;
                font-weight: bold;
              }
              .warning {
                font-size: 28rpx;
              }
            }
            .userInfo_description {
              color: #666666;
              font-weight: normal;
              font-size: 28rpx;
              .time {
                float: right;
                color: #999;
              }
            }
          }
          .iconImg {
            width: 80rpx;
            height: 80rpx;
            margin-right: 20rpx;
            border-radius: 100%;
            color: #ffffff;
            text-align: center;
            line-height: 2.8;
            font-size: 28rpx;
          }
          .sexMan {
            background-color: $sexman-color;
          }
          .sexWoman {
            background-color: $sexwoman-color;
          }
        }
      }
    }
  }
}
</style>
