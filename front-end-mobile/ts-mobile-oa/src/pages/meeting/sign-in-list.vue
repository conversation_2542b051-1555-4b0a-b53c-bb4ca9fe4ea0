<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="签到列表"></page-head>
    <view v-if="showContent" class="uni-list">
      <view
        class="uni-list-cell"
        v-for="(item, index) in signinData"
        :key="index"
        :data-status="item.status"
        @tap="chooseItem"
      >
        <view class="uni-list-cell-navigate uni-navigate-right">
          <view class="content_item_title">{{ item.title }}</view>
          <view v-if="item.status == 0" class="content_item_status">
            <view class="status_item">
              应到
              <text class="status_item_num purpleFont">{{
                item.dueNumber ? item.dueNumber : 0
              }}</text>
            </view>
            <view class="status_item">
              已签
              <text class="status_item_num purpleFont">{{
                item.bePresentNumber ? item.bePresentNumber : 0
              }}</text>
            </view>
            <view class="status_item">
              未签
              <text class="status_item_num orgFont">{{
                Number(item.dueNumber ? item.dueNumber : 0) -
                  Number(item.bePresentNumber ? item.bePresentNumber : 0)
              }}</text>
            </view>
          </view>
          <view v-else class="content_item_status">
            <view class="status_item">
              已签
              <text class="status_item_num purpleFont">{{
                item.leaved ? item.leaved : 0
              }}</text>
            </view>
            <view class="status_item">
              未签
              <text class="status_item_num orgFont">{{
                item.noleave ? item.noleave : 0
              }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showContent: false,
      subject: '',
      meetingId: '',
      signinData: [
        {
          title: '签到情况',
          status: 0 //跳转地址
        },
        {
          title: '签退情况',
          status: 1
        }
      ]
    };
  },
  onLoad(opt) {
    this.meetingId = opt.id;
    this.subject = opt.subject;
    this.getData(opt.id);
  },
  methods: {
    async getData(id) {
      await this.ajax
        .getSignedGroupCount({
          applyId: id
        })
        .then(res => {
          let data = res.object;
          this.signinData[0] =
            data[0] != null
              ? { ...this.signinData[0], ...data[0] }
              : this.signinData[0];
          this.signinData[1] =
            data[1] != null
              ? { ...this.signinData[1], ...data[1] }
              : this.signinData[1];
          this.showContent = true;
        });
    },
    chooseItem(e) {
      let data = e.currentTarget.dataset;
      uni.navigateTo({
        url: `/pages/meeting/sign-in-detail?id=${this.meetingId}&status=${data.status}&subject=${this.subject}`
      });
    },
    //返回上一层
    returnBack() {
      uni.redirectTo({
        url: '/pages/meeting/meeting-list?type=sign-in-list'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  .uni-list-cell-navigate {
    flex-direction: column;
    align-items: flex-start;
  }
  .content_item_title {
    font-size: 32rpx;
    color: #333333;
    font-weight: bolder;
    flex: 1;
  }
  .content_item_status {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 28rpx;
    color: #666;
    overflow: hidden;
    .status_item {
      padding: 0 10rpx;
      &:first-child {
        padding-left: 0;
      }
      .status_item_num {
        padding-left: 4rpx;
      }
      .purpleFont {
        color: #8080ff;
      }
      .orgFont {
        color: #f59a23;
      }
    }
  }
}
</style>
