<template>
  <view class="ts-content">
    <page-head title="我的工资" @clickLeft="returnBack"></page-head>
    <view class="content">
      <view v-if="currentIndex === 0">
        <view class="content_top">
          <view class="content_top_month" @click="showPicker('date')">
            <view class="month_text">2020年2月</view>
            <uni-icons
              type="arrowdown"
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
            ></uni-icons>
          </view>
          <view class="segmented-control segmented-control_button">
            <view
              v-for="(item, index) in tabBars"
              :class="
                index === tabIndex ? 'segmented-control_button_item_active' : ''
              "
              class="segmented-control_button_item"
              :key="index"
              @click="onbtntabchange(index)"
            >
              <text
                :class="
                  index === tabIndex ? 'segmented-control_text_active' : ''
                "
                class="segmented-control_text"
                >{{ item }}</text
              >
            </view>
          </view>
        </view>
      </view>
      <view v-else-if="currentIndex === 1">
        <view class="content_top">
          <view class="content_top_month" @click="showPicker('date1')">
            <view class="month_text">2020年</view>
            <uni-icons
              type="arrowdown"
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
            ></uni-icons>
          </view>
        </view>
      </view>
    </view>
    <view class="segmented-control segmented-control_bottom">
      <view
        v-for="(item, index) in bottomTabBars"
        class="segmented-control_item"
        :key="index"
        @click="ontabchange(index)"
      >
        <text
          :class="index === currentIndex ? 'segmented-control_text_active' : ''"
          class="segmented-control_text"
          >{{ item }}</text
        >
      </view>
    </view>
    <date-picker
      :startDate="2000"
      mode="date"
      :current="true"
      @confirm="onConfirm($event, 'date')"
      @cancel="onCancel"
      ref="date"
      fields="month"
      :disabled-after="true"
    ></date-picker>
    <date-picker
      :startDate="2000"
      mode="date"
      :current="true"
      @confirm="onConfirm($event, 'date1')"
      @cancel="onCancel"
      ref="date1"
      fields="year"
      :disabled-after="true"
    ></date-picker>
  </view>
</template>

<script>
import datePicker from '@/components/picker/date-picker.vue';
export default {
  components: {
    datePicker
  },
  data() {
    return {
      currentIndex: 0, //当前底部选中的tab索引值，从0计数
      bottomTabBars: ['月工资', '年工资'],
      tabIndex: 0, //当前月工资选中的tab索引值，从0计数
      tabBars: ['基本工资', '绩效工资']
    };
  },
  methods: {
    //按钮tab切换
    onbtntabchange(index) {
      if (this.tabIndex !== index) {
        this.tabIndex = index;
      }
    },
    //底部tab切换
    ontabchange(index) {
      if (this.currentIndex !== index) {
        this.currentIndex = index;
      }
    },
    showPicker(type) {
      this.$refs[type].show();
    }
  }
};
</script>

<style lang="scss" scoped>
.segmented-control {
  /* #ifndef APP-NVUE */
  display: flex;
  box-sizing: border-box;
  /* #endif */
  flex-direction: row;
  height: 45px;
  overflow: hidden;
}
.segmented-control_bottom {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  .segmented-control_item {
    /* #ifndef APP-NVUE */
    display: inline-flex;
    box-sizing: border-box;
    /* #endif */
    position: relative;
    flex: 1;
    justify-content: center;
    align-items: center;
    .segmented-control_text {
      color: #333333;
    }
    .segmented-control_text_active {
      color: #005bac;
    }
  }
}
.segmented-control_button {
  justify-content: center;
  align-items: center;
  .segmented-control_button_item {
    /* #ifndef APP-NVUE */
    display: inline-flex;
    box-sizing: border-box;
    /* #endif */
    position: relative;
    justify-content: center;
    align-items: center;
    padding: 3px 8px;
    margin: 0 20rpx;
    font-size: 22rpx;
    color: #333333;
    &:last-child {
      margin: 0;
    }
  }
  .segmented-control_button_item_active {
    border-bottom: 1px solid #005bac;
    .segmented-control_text_active {
      color: #005bac;
    }
  }
}
.content_top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  padding: 0 30rpx;
  .content_top_month {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: #333333;
    .month_text {
      margin-right: 5px;
    }
  }
}
</style>
