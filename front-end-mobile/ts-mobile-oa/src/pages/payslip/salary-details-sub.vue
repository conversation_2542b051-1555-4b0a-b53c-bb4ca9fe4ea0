<template>
  <view class="ts-content">
    <page-head title="工资条" @clickLeft="returnBack"></page-head>
    <view class="salary_wrap">
      <view class="content_top_month">
        <view class="month_text" @click="showPicker('date')">{{
          currentDateStr
        }}</view>
        <uni-icons
          type="arrowdown"
          :size="30"
          class="uni-icon-wrapper"
          color="#333"
          @click="showPicker('date')"
        ></uni-icons>
      </view>
      <view class="salary_content">
        <view class="salary_list" v-if="fieldList.length != 0">
          <view
            class="salary_item"
            v-for="(item, index) in fieldList"
            :key="index"
          >
            <view class="salary_item-text">{{ item.title }}</view>
            <view class="salary" v-for="(one, key, i) in item.data" :key="i">
              <view
                class="salary_item-content"
                v-if="!excludeKey.includes(key)"
              >
                <view class="field_text">{{ key }}</view>
                <view class="field_value">{{ one }}</view>
              </view>
            </view>
          </view>
        </view>
        <view class="nothing" v-else>
          <view class="img_content">
            <image
              class="nothing_img"
              src="../../static/img/nothing.png"
              mode="aspectFit"
            ></image>
          </view>
          <view class="nothing-text">暂未查到数据</view>
        </view>
      </view>
    </view>
    <date-picker
      startDate="2000"
      mode="date"
      :value="currentDate"
      @confirm="onConfirm"
      ref="date"
      fields="month"
      :disabled-after="true"
    ></date-picker>
  </view>
</template>

<script>
import datePicker from '@/components/picker/date-picker.vue';
export default {
  components: {
    datePicker
  },
  data() {
    return {
      tableName: '',
      currentDate: '',
      currentDateStr: '',
      fieldList: [],
      fromPage: '',
      excludeKey: ['主键', '批次号', '发送次数', '状态']
    };
  },
  async onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.currentDate =
      opt.dateStr != '' && opt.dateStr != undefined
        ? opt.dateStr
        : this.$common.getMonth('', 1).timeStr;
    let dateArray = this.currentDate.split('-');
    this.currentDateStr = `${dateArray[0]}年${dateArray[1]}月`;
    await this.getSalaryDetailsList('toa_salary_jbgz', this.currentDate);
    await this.getSalaryDetailsList('toa_salary_jxgz', this.currentDate);
  },
  methods: {
    showPicker(type) {
      this.$refs[type].show();
    },
    onConfirm(e) {
      this.currentDate = e.result;
      let dateArray = e.result.split('-');
      this.currentDateStr = `${dateArray[0]}年${dateArray[1]}月`;
      this.fieldList = [];
      this.$nextTick(() => {
        this.getSalaryDetailsList('toa_salary_jbgz', e.result);
        this.getSalaryDetailsList('toa_salary_jxgz', e.result);
      });
    },
    async getSalaryDetailsList(tableName, dateStr) {
      await this.$request
        .asynRequest({
          api: `/ts-oa/salary/salarySet/salaryList/getSalaryDetailsList/${tableName}/${dateStr}`,
          method: 'GET',
          showLoading: true,
          hideLoading: tableName == 'toa_salary_jxgz'
        })
        .then(res => {
          if (Object.keys(res.object).length != 0) {
            let salary = {};
            if (tableName == 'toa_salary_jbgz') salary.title = '基本工资';
            else if (tableName == 'toa_salary_jxgz') salary.title = '绩效工资';
            salary.data = res.object;
            this.fieldList.push(salary);
          }
        })
        .catch(() => {});
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/${this.fromPage}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  .salary_wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    overflow: hidden;
    .content_top_month {
      width: 100%;
      text-align: center;
      margin: 20rpx auto;
      .month_text {
        display: inline-block;
        margin-right: 10rpx;
        font-size: 32rpx;
        color: #333333;
      }
    }
    .salary_content {
      flex: 1;
      display: flex;
      flex-direction: column;
      width: 94%;
      margin: 0 auto 3%;
      background-color: #ffffff;
      box-sizing: border-box;
      overflow: auto;
      .salary_list {
        width: 100%;
        .salary_item {
          border-bottom: 1px solid #e5e5e5;
        }
        .salary_item-text {
          background-color: #005bac;
          color: #ffffff;
          font-size: 32rpx;
          padding: 10rpx 30rpx;
          box-sizing: border-box;
        }
        .salary_item-content {
          display: table;
          width: 100%;
          min-height: 70rpx;
          .field_text {
            color: #333;
            width: 50%;
            text-align: center;
            border-right: 1px solid #e5e5e5;
            border-top: 1px solid #e5e5e5;
            border-left: 1px solid #e5e5e5;
            display: table-cell;
            vertical-align: middle;
          }
          .field_value {
            text-align: center;
            width: 50%;
            border-right: 1px solid #e5e5e5;
            border-top: 1px solid #e5e5e5;
            display: table-cell;
            vertical-align: middle;
          }
        }
      }
      .nothing {
        width: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .img_content {
          width: 300rpx;
          height: 300rpx;
          .nothing_img {
            width: 100%;
            height: 100%;
          }
        }
        .nothing-text {
          color: #999;
          font-size: 28rpx;
          margin-bottom: 80rpx;
        }
      }
    }
  }
}
</style>
