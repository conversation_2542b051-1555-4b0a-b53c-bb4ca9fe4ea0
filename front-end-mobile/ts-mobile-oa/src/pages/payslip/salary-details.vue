<template>
  <view
    class="ts-content"
    :style="{ backgroundImage: imgSrc }"
    style="background-color: #fff; background-position-x: -60rpx;"
  >
    <page-head title="工资条" @clickLeft="returnBack"></page-head>
    <view class="salary_wrap">
      <view class="content_top_month">
        <view class="top_text" @click="showPicker('date')">
          {{ `${dateStr}年` }}</view
        >
        <uni-icons
          type="arrowdown"
          :size="30"
          class="uni-icon-wrapper"
          color="#333"
          @click="showPicker('date')"
        ></uni-icons>
      </view>
      <view class="salary_content">
        <scroll-view
          class="content-wrap"
          id="content-wrap"
          :scroll-top="scrollTop"
          scroll-y="true"
          :scroll-into-view="scrollViewId"
          :scroll-with-animation="true"
          @scroll="scroll"
        >
          <view
            class="scroll-view-item salary_list"
            v-for="(item, index) in allList"
            :key="index"
            :id="forId(item.month)"
          >
            <view class="salary-month">{{ item.month }}月</view>
            <view
              class="salary_item base-box"
              v-for="(e, eIndex) in item.list"
              :key="eIndex"
            >
              <view class="salary_item-text">{{ e.name }}</view>
              <view class="salary_item-wrap" v-if="e['员工姓名']">
                <view class="salary">
                  <view class="salary_item-content salary_item-title">
                    <view class="field_value">元</view>
                  </view>
                </view>
                <view class="salary" v-for="(objVal, objKey, i) in e" :key="i">
                  <view
                    class="salary_item-content"
                    v-if="!excludeKey.includes(objKey) && objKey != 'name'"
                  >
                    <view class="field_text">{{ objKey }}</view>
                    <view class="field_value">{{ objVal }}</view>
                  </view>
                </view>
              </view>
              <view class="empty-wrap" v-else>
                <image
                  class="empty-img"
                  src="../../static/img/empty.png"
                  mode="aspectFit"
                ></image>
                <text class="empty-text">暂无数据</text>
              </view>
            </view>
          </view>
          <!-- <view
            class="scroll-view-item salary_list"
            v-for="(item, index) in jbList"
            :key="index"
            :id="forId(item.month)"
          >
            <view class="salary-month">{{ item.month }}月</view>
            <view class="salary_item base-box" v-if="showBase">
              <view class="salary_item-text">基本工资</view>
              <view class="salary_item-wrap" v-if="item['员工姓名']">
                <view class="salary">
                  <view class="salary_item-content salary_item-title">
                    <view class="field_text">工资项</view>
                    <view class="field_value">元</view>
                  </view>
                </view>
                <view
                  class="salary"
                  v-for="(objVal, objKey, i) in item"
                  :key="i"
                >
                  <view
                    class="salary_item-content"
                    v-if="!excludeKey.includes(objKey)"
                  >
                    <view class="field_text">{{ objKey }}</view>
                    <view class="field_value">{{ objVal }}</view>
                  </view>
                </view>
              </view>
              <view class="empty-wrap" v-else>
                <image
                  class="empty-img"
                  src="../../static/img/empty.png"
                  mode="aspectFit"
                ></image>
                <text class="empty-text">暂无数据</text>
              </view>
            </view>
            <view class="salary_item performance-box" v-if="showPerformance">
              <view class="salary_item-text">绩效工资</view>
              <view class="salary_item-wrap" v-if="jxList[index]['员工姓名']">
                <view class="salary">
                  <view class="salary_item-content salary_item-title">
                    <view class="field_text">工资项</view>
                    <view class="field_value">元</view>
                  </view>
                </view>
                <view
                  class="salary"
                  v-for="(objVal, objKey, i) in jxList[index]"
                  :key="i"
                >
                  <view
                    class="salary_item-content"
                    v-if="!excludeKey.includes(objKey)"
                  >
                    <view class="field_text">{{ objKey }}</view>
                    <view class="field_value">{{ objVal }}</view>
                  </view>
                </view>
              </view>
              <view class="empty-wrap" v-else>
                <image
                  class="empty-img"
                  src="../../static/img/empty.png"
                  mode="aspectFit"
                ></image>
                <text class="empty-text">暂无数据</text>
              </view>
            </view>
          </view> -->
        </scroll-view>
        <scroll-view
          class="swiper_head"
          :scroll-x="true"
          :show-scrollbar="false"
        >
          <view
            v-for="item in allList"
            :key="item.month"
            class="uni-tab-item"
            :class="
              currentIndex == item.month ? 'uni-tab-item-title-active' : ''
            "
            :data-view-id="`view_${item.month}`"
            :data-current="item.month"
            @click="ontabtap"
          >
            {{ item.month }}
          </view>
        </scroll-view>
      </view>
    </view>
    <canvas
      v-if="showCanvas"
      style="width:150px;height:100px;z-index: -1;"
      canvas-id="firstCanvas"
      id="firstCanvas"
    ></canvas>
    <date-picker
      startDate="2000"
      mode="date"
      :value="dateStr"
      @confirm="onConfirm"
      ref="date"
      fields="year"
      :disabled-after="true"
    ></date-picker>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import datePicker from '@/components/picker/date-picker.vue';
export default {
  components: {
    datePicker
  },
  data() {
    return {
      currentIndex: '',
      dateStr: '',
      salaryList: {},
      fromPage: '',
      excludeKey: [
        '主键',
        '批次号',
        '发送次数',
        '状态',
        'type',
        '工资发放年月',
        'month'
      ],
      jxList: [],
      jbList: [],
      scrollViewId: '',
      scrollStatus: true, //点击状态，是否能点击
      old: {
        scrollTop: 0
      },
      scrollTop: 0,
      nodeInfoList: [],
      month: null,
      year: null,
      imgSrc: '',
      showCanvas: true,
      showPerformance: false,
      showBase: false,
      menuList: [],
      allList: []
    };
  },
  computed: {
    ...mapState(['empcode', 'username', 'globalSetting'])
  },
  async onLoad(opt) {
    this.showPerformance = this.globalSetting.salaryType.indexOf('1') != -1;
    this.showBase = this.globalSetting.salaryType.indexOf('2') != -1;
    this.fromPage = opt.fromPage;
    let curDate = new Date();
    this.year = this.dateStr = curDate.getFullYear().toString();
    this.month = curDate.getMonth() + 1;
    await this.getSalaryTypeMenu();
    await this.getSalaryDetailsList(this.dateStr);
    this.$nextTick(() => {
      this.init();
      this.currentIndex = this.month;
      this.scrollViewId = `view_${this.month}`;
    });
  },
  mounted() {
    let context = uni.createCanvasContext('firstCanvas', this),
      watermarkTextArr = [];
    if (this.username) {
      watermarkTextArr.push(this.username);
    }
    if (this.empcode) {
      watermarkTextArr.push(this.empcode);
    }
    let watermarkText = watermarkTextArr.join('-');
    context.rotate((330 * Math.PI) / 180);
    context.setFontSize(12);
    context.setFillStyle('rgba(169,169,169,.3)');
    context.fillText(watermarkText, -20, 100);
    setTimeout(() => {
      context.draw();
      uni.canvasToTempFilePath({
        x: 0, // 从canvas的x轴的0点开始选中
        y: 0, // 从canvas的y轴的0点开始选中
        width: 150, // 选中canvas多宽
        height: 100, // 选中canvas多宽
        destWidth: 150, // 生成的图片多宽
        destHeight: 100, // 生成的图片多高
        canvasId: 'firstCanvas', // canvas的id
        success: res => {
          this.showCanvas = false;
          // 在H5平台下，tempFilePath 为 base64
          this.imgSrc = `url(${res.tempFilePath})`;
        }
      });
    }, 500);
  },
  methods: {
    init() {
      const query = uni.createSelectorQuery().in(this);
      query
        .selectAll('#content-wrap .salary_list')
        .boundingClientRect(data => {
          this.nodeInfoList = data;
        })
        .exec();
    },
    forId(index) {
      return `view_${index}`;
    },
    //tab点击事件
    ontabtap(e) {
      let data = e.currentTarget.dataset;
      if (!this.scrollStatus) return;
      // 200毫秒才能执行下次点击
      if (this.scrollStatus) {
        this.scrollStatus = false;
        // 解决clickedNavIndex相同触发更新失败
        if (this.scrollViewId == data.viewId) {
          this.scrollViewId = 'view--1';
        }
        this.$nextTick(() => {
          this.scrollViewId = data.viewId;
          this.currentIndex = data.current;
        });
        setTimeout(() => {
          // 200毫秒才能执行下次点击
          this.scrollStatus = true;
        }, 200);
      }
    },
    scroll(e) {
      if (!this.scrollStatus) return;
      let scrollTop = (this.old.scrollTop = e.detail.scrollTop);
      let curIndexId = this.nodeInfoList
        .map((item, index) => ({ index, ...item }))
        .filter(item => item.top <= scrollTop + 250)
        .sort((a, b) => b.top - a.top)[0].id;
      this.currentIndex = Number(curIndexId.split('_')[1]);
    },
    showPicker(type) {
      this.$refs[type].show();
    },
    async onConfirm(e) {
      this.dateStr = e.result;
      this.fieldList = [];
      this.jbList = [];
      this.jxList = [];
      await this.$nextTick(async () => {
        await this.getSalaryDetailsList(e.result);
        setTimeout(() => {
          this.init();
          if (this.year != e.result) {
            this.currentIndex = 12;
            this.scrollViewId = 'view_12';
          } else {
            this.currentIndex = this.month;
            this.scrollViewId = `view_${this.month}`;
          }
        }, 100);
      });
    },
    async getSalaryDetailsList(dateStr) {
      await this.ajax.getSalaryDetailsList('', dateStr).then(res => {
        let newData = res.object;
        // let data = res.object.reverse();
        // data.map(item => {
        //   let dDate = new Date(item['工资发放年月']),
        //     month = dDate.getMonth() + 1;
        //   item.month = month;
        //   if (item.type == 'jbgz') this.jbList.push(item);
        //   else if (item.type == 'jxgz') this.jxList.push(item);
        // });
        let list = [];
        newData.map(item => {
          let dDate = new Date(item['工资发放年月']),
            month = dDate.getMonth() + 1;
          item.month = month;
          if (!list[month]) {
            list[month] = {
              month: month,
              list: []
            };
          }
          this.menuList.forEach(e => {
            item['type'] == e.itemCode &&
              list[month].list.push({ ...item, name: e.itemName });
          });
        });
        list.shift();
        this.allList = list.reverse();
      });
    },
    // 获取配置
    async getSalaryTypeMenu() {
      await this.ajax.getSalaryTypeMenu().then(res => {
        this.menuList = res.object;
      });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/${this.fromPage}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}
.salary_wrap {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.content_top_month {
  width: 100%;
  height: 44px;
  line-height: 44px;
  padding: 0 20rpx;
  background-color: rgba(0, 91, 172, 0.1);
  box-sizing: border-box;
}
.top_text {
  display: inline-block;
  margin-right: 10rpx;
  font-size: 32rpx;
  color: #333333;
  box-sizing: border-box;
}
.salary_content {
  flex: 1;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  overflow: hidden;
}
.content-wrap {
  flex: 1;
  overflow: hidden;
}
.salary_list {
  width: 100%;
}
.salary-month {
  padding: 20rpx;
  color: #333333;
  font-size: 32rpx;
}
.salary_item {
  margin-left: 20rpx;
}
.salary_item-title {
  background-color: $theme-color;
  .field_text,
  .field_value {
    color: #ffffff;
    border-color: $theme-color;
  }
}
.salary_item-text {
  color: #333;
  font-size: 16px;
  padding: 5px 15px;
  box-sizing: border-box;
  text-align: center;
  font-weight: bold;
}
.salary_item-content {
  display: table;
  width: 100%;
  min-height: 70rpx;
}
.salary_item-wrap {
  border-bottom: 1px solid #e5e5e5;
}
.field_text {
  color: #333;
  width: 50%;
  text-align: center;
  border-right: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  border-left: 1px solid #e5e5e5;
  display: table-cell;
  vertical-align: middle;
}
.field_value {
  text-align: center;
  width: 50%;
  border-right: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  display: table-cell;
  vertical-align: middle;
}
.swiper_head {
  width: 80rpx;
  padding: 90rpx 0 0;
  height: 100%;
  flex-direction: row;
  box-sizing: border-box;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
  /* flex-wrap: nowrap; */
  /* border-color: #cccccc;
		border-bottom-style: solid;
		border-bottom-width: 1px; */
  .uni-tab-item {
    color: #999999;
    flex-wrap: nowrap;
    width: 60rpx;
    height: 60rpx;
    line-height: 60rpx;
    box-sizing: border-box;
    text-align: center;
    margin: 5px auto;
    border-radius: 100%;
    &.uni-tab-item-title-active {
      color: $theme-color;
      background-color: rgba(58, 136, 205, 0.2);
    }
  }
}
.top-btn {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  background-color: #f4f4f4;
  padding: 20rpx;
  z-index: 9;
  border-radius: 100%;
}
.empty-wrap {
  width: 100%;
  text-align: center;
  position: relative;
  border-bottom: 0;
  box-sizing: border-box;
  .empty-img {
    width: 400rpx;
  }
  .empty-text {
    color: #999;
    font-size: 28rpx;
    position: absolute;
    bottom: 120rpx;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
