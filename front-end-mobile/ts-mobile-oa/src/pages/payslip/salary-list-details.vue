<template>
  <view class="ts-content">
    <page-head :title="title" @clickLeft="returnBack"></page-head>
    <view class="salary_wrap">
      <view class="content_top">
        <view class="base_user_info">
          <image
            class="info_img"
            :src="
              headimg
                ? $config.BASE_HOST + headimg
                : '../../static/img/headImg.png'
            "
          ></image>
          <view class="">
            <view class="info_item">{{ username }}</view>
            <view class="info_item">{{ usercode }}</view>
          </view>
        </view>
        <view class="content_top_month" @click="showPicker('date')">
          <view class="month_text">{{ currentDateStr }}</view>
          <uni-icons
            type="arrowdown"
            :size="30"
            class="uni-icon-wrapper"
            color="#fff"
          ></uni-icons>
        </view>
      </view>
      <view class="salary_list" v-if="Object.keys(fieldList).length != 0">
        <view
          v-for="(item, key, index) in fieldList"
          :key="index"
          class="salary_item"
        >
          <view class="field_text">{{ key }}：</view>
          <view class="field_value">{{ item }}</view>
        </view>
      </view>
      <view class="nothing" v-else>
        <view class="img_content">
          <image
            class="nothing_img"
            src="../../static/img/nothing.png"
            mode="aspectFit"
          ></image>
        </view>
      </view>
    </view>
    <date-picker
      startDate="2000"
      mode="date"
      :value="currentDate"
      @confirm="onConfirm"
      ref="date"
      fields="month"
      :disabled-after="true"
    ></date-picker>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import datePicker from '@/components/picker/date-picker.vue';
export default {
  components: {
    datePicker
  },
  data() {
    return {
      title: '',
      tableName: '',
      currentDate: '',
      currentDateStr: '',
      fieldList: {},
      fromPage: ''
    };
  },
  computed: {
    ...mapState(['username', 'usercode', 'headimg'])
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.title = opt.tableTitle;
    this.tableName = opt.tableName;
    this.currentDate = opt.salaryDate || this.$common.getDate('month').timeStr;
    let dateArray = this.currentDate.split('-');
    this.currentDateStr = `${dateArray[0]}年${dateArray[1]}月`;
    this.getSalaryDetailsList(this.currentDate);
  },
  methods: {
    showPicker(type) {
      this.$refs[type].show();
    },
    onConfirm(e) {
      this.currentDate = e.result;
      let dateArray = e.result.split('-');
      this.currentDateStr = `${dateArray[0]}年${dateArray[1]}月`;
      this.getSalaryDetailsList(e.result);
    },
    getSalaryDetailsList(dateStr) {
      this.getSalaryDetailsList(this.tableName, dateStr).then(res => {
        this.fieldList = Object.keys(res.object).length != 0 ? res.object : {};
      });
    },
    returnBack() {
      uni.navigateTo({
        url: `/pages/payslip/salary-list?fromPage=${this.fromPage}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  .salary_wrap {
    width: 95%;
    margin: 20rpx auto;
    flex: 1;
    border-radius: 20rpx;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
    box-sizing: border-box;
    .content_top {
      width: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      background-color: #005bac;
      border-radius: 20rpx 20rpx 0 0;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      .content_top_month {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        color: #ffffff;
        .month_text {
          margin-right: 10rpx;
          margin: 10rpx auto;
          font-size: 32rpx;
          width: 100%;
        }
      }
      .base_user_info {
        display: flex;
        align-items: center;
        flex-direction: row;
        justify-content: center;
        .info_img {
          height: 130rpx;
          width: 130rpx;
          border-radius: 100%;
          margin-right: 20rpx;
        }
        .info_item {
          color: #ffffff;
          flex: 1;
        }
      }
    }
    .salary_list {
      flex: 1;
      width: 100%;
      overflow-y: auto;
      .salary_item {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 22rpx 30rpx;
        box-sizing: border-box;
        position: relative;
        &::after {
          content: '';
          position: absolute;
          left: 30rpx;
          right: 0;
          height: 1px;
          transform: scaleY(0.5);
          background-color: #eee;
          bottom: 0;
        }
        &:last-child::after {
          height: 0;
        }
        .field_text {
          width: 200rpx;
          color: #333;
        }
        .field_value {
          flex: 1;
          text-align: right;
        }
      }
    }
    .nothing {
      width: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .img_content {
        width: 300rpx;
        height: 300rpx;
        .nothing_img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
