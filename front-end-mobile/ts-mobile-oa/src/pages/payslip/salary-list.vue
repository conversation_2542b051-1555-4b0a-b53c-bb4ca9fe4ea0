<template>
  <view class="ts-content">
    <page-head title="收入查询" @clickLeft="returnBack"></page-head>
    <uni-list v-if="showContent">
      <uni-list-item
        v-for="(item, index) in salaryList"
        :key="index"
        :title="item.tableTitle"
        thumb="oa-icon-keshiwendang"
        @click="goDetailPage(item.salaryDate, item.tableName, item.tableTitle)"
        iconStyleStr="color: rgb(249, 170, 93);font-size:60rpx"
      ></uni-list-item>
    </uni-list>
  </view>
</template>

<script>
import uniList from '@/components/uni-list/uni-list.vue';
import uniListItem from '@/components/uni-list-item/uni-list-item.vue';

export default {
  components: {
    uniList,
    uniListItem
  },
  data() {
    return {
      showContent: false,
      fromPage: '',
      salaryList: [],
      showBadge: false
    };
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.getSalaryDataList();
  },
  methods: {
    getSalaryDataList() {
      this.ajax.getSalaryDataList().then(res => {
        if (res.rows && res.rows.length > 0) {
          this.salaryList = res.rows;
          this.showContent = true;
        } else {
          uni.showToast({
            title: '工资模板未设置，请联系管理员',
            icon: 'none'
          });
        }
      });
    },
    goDetailPage(salaryDate, tableName, tableTitle) {
      uni.navigateTo({
        url: `/pages/payslip/salary-details?salaryDate=${salaryDate}&tableName=${tableName}&tableTitle=${tableTitle}&fromPage=${this.fromPage}`
      });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/${this.fromPage}`
      });
    }
  }
};
</script>

<style></style>
