<template>
  <view
    class="ts-content"
    :style="{ backgroundImage: imgSrc }"
    style="background-color: #fff; background-position-x: -60rpx;"
  >
    <page-head title="工资条" @clickLeft="returnBack"></page-head>
    <view class="salary_wrap">
      <view class="content_top_month">
        <view class="top_text" @click="showPicker('date')">
          {{ `${dateStr}年` }}
        </view>
        <uni-icons
          type="arrowdown"
          :size="30"
          class="uni-icon-wrapper"
          color="#333"
          @click="showPicker('date')"
        />
        <text class="info">{{ username }} - {{ empcode }}</text>
      </view>

      <view class="salary_content" v-if="payslipList.length">
        <scroll-view
          class="content-wrap"
          id="content-wrap"
          :scroll-top="scrollTop"
          scroll-y="true"
          :scroll-into-view="scrollViewId"
          :scroll-with-animation="true"
        >
          <view
            class="scroll-view-item salary_list"
            v-for="(item, index) in payslipList"
            :key="index"
            :id="`view_${item.month}`"
          >
            <view
              class="salary-list-item"
              @click="handleShowPayslipDetails(item)"
            >
              <view class="top">
                <text>工资条</text>
                <text>{{ item.monthLabel }}</text>
              </view>

              <view class="value">
                {{ item.salary }}
              </view>

              <view class="label">
                {{ item.itemName }}
              </view>
            </view>
          </view>
        </scroll-view>

        <!-- <scroll-view
          class="swiper_head"
          :scroll-x="true"
          :show-scrollbar="false"
        >
          <view
            v-for="item in payslipList"
            :key="item.month"
            class="uni-tab-item"
            :class="
              currentIndex == item.month ? 'uni-tab-item-title-active' : ''
            "
            :data-view-id="`view_${item.month}`"
            :data-current="item.month"
            @click="ontabtap"
          >
            {{ item.month }}
          </view>
        </scroll-view> -->
      </view>

      <view v-else class="nothing">
        <view class="img_content">
          <image
            class="nothing_img"
            src="@/static/img/nothing.png"
            mode="aspectFit"
          ></image>
        </view>
        <view class="tips_text">
          <text>暂无数据</text>
        </view>
      </view>
    </view>
    <canvas
      v-if="showCanvas"
      style="width:150px;height:100px;z-index: -1;"
      canvas-id="firstCanvas"
      id="firstCanvas"
    ></canvas>

    <date-picker
      startDate="2000"
      mode="date"
      :value="dateStr"
      @confirm="onConfirm"
      ref="date"
      fields="year"
      :disabled-after="true"
    ></date-picker>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import datePicker from '@/components/picker/date-picker.vue';
export default {
  components: {
    datePicker
  },
  data() {
    return {
      currentIndex: '',
      dateStr: '',

      fromPage: '',
      payslipList: [],

      scrollViewId: '',
      scrollStatus: true, //点击状态，是否能点击

      scrollTop: 0,

      month: null,
      year: null,

      imgSrc: '',
      showCanvas: true
    };
  },
  computed: {
    ...mapState(['empcode', 'username', 'globalSetting', 'userInfo'])
  },

  async onLoad(opt) {
    this.fromPage = opt.fromPage;

    let curDate = new Date();
    this.year = this.dateStr = curDate.getFullYear().toString();
    this.month = curDate.getMonth() + 1;

    await this.getPayrollByEmployee();
    // this.$nextTick(() => {
    //   this.currentIndex = this.month;
    //   this.scrollViewId = `view_${this.month}`;
    // });
  },

  mounted() {
    let context = uni.createCanvasContext('firstCanvas', this),
      watermarkTextArr = [];
    if (this.username) {
      watermarkTextArr.push(this.username);
    }
    if (this.empcode) {
      watermarkTextArr.push(this.empcode);
    }
    let watermarkText = watermarkTextArr.join('-');
    context.rotate((330 * Math.PI) / 180);
    context.setFontSize(12);
    context.setFillStyle('rgba(169,169,169,.3)');
    context.fillText(watermarkText, -20, 100);
    setTimeout(() => {
      context.draw();
      uni.canvasToTempFilePath({
        x: 0, // 从canvas的x轴的0点开始选中
        y: 0, // 从canvas的y轴的0点开始选中
        width: 150, // 选中canvas多宽
        height: 100, // 选中canvas多宽
        destWidth: 150, // 生成的图片多宽
        destHeight: 100, // 生成的图片多高
        canvasId: 'firstCanvas', // canvas的id
        success: res => {
          this.showCanvas = false;
          // 在H5平台下，tempFilePath 为 base64
          this.imgSrc = `url(${res.tempFilePath})`;
        }
      });
    }, 500);
  },

  methods: {
    //tab点击事件
    ontabtap(e) {
      let data = e.currentTarget.dataset;
      if (!this.scrollStatus) return;
      // 200毫秒才能执行下次点击
      if (this.scrollStatus) {
        this.scrollStatus = false;
        // 解决clickedNavIndex相同触发更新失败
        if (this.scrollViewId == data.viewId) {
          this.scrollViewId = 'view--1';
        }
        this.$nextTick(() => {
          this.scrollViewId = data.viewId;
          this.currentIndex = data.current;
        });
        setTimeout(() => {
          // 200毫秒才能执行下次点击
          this.scrollStatus = true;
        }, 200);
      }
    },

    showPicker(type) {
      this.$refs[type].show();
    },

    async onConfirm(e) {
      this.dateStr = e.result;
      await this.$nextTick(async () => {
        await this.getPayrollByEmployee();
        // setTimeout(() => {
        //   if (this.year != e.result) {
        //     this.currentIndex = 12;
        //     this.scrollViewId = 'view_12';
        //   } else {
        //     this.currentIndex = this.month;
        //     this.scrollViewId = `view_${this.month}`;
        //   }
        // }, 100);
      });
    },

    async handleShowPayslipDetails(e) {
      let payrollDate = e.payrollDate;
      let optionId = e.optionId;
      uni.navigateTo({
        url: `/pages/payslip/salary-payslip-details?payrollDate=${payrollDate}&optionId=${optionId}&fromPage=salary-payslip-list`
      });
    },

    async getPayrollByEmployee() {
      this.payslipList = [];

      await this.ajax
        .getPayrollByEmployee({
          employeeId: this.userInfo.employeeId,
          payrollDate: this.dateStr
        })
        .then(res => {
          this.payslipList = (res.rows || []).map(m => {
            let year = this.$dayjs(m.payrollDate).format('YYYY');
            let month = new Date(m.payrollDate).getMonth() + 1;

            let monthLabel = `${year}年${month}月`;
            return {
              payrollDate: m.payrollDate,
              year,
              month,
              monthLabel,
              salary: m.salary,
              optionId: m.optionId,
              itemName: m.itemName
            };
          });
        });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/${this.fromPage}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}
.salary_wrap {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.content_top_month {
  width: 100%;
  height: 44px;
  line-height: 44px;
  padding: 0 20rpx;
  background-color: rgba(0, 91, 172, 0.1);
  box-sizing: border-box;
  position: relative;
  .info {
    position: absolute;
    right: 8px;
    top: 0;
  }
}
.top_text {
  display: inline-block;
  margin-right: 10rpx;
  font-size: 32rpx;
  color: #333333;
  box-sizing: border-box;
}
.salary_content {
  flex: 1;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  overflow: hidden;
}
.content-wrap {
  flex: 1;
  overflow: hidden;
}
.salary_list {
  width: 100%;
  padding: 8px;
  .salary-list-item {
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    border: 2px solid #e1e1e1;
    padding: 12px;
    .top {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    .label,
    .value {
      text-align: center;
    }
    .value {
      font-size: 16px;
      font-weight: 800;
    }
  }
}

.nothing {
  position: absolute;
  top: 40%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .img_content {
    width: 300rpx;
    height: 300rpx;
    .nothing_img {
      width: 100%;
      height: 100%;
    }
  }
  .tips_text {
    color: #666666;
  }
}
.payslip-details-container {
  height: 100vh;
  width: 260px;
  max-width: 80vw;
  background-color: #fff;
  padding: 40px 16px;
  display: flex;
  flex-direction: column;
  .filter-content {
    flex: 1;
  }
  .filter-title {
    font-size: 16px;
    color: #333;
    font-weight: normal;
    margin-bottom: 8px;
  }
  .action-content {
    display: flex;
    justify-content: center;
    view + view {
      margin-left: 16px;
    }
    view {
      width: 180px;
      border: 1px solid #eee;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 38px;
      &.primary {
        color: #fff;
        background-color: $theme-color;
        border-color: $theme-color;
      }
    }
  }
}

.field_value {
  text-align: center;
  width: 50%;
  border-right: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  display: table-cell;
  vertical-align: middle;
}
.field_value {
  text-align: center;
  width: 50%;
  border-right: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  display: table-cell;
  vertical-align: middle;
}
.swiper_head {
  width: 80rpx;
  padding: 90rpx 0 0;
  height: 100%;
  flex-direction: row;
  box-sizing: border-box;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
  /* flex-wrap: nowrap; */
  /* border-color: #cccccc;
		border-bottom-style: solid;
		border-bottom-width: 1px; */
  .uni-tab-item {
    color: #999999;
    flex-wrap: nowrap;
    width: 60rpx;
    height: 60rpx;
    line-height: 60rpx;
    box-sizing: border-box;
    text-align: center;
    margin: 5px auto;
    border-radius: 100%;
    &.uni-tab-item-title-active {
      color: $theme-color;
      background-color: rgba(58, 136, 205, 0.2);
    }
  }
}
.empty-wrap {
  width: 100%;
  text-align: center;
  position: relative;
  border-bottom: 0;
  box-sizing: border-box;
  .empty-img {
    width: 400rpx;
  }
  .empty-text {
    color: #999;
    font-size: 28rpx;
    position: absolute;
    bottom: 120rpx;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
