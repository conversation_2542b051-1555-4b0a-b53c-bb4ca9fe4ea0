<template>
  <view class="ts-content">
    <page-head
      :title="$config.ENABLE_SMS_VERICATION ? '短信验证' : '密码验证'"
      @clickLeft="returnBack"
      rightText="提交"
      @clickRight="confirm"
    ></page-head>
    <view class="code_wrap" v-if="$config.ENABLE_SMS_VERICATION">
      <image class="user_img" :src="headimg"></image>
      <text class="user_tel">{{ tel }}</text>
      <view class="code_input_contanier">
        <view class="uni-input-wrapper">
          <input
            class="uni-input"
            :placeholder="placeholder"
            type="number"
            :value="inputVal"
            @input="inputValChange"
          />
          <uni-icons
            v-if="showClearIcon"
            :size="48"
            class="uni-icon-wrapper"
            color="#bbb"
            type="clear"
            @tap="clearIcon"
          />
        </view>
        <button
          class="get_code_btn"
          type="primary"
          size="mini"
          :disabled="disabled"
          @click="getCode"
        >
          {{ codeBtnText }}
        </button>
      </view>
    </view>
    <view class="uni-input-group pwd_wrap" v-else>
      <view class="uni-input-row">
        <view class="uni-label">账号</view>
        <view class="uni-input">{{ usercode }}</view>
      </view>
      <view class="uni-input-row">
        <view class="uni-label">密码</view>
        <view class="uni-input-wrapper">
          <input
            class="uni-input"
            :placeholder="placeholder"
            :value="inputVal"
            @input="inputValChange"
          />
          <uni-icons
            v-if="showClearIcon"
            :size="48"
            class="uni-icon-wrapper"
            color="#bbb"
            type="clear"
            @tap="clearIcon"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState } from 'vuex';

export default {
  data() {
    return {
      showContent: true,
      headimg: '',
      tel: '',
      codeBtnText: '获取验证码',
      inputVal: '',
      showClearIcon: false,
      disabled: false,
      placeholder: this.$config.ENABLE_SMS_VERICATION
        ? '请输入验证码'
        : '请输入密码'
    };
  },
  computed: {
    ...mapState(['usercode', 'empcode'])
  },
  onLoad() {
    if (this.$config.ENABLE_SMS_VERICATION) {
      this.ajax
        .getPersonalInformationSettings({
          userCode: this.empcode
        })
        .then(res => {
          this.showContent = true;
          let data = res.object;
          this.headimg =
            `${this.$config.BASE_HOST}${data.empHeadImg}` ||
            '../../static/img/headImg.png';
          this.tel = this.$common.plusxing(data.empPhone, 3, 4);
        });
    }
  },
  methods: {
    //监听输入框
    inputValChange(event) {
      this.inputVal = event.detail.value;
      if (event.detail.value.length > 0) {
        this.showClearIcon = true;
      } else {
        this.showClearIcon = false;
      }
    },
    //输入框删除按钮点击事件
    clearIcon() {
      this.inputVal = '';
      this.showClearIcon = false;
    },
    //获取验证码
    getCode() {
      let second = 60;
      this.disabled = true;
      this.codeBtnText = `${second}s后重新获取`;
      let interval = setInterval(() => {
        --second;
        this.codeBtnText = `${second}s后重新获取`;
      }, 1000);
      setTimeout(() => {
        clearInterval(interval);
        this.disabled = false;
        this.codeBtnText = '获取验证码';
      }, 60000);
    },
    //提交验证
    confirm() {
      if (!this.inputVal) {
        uni.showToast({
          title: this.placeholder,
          icon: 'none'
        });
        return false;
      }
      uni.redirectTo({
        url: '/pages/personalCenter/settingPersonal/payslip'
      });
    },
    //返回
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/my'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  background-color: #ffffff;
  .code_wrap {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 60rpx;
    .code_input_contanier {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      padding: 0 30rpx;
      box-sizing: border-box;
      position: relative;
      margin-top: 100rpx;
      &::after {
        position: absolute;
        right: 30rpx;
        bottom: 0;
        left: 30rpx;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #c8c7cc;
      }
      .uni-input-wrapper {
        flex: 1;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding-right: 20rpx;
      }
      .get_code_btn {
        font-size: 20rpx;
        padding: 0 20rpx;
      }
      // .disabled_btn{
      // 	background-color: #efefef;

      // }
    }
    .user_img {
      width: 120rpx;
      height: 120rpx;
      border-radius: 100%;
    }
    .user_tel {
      margin-top: 20rpx;
      color: #333333;
    }
  }
  .pwd_wrap {
    .uni-input {
      text-align: right;
    }
  }
}
</style>
