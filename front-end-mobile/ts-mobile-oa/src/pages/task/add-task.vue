<template>
  <view class="ts-content" v-if="showContent">
    <page-head
      title="新增登记"
      rightIcon=""
      rightText=""
      @clickLeft="returnBack"
    ></page-head>
    <base-form
      class="base-form"
      ref="baseForm"
      :form-list="formList"
      :form-data.sync="form"
      :rules="rules"
      :showSubmitButton="false"
    ></base-form>
    <view class="action-content">
      <view class="action-item" @click="submit(0)">存草稿</view>
      <view class="action-item submit-btn" @click="submit(1)">提交</view>
    </view>
  </view>
</template>

<script>
import BaseForm from '@/components/base-form/base-form.vue';
import common from '@/common/js/common.js';
export default {
  components: { BaseForm },
  data() {
    return {
      showContent: false,
      formList: [
        {
          title: '督办事项',
          prop: 'registerMatter',
          type: 'text',
          placeholder: '请填写督办事项',
          maxlength: 70,
          required: true
        },
        {
          title: '类型',
          prop: 'registerTypeName',
          propVal: 'registerType',
          type: 'select',
          mode: 'select',
          optionList: [],
          placeholder: '请选择任务类型',
          required: true
        },
        {
          title: '紧急程度',
          prop: 'registerUrgency',
          type: 'radio',
          radioList: [
            {
              label: '普通',
              value: 1
            },
            {
              label: '紧急',
              value: 2
            },
            {
              label: '非常紧急',
              value: 3
            }
          ],
          required: true
        },
        {
          title: '完成时间',
          prop: 'completeDate',
          type: 'select',
          mode: 'time',
          format: 'YYYY-MM-DD',
          placeholder: '请选择计划完成时间',
          required: true
        },
        {
          title: '预警时间(天)',
          prop: 'overdueDay',
          type: 'number',
          placeholder: '请填写预警时间'
        },
        {
          title: '描述',
          prop: 'registerRemark',
          type: 'textarea',
          placeholder: '请填写描述',
          maxlength: 500,
          required: true
        },
        {
          title: '附件',
          prop: 'registerFiles',
          propVal: 'registerFilesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件',
          actionStatus: '9'
        }
      ],
      form: {
        registerMatter: '',
        registerRemark: '',
        registerType: '',
        registerTypeName: '',
        registerUrgency: 1,
        completeDate: '',
        overdueDay: 1,
        registerFiles: common.guid(),
        registerFilesList: []
      },
      rules: {
        registerMatter: [
          {
            required: true,
            message: '请填写督办事项',
            trigger: ''
          }
        ],
        registerRemark: [
          {
            required: true,
            message: '请填写描述',
            trigger: ''
          }
        ],
        registerType: [
          {
            required: true,
            message: '请选择任务类型',
            trigger: ''
          }
        ],
        completeDate: [
          {
            required: true,
            message: '请选择计划完成时间',
            trigger: ''
          }
        ]
      },
      registerTypeList: [],
      action: '',
      toIndex: -1,
      id: ''
    };
  },
  async onLoad(opt) {
    this.action = 'saveTaskRegistrtion';
    await this.ajax
      .getSuperviseTypeList()
      .then(async res => {
        this.registerTypeList = res.object;
        let list = this.registerTypeList.map(e => {
          return {
            label: e.typeName,
            value: e.id
          };
        });
        this.formList[1].optionList = list;
      })
      .catch(() => {});
    if (opt.id) {
      this.action = 'updateTaskRegistrtion';
      this.id = opt.id;
      await this.ajax
        .getTaskDetail({ id: opt.id })
        .then(async res => {
          let list = [];
          if (res.object.registerFiles == null) {
            res.object.registerFiles = common.guid();
          } else {
            list = await this.getFiles(res.object.registerFiles);
          }
          this.form = Object.assign({}, res.object);
          this.$set(this.form, 'registerFilesList', list);
        })
        .catch(() => {});
    }
    if (opt.index) this.toIndex = opt.index;
    this.showContent = true;
  },
  methods: {
    //获取附件
    async getFiles(fileIds) {
      let list = [],
        _self = this;
      await _self.ajax.getFileAttachmentByBusinessIdOrId(fileIds).then(res => {
        list = res.object;
      });
      return list;
    },
    async getSuperviseTypeList() {
      await this.ajax
        .getSuperviseTypeList()
        .then(async res => {
          this.registerTypeList = res.object;
        })
        .catch(() => {});
    },
    async submit(registerStatus) {
      const result = await this.$refs.baseForm.validate();
      if (!result) return;
      const data = Object.assign({}, this.form);
      if (data.registerType == '') {
        uni.showToast({
          title: '请选择任务类型',
          duration: 2000,
          icon: 'none'
        });
        return;
      }
      let checkType = this.registerTypeList.find(
        e => e.id == data.registerType
      );
      data.approveCode = checkType.approveUserCode;
      data.approveName = checkType.approveUserName;
      data.checkCode = checkType.checkUserCode;
      data.checkName = checkType.checkUserName;
      data.registerUser = checkType.superviseUserCode;
      data.registerName = checkType.superviseUserName;
      data.registerStatus = registerStatus;
      if (
        data.registerFilesList == null ||
        data.registerFilesList.length == 0
      ) {
        data.registerFiles = '';
      }
      delete data.registerFilesList;
      const res = await this.ajax[this.action](data);
      if (!res.success) {
        uni.showToast({
          title: res.message || '操作失败!',
          duration: 2000,
          icon: 'none'
        });
        return;
      }
      uni.showToast({
        title: '操作成功',
        duration: 1000,
        icon: 'none'
      });
      setTimeout(() => {
        this.$set(this, 'form', {});
        this.returnBack();
      }, 500);
    },
    //返回上一层
    returnBack() {
      this.form = this.$options.data().form;
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/ts-mobile-oa/pages/task/task-registration?index=${this.toIndex}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  /deep/ .base-form {
    height: calc(100% - 84px);
    overflow: auto;
    .u-form-item--left__content__label {
      font-size: 28rpx;
    }
    .u-switch {
      &.u-switch--on {
        background: $theme-color !important;
      }
    }
  }
  .action-content {
    position: absolute;
    bottom: 0;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    box-shadow: 0 -1px 6px #ccc;
    width: 100%;
    height: 40px;
    .action-item {
      flex: 1;
      text-align: center;
      color: $uni-text-color-grey;
      position: relative;
      &:not(:last-child)::after {
        content: ' ';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 15px;
        border-right: 1px solid #eee;
      }
      &.submit-btn {
        color: $theme-color;
      }
    }
  }
}
</style>
