<template>
  <view class="list_item" @click="itemClick">
    <view class="item_title" v-if="colmun.length && dataSource.registerMatter">
      <view class="title_left">
        <view class="item_status" :class="getRegisterUrgencyStyle(dataSource)">
          {{ getRegisterUrgencyText(dataSource) }}
        </view>
        <view class="title">{{ dataSource.registerMatter }}</view>
      </view>
      <view
        class="title_right"
        v-if="dataSource.isOverdue == '2' && oudivay > 0"
      >
        超期:{{ oudivay }}天
      </view>
    </view>
    <view class="item_content" v-if="colmun.length">
      <view class="content_item" v-for="(item, index) in colmun" :key="index">
        <view class="item_l" v-if="dataSource[item.key]">
          <text class="label">{{ item.label }}：</text>
          <view v-if="item.label.indexOf('附件') > -1" class="key">
            <view class="fiel_list" v-if="fileList.length > 0">
              <view
                class="file_item"
                v-for="(i, index) in fileList"
                :key="index"
              >
                <text @tap.stop="handleClick(i)">{{ i.originalName }}</text>
              </view>
            </view>
            <view v-else>--</view>
          </view>
          <text
            class="key"
            :class="item.keyClass ? item.keyClass(dataSource) : ''"
            v-else-if="!item.formatter"
          >
            {{ dataSource[item.key] }}
          </text>
          <text
            class="key"
            :class="item.keyClass ? item.keyClass(dataSource) : ''"
            v-else
          >
            {{ item.formatter(dataSource[item.key]) }}
          </text>
        </view>
        <view class="item_r" v-if="item.statusKey">
          <text style="color: #0079fe;">{{
            item.statusFormatter(dataSource[item.statusKey])
          }}</text>
        </view>
      </view>
    </view>
    <view class="item_footer" v-if="footBtnList.length > 0">
      <view
        class="footer_btn"
        :class="item.btnClass || 'defaultBtn'"
        v-for="(item, index) in footBtnList"
        :key="index"
        @click.stop="() => item.event(dataSource)"
      >
        {{ item.btnName }}
      </view>
    </view>
  </view>
</template>

<script>
var dayjs = require('dayjs');
export default {
  props: {
    dataSource: {
      type: Object,
      default: () => {}
    },
    colmun: {
      type: Array,
      default: () => []
    },
    footBtnList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      fileList: [],
      oudivay: 0
    };
  },
  watch: {
    dataSource: {
      handler: function() {
        this.getDays();
        this.init();
      },
      immediate: true
    }
  },
  methods: {
    init() {
      this.colmun.forEach(item => {
        if (item.label.indexOf('附件') > -1) {
          this.getFiles(this.dataSource[item.key]);
        }
      });
    },
    itemClick() {
      this.$emit('itemClick', this.dataSource);
    },
    handleClick(i) {
      this.$emit('handleClick', i);
    },
    getDays() {
      if (['2', '3', '4'].indexOf(this.dataSource.registerStatus) > -1) {
        let days =
          this.dataSource.delayDate == null
            ? this.dataSource.completeDate
            : this.dataSource.delayDate;
        this.oudivay = dayjs().diff(dayjs(days), 'day');
      }
    },
    getRegisterUrgencyStyle(row) {
      return row.registerUrgency == 1
        ? 'green'
        : row.registerUrgency == 2
        ? 'yellow'
        : row.registerUrgency == 3
        ? 'red'
        : '';
    },
    getRegisterUrgencyText(row) {
      return row.registerUrgency == 1
        ? '普通'
        : row.registerUrgency == 2
        ? '紧急'
        : row.registerUrgency == 3
        ? '非常紧急'
        : '';
    },
    //获取附件
    async getFiles(fileIds) {
      if (fileIds == null || fileIds == '') return;
      // 附件列表
      const fileList = await this.ajax.getFileAttachmentByBusinessIdOrId(
        fileIds
      );
      this.fileList = fileList.object || [];
    }
  }
};
</script>
<style lang="scss" scoped>
.list_item {
  padding: 20rpx 0 12rpx 0;
  background: #ffffff;
  margin-top: 20rpx;
  .item_title {
    display: flex;
    justify-content: space-between;
    height: 40rpx;
    line-height: 40rpx;
    padding: 0 30rpx;
    .title_left {
      width: calc(100% - 120rpx);
      display: flex;
      .item_status {
        color: #ffffff;
        border-radius: 20rpx;
        font-size: 24rpx;
        min-width: 120rpx;
        text-align: center;
        line-height: 40rpx;
      }
      .title {
        font-size: 30rpx;
        font-weight: 600;
        line-height: 40rpx;
        margin-left: 10rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .red {
        background: #ff3b30;
      }
      .yellow {
        background: #fe9400;
      }
      .green {
        background: #4bd863;
      }
    }
    .title_right {
      font-size: 24rpx;
      color: #f23232;
    }
  }
  .item_content {
    padding: 0 30rpx 14rpx 30rpx;
    .content_item {
      display: flex;
      justify-content: space-between;
      .item_l {
        display: flex;
        flex-wrap: wrap;
      }
      .label {
        font-size: 28rpx;
        font-weight: 600;
      }
      .key {
        font-size: 28rpx;
        color: #999999;
        .fiel_list {
          .file_item {
            font-size: 28rpx;
            color: #0079fe;
          }
        }
      }
      .orange {
        color: #fe9400;
      }
      .red {
        color: #ff3b30;
      }
      .green {
        color: #4bd863;
      }
      .item_r {
        font-size: 28rpx;
      }
    }
  }
  .item_footer {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row-reverse;
    border-top: 1rpx solid #bbbbbb;
    padding: 12rpx 30rpx 0 30rpx;
    .footer_btn {
      height: 54rpx;
      line-height: 54rpx;
      font-size: 28rpx;
      padding: 0 30rpx;
      border-radius: 27rpx;
      margin-left: 12rpx;
    }
    .defaultBtn {
      color: #0079fe;
      border: 2rpx solid #0079fe;
    }
    .greyBtn {
      color: #999999;
      border: 2rpx solid #999999;
    }
  }
}
</style>
