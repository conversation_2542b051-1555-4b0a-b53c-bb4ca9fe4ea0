import common from '@/common/js/common.js';
export default {
  data() {
    return {
      formList: [
        {
          title: '催办说明',
          prop: 'urgentRemark',
          type: 'textarea',
          placeholder: '请填写催办说明',
          maxlength: 500,
          required: true,
          actionStatus: '1'
        },
        {
          title: '撤销原因',
          prop: 'cancelReamrk',
          type: 'textarea',
          placeholder: '请填写撤销原因',
          maxlength: 500,
          required: true,
          actionStatus: '2'
        },
        {
          title: '附件',
          prop: 'cancelFiles',
          propVal: 'cancelFilesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件',
          actionStatus: '2'
        },
        {
          title: '延期到',
          prop: 'delayDate',
          type: 'select',
          mode: 'time',
          format: 'YYYY-MM-DD',
          placeholder: '请选择延期时间',
          required: true,
          actionStatus: '3'
        },
        {
          title: '延期原因',
          prop: 'delayReamrk',
          type: 'textarea',
          placeholder: '请填写延期原因',
          maxlength: 500,
          required: true,
          actionStatus: '3'
        },
        {
          title: '附件',
          prop: 'delayFiles',
          propVal: 'delayFilesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件',
          actionStatus: '3'
        },
        {
          title: '进展',
          prop: 'feedbackReamrk',
          type: 'textarea',
          placeholder: '请填写进展',
          maxlength: 500,
          required: true,
          actionStatus: '4'
        },
        {
          title: '日期',
          prop: 'feedbackDate',
          type: 'select',
          mode: 'time',
          format: 'YYYY-MM-DD',
          placeholder: '请选择日期',
          required: true,
          actionStatus: '4'
        },
        {
          title: '附件',
          prop: 'feedbackFlies',
          propVal: 'feedbackFliesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件',
          actionStatus: '4'
        },
        {
          title: '终止原因',
          prop: 'closeReamrk',
          type: 'textarea',
          placeholder: '请填写终止原因',
          maxlength: 500,
          required: true,
          actionStatus: '5'
        },
        {
          title: '附件',
          prop: 'closeFlies',
          propVal: 'closeFliesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件',
          actionStatus: '5'
        },
        {
          title: '办理日期',
          prop: 'handleDate',
          type: 'select',
          mode: 'time',
          format: 'YYYY-MM-DD',
          placeholder: '请选择办理日期',
          required: true,
          actionStatus: '6'
        },
        {
          title: '办理说明',
          prop: 'handleReamrk',
          type: 'textarea',
          placeholder: '请填写办理说明',
          maxlength: 500,
          required: true,
          actionStatus: '6'
        },
        {
          title: '附件',
          prop: 'handleFlies',
          propVal: 'handleFliesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件',
          actionStatus: '6'
        },
        {
          title: '转办给',
          prop: 'transferUserName',
          propVal: 'transferUser',
          type: 'select',
          mode: 'callBack',
          callBackName: 'select-person-callback',
          placeholder: '请选择转办人',
          required: true,
          actionStatus: '7'
        },
        {
          title: '转办日期',
          prop: 'transferDate',
          type: 'select',
          mode: 'time',
          format: 'YYYY-MM-DD',
          placeholder: '请选择转办日期',
          required: true,
          actionStatus: '7'
        },
        {
          title: '转办说明',
          prop: 'transferRemark',
          type: 'textarea',
          placeholder: '请填写办理说明',
          maxlength: 500,
          required: true,
          actionStatus: '7'
        },
        {
          title: '附件',
          prop: 'transferFlies',
          propVal: 'transferFliesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件',
          actionStatus: '7'
        },
        {
          title: '验收结论',
          prop: 'operationStatus',
          type: 'radio',
          radioList: [
            {
              label: '通过',
              value: 1
            },
            {
              label: '不通过',
              value: 2
            }
          ],
          callback: this.operationStatusChange,
          required: true,
          actionStatus: '8'
        },
        {
          title: '验收日期',
          prop: 'checkDate',
          type: 'select',
          mode: 'time',
          format: 'YYYY-MM-DD',
          placeholder: '请选择验收日期',
          required: true,
          actionStatus: '8'
        },
        {
          title: '验收意见',
          prop: 'checkRemark',
          type: 'textarea',
          placeholder: '请填写验收意见',
          maxlength: 500,
          required: true,
          actionStatus: '8'
        },
        {
          title: '附件',
          prop: 'checkFlies',
          propVal: 'checkFliesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件',
          actionStatus: '8'
        },
        {
          title: '批示结论',
          prop: 'operationStatus',
          type: 'radio',
          radioList: [
            {
              label: '通过',
              value: 1
            },
            {
              label: '不通过',
              value: 2
            }
          ],
          callback: this.operationStatusChange1,
          required: true,
          actionStatus: '9'
        },
        {
          title: '批示日期',
          prop: 'approveDate',
          type: 'select',
          mode: 'time',
          format: 'YYYY-MM-DD',
          placeholder: '请选择批示日期',
          required: true,
          actionStatus: '9'
        },
        {
          title: '批示内容',
          prop: 'approveRemark',
          type: 'textarea',
          placeholder: '请填写批示内容',
          maxlength: 500,
          required: true,
          actionStatus: '9'
        },
        {
          title: '附件',
          prop: 'approveFlies',
          propVal: 'approveFliesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件',
          actionStatus: '9'
        }
      ],
      form: {
        urgentRemark: '',

        cancelReamrk: '',
        cancelFiles: common.guid(),
        cancelFilesList: [],

        delayDate: '',
        delayReamrk: '',
        delayFiles: common.guid(),
        delayFilesList: [],

        feedbackReamrk: '',
        feedbackDate: '',
        feedbackFlies: common.guid(),
        feedbackFliesList: [],

        closeReamrk: '',
        closeFlies: common.guid(),
        closeFliesList: [],

        handleDate: '',
        handleReamrk: '',
        handleFlies: common.guid(),
        handleFliesList: [],

        transferUserName: '',
        transferUser: '',
        transferDate: '',
        transferRemark: '',
        transferFlies: common.guid(),
        transferFliesList: [],

        operationStatus: '1',
        checkDate: '',
        checkRemark: '',
        checkFlies: common.guid(),
        checkFliesList: [],

        approveDate: '',
        approveRemark: '',
        approveFlies: common.guid(),
        approveFliesList: []
      },
      rules: {
        urgentRemark: [
          {
            required: true,
            message: '请填写催办说明',
            trigger: ''
          }
        ],
        cancelReamrk: [
          {
            required: true,
            message: '请填写撤销原因',
            trigger: ''
          }
        ],
        delayDate: [
          {
            required: true,
            message: '请选择延期时间',
            trigger: ''
          }
        ],
        delayReamrk: [
          {
            required: true,
            message: '请填写延期原因',
            trigger: ''
          }
        ],
        feedbackReamrk: [
          {
            required: true,
            message: '请填写进展',
            trigger: ''
          }
        ],
        feedbackDate: [
          {
            required: true,
            message: '请选择反馈日期',
            trigger: ''
          }
        ],
        closeReamrk: [
          {
            required: true,
            message: '请填写终止原因',
            trigger: ''
          }
        ],
        handleDate: [
          {
            required: true,
            message: '请选择办理日期',
            trigger: ''
          }
        ],
        handleReamrk: [
          {
            required: true,
            message: '请填写办理说明',
            trigger: ''
          }
        ],
        transferUserName: [
          {
            required: true,
            message: '请选择转办人',
            trigger: ''
          }
        ],
        transferDate: [
          {
            required: true,
            message: '请选择转办日期',
            trigger: ''
          }
        ],
        transferRemark: [
          {
            required: true,
            message: '请填写转办说明',
            trigger: ''
          }
        ],
        handleReamrk: [
          {
            required: true,
            message: '请填写办理说明',
            trigger: ''
          }
        ],
        checkDate: [
          {
            required: true,
            message: '请选择验收日期',
            trigger: ''
          }
        ],
        checkRemark: [
          {
            required: true,
            message: '请填写验收意见',
            trigger: ''
          }
        ],
        approveDate: [
          {
            required: true,
            message: '请选择批示日期',
            trigger: ''
          }
        ],
        approveRemark: [
          {
            required: true,
            message: '请填写批示内容',
            trigger: ''
          }
        ]
      },
      actions: [
        'saveUrgentTask',
        'saveCancelTask',
        'saveDelayTask',
        'saveFeedbackTask',
        'saveCloseTask',
        'saveHandleTask',
        'saveTransferTask',
        'saveCheckTask',
        'saveApproveTask'
      ]
    };
  },
  methods: {
    operationStatusChange(e) {
      if (this.form.operationStatus == 2) {
        this.useFormList.splice(1, 1);
      } else {
        this.useFormList.splice(1, 0, this.formList[19]);
      }
    },
    operationStatusChange1(e) {
      if (this.form.operationStatus == 2) {
        this.useFormList.splice(1, 1);
      } else {
        this.useFormList.splice(1, 0, this.formList[23]);
      }
    }
  }
};
