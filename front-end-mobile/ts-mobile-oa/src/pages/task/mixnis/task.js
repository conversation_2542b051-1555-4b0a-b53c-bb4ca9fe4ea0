export default {
  data() {
    return {
      tabBars: [
        {
          name: '全部',
          registerStatus: '', //办理状态
          downOption: false,
          isInit: false,
          total: null,
          list: [],
          viewId: 'all'
        },
        {
          name: '待指派',
          registerStatus: 1,
          downOption: false,
          isInit: false,
          total: null,
          list: [],
          viewId: 'waitDo'
        },
        {
          name: '办理中',
          registerStatus: '2,3,4',
          downOption: false,
          isInit: false,
          total: null,
          list: [],
          viewId: 'toDo'
        },
        {
          name: '已完成',
          registerStatus: '5,6,7',
          downOption: false,
          isInit: false,
          total: null,
          list: [],
          viewId: 'waitCheck'
        }
      ],
      tabBarsHandle: [
        {
          name: '待我处理',
          taskIndex: 1, //办理状态
          downOption: false,
          isInit: false,
          total: null,
          list: []
        },
        {
          name: '我已处理',
          taskIndex: 2,
          downOption: false,
          isInit: false,
          total: null,
          list: []
        }
      ],
      colmun: [
        {
          label: '类型',
          key: 'registerTypeName'
        },
        {
          label: '计划完成时间',
          key: 'completeDate'
        },
        {
          label: '延期时间',
          key: 'delayDate'
        },
        {
          label: '当前进度',
          key: 'registerStatus',
          formatter: this.getProgressText,
          keyClass: this.keyClass
        },
        {
          label: '当前未处理人',
          key: 'currentHandleUser'
        },
        {
          label: '创建时间',
          key: 'createDate'
        }
      ],
      colmun1: [
        {
          label: '类型',
          key: 'registerTypeName'
        },
        {
          label: '计划完成时间',
          key: 'completeDate'
        },
        {
          label: '延期时间',
          key: 'delayDate'
        },
        {
          label: '当前进度',
          key: 'registerStatus',
          formatter: this.getProgressText,
          keyClass: this.keyClass
        },
        {
          label: '累计催办',
          key: 'urgeCount',
          formatter: row => {
            return row + '次';
          }
        },
        {
          label: '创建时间',
          key: 'createDate'
        }
      ],
      progress: {
        0: '待登记',
        1: '待指派',
        2: '办理中',
        3: '待验收',
        4: '待批示',
        5: '已完结',
        6: '已撤销',
        7: '已终止'
      },
      footBtnList: [
        {
          btnName: '催办',
          event: this.handleUrgent,
          menuIndex: '0,1',
          isStatus: '1,2,3,4'
        },
        {
          btnName: '编辑',
          event: this.handleEdit,
          menuIndex: '0,1',
          isStatus: '0'
        },
        {
          btnName: '重新提交',
          event: this.handleEdit,
          menuIndex: '0',
          isStatus: '6'
        },
        {
          btnName: '指派',
          event: this.handleAssign,
          menuIndex: '1',
          isStatus: '1'
        },
        {
          btnName: '延期',
          event: this.handleDelay,
          menuIndex: '1',
          isStatus: '1,2'
        },
        {
          btnName: '反馈',
          event: this.handleFeedBack,
          menuIndex: '2',
          isStatus: '2'
        },
        {
          btnName: '批示',
          event: this.handleApprove,
          menuIndex: '2',
          isStatus: '4'
        },
        {
          btnName: '验收',
          event: this.handleCheck,
          menuIndex: '2',
          isStatus: '3'
        },
        {
          btnName: '转办',
          event: this.handleTransfer,
          menuIndex: '2',
          isStatus: '2'
        },
        {
          btnName: '办理',
          event: this.handleHandle,
          menuIndex: '2',
          isStatus: '2'
        },
        {
          btnName: '撤销',
          btnClass: 'greyBtn',
          event: this.handleCancel,
          menuIndex: '0',
          isStatus: '1'
        },
        {
          btnName: '终止',
          btnClass: 'greyBtn',
          event: this.handleClose,
          menuIndex: '1',
          isStatus: '1,2'
        },
        {
          btnName: '删除',
          btnClass: 'greyBtn',
          event: this.handleDelete,
          menuIndex: '0,1',
          isStatus: '0,6,7'
        }
      ],
      scrollViewId: 'all',
      menuList: ['registration', 'supervision', 'handling', 'copy']
    };
  },
  methods: {
    // 进度翻译
    getProgressText(progress) {
      return this.progress[progress];
    },
    keyClass(row) {
      return row.registerStatus == '1'
        ? 'orange'
        : ['2', '3', '4'].indexOf(row.registerStatus) > -1
        ? 'red'
        : row.registerStatus == '5'
        ? 'green'
        : '';
    },
    // 获取卡片底部按钮组
    getfootBtnList(status) {
      if (this.menuIndex == '2' && this.tabIndex == 1) {
        return [];
      } else {
        let list = this.footBtnList.filter(
          e =>
            e.menuIndex.indexOf(this.menuIndex) > -1 &&
            e.isStatus.indexOf(status) > -1
        );
        if (
          list.length > 0 &&
          this.menuIndex == '1' &&
          status == '1' &&
          list[0].btnName == '催办'
        ) {
          list.splice(0, 1);
        }
        return list;
      }
    },
    // 催办
    handleUrgent(row) {
      this.$refs.actionForm.open(1, row);
    },
    // 撤销
    handleCancel(row) {
      this.$refs.actionForm.open(2, row);
    },
    // 延期
    handleDelay(row) {
      this.$refs.actionForm.open(3, row);
    },
    // 反馈
    handleFeedBack(row) {
      this.$refs.actionForm.open(4, row);
    },
    // 终止
    handleClose(row) {
      this.$refs.actionForm.open(5, row);
    },
    // 办理
    handleHandle(row) {
      this.$refs.actionForm.open(6, row);
    },
    // 转办
    handleTransfer(row) {
      this.$refs.actionForm.open(7, row);
    },
    // 验收
    handleCheck(row) {
      this.$refs.actionForm.open(8, row);
    },
    // 批示
    handleApprove(row) {
      this.$refs.actionForm.open(9, row);
    },
    // 编辑
    handleEdit(row) {
      let to = '';
      if (this.menuIndex == '0') {
        to = 'add-task';
      } else if (this.menuIndex == '1') {
        to = 'add-task-sup';
      }
      uni.navigateTo({
        url: `/pages/task/${to}?id=${row.id}&index=${this.tabIndex}`
      });
    },
    // 指派
    handleAssign(row) {
      uni.navigateTo({
        url: `/pages/task/add-task-sup?id=${row.id}&index=${this.tabIndex}&type=assign`
      });
    },
    // 详情
    toDetail(row) {
      uni.navigateTo({
        url: `/pages/task/task-detail?id=${row.id}&form=${
          this.menuList[this.menuIndex]
        }&index=${this.tabIndex}&menuIndex=${this.menuIndex}&taskId=${
          row.taskId
        }`
      });
    }
  }
};
