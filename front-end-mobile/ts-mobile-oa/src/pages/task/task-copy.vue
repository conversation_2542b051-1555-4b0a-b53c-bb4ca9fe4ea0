<template>
  <view class="ts-content">
    <page-head title="抄送给我的" @clickLeft="returnBack"></page-head>
    <view class="search-box">
      <view class="search-form">
        <uni-search-bar
          radius="100"
          bgColor="#FFFFFF"
          searchBgColor="#eeeeee"
          borderColor="transparent"
          cancelButton="none"
          @confirm="search"
          placeholder="请输入任务名称"
        ></uni-search-bar>
      </view>
      <view class="search-sift" @tap="changeDrawer">
        <text class="search-sift-icon oa-icon oa-icon-shaixuan"></text>
      </view>
    </view>
    <swiper class="swiper-box">
      <swiper-item class="swiper-item">
        <mescroll
          ref="mescroll"
          :down="false"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view class="contact-list">
            <list-item
              v-for="(item, index) in list"
              :key="index"
              :dataSource="item"
              :colmun="colmun1"
              :footBtnList="[]"
              @itemClick="toDetail"
            />
          </view>
        </mescroll>
      </swiper-item>
    </swiper>
    <search-drawer
      ref="searchDrawer"
      :registerTypeList="registerTypeList"
      @search="searchByForm"
      @showPicker="showPicker"
      @dateComfirm="dateComfirm"
    />
    <date-picker
      startDate="2000-01-01"
      :value="agentTimeArr"
      endDate="2100-12-31"
      pickerZindex="999"
      mode="range"
      :current="true"
      @confirm="onConfirm"
      @cancel="onCancel"
      ref="range"
    ></date-picker>
  </view>
</template>

<script>
import Mescroll from '@/components/mescroll-swiper/mescroll.vue';
import taskMixnis from './mixnis/task.js';
import listItem from './components/list-item.vue';
import searchDrawer from './components/search-drawer.vue';
import datePicker from '@/components/picker/date-picker.vue';
export default {
  components: {
    Mescroll,
    listItem,
    searchDrawer,
    datePicker
  },
  mixins: [taskMixnis],
  data() {
    return {
      showContent: false,
      keywords: '',
      tabIndex: -1,
      menuIndex: 3,
      queryMap: {},
      agentTimeArr: [],
      registerTypeList: [],
      list: []
    };
  },
  onLoad(opt) {
    this.showContent = true;
    this.getSuperviseTypeList();
    this.refh();
  },
  methods: {
    //搜索
    search(res) {
      this.keywords = res.value;
      this.$nextTick(() => {
        this.$refs.mescroll.downCallback();
      });
    },
    refh() {
      this.$nextTick(() => {
        this.$refs.mescroll.downCallback();
      });
    },
    //显示时间弹出层
    showPicker() {
      this.$refs.range.show();
    },
    //时间选择确认
    onConfirm(res) {
      this.$refs.searchDrawer.onConfirm(res);
    },
    dateComfirm(timeArr) {
      this.agentTimeArr = timeArr;
    },
    //时间取消
    onCancel() {},
    searchByForm(searchForm) {
      this.queryMap = searchForm;
      this.$nextTick(() => {
        this.$refs.mescroll.downCallback();
      });
    },
    //分类抽屉切换
    changeDrawer() {
      this.$refs.searchDrawer.show();
    },
    async getSuperviseTypeList() {
      await this.ajax
        .getSuperviseTypeList()
        .then(async res => {
          this.registerTypeList = res.object;
        })
        .catch(() => {});
    },
    async getListData(page, successCallback, errorCallback, keywords, index) {
      let query = {
        pageSize: page.size,
        pageNo: page.num,
        registerMatter: this.keywords,
        index: 3,
        sidx: 'create_date',
        sord: 'desc'
      };
      for (let key in this.queryMap) {
        query[key] = this.queryMap[key];
      }
      await this.ajax
        .superviseRegister(query)
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      this.list = this.list.concat(rows);
    },
    datasInit(keywords, index) {
      this.list = [];
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  /deep/ .uni-navbar__header-container {
    padding-right: 88rpx;
  }
  /deep/ .uni-navbar__header-btns-right {
    position: absolute;
    right: 0;
  }
  .search-box {
    display: flex;
    align-items: center;
    background-color: #eeeeee;
    .search-form {
      flex: 1;
    }
    .search-sift {
      font-size: 28rpx;
      color: #666666;
      padding-right: 16rpx;
      .search-sift-icon {
        font-size: 36rpx !important;
        color: #666666;
      }
    }
  }
  .swiper-head {
    position: relative;
    width: 750rpx;
    height: 80rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    white-space: nowrap;
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    &::after {
      bottom: 0;
    }
    .uni-tab-item-title-active {
      color: $theme-color;
      border-bottom: 2px solid $theme-color;
    }
    .uni-tab-item {
      display: inline-block;
      flex-wrap: nowrap;
      width: 20%;
      padding-left: 34rpx;
      padding-right: 34rpx;
      margin: 0 2%;
      box-sizing: border-box;
      text-align: center;
      .uni-tab-item-title,
      .uni-tab-item-num {
        color: #666;
        font-size: 30rpx;
        height: 100%;
        line-height: 2.5;
        flex-wrap: nowrap;
        box-sizing: border-box;
        white-space: nowrap;
      }
      .uni-tab-item-num {
        font-size: 28rpx;
        color: #fff;
        background-color: #f59a23;
        border-radius: 40rpx;
        padding: 0 10rpx;
        margin: 0 10rpx;
      }
    }
  }
  .swiper-box {
    flex: 1;
    .swiper-item {
      flex: 1;
      flex-direction: row;
    }
  }
}
</style>
