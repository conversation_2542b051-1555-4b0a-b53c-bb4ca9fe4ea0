<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="详情"></page-head>
    <view class="time_line">
      <time-line ref="timeLine" />
    </view>
    <wf-tab-bar v-if="showContent" :tabBarList="tabBars" v-model="tabIndex" />
    <view
      class="content_wrap"
      :class="btnList.length > 0 ? 'pd100' : 'pd40'"
      v-if="showContent"
    >
      <scroll-view
        class="content"
        scroll-y="true"
        :scroll-into-view="scrollViewId"
        :scroll-with-animation="true"
        @scroll="scroll"
      >
        <view class="form_wrap scroll-view-item" id="form">
          <view class="form_title">
            <view class="line"></view>
            <view class="title">基本信息</view>
          </view>
          <list-item
            :dataSource="dataSource"
            :colmun="colmunSource"
            @handleClick="handleClick"
          ></list-item>
        </view>
        <view
          class="form_wrap scroll-view-item"
          v-if="dataSource.superviseTaskList.length"
        >
          <view class="form_title">
            <view class="line"></view>
            <view class="title">承办人</view>
          </view>
          <list-item
            v-for="(item, index) in dataSource.superviseTaskList"
            :key="index"
            :dataSource="item"
            :colmun="colmunSupervise"
          ></list-item>
        </view>
        <view class="form_wrap scroll-view-item" id="log">
          <view class="form_title">
            <view class="line"></view>
            <view class="title">操作记录</view>
          </view>
          <log-list :infoList="logList" @handleClick="handleClick" />
        </view>
        <view class="form_wrap scroll-view-item" id="feedback">
          <view class="form_title">
            <view class="line"></view>
            <view class="title">反馈记录</view>
          </view>
          <feed-back :infoList="feedBackList" @handleClick="handleClick" />
        </view>
      </scroll-view>
    </view>
    <view class="footer" v-if="btnList.length">
      <list-item
        :dataSource="dataSource"
        :colmun="[]"
        :footBtnList="btnList"
        class="footer-item"
      ></list-item>
    </view>
    <action-form ref="actionForm" @ok="refh" />
    <bottom-menu
      ref="bottomMenu"
      :actions="bottomMenuActions"
      @preview="handlePreview"
      @download="handleDownload"
      @delete="handleDelete"
    />
  </view>
</template>

<script>
import listItem from './components/list-item.vue';
import WfTabBar from './components/wf-tab-bar.vue';
import logList from './components/log-list.vue';
import feedBack from './components/feedBack.vue';
import timeLine from './components/time-line.vue';
import actionForm from './components/action-form.vue';
import taskMixnis from './mixnis/task.js';
import Base64 from '@/common/js/base64.min.js';
import BottomMenu from '@/components/bottom-menu/bottom-menu.vue';
export default {
  components: {
    listItem,
    WfTabBar,
    logList,
    timeLine,
    actionForm,
    BottomMenu,
    feedBack
  },
  mixins: [taskMixnis],
  data() {
    return {
      showContent: false,
      tabIndex: 0,
      menuIndex: -1,
      tabBars: [
        {
          name: '基本信息',
          viewId: 'form'
        },
        {
          name: '操作记录',
          viewId: 'log'
        },
        {
          name: '反馈记录',
          viewId: 'feedback'
        }
      ],
      progress: {
        0: '待登记',
        1: '待指派',
        2: '办理中',
        3: '待验收',
        4: '待批示',
        5: '已完结',
        6: '已撤销',
        7: '已终止'
      },
      scrollViewId: 'form',
      dataSource: {},
      logList: [],
      id: '',
      bottomMenuActions: [
        { label: '预览', emitName: 'preview' },
        { label: '下载', emitName: 'download' }
      ],
      colmunSource: [
        {
          label: '类型',
          key: 'registerTypeName'
        },
        {
          label: '计划完成时间',
          key: 'completeDate'
        },
        {
          label: '延期时间',
          key: 'delayDate'
        },
        {
          label: '描述',
          key: 'registerRemark'
        },
        {
          label: '附件',
          key: 'registerFiles'
        },
        {
          label: '当前进度',
          key: 'registerStatus',
          formatter: this.getProgressText,
          keyClass: this.keyClass
        },
        {
          label: '当前未处理人',
          key: 'currentHandleUser'
        },
        {
          label: '创建人',
          key: 'createUserName'
        },
        {
          label: '创建时间',
          key: 'createDate'
        }
      ],
      colmunSupervise: [
        {
          label: '承办人',
          key: 'taskName',
          statusKey: 'taskStatus',
          statusFormatter: this.getProgressText
        },
        {
          label: '抄送分管领导',
          key: 'copyLeader',
          formatter: this.getCopyLeader
        },
        {
          label: '承办事项',
          key: 'taskRemark'
        }
      ],
      form: '/ts-mobile-oa/pages/task/task-',
      toIndex: null,
      btnList: [],
      taskId: '',
      feedBackList: []
    };
  },
  watch: {
    tabIndex(newVal) {
      this.scrollViewId = this.tabBars[newVal].viewId;
    }
  },
  async onLoad(opt) {
    this.id = opt.id;
    if (opt.index) this.toIndex = opt.index;
    if (opt.form) this.form = this.form + opt.form;
    if (opt.menuIndex) this.menuIndex = opt.menuIndex;
    if (opt.taskId && opt.taskId != 'null') this.taskId = opt.taskId;
    await this.getDataSource();
    await this.getLogList();
    await this.feedbackDetails();
    this.dataSource.taskId = this.taskId;
    if (this.menuIndex == 2 && this.toIndex == 1) {
      this.btnList = [];
    } else {
      this.btnList = this.getfootBtnList(this.dataSource.registerStatus);
    }
    this.showContent = true;
  },
  methods: {
    async refh() {
      this.showContent = false;
      await this.getDataSource();
      await this.getLogList();
      await this.feedbackDetails();
      if (this.menuIndex == 2 && this.toIndex == 1) {
        this.btnList = [];
      } else {
        this.btnList = this.getfootBtnList(this.dataSource.registerStatus);
      }
      this.showContent = true;
    },
    // 是否抄送分管领导翻译
    getCopyLeader(copyLeader) {
      return copyLeader == '1' ? '是' : '否';
    },
    // 进度翻译
    getProgressText(progress) {
      return this.progress[progress];
    },
    async feedbackDetails() {
      await this.ajax.feedbackDetails({ registerId: this.id }).then(res => {
        res.object.forEach(i => {
          if (i.logFile != null && i.logFile != '') {
            i.fileList = [];
          }
        });
        this.feedBackList = res.object;
      });
    },
    async getDataSource() {
      await this.ajax.getTaskDetail({ id: this.id }).then(res => {
        this.dataSource = res.object;
        this.$refs.timeLine.formatData(res.object);
      });
    },
    handleClick(row) {
      this.$refs.bottomMenu.show(row);
    },
    async getLogList() {
      await this.ajax
        .getSuperviseLogsList({ registerId: this.id })
        .then(res => {
          res.object.forEach(i => {
            if (i.logFile != null && i.logFile != '') {
              i.fileList = [];
            }
          });
          this.logList = res.object || [];
        });
    },
    scroll(e) {},
    // 文件预览
    handlePreview(row) {
      let fileId =
        row.fileId ||
        row.id ||
        row.accessoryUrl.split('/')[row.accessoryUrl.split('/').length - 1];
      let filePath = `${
        this.$config.ENABLE_FILE_PREVIEW
          ? this.$config.DOCUMENT_BASE_HOST
          : this.$config.BASE_HOST
      }/ts-basics-bottom/fileAttachment/downloadFile/${fileId}?fullfilename=${fileId}.${
        row.fileExtension
      }`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      } else {
        this.$downloadFile.downloadFile(filePath);
      }
    },
    // 文件下载
    handleDownload(row) {
      let fileId =
        row.fileId ||
        row.id ||
        row.accessoryUrl.split('/')[row.accessoryUrl.split('/').length - 1];
      let filePath = `${this.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/downloadFile/${fileId}`;
      this.$downloadFile.downloadFile(filePath);
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `${this.form}?index=${this.toIndex}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  /deep/ .wf-tab-bar-item_active {
    color: #0079fe !important;
    border-bottom: 2px solid #0079fe !important;
  }
  .scroll-view-item {
    margin-bottom: 20rpx;
    background-color: #ffffff;
  }
  .form_title {
    background: #fff;
    box-sizing: border-box;
    border-bottom: 1rpx solid #bbbbbb;
    margin-bottom: 20rpx;
    height: 70rpx;
    display: flex;
    .line {
      width: 8rpx;
      border-radius: 4rpx;
      height: 40rpx;
      position: relative;
      top: 15rpx;
      background: #0079fe;
      margin-left: 30rpx;
    }
    .title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
      line-height: 70rpx;
      margin-left: 10rpx;
    }
  }
  .list_item {
    padding: 0;
    margin: 0;
  }
  .bg {
    margin-top: 20rpx;
  }
  .pd100 {
    padding-bottom: 100rpx;
  }
  .pd40 {
    padding-bottom: 40rpx;
  }
  .content_wrap {
    flex: 1;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .content {
      flex: 1;
      overflow: hidden;
      width: inherit;
      background-color: #f7f8f8;
    }
  }
  .footer {
    position: absolute;
    bottom: 0;
    height: 80rpx;
    width: 100%;
    background: #fff;
    /deep/ .footer-item {
      .item_footer {
        border: none;
      }
    }
  }
}
</style>
