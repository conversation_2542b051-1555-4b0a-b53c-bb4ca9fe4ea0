<template>
  <view class="ts-content">
    <page-head title="派车" @clickLeft="returnBack"></page-head>
    <base-form
      class="base-form"
      ref="baseForm"
      :form-list="formList"
      :form-data.sync="form"
      :rules="rules"
      :showSubmitButton="false"
      @select-callback="handleSelectCallback"
      @handld-rang-date="handldRangDate"
    ></base-form>

    <view class="action-content">
      <view class="action-item submit-btn" @click="submit">
        确定
      </view>
      <view class="action-item" @click="close">取消</view>
    </view>

    <search-select ref="SearchSelect" />
  </view>
</template>

<script>
import indexJs from './index';
import BaseForm from '@/components/base-form/base-form.vue';
import SearchSelect from '@/components/search-select/search-select.vue';

export default {
  mixins: [indexJs],
  components: {
    BaseForm,
    SearchSelect
  },
  data() {
    return {
      personlist: []
    };
  },
  methods: {
    returnBack() {
      let pages = getCurrentPages();
      if (pages.length === 1) {
        this.$parentTypeFun
          ? this.$parentTypeFun({
              type: 'jumpPage',
              path: '/workbench'
            })
          : history.back();
      } else {
        uni.navigateBack();
      }
    },
    async handleSelectCallback(item) {
      let optionList = [];
      switch (item.propVal) {
        case 'dispatchVehicleId':
          const res = await this.ajax.getVehicleInfoList({
            vehicleStatus: 1,
            vehicleInfoId: this.detail.vehicleId
          });
          let row = res || [];

          optionList = row.map(item => {
            return {
              name: `${item.vehicleNo}-${item.seats}座`,
              id: item.id
            };
          });
          break;
        case 'dispatchDriverId':
          const deriverRes = await this.ajax.getVehicleDriverList({
            driverStatus: 1
          });
          let deriverResRow = deriverRes || [];

          optionList = deriverResRow.map(item => {
            return {
              name: `${item.driverName}-${item.driverPhone}`,
              id: item.driverCode
            };
          });
          break;
      }

      this.$refs.SearchSelect.open({
        optionList,
        defaultVal: this.form[item.propVal],
        callback: select => {
          this.form[item.prop] = select.name.split('-')[0];
          this.form[item.propVal] = select.id;
          this.$forceUpdate();
        }
      });
    },
    async handldRangDate(e) {
      const [applyStartTime, applyEndTime] = e;
      const start = this.$dayjs(applyStartTime, 'YYYY-MM-DD HH:mm');
      const end = this.$dayjs(applyEndTime, 'YYYY-MM-DD HH:mm');
      if (start.isAfter(end)) {
        uni.showToast({
          title: '开始时间不能大于结束时间!',
          icon: 'none'
        });
        this.$set(this.form, 'applyTime', '');
        this.$set(this.form, 'applyTimeVal', []);
        return false;
      }
    },
    async submit() {
      const result = await this.$refs.baseForm.validate();
      if (!result) {
        return;
      }

      const data = Object.assign({}, this.form);
      if (data.isDispatch == 1) {
        const {
          applyTimeVal: [applyStartTime, applyEndTime]
        } = data;
        data.applyStartTime = applyStartTime;
        data.applyEndTime = applyEndTime;
        delete data.applyTime;
        delete data.applyTimeVal;
      }

      const res = await this.ajax.vehicleApplyDispatch(data);
      if (!res.success) {
        uni.showToast({
          title: res.message || '派车失败!',
          duration: 2000,
          icon: 'none'
        });
        return;
      }
      uni.showToast({
        title: '派车成功',
        duration: 2000,
        icon: 'none'
      });
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/vehicle/my-delivery/index?fromPage=workBench&index=0'
        });
      }, 2000);
    },
    close() {
      let pages = getCurrentPages();
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100vh;
  background: #fff;
  position: relative;
  /deep/ .base-form {
    height: calc(100% - 84px);
    overflow: auto;

    .u-switch {
      &.u-switch--on {
        background: $theme-color !important;
      }
    }
  }
  .action-content {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    box-shadow: 0 -1px 6px #ccc;
    width: 100%;
    height: 40px;
    .action-item {
      flex: 1;
      text-align: center;
      color: $uni-text-color-grey;
      position: relative;
      &:not(:last-child)::after {
        content: ' ';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 15px;
        border-right: 1px solid #eee;
      }
      &.submit-btn {
        color: $theme-color;
      }
    }
  }
}
</style>
