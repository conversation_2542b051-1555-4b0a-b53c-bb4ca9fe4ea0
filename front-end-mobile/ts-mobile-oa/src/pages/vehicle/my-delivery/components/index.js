export default {
  data() {
    return {
      formList: [
        {
          title: '是否派车',
          prop: 'isDispatch',
          type: 'radio',
          radioList: [
            {
              label: '派车',
              value: '1'
            },
            {
              label: '不派车',
              value: '2'
            }
          ],
          placeholder: '请选择',
          required: true
        },
        {
          title: '备注',
          prop: 'applyRemark',
          type: 'textarea',
          placeholder: '请输入',
          maxlength: 500
        }
      ],
      form: {
        isDispatch: '',
        applyRemark: '',
        applyTime: '',
        applyTimeVal: [],

        dispatchVehicleNo: '',
        dispatchVehicleId: '',
        dispatchDriverName: '',
        dispatchDriverId: ''
      },
      rules: {
        isDispatch: [
          {
            required: true,
            message: '请选择是否派车',
            trigger: ''
          }
        ],
        dispatchVehicleNo: [
          {
            required: true,
            message: '请选择指派车辆',
            trigger: ''
          }
        ],
        dispatchDriverName: [
          {
            required: true,
            message: '请选择指派司机',
            trigger: ''
          }
        ],
        applyTime: [
          {
            required: true,
            message: '请选择用车时间',
            trigger: ''
          }
        ],
        applyRemark: [
          {
            required: false,
            message: '请输入备注',
            trigger: ''
          }
        ]
      },
      detail: null
    };
  },
  async onLoad(opt) {
    const { id } = opt;
    const res = await this.ajax.getVehicleAppointmentDetail(id);

    if (!res.success) {
      uni.showToast({
        title: '详情获取失败!',
        icon: 'none'
      });
      return;
    }
    this.detail = res.object;
    this.form.isDispatch = '1';
  },
  watch: {
    'form.isDispatch'(val) {
      let isDispatchFormItem = [
        {
          title: '用车时间',
          prop: 'applyTime',
          propVal: 'applyTimeVal',
          type: 'select',
          mode: 'range-picker',
          format: 'YYYY-MM-DD HH:mm',
          placeholder: '请选择',
          required: true
        },
        {
          title: '指派车辆',
          prop: 'dispatchVehicleNo',
          propVal: 'dispatchVehicleId',
          type: 'select',
          mode: 'callBack',
          callBackName: 'select-callback',
          placeholder: '请选择',
          required: true
        },
        {
          title: '指派司机',
          prop: 'dispatchDriverName',
          propVal: 'dispatchDriverId',
          type: 'select',
          mode: 'callBack',
          callBackName: 'select-callback',
          placeholder: '请选择',
          required: this.detail.applyDriver == 1
        }
      ];

      let ishasItem = this.formList.filter(item =>
        ['dispatchVehicleId', 'dispatchDriverId', 'applyTimeVal'].includes(
          item.propVal
        )
      );

      const {
        applyStartTime,
        applyEndTime,
        id,
        vehicleNo,
        vehicleId
      } = this.detail;

      if (val == 1) {
        if (ishasItem && ishasItem.length === 0) {
          this.formList.splice(1, 0, ...isDispatchFormItem);
        }

        this.$set(this.form, 'applyTimeVal', [applyStartTime, applyEndTime]);
        this.$set(
          this.form,
          'applyTime',
          [applyStartTime, applyEndTime].join('-')
        );
        this.$set(this.form, 'id', id);
        this.$set(this.form, 'dispatchVehicleNo', vehicleNo);
        this.$set(this.form, 'dispatchVehicleId', vehicleId);
        this.$set(this.form, 'dispatchDriverName', '');
        this.$set(this.form, 'dispatchDriverId', '');
      }

      if (val == 2) {
        delete this.form.dispatchVehicleNo;
        delete this.form.dispatchVehicleId;
        delete this.form.dispatchDriverName;
        delete this.form.dispatchDriverId;
        delete this.form.applyTime;
        delete this.form.applyTimeVal;

        if (ishasItem && ishasItem.length) {
          this.formList.splice(1, 3);
        }
      }

      this.formList.find(item => item.prop === 'applyRemark').required =
        val == 2;

      this.rules.applyRemark[0].required = val == 2;
    }
  }
};
