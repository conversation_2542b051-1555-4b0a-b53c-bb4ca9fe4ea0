<template>
  <view class="my-train-container">
    <page-head title="我的派车" @clickLeft="returnBack"></page-head>
    <view class="outside-search-container">
      <uni-search-bar
        radius="100"
        placeholder="用车原因"
        borderColor="transparent"
        bgColor="#F4F4F4"
        cancelButton="none"
        @confirm="handleSearchInput"
      />
      <i @click="handleShowPopup" class="oa-icon oa-icon-shaixuan"></i>
    </view>
    <view ref="tabHeader" class="swiper_head">
      <view
        v-for="(tab, index) in tabBars"
        :key="index"
        class="uni-tab-item"
        :data-current="index"
        @click="ontabtap(index)"
        :class="tabIndex == index ? 'uni-tab-item-title-active' : ''"
      >
        <text class="uni-tab-item-title">{{ tab.name }}</text>
        <text
          class="uni-tab-item-num"
          v-if="tab.total != null && tab.total != 0 && index == 0"
        >
          ({{ tab.total >= 100 ? '99+' : tab.total }})
        </text>
      </view>
      <view ref="activeLine" class="text-line"></view>
    </view>

    <swiper
      :current="tabIndex"
      class="swiper_box"
      :duration="300"
      @change="ontabchange"
    >
      <swiper-item
        class="swiper_item"
        v-for="(tabBarsItem, index) in tabBars"
        :key="index"
      >
        <mescroll
          :ref="'mescroll' + (index + 1)"
          :mescrollIndex="index"
          :down="tabBarsItem.downOption"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view
            v-for="item of tabBarsItem.list"
            :key="item.id"
            class="appointment-item"
            @click="handleShowDetail(item)"
          >
            <view class="appointment-title">
              <view>
                <span class="font-weight">{{ item.vehicleNo }}</span>
              </view>
              <view v-if="item.dispatchChange == 1" class="status-icon">
                改
              </view>
              <view
                v-if="item.applyResult === 6"
                class="status-icon add-record"
              >
                补
              </view>
              <view v-if="item.applyResult === 7" class="status-icon urgent">
                急
              </view>
              <view :style="`color: ${item.applyStatus != 5 ? '#5260FF' : ''}`">
                {{ applyStatusLabel[item.applyStatus] }}
              </view>
            </view>
            <view class="appointment-time">
              用车时间：
              <p>{{ item.applyStartTime }} - {{ item.applyEndTime }}</p>
            </view>
            <view class="appointment-info">
              申请时间：{{ item.createDate }}
            </view>
          </view>
        </mescroll>
      </swiper-item>
    </swiper>

    <uni-popup ref="popup" type="right" zIndex="9999">
      <view class="more-search-container">
        <view class="filter-content">
          <view class="filter-title">用车时间</view>

          <view class="time-picker-content">
            <text class="time-item" @tap="showPicker()">{{
              searchForm.applyStartTimeSeach
            }}</text>
            <text class="divider">-</text>
            <text class="time-item" @tap="showPicker()">{{
              searchForm.applyEndTimeSeach
            }}</text>
          </view>
        </view>
        <view class="action-content">
          <view @click="handleResetSearch">重置</view>
          <view class="primary" @click="handleConfirmSearch">确定</view>
        </view>
      </view>
    </uni-popup>

    <date-picker
      ref="datePicker"
      :value="agentTimeArr"
      :current="true"
      mode="range"
      pickerZindex="99999"
      @confirm="onConfirm"
    ></date-picker>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import datePicker from '@/components/picker/date-picker.vue';

import { applyStatus as applyStatusLabel } from '@/pages/vehicle/config.js';
export default {
  components: {
    mescroll,
    datePicker
  },
  data() {
    return {
      applyStatusLabel,
      tabIndex: null, //当前选中的tab索引值，从0计数
      tabBars: [
        {
          name: '待我派车',
          downOption: false, //初始化时是否下拉加载
          isInit: false, //是否已初始化
          total: null,
          list: []
        },
        {
          name: '我已派车',
          downOption: false,
          isInit: false,
          total: null,
          list: []
        }
      ],

      searchForm: {}
    };
  },
  onLoad() {
    this.$nextTick(() => {
      this.ontabtap(0);
    });
  },
  methods: {
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        this.$parentTypeFun({
          type: 'jumpPage',
          path: '/workbench'
        });
      } else {
        uni.navigateBack();
      }
    },
    handleShowDetail(item) {
      let title = this.tabIndex === 0 ? '待我派车' : '我已派车';

      uni.navigateTo({
        url: `/pages/vehicle/vehicle-detail/vehicle-detail?id=${item.id}&type=${
          this.tabIndex === 0 ? '2' : false
        }&title=${title}`
      });
    },
    handleShowPopup() {
      this.$refs.popup.open();
    },
    showPicker() {
      this.$refs.datePicker.show();
    },
    //时间选择确认
    onConfirm(res) {
      this.$set(
        this.searchForm,
        'applyStartTimeSeach',
        `${res.obj.fyear}-${res.obj.fmonth}-${res.obj.fday}`
      );
      this.$set(
        this.searchForm,
        'applyEndTimeSeach',
        `${res.obj.tyear}-${res.obj.tmonth}-${res.obj.tday}`
      );
    },
    //tab点解切换
    async ontabtap(index) {
      this.searchForm = {};
      let header = this.$refs.tabHeader.$el,
        activeNode = header.querySelector(
          `.uni-tab-item:nth-child(${index + 1})`
        ),
        offsetLeft = activeNode.offsetLeft,
        line = this.$refs.activeLine.$el,
        left = activeNode.offsetWidth / 2 + offsetLeft - line.offsetWidth / 2;
      line.style.transform = `translate(${left}px)`;
      await this.switchTab(Number(index));
    },
    async switchTab(index) {
      if (this.tabIndex === index) {
        return;
      } else if (!this.tabBars[index]['isInit']) {
        this.tabBars[index]['isInit'] = true;
        await this.$refs[`mescroll${index + 1}`][0].downCallback();
      }
      this.tabIndex = index;
    },
    handleSearchInput({ value }) {
      this.searchForm.applyReason = value;
      this.$refs[`mescroll${this.tabIndex + 1}`][0].downCallback();
    },
    ontabchange() {
      this.$nextTick(() => {
        this.$refs[`mescroll${this.tabIndex + 1}`][0].downCallback();
      });
    },
    handleResetSearch() {
      this.searchForm = {};
      this.handleConfirmSearch();
    },
    handleConfirmSearch() {
      this.$refs[`mescroll${this.tabIndex + 1}`][0].downCallback();
      this.$refs.popup.close();
    },
    async getListData(page, successCallback, errorCallback, keywords, index) {
      let data = {
        pageSize: page.size,
        pageNo: page.num,
        applyStatus: this.tabIndex ? 3 : 2,
        sidx: 'create_date',
        sord: 'desc',
        ...this.searchForm
      };

      if (data.applyStatus == 3) {
        delete data.applyStatus;
        data.myDispatch = 'Y';
      }

      if (data.applyStartTimeSeach) {
        data.applyStartTimeSeach = data.applyStartTimeSeach + ' 00:00';
      }

      if (data.applyEndTimeSeach) {
        data.applyEndTimeSeach = data.applyEndTimeSeach + ' 23:59';
      }
      await this.ajax
        .getVehicleAppointmentTableData(data)
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      this.tabBars[index]['total'] = totalCount;
      this.tabBars[index]['list'] = this.tabBars[index]['list'].concat(rows);
    },
    datasInit(keywords, index) {
      this.tabBars[index]['list'] = [];
    },
    showTrainDetails(row) {
      // uni.navigateTo({
      //   url: `/pages/trainManagement/train-detail?id=` + row.trainPlanId
      // });
    }
  },
  computed: {
    agentTimeArr: function() {
      return [
        this.searchForm.applyStartTimeSeach ||
          this.$dayjs().format('YYYY-MM-DD'),
        this.searchForm.applyEndTimeSeach || this.$dayjs().format('YYYY-MM-DD')
      ];
    }
  }
};
</script>

<style lang="scss" scoped>
.my-train-container {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.outside-search-container {
  padding-left: 8px;
  overflow: hidden;
  margin: 8px 0;
  background-color: #fff;
  display: flex;
  align-items: center;
  .uni-searchbar {
    flex: 1;
  }
  .oa-icon-shaixuan {
    padding-left: 8px;
    padding-right: 16px;
  }
}
.more-search-container {
  height: 100vh;
  width: 260px;
  max-width: 80vw;
  background-color: #fff;
  padding: 40px 16px;
  display: flex;
  flex-direction: column;
  .filter-content {
    flex: 1;
  }
  .search-title {
    font-size: 16px;
    color: #333;
    font-weight: normal;
    margin-bottom: 8px;
  }
  .filter-title {
    font-size: 16px;
    color: #333;
    font-weight: normal;
    margin-bottom: 8px;
  }
  .action-content {
    display: flex;
    justify-content: center;
    view + view {
      margin-left: 16px;
    }
    view {
      width: 180px;
      border: 1px solid #eee;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 38px;
      &.primary {
        color: #fff;
        background-color: $theme-color;
        border-color: $theme-color;
      }
    }
  }
  .time-picker-content {
    display: flex;
    align-items: center;
    .divider {
      flex: 1;
      text-align: center;
    }
  }
  .time-item {
    width: 40%;
    height: 35px;
    font-size: 14px;
    display: inline-flex;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    color: #333333;
    padding: 0 8px;
    border: 0;
    border-radius: 35px;
    background-color: #f2f2f2;
  }
}
.swiper_head {
  display: flex;
  background-color: #ffffff;
  overflow: hidden;
  line-height: 0;
  position: relative;
  .uni-tab-item {
    display: inline-block;
    flex-wrap: nowrap;
    flex: 1;
    height: 42px;
    line-height: 42px;
    font-size: 14px;
    box-sizing: border-box;
    text-align: center;
    .uni-tab-item-title,
    .uni-tab-item-num {
      color: #666;
      height: 100%;
      font-size: 14px;
      flex-wrap: nowrap;
      box-sizing: border-box;
      /* #ifndef APP-PLUS */
      white-space: nowrap;
      /* #endif */
    }
    &.uni-tab-item-title-active .uni-tab-item-title {
      color: $theme-color;
    }
  }
  .text-line {
    position: absolute;
    bottom: 0;
    border-radius: 100px;
    width: 30px;
    transition-duration: 300ms;
    height: 2px;
    background-color: $theme-color;
  }
}

.swiper_box {
  flex: 1;
  .swiper_item {
    flex: 1;
    flex-direction: row;
  }
}
.appointment-item {
  background-color: #fff;
  padding: 8px;
  margin: 8px;
  border-radius: 4px;
  .appointment-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    view:first-child {
      display: flex;
      align-items: center;
    }
    .font-weight {
      color: #333;
      font-weight: 700;
    }

    .status-icon {
      height: 20px;
      width: 20px;
      line-height: 0;
      border-radius: 50%;
      background-color: $theme-web-color;
      color: #fff;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 8px;
      &.add-record {
        background-color: #62b1c9;
      }
      &.urgent {
        background-color: #dd669f;
      }
    }
  }
  .appointment-time {
    font-size: 14px;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16px;
  }
  .appointment-info {
    font-size: 14px;
  }
}
</style>
