<template>
  <view class="ts-content">
    <page-head title="预约详情" @clickLeft="returnBack"></page-head>
    <view class="search-page-content">
      <u-dropdown>
        <u-dropdown-item
          v-model="searchForm.applyStatus"
          :title="searchForm.applyStatusName"
          :options="options.applyStatus"
          height="300px"
          @change="handleDropdownChange($event, 'applyStatus', '状态')"
        ></u-dropdown-item>
        <u-dropdown-item
          v-model="searchForm.applyResult"
          :title="searchForm.applyResultName"
          :options="options.applyResult"
          height="300px"
          @change="handleDropdownChange($event, 'applyResult', '预约结果')"
        ></u-dropdown-item>
      </u-dropdown>
      <view
        class="oa-icon oa-icon-shaixuan"
        @click.stop="popupVisible = true"
      ></view>
    </view>
    <u-popup v-model="popupVisible" mode="right">
      <view class="search-popup-content">
        <view class="search-content">
          <view class="search-title">状态</view>
          <view class="search-options">
            <view
              v-for="item of options.applyStatus"
              :key="item.value"
              class="option-item"
              :class="{
                active: searchForm.applyStatus == item.value
              }"
              @click="handleDropdownChange(item.value, 'applyStatus', '状态')"
            >
              {{ item.label }}
            </view>
          </view>
          <view class="search-title">预约结果</view>
          <view class="search-options">
            <view
              v-for="item of options.applyResult"
              :key="item.value"
              class="option-item"
              :class="{
                active: searchForm.applyResult == item.value
              }"
              @click="
                handleDropdownChange(item.value, 'applyResult', '预约结果')
              "
            >
              {{ item.label }}
            </view>
          </view>
          <view class="search-title">用车时间</view>
          <view class="time-picker-content">
            <text class="time-item" @tap="showPicker()">{{
              searchForm.applyStartTimeSeach
            }}</text>
            <text class="divider">-</text>
            <text class="time-item" @tap="showPicker()">{{
              searchForm.applyEndTimeSeach
            }}</text>
          </view>
        </view>
        <view class="search-action">
          <view class="search-btn" @tap="handleResetSearch">重置</view>
          <view class="search-btn primary-btn" @click="handleSearch">确定</view>
        </view>
      </view>
    </u-popup>
    <view class="scroll-content">
      <mescroll
        ref="mescroll"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view
          v-for="item of dataList"
          :key="item.id"
          class="appointment-item"
          @click="handleShowDetail(item)"
        >
          <view class="appointment-title">
            <view>
              <span class="font-weight"
                >{{ item.vehicleNo }}·{{ item.vehicleColor }}</span
              >
              <view v-if="item.dispatchChange == 1" class="status-icon">
                改
              </view>
              <view
                v-if="item.applyResult === 6"
                class="status-icon add-record"
              >
                补
              </view>
              <view v-if="item.applyResult === 7" class="status-icon urgent">
                急
              </view>
            </view>
            <view :style="`color: ${item.applyStatusColor}`">
              · {{ item.renderStatus && item.renderStatus.label }}
            </view>
          </view>
          <view class="appointment-time">
            用车时间：
            <p>{{ item.applyStartTime }} - {{ item.applyEndTime }}</p>
          </view>
          <view class="appointment-info">
            {{ item.useUserNum }}人 ·
            <text :style="`color: ${item.applyResultColor}`">
              {{ item.renderResult && item.renderResult.label }}
            </text>
            ·
            {{ item.createDate }}
          </view>
        </view>
      </mescroll>
    </view>

    <date-picker
      ref="datePicker"
      :value="agentTimeArr"
      :current="true"
      mode="range"
      pickerZindex="99999"
      @confirm="onConfirm"
    ></date-picker>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import datePicker from '@/components/picker/date-picker.vue';

import {
  vehicleApprovalStatus,
  vehicleApprovalResultStatus,
  applyResultColor
} from '@/pages/vehicle/config.js';

export default {
  components: {
    mescroll,
    datePicker
  },
  data() {
    return {
      popupVisible: false,
      searchForm: {
        applyStatusName: '状态',
        applyResultName: '预约结果'
      },
      options: {
        applyStatus: [
          {
            label: '请选择',
            value: -1
          }
        ].concat(vehicleApprovalStatus),
        applyResult: [
          {
            label: '请选择',
            value: '-1'
          }
        ].concat(vehicleApprovalResultStatus)
      },

      dataList: []
    };
  },
  methods: {
    returnBack() {
      let pages = getCurrentPages();
      if (pages.length === 1) {
        this.$parentTypeFun
          ? this.$parentTypeFun({
              type: 'jumpPage',
              path: '/workbench'
            })
          : history.back();
      } else {
        uni.navigateBack();
      }
    },
    handleDropdownChange(value, name, emptyName) {
      let options = this.options[name],
        option = options.find(item => item.value == value);

      if (value == -1) {
        this.$set(this.searchForm, name, '');
        this.$set(this.searchForm, name + 'Name', emptyName);
      } else {
        this.$set(this.searchForm, name, value);
        this.$set(this.searchForm, name + 'Name', option.label);
      }
      this.handleSearch();
    },
    showPicker() {
      this.$refs.datePicker.show();
    },
    //时间选择确认
    onConfirm(res) {
      this.$set(
        this.searchForm,
        'applyStartTimeSeach',
        `${res.obj.fyear}-${res.obj.fmonth}-${res.obj.fday}`
      );
      this.$set(
        this.searchForm,
        'applyEndTimeSeach',
        `${res.obj.tyear}-${res.obj.tmonth}-${res.obj.tday}`
      );
      this.$refs.mescroll.downCallback();
    },
    handleResetSearch() {
      this.searchForm = {
        applyStatusName: '状态',
        applyResultName: '预约结果'
      };
      this.$refs.mescroll.downCallback();
    },
    handleSearch() {
      this.$refs.mescroll.downCallback();
      this.popupVisible = false;
    },
    async getListData(page, successCallback, errorCallback, keywords) {
      let params = Object.assign(
        {},
        {
          pageSize: page.size,
          pageNo: page.num,
          sidx: 'create_date',
          sord: 'desc',
          ...this.searchForm
        }
      );
      if (params.applyStartTimeSeach) {
        params.applyStartTimeSeach = params.applyStartTimeSeach + ' 00:00';
      }

      if (params.applyEndTimeSeach) {
        params.applyEndTimeSeach = params.applyEndTimeSeach + ' 23:59';
      }

      if (params.applyStatusName === '状态') delete params.applyStatusName;
      if (params.applyResultName === '预约结果') delete params.applyResultName;
      await this.ajax
        .getVehicleAppointmentTableData(params)
        .then(res => {
          let rows = res.rows.map(row => {
            let renderStatus =
                vehicleApprovalStatus.find(
                  item => item.value == row.applyStatus
                ) || {},
              renderResult =
                vehicleApprovalResultStatus.find(
                  item => item.value == row.applyResult
                ) || {},
              useUserNum = row.applyUser.split(',').filter(item => item).length;
            return {
              ...row,
              renderStatus,
              renderResult,
              applyStatusColor: row.applyStatus != 5 ? '#5260FF' : '',
              applyResultColor: applyResultColor[row.applyResult],
              useUserNum
            };
          });
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(row) {
      this.dataList = this.dataList.concat(row);
    },
    datasInit() {
      this.dataList = [];
    },
    handleShowDetail(item) {
      const { id } = item;
      uni.navigateTo({
        url: `/pages/vehicle/vehicle-detail/vehicle-detail?id=${id}`
      });
    }
  },
  computed: {
    agentTimeArr: function() {
      return [
        this.searchForm.applyStartTimeSeach ||
          this.$dayjs().format('YYYY-MM-DD'),
        this.searchForm.applyEndTimeSeach || this.$dayjs().format('YYYY-MM-DD')
      ];
    }
  }
};
</script>
<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
}
.search-page-content {
  display: flex;
  background-color: #fff;
  align-items: center;
  position: relative;
  /deep/ .u-dropdown__menu {
    margin-right: 50px;
  }
  .oa-icon-shaixuan {
    position: absolute;
    right: 0;
    padding: 0 16px;
    font-size: 16px;
    height: 100%;
    display: flex;
    align-items: center;
  }
}
.search-popup-content {
  width: 280px;
  max-width: 80vw;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 40px 16px;
  .search-content {
    flex: 1;
    .search-title {
      font-size: 16px;
      color: #333;
      font-weight: normal;
      margin-bottom: 8px;
    }
    .option-item {
      min-width: 52px;
      font-size: 14px;
      display: inline-block;
      border: 1px solid $u-form-item-border-color;
      border-radius: 2px;
      padding: 2px 4px;
      text-align: center;
      margin: 0 8px 8px 0;
      &.active {
        color: $u-type-primary;
        border-color: $u-type-primary;
      }
      &:first-child {
        display: none;
      }
    }
    .time-picker-content {
      display: flex;
      align-items: center;
      .divider {
        flex: 1;
        text-align: center;
      }
    }
    .time-item {
      width: 40%;
      height: 35px;
      font-size: 14px;
      display: inline-flex;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      color: #333333;
      padding: 0 8px;
      border: 0;
      border-radius: 35px;
      background-color: #f2f2f2;
    }
  }
  .search-action {
    display: flex;
    align-items: center;
    justify-content: center;
    .search-btn {
      height: 40px;
      width: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid $u-form-item-border-color;
      border-radius: 2px;
      &:not(:first-child) {
        margin-left: 8px;
      }
      &.primary-btn {
        color: #fff;
        background-color: $u-type-primary;
        border-color: $u-type-primary;
      }
    }
  }
}
.scroll-content {
  flex: 1;
  position: relative;
}
.appointment-item {
  background-color: #fff;
  padding: 8px;
  margin: 8px;
  border-radius: 4px;
  .appointment-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    view:first-child {
      display: flex;
      align-items: center;
    }
    .font-weight {
      color: #333;
      font-weight: 700;
    }
    .status-icon {
      height: 20px;
      width: 20px;
      line-height: 0;
      border-radius: 50%;
      background-color: $theme-web-color;
      color: #fff;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 8px;
      &.add-record {
        background-color: #62b1c9;
      }
      &.urgent {
        background-color: #dd669f;
      }
    }
  }
  .appointment-time {
    font-size: 14px;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16px;
  }
  .appointment-info {
    font-size: 14px;
  }
}
</style>
