<template>
  <view>
    <form-preview :columns="columns" :data="data" />
  </view>
</template>

<script>
import formPreview from './form-preview.vue';
export default {
  components: {
    formPreview
  },
  props: {
    data: Object
  },
  data() {
    return {
      columns: [
        {
          label: '出车人',
          prop: 'outUserName'
        },
        {
          label: '出车时间',
          prop: 'outDate'
        },
        {
          label: '是否出车',
          prop: 'outType',
          formatter: (row, prop, cell) => {
            return cell == 1 ? '出车' : '不出车';
          }
        },
        {
          label: '备注',
          prop: 'outRemark'
        }
      ]
    };
  }
};
</script>

<style lang="scss" scoped>
.section-title {
  font-weight: bold;
  font-size: 16px;
}
</style>
