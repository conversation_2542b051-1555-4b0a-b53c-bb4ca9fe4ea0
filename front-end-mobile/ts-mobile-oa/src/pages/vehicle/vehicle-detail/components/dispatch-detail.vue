<template>
  <view>
    <form-preview :columns="columns" :data="data" />
  </view>
</template>

<script>
import formPreview from './form-preview.vue';
export default {
  components: {
    formPreview
  },
  props: {
    data: Object
  },
  data() {
    return {
      columns: [
        {
          label: '指派人',
          prop: 'dispatchUserName'
        },
        {
          label: '指派时间',
          prop: 'dispatchDate'
        },
        {
          label: '是否指派',
          prop: 'isDispatch',
          formatter: (row, prop, cell) => {
            return cell == 1 ? '派车' : '不派车';
          }
        },
        {
          label: '指派车辆',
          prop: 'dispatchVehicleNo'
        },
        {
          label: '指派司机',
          prop: 'dispatchDriverName'
        },
        {
          label: '用车时段',
          prop: 'applyStartTime',
          formatter: row => {
            let { applyStartTime, applyEndTime } = row;
            return [applyStartTime, applyEndTime].join('-');
          }
        },
        {
          label: '备注',
          prop: 'dispatchRemark',
          span: 2
        }
      ]
    };
  }
};
</script>

<style lang="scss" scoped>
.section-title {
  font-weight: bold;
  font-size: 16px;
}
</style>
