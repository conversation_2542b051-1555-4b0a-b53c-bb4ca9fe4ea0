<template>
  <view class="image-box">
    <view class="picture-item" v-for="file in fileList" :key="file.id">
      <text class="file-name">{{ file.fileName }}</text>
      <text class="preview" @tap="onPreview(file)">预览</text>
    </view>
  </view>
</template>

<script>
import Base64 from '@/common/js/base64.min.js';

export default {
  props: {
    businessId: {
      type: String
    }
  },
  data() {
    return {
      fileList: []
    };
  },
  created() {
    this.getFileList();
  },
  methods: {
    /**@desc 获取文件列表 */
    getFileList() {
      this.fileList = [];
      this.ajax
        .getFileAttachmentByBusinessId({ businessId: this.businessId })
        .then(res => {
          this.fileList = res.object.map(item => ({
            ...item,
            uid: item.id,
            url: item.realPath,
            name: item.fileName
          }));
        });
    },

    /**@desc 图片点击预览 */
    onPreview(file) {
      const { id, fileName } = file;
      let _self = this,
        filePath = `${_self.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (_self.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            _self.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.image-box {
  display: flex;
  flex-direction: column;
  .picture-item {
    font-size: 24rpx;
    .file-name {
      display: inline-block;
      margin-right: 8rpx;
    }
    .preview {
      color: $theme-color;
    }
  }
}
</style>
