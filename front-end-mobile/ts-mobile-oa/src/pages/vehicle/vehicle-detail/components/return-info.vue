<template>
  <view class="return-info-box">
    <form-preview :columns="columns" :data="data" />

    <view v-if="data.vehicleApplyRefuel">
      <view class="item-title">加油记录</view>
      <form-preview
        :columns="refuelingRecord"
        :data="data.vehicleApplyRefuel"
      />
    </view>

    <view v-if="data.vehicleApplyMaintenance">
      <view class="item-title">维修记录</view>
      <form-preview
        :columns="maintenanceRecords"
        :data="data.vehicleApplyMaintenance"
      />
    </view>
  </view>
</template>

<script>
import formPreview from './form-preview.vue';
export default {
  components: {
    formPreview
  },
  props: {
    data: Object,
    payTypeOptions: Array
  },
  data() {
    return {
      columns: [
        {
          label: '还车人',
          prop: 'returnUserName'
        },
        {
          label: '还车时间',
          prop: 'returnDate'
        },
        {
          label: '还车时里程数(KM)',
          prop: 'distance'
        },
        {
          label: '本次行驶里程(KM)',
          prop: 'currentDistance'
        },
        {
          label: '高速费用(元)',
          prop: 'highSpeedPrice'
        },
        {
          label: '停车费用(元)',
          prop: 'stopPrice'
        },
        {
          label: '过桥费用(元)',
          prop: 'bridgePrice'
        },
        {
          label: '其他费用(元)',
          prop: 'otherPrice'
        },
        {
          label: '总费用(元)',
          prop: 'totalPrice'
        },
        {
          label: '备注',
          prop: 'returnRemark'
        },
        {
          label: '附件',
          prop: 'image',
          key: 'returnFiles'
        }
      ],
      refuelingRecord: [
        {
          label: '加油日期',
          prop: 'refuelDate'
        },
        {
          label: '加油升数(升)',
          prop: 'refuelLitre'
        },
        {
          label: '支付方式',
          prop: 'refuelPay',
          formatter: (row, prop, cell) => {
            return (
              this.payTypeOptions.find(item => item.itemNameValue == cell) || {}
            ).itemName;
          }
        },
        {
          label: '加油类型',
          prop: 'refuelType'
        },
        {
          label: '油卡卡号',
          prop: 'refuelNo'
        },
        {
          label: '油卡费用(元)',
          prop: 'refuelPrice'
        },
        {
          label: '费用(元)',
          prop: 'cashPrice'
        },
        {
          label: '附件',
          prop: 'image',
          key: 'refuelFiles'
        }
      ],
      maintenanceRecords: [
        {
          label: '维修日期',
          prop: 'maintenanceDate'
        },
        {
          label: '费用(元)',
          prop: 'maintenancePrice'
        },
        {
          label: '维修情况',
          prop: 'maintenanceRemark'
        },
        {
          label: '附件',
          prop: 'image',
          key: 'maintenanceFiles'
        }
      ]
    };
  }
};
</script>

<style lang="scss" scoped>
.return-info-box {
  .item-title {
    font-size: 12px;
    color: #333;
    font-weight: 600;
    margin-bottom: 4px;
    padding-left: 8px;
    &::before {
      content: '1';
      color: $theme-web-color;
      width: 2px;
      height: 10px;
      background: $theme-web-color;
      margin-right: 4px;
      overflow: hidden;
      box-sizing: border-box;
    }
  }
}
</style>
