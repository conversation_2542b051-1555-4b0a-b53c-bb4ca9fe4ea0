export default [
  {
    path: '/material/inventory-management/material-store-manage',
    component: resolve =>
      require([
        `@/views/moduleMaterial/inventoryManagement/materialStoreManage/index.vue`
      ], resolve),
    name: '入库管理'
  },
  {
    path: '/material/inventory-management/material-outbound-manage',
    component: resolve =>
      require([
        `@/views/moduleMaterial/inventoryManagement/materialOutboundManage/index.vue`
      ], resolve),
    name: '出库管理'
  },
  {
    path: '/material/inventory-management/material-return-goods-manage',
    component: resolve =>
      require([
        `@/views/moduleMaterial/inventoryManagement/materialReturnGoodsManage/index.vue`
      ], resolve),
    name: '退货管理'
  },
  {
    path: '/material/inventory-management/material-return-store-manage',
    component: resolve =>
      require([
        `@/views/moduleMaterial/inventoryManagement/materialReturnStoreManage/index.vue`
      ], resolve),
    name: '退库管理'
  }
  // {
  //   path: '/material/inventory-management/material-dept-application',
  //   component: resolve =>
  //     require([
  //       `@/views/moduleMaterial/inventoryManagement/materialDeptApplication/index.vue`
  //     ], resolve),
  //   name: '科室申领'
  // }
];
