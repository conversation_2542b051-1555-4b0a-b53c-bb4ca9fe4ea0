//深拷贝
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone');
  }
  const targetObj = source.constructor === Array ? [] : {};
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys]);
    } else {
      targetObj[keys] = source[keys];
    }
  });
  return targetObj;
}

/**@desc 校验输入两位小数 */
export function inputTowDecimalPlaces(value) {
  let matchList = value.match(/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2}|\.{1})?/);
  if (!matchList) {
    return null;
  }
  return matchList[0];
}

/**@desc 输入整数 */
export function inputNumbers(value) {
  let matchList = value.match(/\d+/);
  if (!matchList) {
    return null;
  }
  return matchList[0];
}

/**@desc 数字转千分位2位小数 */
export function numToLocaleStrFixed2(value) {
  const num = Number(value);
  if (!isFinite(num)) return '0.00';

  const fixed = num.toFixed(2); // 转成 2 位小数字符串
  const parts = fixed.split('.');
  // 添加千分位
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return parts.join('.');
}
