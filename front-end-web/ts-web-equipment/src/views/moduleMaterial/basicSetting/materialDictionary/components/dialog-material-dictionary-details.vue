<template>
  <vxe-modal
    className="dialog-material-dictionary-details"
    title="物资字典详情"
    v-model="visible"
    width="1080"
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <div class="content">
        <ts-form ref="form" :model="form">
          <div class="form-group-tips">
            <span>基本信息</span>
          </div>

          <div class="basic-info">
            <div class="item" v-for="item in basicInfoColumns" :key="item.prop">
              <span class="label">{{ item.label }}：</span>
              <span class="value" v-if="item.prop === 'price'">
                {{ numToLocaleStrFixed2(form[item.prop]) }}元
              </span>
              <span class="value" v-else>
                {{ form[item.prop] }}
              </span>
            </div>
          </div>

          <div class="form-group-tips">
            <span>注册证</span>
          </div>
          <base-upload v-model="form.regFile" onlyRead />
        </ts-form>
      </div>
    </template>

    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button class="shallowButton" @click="close">
          关 闭
        </ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import cloneDeep from 'lodash-es/cloneDeep';
import { numToLocaleStrFixed2 } from '@/unit/commonHandle';

export default {
  name: 'DialogMaterialDictionaryDetails',
  data() {
    return {
      visible: false,
      basicInfoColumns: [
        { label: '物资分类', prop: 'categoryName' },
        { label: '物资编码', prop: 'flowNo' },
        { label: '物资名称', prop: 'name' },
        { label: '首拼码', prop: 'sp' },
        { label: '全拼码', prop: 'qp' },
        { label: '规格型号', prop: 'model' },
        { label: '单位', prop: 'unitShow' },
        { label: '最小单位', prop: 'minUnitShow' },
        { label: '单位系数', prop: 'unitCoefficient' },
        { label: '参考单价', prop: 'price' },
        { label: '供应商', prop: 'supplyName' },
        { label: '生产厂家', prop: 'manufacturerName' },
        { label: '品牌', prop: 'brand' },
        { label: '级别', prop: 'levelShow' },
        { label: '会计科目', prop: 'accountSubjectShow' },
        { label: '集采类型', prop: 'jclxTypeShow' },
        { label: '是否高值', prop: 'isHighValueShow' },
        { label: '高值分类', prop: 'highValueCateShow' },
        { label: '是否收费', prop: 'isChargeShow' },
        { label: '医保类别', prop: 'miTypeShow' },
        { label: '医保编码', prop: 'miNo' },
        { label: 'HIS收费代码', prop: 'hisChargeNo' },
        { label: 'HIS收费名称', prop: 'hisChargeName' },
        { label: '来源', prop: 'originShow' },
        { label: '使用方式', prop: 'useTypeShow' },
        { label: '采购方式', prop: 'purchaseTypeShow' },
        { label: '物资库房', prop: 'warehouseName' },
        { label: '货位编码', prop: 'slNo' },
        { label: '保质期', prop: 'qgp' },
        { label: '注册证编号', prop: 'regNo' }
      ],
      form: {}
    };
  },

  methods: {
    numToLocaleStrFixed2,

    async open({ data = {} }) {
      this.form = cloneDeep(data);
      this.visible = true;
    },

    close() {
      this.form = {};
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-material-dictionary-details {
  ::v-deep {
    > .vxe-modal--box {
      > .vxe-modal--body {
        > .vxe-modal--content {
          > .content {
            padding: 0px;

            .ts-form {
              height: 100%;
              .form-group-tips {
                width: 100%;
                color: #333;
                font-weight: 800;
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;
                > span {
                  font-weight: 800;
                  &::before {
                    content: '';
                    display: inline-block;
                    width: 4px;
                    height: 16px;
                    background-color: $primary-blue;
                    margin-right: 8px;
                    border-radius: 4px;
                    transform: translateY(2px);
                  }
                }
              }
              .basic-info {
                display: flex;
                flex-wrap: wrap;
                padding-top: 12px;
                background: #fff;
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.14);
                border: 1px solid #e5e5e5;
                border-bottom: none;
                margin-bottom: 8px;

                .item {
                  width: 33%;
                  display: flex;
                  align-items: flex-start;
                  margin-bottom: 12px;

                  .label {
                    width: 130px;
                    text-align: right;
                    color: #606266;
                    font-size: 14px;
                  }

                  .value {
                    flex: 1;
                    color: #333;
                    font-size: 14px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
