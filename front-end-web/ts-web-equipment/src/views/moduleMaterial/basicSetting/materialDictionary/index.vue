<template>
  <div class="common-material-dictionary-container">
    <div class="content">
      <div class="left">
        <new-base-search-tree
          class="node-tree"
          ref="searchTree"
          :title="renderSetting.title"
          :apiFunction="apiFunction"
          :activeId="this.treeNode ? this.treeNode.id : ''"
          placeholder="请输入分类名称进行搜索"
          @beforeClick="clickItemTree"
        />
      </div>
      <div class="right">
        <ts-search-bar-new
          v-model="searchForm"
          :formList="searchList"
          @search="search"
          @reset="reset"
        >
          <template slot="status">
            <ts-radio-group v-model="searchForm.status" @change="search">
              <ts-radio label="">全部</ts-radio>
              <ts-radio label="1">启用</ts-radio>
              <ts-radio label="2">禁用</ts-radio>
            </ts-radio-group>
          </template>

          <template slot="right">
            <ts-button type="primary" @click="handleAddMaterialDictionary">
              新增
            </ts-button>
            <ts-button
              @click="handleCopyAddMaterialDictionary"
              class="shallowButton"
            >
              复制新增
            </ts-button>
            <ts-button
              @click="handleEditMaterialDictionaryRecord"
              class="shallowButton"
              type="primary"
            >
              修改记录
            </ts-button>
            <!-- <ts-button
              @click="handleImportMaterialDictionary"
              class="shallowButton"
              type="primary"
              style="margin-right: 8px"
            >
              导入
            </ts-button> -->

            <el-popover
              placement="bottom"
              width="65"
              trigger="hover"
              popper-class="material-dictionary-btn-popover"
              style="margin-left: 8px"
            >
              <ts-button class="shallowButton" type="primary" slot="reference">
                导出
                <span class="vxe-icon-arrow-down"></span>
              </ts-button>
              <p @click="handleExportMaterialDictionary('all')">导出所有数据</p>
              <p @click="handleExportMaterialDictionary('category')">
                导出选中分类数据
              </p>
              <p @click="handleExportMaterialDictionary('row')">
                导出选中行数据
              </p>
            </el-popover>
          </template>
        </ts-search-bar-new>

        <ts-vxe-base-table
          id="table_material_dictionary"
          ref="table"
          minHeight="100%"
          :columns="columns"
          @refresh="handleRefreshTable"
          @selection-change="handleSelectionChange"
        />
      </div>
    </div>

    <dialog-add-material-dictionary
      ref="dialogAddMaterialDictionary"
      @refresh="handleRefreshTable"
    />

    <dialog-material-dictionary-details ref="dialogMaterialDictionaryDetails" />

    <dialog-edit-record ref="dialogEditRecord" />
  </div>
</template>

<script>
import { primaryBlue } from '@/styles/variables.scss';
import cloneDeep from 'lodash-es/cloneDeep';
import dialogAddMaterialDictionary from './components/dialog-add-material-dictionary.vue';
import dialogMaterialDictionaryDetails from './components/dialog-material-dictionary-details.vue';
import dialogEditRecord from './components/dialog-edit-record.vue';
import { numToLocaleStrFixed2 } from '@/unit/commonHandle';

export default {
  name: 'MaterialDictionary',
  components: {
    dialogAddMaterialDictionary,
    dialogMaterialDictionaryDetails,
    dialogEditRecord
  },
  data() {
    return {
      allId: '99999',
      searchForm: {
        status: '',
        name: ''
      },
      searchList: [
        {
          label: '物资名称',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入物资名称'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '状态',
          value: 'status'
        }
      ],

      columns: [
        { type: 'checkbox', width: 50, align: 'center' },
        { label: '序号', prop: 'pageIndex', width: 50, align: 'center' },
        {
          label: '物资分类',
          align: 'center',
          prop: 'categoryName',
          width: 100
        },
        {
          label: '物资编码',
          align: 'center',
          prop: 'flowNo',
          width: 160,
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'action-cell title',
                on: {
                  click: () => {
                    this.handleDetailsMaterialDictionary(row);
                  }
                }
              },
              row.name
            );
          }
        },
        { label: '物资名称', align: 'center', prop: 'name', minWidth: 150 },
        {
          label: '规格型号',
          align: 'center',
          prop: 'model',
          minWidth: 120
        },
        { label: '供应商', align: 'center', prop: 'supplyName', minWidth: 140 },
        {
          label: '生产厂家',
          align: 'center',
          prop: 'manufacturerName',
          width: 140
        },
        { label: '单位', align: 'center', prop: 'unitShow', width: 70 },
        { label: '最小单位', align: 'center', prop: 'minUnitShow', width: 70 },
        {
          label: '单位系数',
          align: 'center',
          prop: 'unitCoefficient',
          width: 80
        },
        {
          label: '参考单价',
          align: 'right',
          prop: 'price',
          width: 100,
          render: (h, { row }) => {
            return h('span', null, numToLocaleStrFixed2(row.price));
          }
        },
        {
          label: '会计科目',
          align: 'center',
          prop: 'accountSubjectShow',
          width: 100
        },
        {
          label: '注册证编号',
          align: 'center',
          prop: 'regNo',
          width: 100
        },
        {
          label: '状态',
          align: 'center',
          prop: 'status',
          width: 60,
          fixed: 'right',
          render: (h, { row }) => {
            return h('ts-switch', {
              attrs: {
                value: row['status'],
                activeValue: '1',
                inactiveValue: '0'
              },
              on: {
                change: () => {
                  this.handleChangeMaterialDictionaryStatus(row, 'status');
                }
              }
            });
          }
        },
        {
          label: '操作',
          align: 'center',
          prop: 'operation',
          width: 80,
          fixed: 'right',
          render: (h, { row }) => {
            const actions = [
              {
                label: '编辑',
                event: this.handleEditMaterialDictionaryRow
              },
              {
                label: '删除',
                className: 'actionDel',
                event: this.handleDeleteMaterialDictionary
              }
            ];

            return h(
              'BaseActionCell',
              {
                on: {
                  'action-select': e => e(row)
                },
                attrs: {
                  actions
                }
              },
              this.$slots.default
            );
          }
        }
      ],

      apiFunction: null,
      treeNode: null,
      selection: []
    };
  },
  computed: {
    renderSetting() {
      return {
        title: '物资分类',
        treeApi: this.ajax.getMaterialClassifyTree,
        saveApi: this.ajax.materialClassifyTreeSave,
        updateApi: this.ajax.materialClassifyTreeUpdate,
        deleteApi: this.ajax.materialClassifyTreeDelete,
        detailsApi: this.ajax.materialClassifyTreeDetails,
        listApi: this.ajax.materialCategoryList
      };
    }
  },
  created() {
    this.apiFunction = this.handleGetClassificationTree;
  },
  methods: {
    // 优化后的：提供给树组件的接口
    async handleGetClassificationTree() {
      try {
        const res = await this.renderSetting.treeApi();
        if (!res.success) {
          this.$newMessage('error', res.message || '获取分类列表失败');
          return {
            success: false,
            object: [],
            statusCode: 500
          };
        }

        const treeData = Array.isArray(res.object) ? res.object : [];
        const allNode = {
          id: this.allId,
          name: '全部',
          open: true,
          children: treeData.map(item => ({
            ...item,
            pid: this.allId
          }))
        };

        // 第一层节点展开
        allNode.children.forEach(item => {
          item.open = true;
        });
        return {
          object: treeData.length ? [allNode] : [],
          success: true,
          statusCode: 200
        };
      } catch (error) {
        this.$newMessage('error', '获取分类列表异常');
        return {
          success: false,
          object: [],
          statusCode: 500
        };
      }
    },

    async refresh() {
      await this.$refs.searchTree.getTreeData();
      this.search();

      this.$nextTick(() => {
        // 初始化 如果没有选中Tree节点 则选中全部节点
        if (!this.treeNode) this.handleSetTreeFirstNode();
      });
    },

    // 选中全部节点
    handleSetTreeFirstNode() {
      let tree = this.$refs.searchTree?.treeClass;
      if (tree) {
        let node = tree?.getNodeByParam('id', this.allId);
        tree?.selectNode(node);
        this.treeNode = node;
      }
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    reset() {
      this.searchForm = {
        status: '',
        name: ''
      };
      this.handleSetTreeFirstNode();
    },

    // 树 item点击
    clickItemTree(select) {
      this.treeNode = select;
      this.search();
    },

    handleCopyAddMaterialDictionary() {
      let data = this.$refs.table.tsVxeTableRef().getCurrentRecord();
      if (!data) {
        this.$newMessage('warning', '请选择需要【复制】的记录');
        return;
      }

      //复制新增时 删除id和regFile 这两个字段
      delete data.id;
      delete data.regFile;
      this.$refs.dialogAddMaterialDictionary.open({
        type: 'add',
        data,
        treeData: this.$refs.searchTree?.treeData || []
      });
    },

    handleEditMaterialDictionaryRecord() {
      let data = this.$refs.table.tsVxeTableRef().getCurrentRecord();
      this.$refs.dialogEditRecord.open(data?.flowNo || '');
    },

    handleImportMaterialDictionary() {
      console.log('导入');
    },

    handleExportMaterialDictionary(type) {
      let searchForm = cloneDeep(this.searchForm);
      if (type == 'all') {
      } else if (type == 'category') {
        searchForm.categoryId = this.treeNode?.id || '';
        if (this.treeNode?.id === this.allId) {
          delete searchForm.categoryId;
        }
      } else if (type == 'row') {
        if (!this.selection.length) {
          this.$newMessage('warning', '请选择需要导出的分类');
          return;
        }
        searchForm.idList = this.selection.map(item => item.id);
      }

      let API = `/ts-ams/api/material/sku/export`;
      let xhr = new XMLHttpRequest();
      xhr.open('post', API, true);
      xhr.responseType = 'blob';
      xhr.setRequestHeader('Content-Type', 'application/json');
      xhr.send(JSON.stringify(searchForm));
      xhr.onload = function() {
        if (this.status === 200) {
          let url = window.URL.createObjectURL(new Blob([this.response]));
          let link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', `物资字典导出数据.xlsx`);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link); //下载完成移除元素
          window.URL.revokeObjectURL(url); //释放掉blob对象
        }
      };
    },

    // 新增分类
    handleAddMaterialDictionary() {
      let data = {};
      if (this.treeNode && this.treeNode.id !== this.allId) {
        data.categoryId = this.treeNode.id;
        data.categoryName = this.treeNode.name;
      }
      this.$refs.dialogAddMaterialDictionary.open({
        type: 'add',
        data,
        treeData: this.$refs.searchTree?.treeData || []
      });
    },

    // 修改分类
    async handleEditMaterialDictionaryRow(data) {
      this.$refs.dialogAddMaterialDictionary.open({
        data,
        type: 'edit',
        treeData: this.$refs.searchTree?.treeData || []
      });
    },

    async handleChangeMaterialDictionaryStatus(data) {
      try {
        const status = data['status'] === '1';
        const actionText = status ? '禁用' : '启用';
        const color = status ? 'red' : primaryBlue;
        const confirmMsg = `确定<span style="color: ${color}">【${actionText}】</span>当前选中的记录吗?`;

        await this.$newConfirm(confirmMsg);

        const payload = { ...data, status: status ? '0' : '1' };
        const res = await this.ajax.updateMaterialSku(payload);

        if (!res.success) {
          this.$newMessage('error', res.message || `【${actionText}】失败!`);
          return;
        }
        this.$newMessage('success', `【${actionText}】成功!`);
        this.handleRefreshTable();
      } catch (e) {
        console.error(e);
      }
    },

    // 删除分类
    async handleDeleteMaterialDictionary(data) {
      try {
        await this.$newConfirm(
          `确认<span style="color: red">【删除】</span>当前选中的记录吗？`
        );
        const res = await this.ajax.deleteMaterialSku(data.id);
        if (!res.success) {
          this.$newMessage('error', res.message || '【删除】失败!');
          return;
        }
        this.$newMessage('success', '【删除】成功!');
        this.handleRefreshTable();
      } catch (e) {
        console.error(e);
      }
    },

    handleRefreshTable(page = {}) {
      let { pageNo = 1, pageSize = 100 } = page,
        data = {
          ...this.searchForm,
          pageNo,
          pageSize,
          categoryId: this.treeNode?.id || ''
        };
      Object.keys(data).map(key => {
        if (data[key] === null || data[key] === undefined) {
          delete data[key];
        }
      });

      if (data.categoryId === this.allId) delete data.categoryId;

      this.ajax.getMaterialSkuList(data).then(res => {
        if (res.success == false) {
          this.$newMessage('error', res.message || '表格数据获取失败!');
          return;
        }
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    },

    handleDetailsMaterialDictionary(data) {
      this.$refs.dialogMaterialDictionaryDetails.open({
        data
      });
    },

    handleSelectionChange(selection) {
      this.selection = selection;
    }
  }
};
</script>

<style lang="scss">
.material-dictionary-btn-popover {
  min-width: 130px;
  max-width: 130px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0;
  background-color: #fff;
  border-radius: 4px;
  p {
    margin: 0;
    padding-left: 8px;
    height: 26px;
    line-height: 26px;
    font-size: 14px;
    color: #333;
    text-align: left;
    cursor: pointer;
    &:hover {
      background-color: #d2dcfc;
      color: #295cf9;
    }
  }
}
</style>

<style lang="scss" scoped>
.common-material-dictionary-container {
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  ::v-deep {
    .content {
      display: flex;
      height: 100%;
      padding: 8px;

      .left {
        width: 216px;
        height: 100%;
        margin-right: 8px;
        background: #fff;
        .classAction {
          margin: 8px 0 0 8px;
          display: flex;
          .share {
            margin-left: 0;
            margin-top: 4px;
          }
          .refresh {
            margin-top: 4px;
            margin-left: 8px;
          }
        }
      }
      .right {
        padding: 12px 8px;
        border-radius: 3px;
        border: 1px solid#295cf9;
        flex: 1;
        height: 100%;
        background: #fff;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        position: relative;
      }
    }
  }
}
</style>
