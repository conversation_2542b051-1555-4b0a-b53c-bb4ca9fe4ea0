<!-- 操作记录 -->
<template>
  <vxe-modal
    className="dialog-add-material-warehouse"
    v-model="visible"
    width="400"
    :title="title"
    showFooter
  >
    <template #default>
      <ts-form ref="form" :model="form" labelWidth="110px">
        <ts-form-item prop="level" label="库房级别" :rules="rules.required">
          <ts-select
            v-model="form.level"
            placeholder="请选择库房级别"
            style="width: 100%;"
          >
            <ts-option
              v-for="item in levelList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ts-select>
        </ts-form-item>

        <ts-form-item prop="name" label="库房名称" :rules="rules.required">
          <ts-input v-model="form.name" maxlength="50" />
        </ts-form-item>

        <ts-form-item label="库房代码">
          <ts-input
            v-model="form.whCode"
            placeholder="系统自动生成"
            maxlength="10"
            disabled
          />
        </ts-form-item>

        <ts-form-item label="拼音码">
          <ts-input
            v-model="form.sp"
            maxlength="50"
            placeholder="系统自动生成"
            disabled
          />
        </ts-form-item>

        <ts-form-item label="全拼码">
          <ts-input
            v-model="form.qp"
            maxlength="50"
            placeholder="系统自动生成"
            disabled
          />
        </ts-form-item>

        <!-- <ts-form-item label="五笔码" v-show="isEdit">
          <ts-input v-model="form.spellCode" maxlength="50" disabled />
        </ts-form-item> -->

        <ts-form-item label="货位管理">
          <ts-switch v-model="form.isLoc" active-value="1" inactive-value="0" />
        </ts-form-item>

        <ts-form-item label="出库方式">
          <ts-radio-group v-model="form.stockOutType">
            <ts-radio label="1">按批次</ts-radio>
            <ts-radio label="2">按库存</ts-radio>
          </ts-radio-group>
        </ts-form-item>

        <ts-form-item label="盘点方式">
          <ts-radio-group v-model="form.stockCheckType">
            <ts-radio label="1">按批次</ts-radio>
            <ts-radio label="2">按库存</ts-radio>
          </ts-radio-group>
        </ts-form-item>
      </ts-form>
    </template>

    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" @click="submit">确 定</ts-button>
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      visible: false,
      type: '',

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },

      levelList: []
    };
  },
  computed: {
    isEdit() {
      return this.type === 'edit';
    },
    title() {
      return this.isEdit ? '编辑' : '新增';
    }
  },
  methods: {
    async open({ data, type }) {
      this.handleGetLevelList();
      this.type = type;
      this.$set(this, 'form', deepClone(data));

      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
    },

    async handleGetLevelList() {
      let res = await this.ajax.getDataByDataLibrary('AMS_WAREHOUSE_LEVEL');
      if (!res.success) {
        this.$newMessage('error', res.message || '字典数据获取失败');
        this.ymdList = [];
        return;
      }

      this.levelList = (res.object || []).map(item => ({
        label: item.itemName,
        value: item.itemNameValue,
        itemCode: item.itemCode,
        element: 'ts-option'
      }));
    },

    async submit() {
      try {
        await this.$refs.form.validate();
        let formData = deepClone(this.form);
        this.submitLoading = true;
        let API = this.ajax.saveMaterialWarehouse;
        if (this.isEdit) {
          API = this.ajax.updateMaterialWarehouse;
        } else {
          formData.status = '0';
        }
        const res = await API(formData);
        if (!res.success) {
          this.submitLoading = false;
          this.$newMessage('error', res.message || `【${this.title}】失败!`);
          return;
        }

        this.submitLoading = false;
        this.$newMessage('success', `【${this.title}】成功`);
        this.$emit('refresh');
        this.close();
      } catch (error) {
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },

    close() {
      this.visible = false;
    }
  }
};
</script>
