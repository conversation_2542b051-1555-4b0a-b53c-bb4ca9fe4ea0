<template>
  <div class="materialWarehouse">
    <div class="content">
      <div class="left">
        <new-base-search-tree
          class="node-tree"
          ref="searchTree"
          title="库房级别"
          :apiFunction="apiFunction"
          :activeId="this.treeNode ? this.treeNode.id : ''"
          placeholder="请输入名称进行搜索"
          @beforeClick="clickItemTree"
        />
      </div>
      <div class="right">
        <ts-search-bar-new
          v-model="searchForm"
          :formList="searchList"
          @search="search"
          @reset="reset"
        >
          <template slot="status">
            <ts-radio-group v-model="searchForm.status" @change="search">
              <ts-radio label="">全部</ts-radio>
              <ts-radio label="1">启用</ts-radio>
              <ts-radio label="0">禁用</ts-radio>
            </ts-radio-group>
          </template>
          <template slot="right">
            <ts-button
              @click="handleAddMaterialWarehouse"
              class="shallowButton"
              type="primary"
            >
              新增
            </ts-button>
          </template>
        </ts-search-bar-new>

        <ts-vxe-base-table
          id="table_material_warehouse"
          ref="table"
          minHeight="100%"
          :columns="columns"
          @refresh="handleRefreshTable"
        />
      </div>
    </div>

    <dialog-add-material-warehouse
      ref="dialogAddMaterialWarehouse"
      @refresh="refresh"
    />
  </div>
</template>

<script>
import { primaryBlue } from '@/styles/variables.scss';
import DialogAddMaterialWarehouse from './components/dialog-add-material-warehouse.vue';
export default {
  components: {
    DialogAddMaterialWarehouse
  },
  data() {
    return {
      columns: [
        { label: '序号', prop: 'index', width: 50, align: 'center' },
        { label: '库房代码', align: 'center', prop: 'whCode', width: 165 },
        { label: '库房名称', align: 'center', prop: 'name' },
        {
          label: '库房级别',
          align: 'center',
          prop: 'levelShow',
          width: 120
        },
        { label: '首拼码', align: 'center', prop: 'sp', width: 140 },
        { label: '全拼码', align: 'center', prop: 'qp', width: 165 },
        {
          label: '货位管理',
          align: 'center',
          prop: 'isLoc',
          width: 80,
          render: (h, { row }) => {
            return h('ts-switch', {
              attrs: {
                value: row['isLoc'],
                activeValue: '1',
                inactiveValue: '0'
              },
              on: {
                change: () => {
                  this.handleEditField(row, 'isLoc');
                }
              }
            });
          }
        },
        {
          label: '状态',
          align: 'center',
          prop: 'status',
          width: 60,
          render: (h, { row }) => {
            return h('ts-switch', {
              attrs: {
                value: row['status'],
                activeValue: '1',
                inactiveValue: '0'
              },
              on: {
                change: () => {
                  this.handleEditField(row, 'status');
                }
              }
            });
          }
        },
        {
          label: '操作',
          align: 'center',
          prop: 'operation',
          width: 80,
          render: (h, { row }) => {
            const actions = [
              {
                label: '编辑',
                event: this.handleEditMaterialWarehouse
              },
              {
                label: '删除',
                className: 'actionDel',
                event: this.handleDeleteMaterialWarehouse
              }
            ];

            return h(
              'BaseActionCell',
              {
                on: {
                  'action-select': e => e(row)
                },
                attrs: {
                  actions
                }
              },
              this.$slots.default
            );
          }
        }
      ],

      apiFunction: null,
      treeNode: null,
      allId: '99999',

      searchForm: {
        status: '',
        name: ''
      },
      searchList: [
        {
          label: '库房名称',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入库房名称'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '状态',
          value: 'status'
        }
      ]
    };
  },
  created() {
    this.apiFunction = this.handleGetMaterialTree;
  },
  methods: {
    // 提供给树组件的接口
    async handleGetMaterialTree() {
      let res = await this.ajax.getDataByDataLibrary('AMS_WAREHOUSE_LEVEL');
      if (!res.success) {
        this.$newMessage('error', res.message || '获取库房列表失败');
        return {
          success: false,
          object: [],
          statusCode: 500
        };
      }
      return {
        object: [
          {
            id: this.allId,
            name: '全部',
            open: true,
            children: (res.object || []).map(item => ({
              name: item.itemName,
              id: item.itemNameValue,
              open: true,
              pid: this.allId
            }))
          }
        ],
        success: true,
        statusCode: 200
      };
    },

    // 选中全部节点
    handleSetTreeFirstNode() {
      let tree = this.$refs.searchTree?.treeClass;
      let node = tree?.getNodeByParam('id', this.allId);
      tree?.selectNode(node);
      this.treeNode = node;
    },

    async refresh() {
      await this.$refs.searchTree.getTreeData();
      this.search();

      this.$nextTick(() => {
        // 初始化 如果没有选中Tree节点 则选中全部节点
        if (!this.treeNode) this.handleSetTreeFirstNode();
      });
    },

    // 搜索重置
    reset() {
      this.$set(this, 'searchForm', {
        status: '',
        name: ''
      });
      this.handleSetTreeFirstNode();
    },

    // 列表查询
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    // 树 item点击
    clickItemTree(select) {
      this.treeNode = select;
      this.search();
    },

    // 新增库房
    handleAddMaterialWarehouse() {
      let data = {
        isLoc: '0',
        stockOutType: '1',
        stockCheckType: '1'
      };
      // 如果树节点存在，则设置库房级别 且不是全部
      if (this.treeNode && this.treeNode.id !== this.allId) {
        data.level = this.treeNode.id;
      }

      this.$refs.dialogAddMaterialWarehouse.open({
        data,
        type: 'add'
      });
    },

    // 修改库房
    async handleEditMaterialWarehouse(data) {
      this.$refs.dialogAddMaterialWarehouse.open({
        data,
        type: 'edit'
      });
    },

    async handleEditField(data, field) {
      try {
        const value = data[field];
        const isStatusField = field === 'status';
        const isEnable = value === '1';
        const actionText = isEnable ? '禁用' : '启用';
        const color = isEnable ? 'red' : primaryBlue;

        const statusIcon = `<span style="color: ${color}">【${actionText}】</span>`;
        const confirmMsg = `确定${statusIcon}${
          isStatusField ? '当前选中的记录吗?' : '货位管理？'
        }`;

        await this.$newConfirm(confirmMsg);

        const payload = { ...data, [field]: isEnable ? '0' : '1' };
        const res = await this.ajax.updateMaterialWarehouse(payload);

        if (!res.success) {
          this.$newMessage('error', res.message || `【${actionText}】失败!`);
          return;
        }
        this.$newMessage('success', `【${actionText}】成功!`);
        this.handleRefreshTable();
      } catch (e) {}
    },

    // 删除库房
    async handleDeleteMaterialWarehouse(data) {
      try {
        await this.$newConfirm(
          `确定【<span style="color: red">删除</span>】当前选中的记录吗？`
        );

        const res = await this.ajax.deleteMaterialWarehouse(data.id);
        if (!res.success) {
          this.$newMessage('error', res.message || '【删除】失败!');
          return;
        }
        this.$newMessage('success', '【删除】成功!');
        this.handleRefreshTable();
      } catch (e) {
        console.error(e);
      }
    },

    handleRefreshTable(page = {}) {
      let { pageNo = 1, pageSize = 100 } = page,
        data = {
          ...this.searchForm,
          pageNo,
          pageSize,
          level: this.treeNode?.id || ''
        };
      Object.keys(data).map(key => {
        if (data[key] === null || data[key] === undefined) {
          delete data[key];
        }
      });

      // 选中全部 则删除 level
      if (data.level === this.allId) delete data.level;

      this.ajax.materialWarehouseList(data).then(res => {
        if (res.success == false) {
          this.$newMessage('error', res.message || '表格数据获取失败!');
          return;
        }
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            index: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.materialWarehouse {
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  ::v-deep {
    .content {
      display: flex;
      height: 100%;
      padding: 8px;

      .left {
        width: 216px;
        height: 100%;
        margin-right: 8px;
        background: #fff;
      }
      .right {
        padding: 12px 8px;
        border-radius: 3px;
        border: 1px solid#295cf9;
        flex: 1;
        height: 100%;
        background: #fff;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        position: relative;
      }
    }
  }
}
</style>
