<template>
  <vxe-modal
    className="dialog-add-material-dictionary"
    title="选择物资"
    v-model="visible"
    width="85%"
    height="80%"
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <div class="content">
        <new-base-search-tree
          class="node-tree"
          ref="searchTree"
          title="物资分类"
          :apiFunction="apiFunction"
          :activeId="treeNode ? treeNode.id : ''"
          placeholder="请输入分类名称进行搜索"
          @beforeClick="clickItemTree"
        />

        <div class="content-center">
          <ts-search-bar-new
            v-model="searchForm"
            :formList="searchList"
            @search="search"
          />

          <ts-vxe-base-table
            class="form-table"
            id="table_material_dictionary"
            ref="table"
            auto-resize
            minHeight="100%"
            :columns="columns"
            :pageSizes="[1, 100, 200, 500, 1000]"
            :checkbox-config="{ reserve: true, showHeader: true }"
            @selection-change="handleSelectionChange"
            @refresh="handleRefreshTable"
          />
        </div>

        <div class="sel-container">
          <div class="dictionary-container">
            <SelectModule
              :data="selectionList"
              selectTitle="已选物资"
              @handle-clear="handleClearSelectDictionary"
              @handle-delete="handleDeleteSelectDictionary"
            />
          </div>
        </div>
      </div>
    </template>

    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" @click="submit">
          确 定
        </ts-button>
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import SelectModule from './select-module.vue';
export default {
  components: {
    SelectModule
  },
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入资产名称查询'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        }
      ],
      columns: [
        { type: 'checkbox', width: 50, align: 'center' },
        { label: '序号', prop: 'pageIndex', width: 50, align: 'center' },
        {
          label: '物资编码',
          align: 'center',
          prop: 'skuCode',
          width: 160
        },
        { label: '物资名称', align: 'center', prop: 'name', minWidth: 150 },
        {
          label: '规格型号',
          align: 'center',
          prop: 'model',
          minWidth: 120
        },
        { label: '单位', align: 'center', prop: 'unitShow', width: 70 },
        {
          label: '参考单价',
          align: 'center',
          prop: 'price',
          width: 100,
          render: (h, { row }) => {
            return h('span', null, this.formatNumber(row.price));
          }
        },
        { label: '品牌', align: 'center', prop: 'brand', minWidth: 140 },
        {
          label: '生产厂家',
          align: 'center',
          prop: 'manufacturerName',
          width: 140
        },
        { label: '供应商', align: 'center', prop: 'supplyName', minWidth: 140 }
      ],

      ignoreId: '',
      visible: false,

      selectionList: [],

      treeNode: null,
      apiFunction: null,
      allId: '99999'
    };
  },
  methods: {
    show({ ignoreId = '' }) {
      this.ignoreId = ignoreId;
      this.selectionList = [];
      this.treeNode = null;
      this.apiFunction = this.handleGetClassificationTree;

      this.visible = true;
      this.$nextTick(async () => {
        this.$refs.table.pageNo = 1;
        await this.handleRefreshTable();
      });
    },

    // 优化后的：提供给树组件的接口
    async handleGetClassificationTree() {
      try {
        const res = await this.ajax.getMaterialClassifyTree();
        if (!res.success) {
          this.$newMessage('error', res.message || '获取分类列表失败');
          return {
            success: false,
            object: [],
            statusCode: 500
          };
        }

        const treeData = Array.isArray(res.object) ? res.object : [];
        const allNode = {
          id: this.allId,
          name: '全部',
          open: true,
          children: treeData.map(item => ({
            ...item,
            pid: this.allId
          }))
        };

        // 第一层节点展开
        allNode.children.forEach(item => {
          item.open = true;
        });
        return {
          object: treeData.length ? [allNode] : [],
          success: true,
          statusCode: 200
        };
      } catch (error) {
        this.$newMessage('error', '获取分类列表异常');
        return {
          success: false,
          object: [],
          statusCode: 500
        };
      }
    },

    formatNumber(value) {
      return isNaN(Number(value))
        ? value
        : Number(value).toLocaleString('zh-CN');
    },

    // 树 item点击
    clickItemTree(select) {
      this.treeNode = select;
      this.search();
    },

    tableInstance() {
      return this.$refs.table.tsVxeTableRef();
    },

    handleSelectionChange(selection) {
      this.selectionList = selection;
    },

    handleDeleteSelectDictionary({ id }) {
      this.tableInstance().setCheckboxRow({ id }, false);
      let index = this.selectionList.findIndex(f => f.id === id);
      this.selectionList.splice(index, 1);
    },

    handleClearSelectDictionary() {
      this.tableInstance().clearCheckboxRow();
      this.tableInstance().clearCheckboxReserve();
      this.selectionList = [];
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.$refs.table.triggerRefresh();
    },

    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize;
      let data = {
        ...this.searchForm,
        pageNo,
        pageSize,
        categoryId: this.treeNode?.id || '',
        ignoreId: this.ignoreId || ''
      };
      if (data.categoryId === this.allId) delete data.categoryId;

      let res = await this.ajax.getMaterialSkuList(data);
      if (res.success == false) {
        this.$newMessage('error', res.message || '表格数据获取失败!');
        return;
      }
      let rows = res.rows.map((item, index) => {
        return {
          ...item,
          pageIndex: index + 1 + (pageNo - 1) * pageSize
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },

    submit() {
      this.$emit('submit', this.selectionList || []);
      this.close();
    },

    close() {
      this.selectionList = [];
      this.$refs.table.clearSelection();
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-material-dictionary {
  ::v-deep {
    > .vxe-modal--box {
      > .vxe-modal--body {
        > .vxe-modal--content {
          > .content {
            width: 100%;
            height: 100%;

            display: flex;
            gap: 8px;

            .search-tree-box {
              width: 200px;
              min-height: 200px;
              overflow: hidden;
            }

            .content-center {
              flex: 1;
              display: flex;
              flex-direction: column;
              overflow: hidden;

              .form-table {
                flex: 1;
                overflow: hidden;
              }
            }

            .sel-container {
              width: 200px;
              display: flex;
              flex-direction: column;
              overflow: hidden;

              .dictionary-container {
                height: 100%;
              }
            }
          }
        }
      }
    }
  }
}
</style>
