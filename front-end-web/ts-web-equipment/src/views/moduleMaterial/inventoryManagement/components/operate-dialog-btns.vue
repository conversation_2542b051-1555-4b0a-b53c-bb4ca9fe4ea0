<template>
  <div class="operate-dialog-btns">
    <ts-button
      class="shallowButton"
      v-for="item in btnOperateList"
      :key="item.id"
      :disabled="item.dised"
      @click="handleBtnOperate(item.event)"
    >
      {{ item.name }}
    </ts-button>
  </div>
</template>

<script>
let typeDic = {
  1: '入库',
  2: '出库',
  3: '退货',
  4: '退库'
};

export default {
  name: 'OperateDialogBtns',
  props: {
    type: {
      type: String,
      default: ''
    }
  },

  computed: {
    btnOperateList() {
      const type = String(this.type);
      const isReturnOrBack = type === '3' || type === '4';
      const itemTitle = type === '1' ? '退货' : type === '2' ? '退库' : '';
      const btns = [
        { name: '新增', id: '1', event: 'operateAdd' },
        { name: '删除单据', id: '2', event: 'operateDel' },
        { name: '保存', id: '3', event: 'operateSave' },
        { name: '清空', id: '4', event: 'operateClear' },
        { name: `审核${typeDic[type] || ''}`, id: '5', event: 'operateAudit' },
        { name: '取消审核', id: '6', event: 'operateCancelAudit' },
        // 退货、退库单据没有登记按钮
        !isReturnOrBack && {
          name: `${itemTitle}登记`,
          id: '7',
          event: 'operateReturn'
        },
        { name: '上一张', id: '8', event: 'operatePrevious' },
        { name: '下一张', id: '9', event: 'operateNext' },
        { name: '单据打印', id: '10', event: 'operatePrint' },
        { name: '导出', id: '11', event: 'operateExport' },
        { name: '退出', id: '12', event: 'close' }
      ];

      // 统一加上 dised: false，过滤掉无效按钮
      return btns.filter(Boolean).map(btn => ({
        ...btn,
        dised: false
      }));
    }
  },

  methods: {
    handleBtnOperate(event) {
      this.$emit('btnOperate', event);
    }
  }
};
</script>

<style lang="scss" scoped>
.operate-dialog-btns {
  .ts-button {
    &.is-disabled {
      background-color: #f0f0f0 !important;
      color: #c0c4cc !important;
      border-color: #f0f0f0 !important;
      cursor: not-allowed;
    }
  }
}
</style>
