<template>
  <div class="receipt-details-table-container">
    <ts-search-bar-new
      v-model="searchForm"
      :formList="searchList"
      :resetData="resetData"
      @search="search"
    />

    <ts-vxe-base-table
      :id="tableId"
      class="form-table"
      ref="table"
      minHeight="100%"
      :hasPage="false"
      :columns="columns"
      @refresh="handleRefreshDetailsTable"
    />
  </div>
</template>

<script>
let typeDic = {
  1: 'store',
  2: 'outbound',
  3: 'returnGoods',
  4: 'returnStore'
};

export default {
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableId: `receipt-details-table-${typeDic[this.type] || ''}`,
      resetData: { name: '' },
      searchForm: { name: '' },
      searchList: [
        {
          label: '',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入物资编码或名称进行搜索'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        }
      ]
    };
  },
  methods: {
    search() {},
    handleRefreshDetailsTable() {}
  }
};
</script>

<style lang="scss" scoped>
.receipt-details-table-container {
  height: 249px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid#295cf9;
  border-radius: 4px;
  padding: 8px;
}
</style>
