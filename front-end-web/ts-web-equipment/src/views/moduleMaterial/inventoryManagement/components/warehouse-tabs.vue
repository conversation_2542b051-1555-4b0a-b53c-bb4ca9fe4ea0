<template>
  <ts-tabs
    v-model="active"
    class="type-tabs-container"
    :type="null"
    @tab-click="handleTabStatusClick"
  >
    <ts-tab-pane v-for="item in warehouseList" :key="item.id" :name="item.id">
      <span slot="label">{{ item.name }}</span>
    </ts-tab-pane>
  </ts-tabs>
</template>

<script>
export default {
  name: 'WarehouseTabs',
  data() {
    return {
      active: '0',
      warehouseList: []
    };
  },
  methods: {
    handleTabStatusClick(tab) {
      this.active = tab.name;
    },
    // 获取所有库房
    async handleGetAllWarehouseList() {
      try {
        const res = await this.ajax.materialWarehouseList({
          pageNo: 1,
          pageSize: 9999,
          status: '1'
        });
        this.warehouseList = Array.isArray(res.rows) ? res.rows : [];

        // 如果当前未选中tab或选中的tab已不存在，则默认选中第一个
        let isNoSelect = !this.active || this.active === '0';
        let isNoExist = !this.warehouseList.some(
          item => item.id === this.active
        );
        if (this.warehouseList.length && (isNoSelect || isNoExist)) {
          this.active = this.warehouseList[0].id;
        }
      } catch (e) {
        this.$newMessage && this.$newMessage('error', '获取库房列表失败');
        this.warehouseList = [];
      }
    }
  }
};
</script>
