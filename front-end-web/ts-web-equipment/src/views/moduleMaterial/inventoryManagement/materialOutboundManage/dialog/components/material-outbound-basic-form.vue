<template>
  <div class="material-outbound-basic-form">
    <ts-row>
      <ts-col :span="6">
        <ts-form-item
          label="出库类型"
          prop="materialOutbound.outboundType"
          :rules="rules.required"
        >
          <ts-select
            style="width: 100%"
            v-model="form.materialOutbound.outboundType"
            placeholder="请选择"
            filterable
            clearable
          >
            <ts-option
              v-for="item of outboundTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ts-select>
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item
          label="出库科室"
          prop="materialOutbound.outboundDept"
          :rules="rules.required"
        >
          <input-tree
            v-model="form.materialOutbound.outboundDept"
            placeholder="请选择所属科室"
            :treeData="deptTreeData"
            :defaultExpandedKeys="defaultExpandedKeys"
            key="deptId"
          />
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item
          label="领用人"
          prop="materialOutbound.receiveUser"
          :rules="rules.required"
        >
          <base-select
            style="width: 100%"
            v-model="form.materialOutbound.receiveUser"
            :inputText.sync="form.materialOutbound.receiveUserName"
            :loadMethod="handleGetPersonList"
            label="empName"
            value="empCode"
            searchInputName="empName"
            :otherSearchParams="{
              empDeptCode: form.materialOutbound.outboundDept
            }"
            clearable
          />
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item
          label="出库日期"
          prop="materialOutbound.outboundDate"
          :rules="rules.required"
        >
          <ts-date-picker
            style="width:100%"
            v-model="form.materialOutbound.outboundDate"
            :disabledDate="disabledDate"
            valueFormat="YYYY-MM-DD"
            placeholder="请选择出库日期"
          />
        </ts-form-item>
      </ts-col>
    </ts-row>

    <ts-row>
      <ts-col :span="6">
        <ts-form-item label="出库人">
          <ts-input
            v-model="form.materialOutbound.outboundUser"
            placeholder="请输入出库人"
            maxlength="20"
            disabled
          />
        </ts-form-item>
      </ts-col>

      <ts-col :span="18">
        <ts-form-item label="备注">
          <ts-input
            v-model="form.materialOutbound.remark"
            placeholder="请输入备注"
            maxlength="300"
            type="textarea"
            class="textarea"
            showWordLimit
          />
        </ts-form-item>
      </ts-col>
    </ts-row>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import InputTree from '@/components/input-tree/index.vue';
export default {
  name: 'MaterialOutboundBasicForm',
  components: {
    InputTree
  },
  props: {
    form: {
      type: Object,
      default: () => ({})
    },
    rules: {
      type: Object,
      default: () => ({})
    },
    outboundTypeList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      deptTreeData: [],
      defaultExpandedKeys: []
    };
  },
  methods: {
    init() {
      this.initForm();
    },

    initForm() {
      if (!this.form.materialOutbound) {
        this.$set(this.form, 'materialOutbound', {
          outboundType: '',
          supplyId: '',
          outboundUser: '',
          outboundUserName: '',
          outboundDate: dayjs().format('YYYY-MM-DD'),
          applyDept: '',
          applyDeptName: '',
          receiveUser: '',
          receiveUserName: '',
          isDirectSale: 0,
          remark: ''
        });
      }
    },

    async getDeptTree() {
      try {
        const res = await this.ajax.noPermissionOrganizationZTreeList();
        if (!res.success) {
          throw new Error(res.message || '科室数据获取失败!');
        }
        this.deptTreeData = res.object || [];
        this.defaultExpandedKeys = [this.deptTreeData[0]?.id];
      } catch (error) {
        throw error;
      }
    },

    async handleGetPersonList(params) {
      try {
        const res = await this.ajax.getPersonList(params);
        return res.rows || [];
      } catch (error) {
        console.error('获取人员列表失败:', error);
        return [];
      }
    },

    disabledDate(time) {
      return time.getTime() > Date.now();
    }
  }
};
</script>

<style lang="scss" scoped>
.material-outbound-basic-form {
  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 110px !important;
        max-height: 200px !important;
      }
    }
  }
}
</style>
