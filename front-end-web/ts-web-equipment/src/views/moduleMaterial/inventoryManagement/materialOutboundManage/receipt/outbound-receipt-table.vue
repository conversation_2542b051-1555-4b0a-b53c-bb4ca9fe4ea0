<template>
  <ts-vxe-base-table
    class="outbound-receipt-table form-table"
    id="outbound-receipt-table"
    ref="table"
    minHeight="100%"
    :columns="columns"
    @refresh="refresh"
    @selection-change="handleSelectionChange"
    :rowClassName="handleRowClassName"
  />
</template>

<script>
export default {
  name: 'OutboundReceiptTable',
  data() {
    return {
      selection: [],
      columns: [
        {
          type: 'checkbox',
          align: 'center',
          width: 50
        },
        {
          label: '序号',
          prop: 'pageIndex',
          align: 'center',
          width: 50
        },
        {
          label: '单据状态',
          prop: 'receiptStatus',
          align: 'center',
          width: 100
        },
        {
          label: '单据号',
          align: 'center',
          prop: 'receiptNo',
          width: 140,
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'action-cell',
                on: {
                  click: () => {
                    this.$emit('details', row);
                  }
                }
              },
              row.receiptNo
            );
          }
        },
        {
          label: '出库类型',
          prop: 'typeShow',
          align: 'center',
          width: 120
        },
        {
          label: '库房名称',
          prop: 'warehouseName',
          align: 'center',
          minWidth: 140
        },
        {
          label: '科室名称',
          prop: 'deptName',
          align: 'center',
          minWidth: 140
        },
        {
          label: '领用人',
          prop: 'receiveUserName',
          align: 'center',
          width: 90
        },
        {
          label: '金额(元)',
          prop: 'amount',
          align: 'right',
          width: 100
        },
        {
          label: '打印状态',
          prop: 'printStatus',
          width: 100,
          align: 'center'
        },
        {
          label: '出库人',
          prop: 'createUserName',
          width: 75,
          align: 'center'
        },
        {
          label: '出库日期',
          prop: 'outboundCreateDate',
          width: 100,
          align: 'center'
        },
        {
          label: '登记时间',
          prop: 'createDate',
          align: 'center',
          width: 100
        },
        {
          label: '审核人',
          prop: 'auditUserName',
          align: 'center',
          width: 75
        },
        {
          label: '审核时间',
          prop: 'auditDate',
          align: 'center',
          width: 100
        },
        {
          label: '备注',
          prop: 'remark',
          align: 'center',
          minWidth: 120
        }
      ]
    };
  },
  methods: {
    setPageNo(pageNo) {
      this.$refs.table.pageNo = pageNo;
    },

    setPageData(data) {
      this.$refs.table.refresh(data);
    },

    refresh() {
      this.$emit('refresh');
    },

    handleSelectionChange(selection) {
      this.selection = selection;
    },

    handleRowClassName({ row }) {
      let dic = {
        '1': 'row-all-back',
        '2': 'row-part-back'
      };

      return dic[row.status] || '';
    }
  }
};
</script>

<style lang="scss" scoped>
.outbound-receipt-table {
  ::v-deep {
    .row-all-back {
      color: #f64e4b;
    }

    .row-part-back {
      color: #f4a622;
    }
  }
}
</style>
