<template>
  <div class="material-return-goods-basic-form">
    <ts-row>
      <ts-col :span="6">
        <ts-form-item
          label="供应商"
          prop="materialReturnGoods.supplyId"
          :rules="rules.required"
        >
          <ts-select
            v-model="form.materialReturnGoods.supplyId"
            placeholder="请选择"
            style="width: 100%;"
            clearable
            filterable
          >
            <ts-option
              v-for="item in supplierList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </ts-select>
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item
          label="退货日期"
          prop="materialReturnGoods.returnDate"
          :rules="rules.required"
        >
          <ts-date-picker
            style="width:100%"
            v-model="form.materialReturnGoods.returnDate"
            valueFormat="YYYY-MM-DD"
            placeholder="请选择退货日期"
          />
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item label="退货人">
          <ts-input
            v-model="form.materialReturnGoods.returnPerson"
            placeholder="请输入退货人"
            maxlength="30"
            disabled
          />
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item label="关联单据号">
          <ts-input
            v-model="form.materialReturnGoods.returnPerson"
            placeholder="请输入关联单据号"
            maxlength="30"
            disabled
          />
        </ts-form-item>
      </ts-col>
    </ts-row>

    <ts-form-item label="备注">
      <ts-input
        v-model="form.materialReturnGoods.note"
        type="textarea"
        class="textarea"
        maxlength="300"
        placeholder="请输入"
        show-word-limit
      />
    </ts-form-item>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  name: 'MaterialReturnGoodsBasicForm',
  props: {
    form: {
      type: Object,
      required: true
    },
    rules: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      supplierList: []
    };
  },
  created() {
    this.getSupplierList();
  },
  methods: {
    // 供应商列表
    async getSupplierList() {
      let res = await this.ajax.materialSupplierListNoPage({
        status: '1'
      });
      if (!res.success) {
        this.$newMessage('error', res.message || '获取供应商列表失败!');
        this.supplierList = [];
        return;
      }
      this.supplierList = res.object || [];
    }
  }
};
</script>

<style lang="scss" scoped>
.material-return-goods-basic-form {
  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 100px !important;
        max-height: 100px !important;
      }
    }
  }
}
</style>
