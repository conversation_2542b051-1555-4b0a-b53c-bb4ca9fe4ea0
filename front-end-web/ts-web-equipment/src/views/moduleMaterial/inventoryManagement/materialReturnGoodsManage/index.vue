<template>
  <div class="material-return-goods-manage-box">
    <warehouse-tabs ref="warehouseTabs" />

    <div class="receipt-container">
      <return-goods-receipt-search
        ref="returnGoodsReceiptSearch"
        @search="OperateSearch"
        @add="OperateAdd"
        @delete="OperateDelete"
        @audit="OperateAudit"
        @cancelAudit="OperateCancelAudit"
        @returnGoods="OperateReturnGoods"
        @print="OperatePrint"
        @export="OperateExport"
      />

      <return-goods-receipt-table
        ref="returnGoodsReceiptTable"
        @refresh="handleRefreshReturnGoodsReceiptTable"
      />
    </div>
    <receipt-details-table-container type="3" :columns="detailsColumns" />

    <dialog-add-material-return-goods
      ref="dialogAddMaterialReturnGoods"
      @submit="handleSubmitMaterialReturnGoods"
    />
  </div>
</template>

<script>
import ReturnGoodsReceiptSearch from './receipt/return-goods-receipt-search.vue';
import ReturnGoodsReceiptTable from './receipt/return-goods-receipt-table.vue';

import ReceiptDetailsTableContainer from '../components/receipt-details-table-container.vue';
import { detailsColumns } from './details/detailsColumns';
import DialogAddMaterialReturnGoods from './dialog/dialog-add-material-return-goods.vue';
import WarehouseTabs from '../components/warehouse-tabs.vue';
export default {
  name: 'MaterialReturnGoodsManage',
  components: {
    ReturnGoodsReceiptSearch,
    ReturnGoodsReceiptTable,
    ReceiptDetailsTableContainer,
    DialogAddMaterialReturnGoods,
    WarehouseTabs
  },
  data() {
    return {
      detailsColumns
    };
  },

  computed: {
    active() {
      return this.$refs.warehouseTabs.active;
    }
  },

  methods: {
    refresh() {
      this.$refs.warehouseTabs.handleGetAllWarehouseList();
      this.handleRefreshReturnGoodsReceiptTable();
    },

    // 新增
    OperateAdd() {
      this.$refs.dialogAddMaterialReturnGoods.show({
        type: 'add',
        title: '新增退货单据'
      });
    },
    // 删除
    OperateDelete() {},
    // 审核
    OperateAudit() {},
    // 取消审核
    OperateCancelAudit() {},
    // 退货
    OperateReturnGoods() {},
    // 打印
    OperatePrint() {},
    // 导出
    OperateExport() {},

    // 查询退货单据
    OperateSearch() {
      this.$refs.returnGoodsReceiptTable.setPageNo(1);
      this.handleRefreshReturnGoodsReceiptTable();
    },
    // 刷新退货单据
    async handleRefreshReturnGoodsReceiptTable() {
      if (!this.checkQueryParam()) {
        return;
      }

      let searchForm = this.$refs.returnGoodsReceiptSearch.searchForm;
      let { date = [] } = searchForm,
        pageNo = this.$refs.returnGoodsReceiptTable.pageNo,
        pageSize = this.$refs.returnGoodsReceiptTable.pageSize,
        formData = {
          ...searchForm,
          pageNo,
          pageSize
        };

      const [startD, endD] = date;
      formData.createDateQuery =
        startD && endD ? `${startD} 00:00:00,${endD} 23:59:59` : '';
      !formData.createDateQuery && delete formData.createDateQuery;
      delete formData.date;

      let res = await this.ajax.getMaterialAccountingPeriodList(formData);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }

      let rows = res.rows.map((item, i) => {
        let pageIndex = (pageNo - 1) * pageSize + i + 1;
        return {
          pageIndex,
          ...item
        };
      });

      rows[0].status = '1';
      rows[1].status = '2';
      this.$refs.returnGoodsReceiptTable.setPageData({
        ...res,
        rows
      });
      // // 设置选中行 用于编辑、新增
      // if (setRowId) {
      //   let row = rows.find(item => item.id === setRowId);
      //   if (row) {
      //     this.$refs.table.handleSetCurrentRow(row);
      //   }
      //   return;
      // }

      // // 设置第一行选中
      // if (rows.length) {
      //   this.$refs.storeReceiptTable.handleSetCurrentRow(rows[0]);
      // }
    },

    handleSubmitMaterialReturnGoods(data) {
      console.log(data);
    },

    checkData([start, end]) {
      let num = 0;
      start && num++;
      end && num++;
      return num === 1;
    },

    checkQueryParam() {
      const { date = [] } = this.$refs.returnGoodsReceiptSearch.searchForm;
      if (this.checkData(date)) {
        this.$newMessage('warning', '请选择退货日期完整的时间区间查询');
        return false;
      }
      return true;
    }
  }
};
</script>

<style scoped lang="scss">
.material-return-goods-manage-box {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;

  ::v-deep {
    .type-tabs-container .el-tabs__item {
      width: auto !important;
    }

    .type-tabs-container .el-tabs__nav-wrap::after {
      height: 0;
    }
    .base-date-range-picker {
      display: inline-flex;
      align-items: center;
      .range-separator {
        margin: 0 4px;
      }
      .date-picker {
        display: flex;
        align-items: center;
        background: transparent;
        width: 140px !important;
      }
    }
  }

  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);
  }

  .receipt-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid#295cf9;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
  }

  .details-container {
    height: 249px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid#295cf9;
    border-radius: 4px;
    padding: 8px;
  }
}
</style>
