<template>
  <ts-search-bar-new
    v-model="searchForm"
    :formList="searchList"
    :actions="actions"
    :resetData="resetData"
    @search="search"
  >
    <template slot="date">
      <base-date-range-picker v-model="searchForm.date" type="daterange" />
    </template>
    <template slot="status">
      <ts-radio-group v-model="searchForm.status" @change="search">
        <ts-radio label="">全部</ts-radio>
        <ts-radio label="1">已登记</ts-radio>
        <ts-radio label="2">已审核</ts-radio>
      </ts-radio-group>
    </template>
    <template slot="printStatus">
      <ts-radio-group v-model="searchForm.printStatus" @change="search">
        <ts-radio label="">全部</ts-radio>
        <ts-radio label="1">已打印</ts-radio>
        <ts-radio label="2">未打印</ts-radio>
      </ts-radio-group>
    </template>
  </ts-search-bar-new>
</template>

<script>
import dayjs from 'dayjs';
let resetData = () => {
  return {
    date: [
      dayjs()
        .subtract(7, 'day')
        .format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD')
    ],
    status: '',
    printStatus: ''
  };
};

export default {
  name: 'ReturnGoodsReceiptSearch',
  data() {
    return {
      resetData,
      searchForm: resetData(),
      actions: [
        {
          label: '新增',
          prop: { type: 'primary' },
          click: this.add
        },
        {
          label: '删除单据',
          prop: { type: 'danger' },
          click: this.delete
        },
        {
          label: '审核退货',
          prop: { class: 'shallowButton' },
          click: this.audit
        },
        {
          label: '取消审核',
          prop: { class: 'shallowButton' },
          click: this.cancelAudit
        },
        {
          label: '单据打印',
          prop: { class: 'shallowButton' },
          click: this.print
        },
        {
          label: '导出',
          prop: { class: 'shallowButton' },
          click: this.export
        }
      ],
      searchList: [
        {
          label: '退货日期',
          value: 'date'
        },
        {
          label: '单据号',
          value: 'batchNo',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入单据号'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '单据状态',
          value: 'status'
        },
        {
          label: '单据号',
          value: 'batchNo',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入单据号'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '供应商',
          value: 'supplier',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入供应商'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '退货人',
          value: 'doerName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入退货人'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '打印状态',
          value: 'printStatus'
        }
      ]
    };
  },
  methods: {
    search() {
      this.$emit('search');
    },

    add() {
      this.$emit('add');
    },

    delete() {
      this.$emit('delete');
    },

    audit() {
      this.$emit('audit');
    },

    cancelAudit() {
      this.$emit('cancelAudit');
    },

    returnGoods() {
      this.$emit('returnGoods');
    },

    print() {
      this.$emit('print');
    },

    export() {
      this.$emit('export');
    }
  }
};
</script>
