export const detailsColumns = [
  {
    label: '序号',
    prop: 'pageIndex',
    align: 'center',
    width: 50
  },
  {
    label: '单据号',
    align: 'center',
    prop: 'receiptNo',
    width: 140
  },
  {
    label: '物资编码',
    align: 'center',
    prop: 'materialCode',
    width: 140
  },
  {
    label: '物资名称',
    prop: 'materialName',
    align: 'center',
    minWidth: 140
  },
  {
    label: '物资分类',
    prop: 'materialCategory',
    align: 'center',
    minWidth: 140
  },
  {
    label: '规格型号',
    prop: 'model',
    align: 'center',
    minWidth: 140
  },
  {
    label: '单位',
    prop: 'unit',
    align: 'center',
    minWidth: 140
  },
  {
    label: '退库数量',
    prop: 'quantity',
    width: 100,
    align: 'right'
  },
  {
    label: '单价(元)',
    prop: 'price',
    align: 'right',
    width: 100
  },
  {
    label: '退库金额(元)',
    prop: 'amount',
    width: 75,
    align: 'right'
  },
  {
    label: '生产批号',
    prop: 'batchNo',
    width: 100,
    align: 'center'
  },
  {
    label: '生产日期',
    prop: 'productionDate',
    align: 'center',
    width: 100
  },
  {
    label: '失效日期',
    prop: 'expiryDate',
    align: 'center',
    width: 75
  },
  {
    label: '注册证号',
    prop: 'registrationNo',
    align: 'center',
    width: 140
  },
  {
    label: '品牌',
    prop: 'brand',
    align: 'center',
    width: 100
  },
  {
    label: '生产厂家',
    prop: 'manufacturer',
    align: 'center',
    width: 80
  },
  {
    label: '备注',
    prop: 'remark',
    align: 'center',
    minWidth: 120
  }
];
