<template>
  <div class="material-return-store-basic-form">
    <ts-row>
      <ts-col :span="6">
        <ts-form-item
          label="退库科室"
          prop="materialReturnStore.rdeptId"
          :rules="rules.required"
        >
          <ts-ztree-select
            :data="deptTree"
            :inpText.sync="form.materialReturnStore.rdeptName"
            :inpVal.sync="form.materialReturnStore.rdeptId"
            style="width: 100%;"
            @change="handleDeptSelectChange"
            @clear="handleDeptSelectChange"
          />
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item
          label="退库日期"
          prop="materialReturnStore.returnDate"
          :rules="rules.required"
        >
          <ts-date-picker
            style="width:100%"
            v-model="form.materialReturnStore.returnDate"
            valueFormat="YYYY-MM-DD"
            placeholder="请选择退库日期"
          />
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item label="退库人">
          <ts-input
            v-model="form.materialReturnStore.rcreateUserName"
            placeholder="请输入退库人"
            maxlength="30"
            disabled
          />
        </ts-form-item>
      </ts-col>
    </ts-row>

    <ts-form-item label="备注">
      <ts-input
        v-model="form.materialReturnStore.note"
        type="textarea"
        class="textarea"
        maxlength="300"
        placeholder="请输入"
        show-word-limit
      />
    </ts-form-item>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  name: 'MaterialReturnStoreBasicForm',
  props: {
    form: {
      type: Object,
      required: true
    },
    rules: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      deptTree: [],
      defaultExpandedKeys: []
    };
  },
  created() {
    this.getDeptTreeData();
  },
  methods: {
    async getDeptTreeData() {
      try {
        const res = await this.ajax.noPermissionOrganizationZTreeList();
        if (!res.success) {
          throw res.message;
        }
        this.deptTree = res.object || [];
        this.defaultExpandedKeys = [this.deptTree[0]?.id];
      } catch (e) {
        this.$message.error(e || '科室数据获取失败');
      }
    },

    handleDeptSelectChange() {
      this.form.materialReturnStore.rdeptId = '';
      this.form.materialReturnStore.rdeptName = '';
      this.form.materialReturnStore.rcreateUserName = '';
      this.form.materialReturnStore.rcreateUser = '';
    }
  }
};
</script>

<style lang="scss" scoped>
.material-return-store-basic-form {
  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 100px !important;
        max-height: 100px !important;
      }
    }
  }
}
</style>
