<template>
  <div class="material-return-store-details-dictionary">
    <table class="custom-form-table">
      <thead class="custom-form-table__header">
        <tr class="custom-form-table__header-row">
          <td
            class="custom-form-table__header-cell"
            :style="{ width: item.width ? item.width + 'px' : 'auto' }"
            v-for="item in materialDictionaryColumns"
            :key="item.prop"
          >
            <template>
              <span v-if="item.requiredIcon" class="required-icon">*</span>
              <span class="custom-form-table__header-cell-label">
                {{ item.label }}
              </span>
            </template>
          </td>
        </tr>
      </thead>
      <tbody class="custom-form-table__body">
        <tr
          class="custom-form-table__body-row"
          v-for="(item, index) in form.materialReturnStoreDetailList"
          :key="item.id"
        >
          <td
            class="custom-form-table__body-cell"
            v-for="column in materialDictionaryColumns"
            :key="column.prop"
          >
            <template v-if="column.render === 'index'">
              <span>{{ index + 1 }}</span>
            </template>

            <template v-if="column.render === 'checkbox'">
              <input type="checkbox" v-model="item[column.prop]" />
            </template>

            <template v-if="column.render === 'text'">
              <span>{{ item[column.prop] }}</span>
            </template>

            <template v-else>
              <template v-if="column.prop === 'returnQuantity'">
                <comp-integer-input
                  class="auto-input-styles"
                  v-model="item[column.prop]"
                  :prop="
                    `materialReturnStoreDetailList.${index}.${column.prop}`
                  "
                  :rules="column.requiredIcon ? rules.required : undefined"
                  :disabled="isUpdate"
                  @blur="() => handleBlurComputedTotal(item)"
                />
              </template>

              <template v-if="column.prop === 'amount'">
                <span>{{
                  !isNaN(item[column.prop])
                    ? Number(item[column.prop]).toLocaleString('en-CN')
                    : ''
                }}</span>
              </template>
            </template>

            <template v-if="column.render === 'operate'">
              <div class="delete-device" @click="() => handleDel(index)">
                移除
              </div>
            </template>
          </td>
        </tr>

        <tr
          class="total-amount"
          v-if="form.materialReturnStoreDetailList.length"
        >
          <td
            class="footer-total-amount-item"
            v-for="(item, index) in materialDictionaryColumns"
            :style="{ width: item.width ? item.width + 'px' : 'auto' }"
            :key="item.prop"
          >
            <template v-if="index === 0">
              合计
            </template>

            <template v-if="item.prop === 'amount'">
              {{ totalAmount }}
            </template>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import moment from 'moment';
import { Decimal } from 'decimal.js';
import CompIntegerInput from '@/components/busi-form-table-components/comp-integer-input.vue';
export default {
  name: 'MaterialReturnStoreDetailsDictionary',
  components: {
    CompIntegerInput
  },
  props: {
    isUpdate: {
      type: Boolean,
      default: false
    },
    form: {
      type: Object,
      required: true
    },
    rules: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      materialDictionaryColumns: [
        {
          label: '',
          render: 'checkbox',
          prop: 'isCheck',
          width: 50
        },
        {
          label: '序号',
          prop: 'index',
          render: 'index',
          width: 50
        },
        {
          prop: 'materialCode',
          label: '物资编码',
          align: 'center',
          render: 'text',
          width: 150
        },
        {
          prop: 'materialName',
          label: '物资名称',
          align: 'center',
          render: 'text',
          width: 150
        },
        {
          prop: 'materialType',
          label: '物资分类',
          align: 'center',
          render: 'text',
          width: 135
        },
        {
          prop: 'materialModel',
          label: '规格型号',
          align: 'center',
          render: 'text',
          width: 135
        },
        {
          prop: 'unit',
          label: '单位',
          align: 'center',
          render: 'text',
          width: 95
        },
        {
          prop: 'outboundQuantity',
          label: '出库数量',
          align: 'center',
          render: 'text',
          width: 65
        },
        {
          prop: 'returnQuantity',
          label: '退库数量',
          align: 'center',
          width: 80,
          requiredIcon: true
        },
        {
          prop: 'unitprice',
          label: '单价(元)',
          align: 'center',
          render: 'text',
          width: 105
        },
        {
          prop: 'amount',
          label: '金额(元)',
          align: 'center',
          render: 'text',
          width: 100
        },
        {
          prop: 'batchNumber',
          label: '生产批号',
          align: 'center',
          render: 'text',
          width: 145
        },
        {
          prop: 'productionDate',
          label: '生产日期',
          align: 'center',
          render: 'text',
          width: 145
        },
        {
          prop: 'expirationDate',
          label: '失效日期',
          align: 'center',
          render: 'text',
          width: 145
        },
        {
          prop: 'registrationNumber',
          label: '注册证号',
          align: 'center',
          render: 'text',
          width: 135
        },
        {
          prop: 'brand',
          label: '品牌',
          align: 'center',
          render: 'text',
          width: 135
        },
        {
          prop: 'manufacturer',
          label: '生产厂家',
          align: 'center',
          render: 'text',
          width: 135
        }
      ]
    };
  },
  computed: {
    totalAmount() {
      const total = this.form.materialReturnStoreDetailList
        .reduce((sum, item) => sum.plus(item.amount || 0), new Decimal(0))
        .toString();

      const formattedTotal = Number(total).toLocaleString('en-CN');
      return formattedTotal;
    }
  },
  methods: {
    getSelection() {
      return this.form.materialReturnStoreDetailList
        .filter(item => item.isCheck)
        .map(item => item.id);
    },

    // 计算总金额
    handleBlurComputedTotal(row) {
      const quantity = parseFloat(row.quantity);
      const unitprice = parseFloat(row.unitprice);

      if (isNaN(quantity) || isNaN(unitprice)) {
        row.amount = 0;
        return;
      }

      row.quantity = quantity;
      row.unitprice = unitprice;
      row.amount = Decimal.mul(unitprice, quantity).toNumber();
    },

    async handleDel(index) {
      try {
        await this.$newConfirm(
          '您是否确认【<span style="color: red">删除</span>】当前数据？'
        );
        this.form.materialReturnStoreDetailList.splice(index, 1);
      } catch (error) {
        console.error('删除标的物失败:', error);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.material-return-store-details-dictionary {
  overflow-x: auto;

  ::v-deep {
    .auto-input-styles {
      width: 100%;
      .ts-input {
        min-width: 100% !important;
      }
    }

    .custom-form-table__body-cell {
      .auto-height-input-container {
        .el-form-item__content {
          line-height: 10px !important;
        }
      }
    }
  }

  .total-amount {
    height: 32px;
    border: 1px solid rgba(235, 238, 245, 1);
    border-top: none;
    background-color: #f3f6f9;

    .footer-total-amount-item {
      text-align: center;
      font-weight: bold;
      color: $warning-color;
    }
  }

  .delete-device {
    color: red;
    cursor: pointer;
  }
}
</style>
