<template>
  <ts-search-bar-new
    v-model="searchForm"
    :formList="searchList"
    :actions="actions"
    :resetData="resetData"
    @search="search"
  >
    <template slot="date">
      <base-date-range-picker v-model="searchForm.date" type="daterange" />
    </template>
    <template slot="dept">
      <input-tree
        v-model="searchForm.rdeptId"
        placeholder="请选择"
        :treeData="deptTree"
        :defaultExpandedKeys="defaultExpandedKeys"
        key="deptId"
      />
    </template>
    <template slot="status">
      <ts-radio-group v-model="searchForm.status" @change="search">
        <ts-radio label="">全部</ts-radio>
        <ts-radio label="1">已登记</ts-radio>
        <ts-radio label="2">已审核</ts-radio>
      </ts-radio-group>
    </template>
    <template slot="printStatus">
      <ts-radio-group v-model="searchForm.printStatus" @change="search">
        <ts-radio label="">全部</ts-radio>
        <ts-radio label="1">已打印</ts-radio>
        <ts-radio label="2">未打印</ts-radio>
      </ts-radio-group>
    </template>
  </ts-search-bar-new>
</template>

<script>
import dayjs from 'dayjs';
import InputTree from '@/components/input-tree/index.vue';
let resetData = () => {
  return {
    date: [
      dayjs()
        .subtract(7, 'day')
        .format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD')
    ],
    status: '',
    printStatus: ''
  };
};

export default {
  name: 'StoreReceiptSearch',
  components: {
    InputTree
  },
  data() {
    return {
      resetData,
      searchForm: resetData(),
      actions: [
        {
          label: '新增',
          prop: { type: 'primary' },
          click: this.add
        },
        {
          label: '删除单据',
          prop: { type: 'danger' },
          click: this.delete
        },
        {
          label: '审核入库',
          prop: { class: 'shallowButton' },
          click: this.audit
        },
        {
          label: '取消审核',
          prop: { class: 'shallowButton' },
          click: this.cancelAudit
        },
        {
          label: '退货登记',
          prop: { class: 'shallowButton' },
          click: this.returnGoods
        },
        {
          label: '单据打印',
          prop: { class: 'shallowButton' },
          click: this.print
        },
        {
          label: '导出',
          prop: { class: 'shallowButton' },
          click: this.export
        }
      ],
      searchList: [
        {
          label: '退库日期',
          value: 'date'
        },
        {
          label: '单据号',
          value: 'batchNo',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入单据号'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '单据状态',
          value: 'status'
        },
        {
          label: '科室名称',
          value: 'dept'
        },
        {
          label: '退库人',
          value: 'doerName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入退库人'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '打印状态',
          value: 'printStatus'
        }
      ],
      deptTree: [],
      defaultExpandedKeys: []
    };
  },
  created() {
    this.getDeptTreeData();
  },
  methods: {
    async getDeptTreeData() {
      try {
        const res = await this.ajax.noPermissionOrganizationZTreeList();
        if (!res.success) {
          throw res.message;
        }
        this.deptTree = res.object || [];
        this.defaultExpandedKeys = [this.deptTree[0]?.id];
      } catch (e) {
        this.$message.error(e || '科室数据获取失败');
      }
    },

    search() {
      this.$emit('search');
    },

    add() {
      this.$emit('add');
    },

    delete() {
      this.$emit('delete');
    },

    audit() {
      this.$emit('audit');
    },

    cancelAudit() {
      this.$emit('cancelAudit');
    },

    returnGoods() {
      this.$emit('returnGoods');
    },

    print() {
      this.$emit('print');
    },

    export() {
      this.$emit('export');
    }
  }
};
</script>
