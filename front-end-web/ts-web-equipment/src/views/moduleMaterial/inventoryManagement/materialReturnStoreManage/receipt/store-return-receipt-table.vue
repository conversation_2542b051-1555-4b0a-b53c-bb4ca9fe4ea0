<template>
  <ts-vxe-base-table
    class="store-return-receipt-table form-table"
    id="store-return-receipt-table"
    ref="table"
    minHeight="100%"
    :columns="columns"
    @refresh="refresh"
    @selection-change="handleSelectionChange"
  />
</template>

<script>
export default {
  name: 'StoreReturnReceiptTable',
  data() {
    return {
      selection: [],
      columns: [
        {
          type: 'checkbox',
          align: 'center',
          width: 50
        },
        {
          label: '序号',
          prop: 'pageIndex',
          align: 'center',
          width: 50
        },
        {
          label: '单据状态',
          prop: 'receiptStatus',
          align: 'center',
          width: 120
        },
        {
          label: '单据号',
          align: 'center',
          prop: 'receiptNo',
          width: 140,
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'action-cell',
                on: {
                  click: () => {
                    this.$emit('details', row);
                  }
                }
              },
              row.receiptNo
            );
          }
        },
        {
          label: '库房名称',
          prop: 'warehouseName',
          align: 'center',
          minWidth: 140
        },
        {
          label: '科室名称',
          prop: 'deptName',
          align: 'center',
          minWidth: 140
        },
        {
          label: '退库人',
          prop: 'doerName',
          width: 75,
          align: 'center'
        },
        {
          label: '退库金额(元)',
          prop: 'amount',
          align: 'right',
          width: 100
        },
        {
          label: '打印状态',
          prop: 'printStatus',
          width: 100,
          align: 'center'
        },
        {
          label: '退库日期',
          prop: 'storeCreateDate',
          width: 100,
          align: 'center'
        },
        {
          label: '登记时间',
          prop: 'createDate',
          align: 'center',
          width: 100
        },
        {
          label: '审核人',
          prop: 'auditUserName',
          align: 'center',
          width: 75
        },
        {
          label: '审核时间',
          prop: 'auditDate',
          align: 'center',
          width: 100
        },
        {
          label: '备注',
          prop: 'remark',
          align: 'center',
          minWidth: 120
        }
      ]
    };
  },
  methods: {
    setPageNo(pageNo) {
      this.$refs.table.pageNo = pageNo;
    },

    setPageData(data) {
      this.$refs.table.refresh(data);
    },

    refresh() {
      this.$emit('refresh');
    },

    handleSelectionChange(selection) {
      this.selection = selection;
    }
  }
};
</script>
