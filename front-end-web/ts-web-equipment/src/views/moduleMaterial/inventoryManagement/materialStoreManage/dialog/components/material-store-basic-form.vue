<template>
  <div class="material-store-basic-form">
    <ts-row>
      <ts-col :span="6">
        <ts-form-item
          label="入库类型"
          prop="materialStore.storeType"
          :rules="rules.required"
        >
          <ts-select
            style="width: 100%"
            v-model="form.materialStore.storeType"
            placeholder="请选择"
            filterable
            clearable
          >
            <ts-option
              v-for="item of storeTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ts-select>
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item label="供应商" prop="materialStore.supplyId">
          <ts-select
            v-model="form.materialStore.supplyId"
            placeholder="请选择"
            style="width: 100%;"
            clearable
            filterable
          >
            <ts-option
              v-for="item in supplierList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </ts-select>
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item
          label="采购人"
          prop="materialStore.rcreateUser"
          :rules="rules.required"
        >
          <base-select
            style="width: 100%"
            v-model="form.materialStore.rcreateUser"
            :inputText.sync="form.materialStore.rcreateUserName"
            :loadMethod="handleGetPersonList"
            label="empName"
            value="empCode"
            searchInputName="empName"
            clearable
          />
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item label="采购日期">
          <ts-date-picker
            style="width:100%"
            v-model="form.materialStore.purchaseDate"
            :disabledDate="disabledDate"
            valueFormat="YYYY-MM-DD"
            placeholder="请选择采购日期"
          />
        </ts-form-item>
      </ts-col>
    </ts-row>

    <ts-row>
      <ts-col :span="6">
        <ts-form-item label="发票号码">
          <ts-input
            v-model="form.materialStore.invoiceNo"
            placeholder="请输入"
            maxlength="30"
            v-enter-next-input
          />
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item label="开票日期">
          <ts-date-picker
            style="width:100%"
            v-model="form.materialStore.invoiceDate"
            :disabledDate="disabledDate"
            valueFormat="YYYY-MM-DD"
            placeholder="请选择开票日期"
          />
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item label="入库人">
          <ts-input
            v-model="form.materialStore.inboundPerson"
            placeholder="请输入入库人"
            maxlength="30"
            disabled
            v-enter-next-input
          />
        </ts-form-item>
      </ts-col>

      <ts-col :span="6">
        <ts-form-item
          label="入库日期"
          prop="materialStore.inboundDate"
          :rules="rules.required"
        >
          <ts-date-picker
            style="width:100%"
            v-model="form.materialStore.inboundDate"
            valueFormat="YYYY-MM-DD"
            placeholder="请选择入库日期"
            :disabledDate="inboundDateDisabledDate"
          />
        </ts-form-item>
      </ts-col>
    </ts-row>

    <ts-row>
      <ts-col :span="6">
        <ts-form-item label="是否直销">
          <ts-radio-group
            v-model="form.materialStore.isDirectSale"
            @change="handleIsDirectSaleChange"
          >
            <ts-radio label="1">是</ts-radio>
            <ts-radio label="0">否</ts-radio>
          </ts-radio-group>
        </ts-form-item>
      </ts-col>

      <ts-col :span="6" v-if="isDirectSale">
        <ts-form-item
          label="申请科室"
          prop="materialStore.rdeptName"
          :rules="isDirectSale ? rules.required : {}"
        >
          <ts-ztree-select
            :data="deptTree"
            :inpText.sync="form.materialStore.rdeptName"
            :inpVal.sync="form.materialStore.rdeptId"
            style="width: 100%;"
            @change="handleDeptSelectChange"
            @clear="handleDeptSelectChange"
          />
        </ts-form-item>
      </ts-col>

      <ts-col :span="6" v-if="isDirectSale">
        <ts-form-item label="领用人">
          <base-select
            style="width: 100%"
            v-model="form.materialStore.rcreateUser"
            :inputText.sync="form.materialStore.rcreateUserName"
            :loadMethod="handleGetPersonList"
            label="empName"
            value="empCode"
            searchInputName="empName"
            :otherSearchParams="{ empDeptCode: form.materialStore.rdeptId }"
            clearable
          />
        </ts-form-item>
      </ts-col>
    </ts-row>

    <ts-form-item label="备注">
      <ts-input
        v-model="form.materialStore.note"
        type="textarea"
        class="textarea"
        maxlength="300"
        placeholder="请输入"
        show-word-limit
      />
    </ts-form-item>
  </div>
</template>

<script>
import moment from 'moment';
import enterNextInput from '@/directive/enterNextInput';
export default {
  name: 'MaterialStoreBasicForm',
  directives: {
    enterNextInput
  },
  props: {
    storeTypeList: Array,
    form: {
      type: Object,
      required: true
    },
    rules: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      supplierList: [],
      deptTree: [],
      defaultExpandedKeys: []
    };
  },
  created() {
    this.getSupplierList();
    this.getDeptTreeData();
  },
  computed: {
    isDirectSale() {
      return this.form.materialStore.isDirectSale === '1';
    }
  },
  methods: {
    async getDeptTreeData() {
      try {
        const res = await this.ajax.noPermissionOrganizationZTreeList();
        if (!res.success) {
          throw res.message;
        }
        this.deptTree = res.object || [];
        this.defaultExpandedKeys = [this.deptTree[0]?.id];
      } catch (e) {
        this.$message.error(e || '科室数据获取失败');
      }
    },

    // 采购人列表
    async handleGetPersonList(data) {
      try {
        const res = await this.ajax.getEmployeeList({
          pageSize: 15,
          status: 1,
          sidx: 'create_date',
          sord: 'desc',
          ...data
        });
        return res.rows;
      } catch (error) {
        console.error('获取人员列表失败:', error);
        return false;
      }
    },

    // 供应商列表
    async getSupplierList() {
      let res = await this.ajax.materialSupplierListNoPage({
        status: '1'
      });
      if (!res.success) {
        this.$newMessage('error', res.message || '获取供应商列表失败!');
        this.supplierList = [];
        return;
      }
      this.supplierList = res.object || [];
    },

    handleDeptSelectChange() {
      this.form.materialStore.rcreateUser = '';
      this.form.materialStore.rcreateUserName = '';
    },

    disabledDate: current => {
      return current && current > moment();
    },

    // 禁用不是本月的日期
    inboundDateDisabledDate: current => {
      if (!current) return false;
      const now = moment();
      return current.month() !== now.month() || current.year() !== now.year();
    },

    handleIsDirectSaleChange() {
      this.form.materialStore.rdeptId = '';
      this.form.materialStore.rdeptName = '';
      this.form.materialStore.rcreateUserName = '';
      this.form.materialStore.rcreateUser = '';
    }
  }
};
</script>

<style lang="scss" scoped>
.material-store-basic-form {
  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 100px !important;
        max-height: 100px !important;
      }
    }
  }
}
</style>
