<template>
  <vxe-modal
    className="dialog-add-material-store"
    :title="title"
    v-model="visible"
    width="90%"
    height="85%"
    :before-hide-method="close"
  >
    <template #default>
      <div class="content-container">
        <operate-dialog-btns @btnOperate="handleBtnOperate" type="1" />

        <ts-form ref="form" :model="form">
          <div class="form-group-tips">
            <span>单据信息</span>
          </div>

          <material-store-basic-form
            ref="materialStoreBasicForm"
            :form="form"
            :rules="rules"
            :storeTypeList="storeTypeList"
          />

          <div class="form-group-tips">
            <span>入库物资明细</span>

            <div>
              <ts-button
                class="shallowButton"
                type="primary"
                @click="handleAddMaterialDictionary"
              >
                添加物资
              </ts-button>
              <ts-button type="danger" @click="handleRemoveMaterialDictionary">
                删除
              </ts-button>
            </div>
          </div>

          <material-store-details-dictionary
            ref="materialStoreDetailsDictionary"
            :form="form"
            :rules="rules"
          />
        </ts-form>

        <dialog-add-material-dictionary
          ref="dialogAddMaterialDictionary"
          @submit="handleSubmitMaterialDictionary"
        />
      </div>
    </template>
  </vxe-modal>
</template>

<script>
import cloneDeep from 'lodash-es/cloneDeep';
import MaterialStoreBasicForm from './components/material-store-basic-form.vue';
import MaterialStoreDetailsDictionary from './components/material-store-details-dictionary.vue';
import DialogAddMaterialDictionary from '@/views/moduleMaterial/inventoryManagement/components/dialog-add-material-dictionary.vue';
import OperateDialogBtns from '@/views/moduleMaterial/inventoryManagement/components/operate-dialog-btns.vue';
export default {
  components: {
    MaterialStoreBasicForm,
    MaterialStoreDetailsDictionary,
    DialogAddMaterialDictionary,
    OperateDialogBtns
  },
  data() {
    return {
      visible: false,
      type: 'add',
      title: '',

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },

      storeTypeList: [
        {
          label: '物资类型',
          value: '1'
        },
        {
          label: '物资类型',
          value: '2'
        }
      ],
      disabled: true
    };
  },

  computed: {
    isAdd() {
      return this.type === 'add';
    },
    isEdit() {
      return this.type === 'edit';
    }
  },

  methods: {
    async show({ type = 'add', data = {}, title = '' }) {
      this.title = title;
      this.type = type;
      this.$set(this, 'form', this.isEdit ? cloneDeep(data) : this.initForm());

      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },

    initForm() {
      let parentStoreInfo = this.$getParentStoreInfo();
      let userInfo = parentStoreInfo?.userInfo || {};
      return {
        materialStore: {
          inboundPerson: userInfo.employeeName,
          inboundDate: this.$dayjs().format('YYYY-MM-DD'),
          rdeptId: '',
          rdeptName: '',
          rcreateUserName: '',
          rcreateUser: ''
        },
        materialStoreDetailList: [
          {
            index: 1,
            isCheck: false,
            materialName: '',
            materialType: '',
            materialModel: '',
            unit: '',
            quantity: '',
            unitprice: '',
            amount: '',
            batchNumber: '',
            productionDate: '',
            expirationDate: '',
            remark: ''
          }
        ]
      };
    },

    handleAddMaterialDictionary() {
      this.$refs.dialogAddMaterialDictionary.show({
        ignoreId: this.form.materialStoreDetailList.map(item => item.id)
      });
    },

    async handleRemoveMaterialDictionary() {
      let selection = this.$refs.materialStoreDetailsDictionary.getSelection();
      if (selection.length === 0) {
        this.$newMessage('error', '请选择要删除的物资');
        return;
      }

      try {
        await this.$newConfirm(
          '您是否确认【<span style="color: red">删除</span>】当前数据？'
        );

        this.form.materialStoreDetailList = this.form.materialStoreDetailList.filter(
          item => !selection.includes(item.id)
        );
      } catch (error) {
        console.error(error);
      }
    },

    handleSubmitMaterialDictionary(data) {
      let formatData = this.handleFormatMaterialDictionary(data);
      this.form.materialStoreDetailList.push(...formatData);

      console.log(cloneDeep(this.form.materialStoreDetailList), '1');
    },

    handleFormatMaterialDictionary(data) {
      return data.map(item => {
        return {
          id: item.id,
          name: item.name,
          categoryName: item.categoryName,
          model: item.model,
          unit: item.unit,
          unitShow: item.unitShow,
          quantity: item.quantity,
          unitprice: item.price || 0,
          amount: 0,
          isCheck: false,

          batchNumber: item.batchNumber,
          productionDate: item.productionDate,
          expirationDate: item.expirationDate,

          regNo: item.regNo,
          brand: item.brand,
          manufacturerName: item.manufacturerName,
          remark: item.remark
        };
      });
    },

    async submit() {
      try {
        this.submitLoading = true;
        await this.$refs.ledgerForm.$refs.form.validate();
        let data = Object.assign({}, this.$refs.ledgerForm.form);

        let API = this.ajax.addEquipmentLedger;
        if (this.isEdit) {
          API = this.ajax.updateEquipmentLedger;
        }

        let res = await API(data);
        this.submitLoading = false;
        if (res.success && res.statusCode === 200) {
          this.$newMessage('success', res.message || this.title + '成功!');
          this.$emit('submit');
          this.close();
        } else {
          this.$newMessage('error', res.message || this.title + '失败!');
        }
      } catch (error) {
        this.submitLoading = false;
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },

    handleBtnOperate(event) {
      this[event]();
    },

    operateAdd() {
      console.log('add');
    },
    operateDel() {
      console.log('del');
    },
    operateSave() {
      console.log('save');
    },
    operateClear() {
      console.log('clear');
    },
    operateAudit() {
      console.log('audit');
    },
    operateCancelAudit() {
      console.log('cancelAudit');
    },
    operateReturn() {
      console.log('return');
    },
    operatePrevious() {
      console.log('previous');
    },
    operateNext() {
      console.log('next');
    },
    operatePrint() {
      console.log('print');
    },
    operateExport() {
      console.log('export');
    },

    close() {
      this.submitLoading = false;
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-material-store {
  ::v-deep {
    .content-container {
      display: flex;
      flex-direction: column;
      justify-content: normal !important;
      background: #fff;

      .form-group-tips {
        width: 100%;
        color: #333;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
        margin: 8px 0;
        background-color: #f2f3f4;
        border-radius: 4px;

        > span {
          font-size: 15px;
          font-weight: 600;
          display: flex;
          align-items: center;

          &::before {
            content: '';
            display: inline-block;
            width: 4px;
            height: 16px;
            background-color: #5260ff;
            margin-right: 4px;
            border-radius: 4px;
          }
        }

        .shallowButton {
          margin-left: 8px;
        }
      }
    }
  }
}
</style>
