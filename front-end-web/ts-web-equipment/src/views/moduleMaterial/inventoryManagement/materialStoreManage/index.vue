<template>
  <div class="material-store-manage-box">
    <warehouse-tabs ref="warehouseTabs" />

    <div class="receipt-container">
      <store-receipt-search
        ref="storeReceiptSearch"
        @search="handleStoreReceiptSearch"
        @add="operateAdd"
        @delete="operateDelete"
        @audit="operateAudit"
        @cancelAudit="operateCancelAudit"
        @returnGoods="operateReturnGoods"
        @print="operatePrint"
        @export="operateExport"
      />

      <store-receipt-table
        ref="storeReceiptTable"
        @details="operateDetails"
        @refresh="handleRefreshStoreReceiptTable"
      />
    </div>

    <receipt-details-table-container type="1" :columns="detailsColumns" />

    <dialog-add-material-store
      ref="dialogAddMaterialStore"
      @submit="handleSubmitMaterialStore"
    />
  </div>
</template>

<script>
import StoreReceiptSearch from './receipt/store-receipt-search.vue';
import StoreReceiptTable from './receipt/store-receipt-table.vue';

import ReceiptDetailsTableContainer from '../components/receipt-details-table-container.vue';
import { detailsColumns } from './details/detailsColumns';
import DialogAddMaterialStore from './dialog/dialog-add-material-store.vue';
import WarehouseTabs from '../components/warehouse-tabs.vue';
export default {
  name: 'MaterialStoreManage',
  components: {
    StoreReceiptSearch,
    StoreReceiptTable,
    ReceiptDetailsTableContainer,
    DialogAddMaterialStore,
    WarehouseTabs
  },
  data() {
    return {
      detailsColumns
    };
  },

  computed: {
    active() {
      return this.$refs.warehouseTabs.active;
    }
  },

  methods: {
    refresh() {
      this.$refs.warehouseTabs.handleGetAllWarehouseList();
      this.handleRefreshStoreReceiptTable();
    },

    handleStoreReceiptSearch() {
      this.$refs.storeReceiptTable.setPageNo(1);
      this.handleRefreshStoreReceiptTable();
    },

    // 新增入库单据
    operateAdd() {
      this.$refs.dialogAddMaterialStore.show({
        type: 'add',
        title: '新增入库单据',
        data: {}
      });
    },

    // 新增入库单据提交完毕事件
    handleSubmitMaterialStore() {
      this.handleRefreshStoreReceiptTable();
    },

    // 删除入库单据
    operateDelete() {},

    // 审核入库单据
    operateAudit() {},

    // 取消审核入库单据
    operateCancelAudit() {},

    // 退货登记
    operateReturnGoods() {},

    // 单据打印
    operatePrint() {},

    // 导出
    operateExport() {},

    // 查看入库单据详情
    operateDetails() {},

    async handleRefreshStoreReceiptTable() {
      if (!this.checkQueryParam()) {
        return;
      }

      let searchForm = this.$refs.storeReceiptSearch.searchForm;
      let { date = [] } = searchForm,
        pageNo = this.$refs.storeReceiptTable.pageNo,
        pageSize = this.$refs.storeReceiptTable.pageSize,
        formData = {
          ...searchForm,
          pageNo,
          pageSize
        };

      const [startD, endD] = date;
      formData.createDateQuery =
        startD && endD ? `${startD} 00:00:00,${endD} 23:59:59` : '';
      !formData.createDateQuery && delete formData.createDateQuery;
      delete formData.date;

      let res = await this.ajax.getMaterialAccountingPeriodList(formData);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }

      let rows = res.rows.map((item, i) => {
        let pageIndex = (pageNo - 1) * pageSize + i + 1;
        return {
          pageIndex,
          ...item
        };
      });

      rows[0].status = '1';
      rows[1].status = '2';
      this.$refs.storeReceiptTable.setPageData({
        ...res,
        rows
      });
      // // 设置选中行 用于编辑、新增
      // if (setRowId) {
      //   let row = rows.find(item => item.id === setRowId);
      //   if (row) {
      //     this.$refs.table.handleSetCurrentRow(row);
      //   }
      //   return;
      // }

      // // 设置第一行选中
      // if (rows.length) {
      //   this.$refs.storeReceiptTable.handleSetCurrentRow(rows[0]);
      // }
    },

    checkData([start, end]) {
      let num = 0;
      start && num++;
      end && num++;
      return num === 1;
    },

    checkQueryParam() {
      const { date = [] } = this.$refs.storeReceiptSearch.searchForm;
      if (this.checkData(date)) {
        this.$newMessage('warning', '请选择入库日期完整的时间区间查询');
        return false;
      }
      return true;
    }
  }
};
</script>

<style scoped lang="scss">
.material-store-manage-box {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;

  ::v-deep {
    .type-tabs-container .el-tabs__item {
      width: auto !important;
    }

    .type-tabs-container .el-tabs__nav-wrap::after {
      height: 0;
    }
    .base-date-range-picker {
      display: inline-flex;
      align-items: center;
      .range-separator {
        margin: 0 4px;
      }
      .date-picker {
        display: flex;
        align-items: center;
        background: transparent;
        width: 140px !important;
      }
    }
  }

  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);
  }

  .receipt-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid#295cf9;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
  }
}
</style>
