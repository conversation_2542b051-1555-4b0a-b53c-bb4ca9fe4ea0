@font-face {
  font-family: "oa-icon"; /* Project id 3031760 */
  src: url('iconfont.woff2?t=1676882479949') format('woff2'),
       url('iconfont.woff?t=1676882479949') format('woff'),
       url('iconfont.ttf?t=1676882479949') format('truetype');
}

.oa-icon {
  font-family: "oa-icon" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.oa-pc-liucheng:before {
  content: "\e60e";
}

.oa-pc-quanping:before {
  content: "\e60a";
}

.oa-pc-tuichuquanping:before {
  content: "\e793";
}

.oa-pc-quanpingtuichu:before {
  content: "\e611";
}

.oa-pc-shanchu:before {
  content: "\e792";
}

.oa-pc-bianji:before {
  content: "\e61e";
}

.oa-pc-jian:before {
  content: "\e60d";
}

.oa-pc-duigouxiao:before {
  content: "\e8bd";
}

