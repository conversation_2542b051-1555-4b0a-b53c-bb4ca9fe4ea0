<template>
  <div class="flex-grow">
    <ts-upload
      v-if="!onlyRead && fileList.length < limit"
      ref="tsUpload"
      action="/ts-basics-bottom/fileAttachment/upload?moduleName=global"
      :drag="drag"
      :limit="limit"
      :fileList.sync="fileList"
      :show-file-list="false"
      :on-exceed="masterFileMax"
      :http-request="handleUploadFile"
    >
      <slot>
        <div v-if="drag" class="file-drag-container flex-center flex-column">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </div>
        <ts-button v-else>{{ buttonName }}</ts-button>
      </slot>
      <div slot="tip" class="el-upload__tip">
        {{ tips }}
      </div>
    </ts-upload>
    <slot name="uploadFiles" :fileList="fileList">
      <ts-upload-file-list
        :fileList.sync="fileList"
        :on-remove="onRemove"
        :on-abort="onAbort"
        :on-upload="onUpload"
        :onPreview="onPreview"
        :showPreview="showPreview"
        :showUpload="showUpload"
        :showRemove="showRemove && !onlyRead"
        :type="fileListType"
      ></ts-upload-file-list>
    </slot>

    <el-image
      ref="preview"
      style="display: none;"
      :src="previewFile"
      :preview-src-list="previewFileList"
      :z-index="3000"
    >
    </el-image>
  </div>
</template>

<script>
import commonUtils from '@/unit/file.js';

export default {
  model: {
    prop: 'businessId',
    event: 'input'
  },
  props: {
    businessId: {
      type: String,
      default: () => ''
    },
    tips: {
      type: String,
      default: () => ''
    },
    onlyRead: {
      type: Boolean,
      default: () => false
    },
    drag: {
      type: Boolean,
      default: () => false
    },
    limit: {
      type: Number,
      default: 99
    },
    actions: {
      type: [Array, String],
      default: () => ['preview', 'downLoad', 'remove']
    },
    fileListType: {
      type: String,
      default: () => 'mixture'
    },
    staticData: {
      type: Array,
      default: () => []
    },
    buttonName: {
      type: String,
      default: () => '文件上传'
    },
    moduleName: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      fileList: [],
      innerBusinessId: '',
      previewFile: '',
      previewFileList: []
    };
  },
  computed: {
    showPreview() {
      let actions = this.actions;
      if (this.actions instanceof String) {
        actions = [this.actions];
      }
      return actions.includes('preview');
    },
    showUpload() {
      let actions = this.actions;
      if (this.actions instanceof String) {
        actions = [this.actions];
      }
      return actions.includes('downLoad');
    },
    showRemove() {
      let actions = this.actions;
      if (this.actions instanceof String) {
        actions = [this.actions];
      }
      return actions.includes('remove');
    }
  },
  watch: {
    businessId: {
      handler(val) {
        this.innerBusinessId = val;
        this.fileList = [];
        if (val) {
          this.getFileList();
        }
        if (!val && this.staticData.length) {
          this.fileList = this.staticData;
        }
      },
      immediate: true
    }
  },
  methods: {
    /**@desc 获取文件列表 */
    getFileList() {
      this.fileList = [];
      this.ajax
        .getFileAttachmentByBusinessId({ businessId: this.innerBusinessId })
        .then(res => {
          if (res.success == false) {
            return;
          }
          this.fileList = res.object.map(item => ({
            ...item,
            uid: item.id,
            url: item.realPath,
            name: item.fileName,
            status: 'success'
          }));

          if (!this.fileList.length) {
            this.$emit('input', null);
          }
        });
    },
    /**@desc 替换原生上传 */
    handleUploadFile(params) {
      let businessId = this.innerBusinessId;
      if (!this.innerBusinessId) {
        businessId = commonUtils.guid();
      }
      let data = new FormData();
      data.append('file', params.file);
      data.append('businessId', businessId);
      this.ajax
        .uploadFile(data, this.moduleName)
        .then(res => {
          if (res.success) {
            this.fileList.push({
              ...res.object[0],
              realPath: res.object[0].filePath,
              url: res.object[0].filePath,
              uid: res.object[0].fileId,
              name: res.object[0].fileRealName
            });

            !this.innerBusinessId && this.$emit('input', businessId);
            this.$emit('action-input', businessId);
          } else {
            this.$message.error(res.message || '上传失败');
          }
        })
        .catch(err => {
          this.$message.error('上传失败，请重试!');
        });
    },

    /**@desc 删除 */
    onRemove(file) {
      let idx = this.fileList.findIndex(e => {
          return e.uid === file.uid;
        }),
        deleteFile = this.fileList[idx] || {};
      this.ajax.deleteFileId({ fileid: deleteFile.uid }).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '删除失败');
          return;
        }
        this.$message.success('删除成功');
        this.fileList.splice(idx, 1);

        if (!this.fileList.length) {
          this.$emit('input', null);
          this.$emit('action-input', null);
        }
      });
    },
    onAbort(file) {
      this.$refs.tsUpload.abort(file);
      let idx = this.fileList.findIndex(e => {
        return e.uid === file.uid;
      });
      this.fileList.splice(idx, 1);
    },
    /**@desc 图片点击下载 */
    onUpload(file) {
      let a = document.createElement('a');
      a.href = file.url;
      a.click();
    },
    /**@desc 图片点击预览 */
    onPreview(file) {
      if (commonUtils.isDoc(file.fileName)) {
        commonUtils.viewerDocBase(file.realPath, file.fileName);
      } else {
        this.previewFile = location.origin + file.url;
        this.previewFileList = this.fileList
          .filter(item => commonUtils.isImg(item.name))
          .map(item => location.origin + item.url);
        this.$nextTick(() => {
          this.$refs.preview.clickHandler();
        });
      }
    },
    masterFileMax() {
      this.$message.warning(`最多上传 ${this.limit} 个文件。`);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-upload-dragger {
    width: 210px !important;
    height: 80px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
      border-color: $theme-color-80 !important;
    }

    &:active {
      border-color: $theme-color-80 !important;
      color: transparent !important;
    }

    &.is-dragover {
      border: 2px dashed $theme-color-80 !important;
      background-color: $list-hover-color !important;
    }

    .file-drag-container {
      width: 100%;
      height: 100%;
      .el-icon-upload {
        margin: 0px;
        padding: 0px;
        font-size: 40px;
        line-height: 44px;
      }

      .el-upload__text {
        em {
          color: $theme-color-80;
        }
      }
    }
  }
  .ts-upload-file-list {
    .picture-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, 104px);
      grid-template-rows: repeat(auto-fill, 104px);
      gap: 8px;
      margin-top: 8px;
      .picture-item {
        width: 100%;
        height: 100%;
        border-radius: 2px;
        position: relative;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
          border-radius: 2px;
        }
        .ts-upload-list__item-actions {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0px;
          top: 0px;
          cursor: default;
          text-align: center;
          color: rgb(255, 255, 255);
          opacity: 0;
          font-size: 20px;
          background-color: rgba(0, 0, 0, 0.5);
          transition: opacity 0.3s ease 0s;
          display: flex;
          align-items: center;
          justify-content: center;
          &:hover {
            opacity: 1;
          }
          .ts-upload-list__item-icon {
            cursor: pointer;
            &:not(:first-child) {
              margin-left: 8px;
            }
            i {
              speak: none;
              font-style: normal;
              font-weight: 400;
              font-variant: normal;
              text-transform: none;
              line-height: 1;
              vertical-align: baseline;
              display: inline-block;
              -webkit-font-smoothing: antialiased;
              font-family: element-icons !important;
            }
          }
        }
      }
    }
    .text-item {
      width: 100%;
      height: 32px;
      position: relative;
      display: flex;
      align-items: center;
      i {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
      }
      .text-item-name {
        width: 100%;
        height: 100%;
        line-height: 32px;
        overflow: hidden;
        text-overflow: ellipsis;
        padding-left: 20px;
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
      }
      .success-text {
        flex-shrink: 0;
        margin-left: 8px;
        a {
          font-size: 14px;
          font-family: MicrosoftYaHei;
          color: rgb(82, 96, 255);
          cursor: pointer;
          &:last-child {
            color: rgb(226, 66, 66);
          }
          &:not(:first-child) {
            margin-left: 8px;
          }
        }
      }
    }
  }
}
</style>
