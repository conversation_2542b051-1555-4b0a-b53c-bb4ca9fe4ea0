<template>
  <div class="dialog-new-message">
    <div class="content">
      <div class="header">
        <div class="flex-col-center">
          <img
            class="message-icon"
            src="@/assets/img/new-message/message.svg"
            width="26"
            height="26"
          />
          消息中心
        </div>
        <div class="flex-col-center">
          <el-popover
            placement="bottom-end"
            trigger="click"
            popper-class="message-action-popover"
          >
            <i slot="reference" class="el-icon-setting"></i>
            <div class="action-item" @click="handleMarkAllRead">
              标记全部已读
            </div>
            <div class="action-item" @click="handleMarkAllClear">
              全部清空
            </div>
          </el-popover>
          <img
            class="close-icon"
            src="@/assets/img/new-message/close.svg"
            width="12"
            height="12"
            @click="handleClose"
          />
        </div>
      </div>
      <div class="bottom">
        <div class="left-type">
          <el-scrollbar style="height: 100%">
            <ul>
              <li
                v-for="(item, index) of messageTypeList"
                :key="index"
                class="type-item"
                :class="{
                  active: messageType == item.source
                }"
                @click="handleMessageTypeChange(item)"
              >
                <i class="oa-icon oa-pc-liucheng"></i>
                <div class="type-name">
                  {{ item.source }}
                </div>
                <div v-if="item.unReadCount" class="no-read-tips">
                  {{ item.unReadCount }}
                </div>
              </li>
            </ul>
          </el-scrollbar>
        </div>
        <div class="message-tips-right">
          <el-scrollbar ref="dialogScroll" style="height: 100%">
            <div v-show="loading" class="show-history-message">
              <i class="el-icon-loading"></i>
              加载中...
            </div>
            <div
              v-show="!finished && !loading"
              class="show-history-message"
              @click="getMessageDialogList"
            >
              <i class="el-icon-time"></i>
              点击查看更多
            </div>

            <div
              v-for="(item, index) of dialogMessageList"
              :key="index"
              class="message-item"
              :class="{
                readed: item.browserId
              }"
            >
              <p class="time">{{ item.createTime }}</p>
              <div class="message-content" @click="handleReadDetail(item)">
                <div class="message-info">
                  <div class="message-title">
                    <p style="display: flex;justify-content: space-between;">
                      {{ item.subject }}
                      <span @click.stop="handleDeleteDetail(item)">
                        <i class="el-icon-close"></i>
                      </span>
                    </p>
                    <p>
                      {{ item.content }}
                    </p>
                  </div>
                  <div class="message-create-info">
                    <p>操作人：{{ item.senderName }}</p>
                    <p>消息时间：{{ item.createTime }}</p>
                  </div>
                </div>
                <div class="message-item-action">
                  <span>查看详情</span>
                  <i>></i>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import configJs from '@/views/index/components/process/indexConfig.js';
import { deepClone } from '@/utils/deepClone';
export default {
  mixins: [configJs],
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    show: {
      type: Boolean
    }
  },
  data() {
    return {
      timer: null,
      visible: false,

      messageType: '',
      messageTypeList: [],

      pageNo: 1,
      finished: false,
      loading: false,
      dialogMessageList: []
    };
  },
  created() {
    this.timer = setInterval(() => {
      //监听子页面关闭事件,轮询时间1000毫秒
      let openWin = this.indexWinSon.filter(item => {
        return !item.closed;
      });
      if (openWin && openWin.length < this.indexWinSon.length) {
        this.indexWinSon = deepClone(openWin);
        this.$root.$emit('handleRefreshIndexProcess');
      }
    }, 1000);
  },
  destroyed() {
    this.timer && clearInterval(this.timer);
  },
  methods: {
    async refresh() {
      this.messageType = '';
      await this.getMessageTypeList();
      this.pageNo = 1;
      this.finished = false;
      this.dialogMessageList = [];
      this.getMessageDialogList();
    },
    async getMessageTypeList() {
      let res = await this.ajax.getMessageTypeList({
        pageNo: 1,
        pageSize: 15
      });

      if (res.success) {
        if (!this.showEmail) {
          let emailIndex = res.object.findIndex(f => f.source === '电子邮箱');
          if (emailIndex > -1) res.object.splice(emailIndex, 1);
        }

        this.messageTypeList = res.object;

        !this.messageType && (this.messageType = (res.object[0] || {}).source);
      }
    },
    async getMessageDialogList() {
      if (this.finished) {
        return;
      }
      this.loading = true;
      let oldScrollHeight = this.$refs.dialogScroll.wrap.scrollHeight,
        res = await this.ajax.getMessageDialogList({
          pageNo: this.pageNo,
          pageSize: 15,
          source: this.messageType
        });
      this.loading = false;
      if (res.success == false) {
        this.$message.error(res.message || '信息获取失败');
        return;
      }
      let list = res.rows.reverse();
      this.dialogMessageList = list.concat(this.dialogMessageList);
      this.finished = this.pageNo >= res.pageCount;
      if (this.pageNo == 1) {
        this.$nextTick(() => {
          this.$refs.dialogScroll.wrap.scrollTop = this.$refs.dialogScroll.wrap.scrollHeight;
        });
      } else {
        this.$nextTick(() => {
          this.$refs.dialogScroll.wrap.scrollTop =
            this.$refs.dialogScroll.wrap.scrollHeight - oldScrollHeight;
        });
      }
      this.pageNo++;
    },
    handleMessageTypeChange(item) {
      this.messageType = item.source;
      this.pageNo = 1;
      this.finished = false;
      this.dialogMessageList = [];
      this.getMessageDialogList();
    },
    async handleDeleteDetail(item) {
      const result = await this.ajax.deleteBrowser({ noticeId: item.id });
      const { success, statusCode } = result;
      if (success && statusCode === 200) {
        this.$store.dispatch('common/getMessageCount');
        this.pageNo = 1;
        this.finished = false;
        this.dialogMessageList = [];
        this.getMessageDialogList();
      }
    },
    async handleReadDetail(item) {
      const { source, toUrl, id, businessId, subject } = item;
      // 未读设置已读
      if (!item.browserId) {
        const result = await this.ajax.saveNoticeBrowser({ noticeId: id });

        const { success, statusCode } = result;
        if (success && statusCode === 200) {
          this.$store.dispatch('common/getMessageCount');
          this.getMessageTypeList();
        }
      }

      if (source == '信息管理') {
        let res = await this.ajax.selectInformationContentDetails(businessId);
        if (res.success == false) {
          this.$message.error(res.message || '信息发布详情获取失败');
          return;
        }
        let row = res.object || {};
        if (row.validendTime) {
          const validendTime = new Date(row.validendTime.replace(/-/g, '/'));
          if (new Date() > validendTime) {
            this.$message.warning('该条信息已过期，不能进行查看!');
            return;
          }
        }

        this.$router.push('/ts-web-oa/information-manage/message-read');
        this.$root.$emit('broadcastInformation', {
          event: 'informationMessageRead',
          data: {
            path: '/message-read',
            businessId,
            type: 'information-details'
          }
        });
        return;
      }
      this.handleClose();

      let messageInfo = await this.ajax.getMessageActionInfo({
        businessId,
        source
      });
      if (messageInfo.success && source == '流程管理') {
        let tabIndex = {
          流程待办提醒: 1,
          流程抄送提醒: 4,
          流程办结提醒: 4,
          流程退回提醒: 4,
          流程终止提醒: 4
        }[subject];
        if (tabIndex == 4) {
          let res = await this.handleCopyProcessAsRead(
            messageInfo.object.copyId
          );
          if (res.success && res.statusCode) {
            this.$root.$emit('handleRefreshIndexProcess');
          }
        }
        this.processDeal(messageInfo.object, tabIndex);
        return;
        this.$root.$emit('sendMessageToOldFrame', {
          detail: {
            type: 'mainMessage',
            data: {
              key: 'messageToastEvent',
              value: {
                subject: messageInfo.object.taskId ? subject : '流程办结提醒',
                source,
                businessId,
                data: messageInfo.object || {}
              }
            }
          }
        });
        return;
      }
      if (source == '电子邮箱') {
        this.$router.push('/ts-web-oa/email/emailManagement');

        // this.$root.$emit('broadcastInformation', {
        //   event: 'messageToastEvent',
        //   data: {
        //     path: '/emailManagement',
        //     id: businessId,
        //     statusId: item.statusId
        //   }
        // });
        return false;
      }
      if (source === '工单管理') {
        this.$router.push(
          '/ts-web-work-order/workSheet/workOrderHomePage/applicantPerson'
        );
        window.dispatchEvent(
          new CustomEvent('qiankunOpenWorkInfoHandle', {
            detail: {
              workNumber: toUrl.split('#').pop()
            }
          })
        );
      } else {
        this.$router.push(toUrl);
      }
      if (toUrl.startsWith('/ts-web')) {
        this.$mainMessageStore.setGlobalState({
          event: 'messageToastEvent',
          data: {
            path: toUrl,
            businessId,
            data: messageInfo.object || {}
          }
        });
      } else {
        this.$root.$emit('sendMessageToOldFrame', {
          detail: {
            type: 'mainMessage',
            data: {
              key: 'messageToastEvent',
              value: {
                path: toUrl,
                source,
                businessId,
                isChangeRouterMessage: true,
                data: messageInfo.object || {}
              }
            }
          }
        });
      }
    },
    async handleMarkAllRead() {
      const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        }),
        result = await this.ajax.setAllNoticeBrowser();
      loading.close();

      const { success, statusCode } = result;
      if (success && statusCode === 200) {
        this.$store.dispatch('common/getMessageCount');
        this.$message.success('标记成功');
      }
      this.refresh();
    },
    async handleMarkAllClear() {
      const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        }),
        result = await this.ajax.setAllClearBrowser();
      loading.close();

      const { success, statusCode } = result;
      if (success && statusCode === 200) {
        this.$store.dispatch('common/getMessageCount');
        this.$message.success('清空成功');
      }
      this.refresh();
    },
    handleClose() {
      this.$emit('change', false);
    }
  },
  watch: {
    show: {
      handler(val) {
        if (val) {
          this.refresh();
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-new-message {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0%;
  top: 0%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.5s;
  z-index: 999;
  * {
    box-sizing: border-box;
  }

  .content {
    width: 900px;
    height: 648px;
    overflow: hidden;
    background: #fff;
    border-radius: 6px;

    .header {
      border-radius: 6px 6px 0 0;
      background-color: #f9f9f9;
      border-bottom: 1px solid #e9e9e9;
      padding: 0 20px;
      color: #666;
      height: 48px;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .message-icon {
        margin-right: 4px;
      }

      .close-icon {
        cursor: pointer;
      }
      .el-icon-setting {
        font-size: 16px;
        margin-right: 8px;
        color: #999;
        cursor: pointer;
        margin-top: 2px;
      }
    }

    .bottom {
      height: 600px;
      display: flex;
      border-radius: 0px 0px 6px 6px;

      .left-type {
        width: 225px;
        height: 100%;
        background: #fff;
        border-right: 1px solid #e2e2e2;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .type-item {
          flex-shrink: 0;
          padding: 0 20px;
          width: 100%;
          height: 40px;
          display: flex;
          align-items: center;
          color: #333;
          cursor: pointer;
          overflow: hidden;
          border-bottom: 1px solid #e2e2e2;

          &.active {
            background-color: color-mix($theme-color, #fff, 10%);
          }

          .oa-pc-liucheng {
            color: $theme-color;
            font-size: 22px;
            margin-right: 20px;
          }

          .type-name {
            flex: 1;
            overflow: hidden;
            -o-text-overflow: ellipsis;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .no-read-tips {
            background-color: #ff3b30;
            color: #fff;
            font-size: 12px;
            padding: 0 8px;
            border-radius: 8px;
          }
        }
      }

      .message-tips-right {
        padding: 12px;
        width: 675px;
        height: 100%;
        border-right: 1px solid #e2e2e2;
        overflow: hidden;

        .show-history-message {
          text-align: center;
          cursor: pointer;
          color: #1396ff;
        }

        .message-item {
          width: 500px;
          margin: 0 auto;

          &.readed .message-content .message-title p {
            color: #999;
          }

          .time {
            text-align: center;
            color: #999;
            margin: 8px 0;
            font-weight: 600;
            font-size: 12px;
          }

          .message-content {
            height: 180px;
            padding: 8px;
            padding-bottom: 0;
            background: #fff;
            border: 1px solid #e2e2e2;
            display: flex;
            flex-direction: column;
            cursor: pointer;

            .message-info {
              flex: 1;
              display: flex;
              flex-direction: column;
              justify-content: space-between;

              .message-title p {
                font-weight: 600;
                &:nth-child(2) {
                  text-overflow: ellipsis;
                  line-clamp: 2;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                }
              }

              .message-create-info {
                color: #999;
              }
            }

            .message-item-action {
              flex-shrink: 0;
              margin-top: 16px;
              padding: 8px 0;
              border-top: 1px solid #eee;
              color: #999;
              display: flex;
              justify-content: space-between;
            }
          }
        }
      }
    }
  }
}
.action-item {
  text-align: center;
  cursor: pointer;
  line-height: 30px;
  white-space: nowrap;
  &:hover {
    background-color: rgba($theme-color, 0.1);
  }
}
</style>

<style lang="scss">
.el-popover.message-action-popover {
  min-width: unset;
  padding: 8px 0;
  width: 120px;
}
</style>
