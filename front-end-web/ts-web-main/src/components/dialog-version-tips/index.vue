<template>
  <div class="dialog-version-tips">
    <div class="content">
      <div class="tips-title">发现新版本</div>
      <span class="item-label">
        最新版本号:
        <span class="value"
          >{{ versionInfo.version || '空' }} ({{
            versionInfo.versionDate || ''
          }})</span
        >
      </span>
      <div class="title-content-tips">更新内容:</div>
      <div class="update-content" v-html="versionInfo.content"></div>
      <div class="xian">
        <div class="submit" @click="handleView">确认</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    show: {
      type: Boolean
    },
    versionInfo: {
      type: Object
    }
  },
  data() {
    return {
      visible: false
    };
  },
  methods: {
    async handleView() {
      const res = await this.ajax.versionRecordSave({
        versionId: this.versionInfo.id
      });
      if (res.success == false) {
        this.$message.error(res.message || '确认查看版本失败!');
        return;
      }

      const dom = document.getElementsByClassName('dialog-version-tips')[0];
      dom.style.left = '100%';

      setTimeout(() => {
        this.visible = false;
        this.$emit('change', false);
      }, 500);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-version-tips {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0%;
  top: 0%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.5s;
  .content {
    width: 500px;
    border-radius: 4px;
    background-color: #fff;
    padding: 8px 16px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    position: relative;
    .tips-title {
      text-align: center;
      font-size: 22px;
      color: #000;
      margin-bottom: 8px;
    }
    .item-label {
      font-size: 14px;
      margin-bottom: 8px;
      color: #999;
      .value {
        color: #000;
      }
    }
    .title-content-tips {
      font-size: 16px;
      font-weight: 600;
      padding-top: 8px;
      margin-bottom: 8px;
      border-top: 1px solid #eee;
    }
    .update-content {
      min-height: 100px;
      max-height: 290px;
      width: 100%;
      font-size: 14px;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 6px;
      }
      // 滚动条的滑块
      &::-webkit-scrollbar-thumb {
        border-radius: 3px;
        background: rgba(153, 153, 153, 0.4);
        &:hover {
          background: rgba(153, 153, 153, 0.8);
        }
      }
    }
    .xian {
      margin-top: 8px;
      padding-top: 8px;
      width: 100%;
      border-top: 1px solid #eee;
      display: flex;
      justify-content: flex-end;
      .submit {
        width: 60px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        border-radius: 4px;
        font-size: 12px;
        background-color: #5260ff;
        color: #fff;
        cursor: pointer;
      }
    }
  }
}
</style>
