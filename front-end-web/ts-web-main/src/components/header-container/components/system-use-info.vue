<template>
  <el-dialog
    custom-class="system-use-info"
    title="今日系统使用情况"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="system-dialog-title" slot="title">
      <span class="title">系统使用情况</span>

      <el-popover
        placement="top-start"
        popper-class="system-use-info-popover"
        title="统计指标说明："
        width="460"
        trigger="hover"
      >
        <div>
          <p>当前在线人数：当前PC端和移动端 (未退出)在线人数</p>
          <p>
            累计访问人数： 使用过系统的人员累计
            一个账号多次登陆，多端(移动端，PC端) 登陆，只算作一次记录。
          </p>
          <p>PC端访问人数：PC端使用过系统的人员统计</p>
          <p>移动端访问人数：移动端使用过系统的人员统计</p>
          <p>累计访问次数：登录 (含重复) 访问系统的次数 (含PC端和移动端)</p>
        </div>
        <img
          class="tips-img"
          src="@/assets/img/tips-img.svg"
          alt=""
          slot="reference"
        />
      </el-popover>
      <span class="title">({{ hospitolName }})</span>
    </div>
    <div class="content">
      <div class="left-type">
        <el-form
          ref="form"
          :model="form"
          label-width="50px"
          @submit.native.prevent
        >
          <el-col class="search-container">
            <el-form-item label="日期">
              <el-date-picker
                v-model="form.queryDate"
                type="date"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :clearable="false"
                placeholder="选择日期"
                @change="queryDateChange"
                style="width: 140px;"
                :disabled="useType == '5' || useType == '6'"
              />
            </el-form-item>
          </el-col>
        </el-form>
        <ul class="ul-container">
          <li
            v-for="(item, index) of useTypeList"
            :key="index"
            class="type-item"
            :class="{
              active: useType == item.useType
            }"
            @click="handleChangeType(item)"
          >
            <div class="type-name">
              {{ item.label }}
              <span v-if="item.useType != '5' && item.useType != '6'"
                >（{{ item.value }}）</span
              >
            </div>
          </li>
        </ul>

        <p class="tips-container" v-if="useType != undefined">
          {{ useTypeList[useType].tips }}
        </p>
      </div>
      <div
        class="right-table"
        v-loading="loading"
        v-if="resourceId == 'canSeeCount'"
      >
        <div class="top-search-form" v-if="useType != '5'">
          <el-form
            ref="form"
            :model="form"
            label-width="50px"
            @submit.native.prevent
          >
            <el-col class="search-container">
              <el-form-item label="姓名:" v-if="useType != '6'">
                <el-input
                  v-model="form.name"
                  placeholder="请输入姓名"
                  style="width: 150px;"
                />
              </el-form-item>
              <el-form-item label="日期:" v-if="useType == '6'">
                <el-date-picker
                  v-model="form.queryDateRang[0]"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  :clearable="false"
                  placeholder="选择日期"
                  @change="queryDateChangeRange"
                  style="width: 140px;"
                />
              </el-form-item>
              <el-form-item
                label=" ~ "
                v-if="useType == '6'"
                label-width="30px"
              >
                <el-date-picker
                  v-model="form.queryDateRang[1]"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  :clearable="false"
                  placeholder="选择日期"
                  @change="queryDateChangeRange"
                  style="width: 140px;"
                />
              </el-form-item>
              <el-button
                class="ts-btn primary"
                @click="handleSearch"
                v-if="useType != '6'"
              >
                搜索
              </el-button>
              <span
                @click="handleReset"
                class="el-icon-refresh"
                v-if="useType != '6'"
              ></span>
              <el-button
                v-if="useType == '6'"
                class="ts-btn primary exportDept"
                @click="exportDeptTable"
              >
                导出
              </el-button>
              <el-button
                v-if="useType == '6' && showUserTable"
                class="ts-btn primary export"
                @click="exportUserTable"
              >
                导出
              </el-button>
            </el-col>
          </el-form>
        </div>
        <div class="title-year" v-if="useType == '5'">
          <el-date-picker
            style="width: 100px;margin-right: 3px;"
            v-model="queryYear"
            ref="yearHide"
            :append-to-body="true"
            value-format="yyyy"
            type="year"
            :clearable="false"
            popper-class="select-date-year-set-style"
            placeholder="请选择年度"
            v-clickoutside="yearHide"
            @change="renderSubAndFinishedTrendCell"
          >
          </el-date-picker>
          年度系统使用趋势图
          <el-popover
            placement="top-start"
            popper-class="system-use-info-popover"
            title="年度系统使用趋势图说明："
            width="460"
            trigger="hover"
          >
            <div>
              <p>月累计平均每天访问人数:(月累计访问人数÷当月天数)</p>
              <p>
                月累计平均每天PC端访问次数:(月累计PC端访问次数÷当月天数)
              </p>
              <p>
                月累计平均每天移动端访问次数:(月累计移动端访问次数÷当月天数)
              </p>
            </div>
            <img
              class="tips-img"
              src="@/assets/img/tips-img.svg"
              alt=""
              slot="reference"
            />
          </el-popover>
        </div>
        <div class="right-table-content echart" v-if="useType == '5'">
          <div ref="MonthlyAdmissionDeparture" class="cell-echarts"></div>
          <div>在职人数：{{ totalCount }}</div>
        </div>
        <div class="right-table-content" v-show="useType != '5'">
          <!-- 原始表单 -->
          <ts-table
            v-show="useType != '6'"
            ref="table"
            v-bind="inlineTableProps"
            @refresh="refreshPeopleInfoTable"
          />
          <!-- 科室使用分析表单 -->
          <ts-table
            ref="deptTable"
            v-bind="deptTableProps"
            @refresh="refreshDeptInfoTable"
            @userTable="userTable"
            class="deptTable"
            v-show="useType == '6'"
          />
          <!-- 人员表单 -->
          <ts-table
            v-show="showUserTable"
            ref="userTable"
            v-bind="userTableProps"
            @refresh="refreshUserInfoTable"
            style="width: calc(42% - 20px); margin-left: 10px"
          />
          <div
            class="list-no-data-imgs"
            v-show="useType == '6' && !showUserTable"
          >
            <img src="@/assets/img/noData.svg" />
            <div>点击蓝色字体可查看统计详情</div>
          </div>
        </div>
      </div>
      <div class="right-table noData" v-else>
        <div class="list-no-data-img">
          <img src="@/assets/img/noData.svg" />
          <div>您暂无查看权限</div>
        </div>
      </div>
    </div>
    <div slot="footer" class="inline-dialog-footer">
      <el-button class="ts-btn" @click="visible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import tsTable from '../../ts-table.vue';
import { $api } from '@/api/ajax.js';
import { deepClone } from '@/utils/deepClone.js';
import { downloadXlsx } from './config';
import moment from 'moment';
export default {
  components: {
    tsTable
  },
  data() {
    return {
      visible: false,
      useType: undefined,
      loading: false,
      content: false,
      loginStat: {},
      activeTableData: [],
      activeTableDataUser: [],
      activeTableDataDept: [],
      showUserTable: false,
      useTypeList: [
        {
          label: '当前在线人数 ',
          value: '0',
          useType: '0',
          tips: '当前在线人数：当前PC端和移动端 (未退出)在线人数'
        },
        {
          label: '累计访问人数 ',
          value: '0',
          useType: '1',
          tips:
            '累计访问人数： 使用过系统的人员累计 一个账号多次登陆，多端(移动端，PC端) 登陆，只算作一次记录。'
        },
        {
          label: 'PC端访问次数 ',
          value: '0',
          useType: '2',
          tips: 'PC端访问次数：PC端使用过系统的次数统计'
        },
        {
          label: '移动端访问次数 ',
          value: '0',
          useType: '3',
          tips: '移动端访问次数：移动端使用过系统的次数统计'
        },
        {
          label: '累计访问次数 ',
          value: '0',
          useType: '4',
          tips: '累计访问次数：登录 (含重复) 访问系统的次数 (含PC端和移动端)'
        },
        {
          label: '年度使用趋势图 ',
          value: '0',
          useType: '5',
          tips: ''
        },
        {
          label: '科室使用分析 ',
          value: '0',
          useType: '6',
          tips: ''
        }
      ],
      form: {
        name: '',
        queryDate: moment().format('YYYY-MM-DD'),
        queryDateRang: [
          moment().format('YYYY-MM-DD'),
          moment().format('YYYY-MM-DD')
        ]
      },
      now: moment().format('YYYY-MM-DD'),
      queryYear: moment().format('YYYY'),
      //在线人员表格
      inlineTableProps: {
        hasPage: false,
        columns: [
          {
            label: '序号',
            prop: 'index',
            align: 'center',
            width: 60
          },
          {
            label: '姓名',
            prop: 'userName',
            align: 'center',
            width: 140
          },
          {
            label: '科室',
            prop: 'orgName',
            align: 'center'
          },
          {
            label: '登录IP',
            align: 'center',
            prop: 'loginIp',
            width: 140
          },
          {
            label: '登录时间',
            align: 'center',
            prop: 'loginTime',
            width: 140,
            formatter: (row, column, cellValue, index) => {
              return (
                (cellValue && cellValue.slice(0, cellValue.length - 3)) || ''
              );
            },
            width: 180
          }
        ]
      },
      //科室使用分析表格
      deptTableProps: {
        hasPage: false,
        showSummary: true,
        columns: [
          {
            label: '序号',
            prop: 'index',
            align: 'center',
            width: 50
          },
          {
            label: '科室',
            prop: 'name',
            align: 'center',
            formatter: (row, column, cellValue, index) => {
              return (
                <span
                  class="details-span"
                  onClick={() => {
                    this.userTable(row);
                  }}>
                  {row.name}
                </span>
              );
            }
          },
          {
            label: '总人数',
            prop: 'totalNumbers',
            align: 'center',
            width: 80
          },
          {
            label: '使用人数',
            align: 'center',
            prop: 'useNumbers',
            sortable: 'true',
            sortIndex: 'useNumbers',
            width: 110,
            formatter: (row, column, cellValue, index) => {
              return (
                <span
                  class="details-span"
                  onClick={() => {
                    this.userTable(row, 1);
                  }}>
                  {row.useNumbers}
                </span>
              );
            }
          },
          {
            label: '未使用人数',
            align: 'center',
            prop: 'unUseNumbers',
            sortable: 'true',
            sortIndex: 'unUseNumbers',
            width: 120,
            formatter: (row, column, cellValue, index) => {
              return (
                <span
                  class="details-span"
                  onClick={() => {
                    this.userTable(row, 2);
                  }}>
                  {row.unUseNumbers}
                </span>
              );
            }
          },
          {
            label: '使用占比',
            align: 'center',
            prop: 'unUseRate',
            sortable: 'true',
            sortIndex: 'unUseRate',
            width: 110,
            formatter: (row, column, cellValue, index) => {
              return <span>{row.unUseRate}%</span>;
            }
          }
        ]
      },
      //未使用人表格
      userTableProps: {
        hasPage: false,
        columns: [
          {
            label: '序号',
            prop: 'index',
            align: 'center',
            width: 50
          },
          {
            label: '工号',
            align: 'center',
            prop: 'employee_no',
            width: 80
          },
          {
            label: '姓名',
            prop: 'employee_name',
            align: 'center',
            width: 110
          },
          {
            label: '科室',
            prop: 'name',
            align: 'center',
            width: 100
          },
          {
            label: '状态',
            align: 'center',
            prop: 'create_time',
            width: 70,
            formatter: (row, column, cellValue, index) => {
              let status = row.create_time ? '已使用' : '未使用';
              return <span>{status}</span>;
            }
          }
        ]
      },
      MonthlyAdmissionDeparture: null,
      resourceId: '',
      monthLabel: [
        '1月',
        '2月',
        '3月',
        '4月',
        '5月',
        '6月',
        '7月',
        '8月',
        '9月',
        '10月',
        '11月',
        '12月'
      ],
      totalCount: 0
    };
  },
  computed: {
    userInfo() {
      return this.$store.state.common.userInfo;
    },
    hospitolName() {
      return this.$store.state.common.globalSetting.webTitle;
    }
  },
  methods: {
    async getMenuSource() {
      let activeMenu = this.$store.state.common.menuLineList.filter(
        item => item.alink == '/index'
      )[0];
      if (!activeMenu) {
        return [];
      }

      let res = await this.ajax.getUserMenuSourceData(activeMenu.id);
      this.resourceId = res.length ? res[0].resourceId : '';
    },
    async open() {
      this.visible = true;
      this.useType = '0';
      await this.getMenuSource();
      this.handleGetLoginSate();
      this.handleChangeType({ useType: '0' });
    },
    userTable(row, useStatus) {
      let param = {
        organizationId: row != {} ? row.organization_id : '',
        useStatus: useStatus
      };
      if (useStatus == '') delete params.useStatus;
      if (param.organizationId == '') delete params.organizationId;
      this.showUserTable = true;
      this.$nextTick(() => {
        this.refreshUserInfoTable(param);
      });
    },
    exportDeptTable() {
      let records = this.activeTableDataDept,
        headTitle = [
          {
            label: '科室',
            prop: 'name',
            width: 120
          },
          {
            label: '总人数',
            prop: 'totalNumbers',
            width: 100
          },
          {
            label: '使用人数',
            prop: 'useNumbers',
            width: 100
          },
          {
            label: '未使用人数',
            prop: 'unUseNumbers',
            width: 100
          },
          {
            label: '未使用占比',
            prop: 'unUseRate',
            width: 100
          }
        ],
        merges = [],
        rows = [],
        cols = [],
        tableList = [];
      headTitle.forEach(i => {
        cols.push({ wpx: i.width });
      });
      let timeArr =
        moment(this.form.queryDateRang[0]).format('YYYY年MM月DD日') +
        '-' +
        moment(this.form.queryDateRang[1]).format('YYYY年MM月DD日');
      tableList.push([`科室使用分析表(${timeArr})`]);
      tableList.push(headTitle.map(e => e.label));
      records.forEach(e => {
        let row = [];
        headTitle.forEach(item => {
          if (item.prop == 'unUseRate') {
            row.push(e[item.prop] + '%');
          } else {
            row.push(e[item.prop]);
          }
        });
        tableList.push(row);
      });
      rows = tableList.map(e => {
        return { hpx: 20 };
      });
      tableList.push([
        '导出人：' +
          this.userInfo.employeeName +
          '      导出时间：' +
          moment().format('YYYY年MM月DD日')
      ]);
      merges.push({ s: { r: 0, c: 0 }, e: { r: 0, c: 4 } });
      merges.push({
        s: { r: records.length + 2, c: 0 },
        e: { r: records.length + 2, c: 4 }
      });
      //导出插件使用
      downloadXlsx(tableList, '科室使用分析.xlsx', merges, cols, rows);
    },
    exportUserTable() {
      let records = this.activeTableDataUser,
        headTitle = [
          {
            label: '工号',
            prop: 'employee_no',
            width: 120
          },
          {
            label: '姓名',
            prop: 'employee_name',
            width: 80
          },
          {
            label: '科室',
            prop: 'name',
            width: 120
          },
          {
            label: '状态',
            prop: 'create_time',
            width: 80
          }
        ],
        merges = [],
        rows = [],
        cols = [],
        tableList = [];
      headTitle.forEach(i => {
        cols.push({ wpx: i.width });
      });
      let timeArr =
        moment(this.form.queryDateRang[0]).format('YYYY年MM月DD日') +
        '-' +
        moment(this.form.queryDateRang[1]).format('YYYY年MM月DD日');
      tableList.push([`人数使用分析(${timeArr})`]);
      tableList.push(headTitle.map(e => e.label));
      records.forEach(e => {
        let row = [];
        headTitle.forEach(item => {
          if (item.prop == 'create_time') {
            let status = e[item.prop] ? '已使用' : '未使用';
            row.push(status);
          } else {
            row.push(e[item.prop]);
          }
        });
        tableList.push(row);
      });
      rows = tableList.map(e => {
        return { hpx: 20 };
      });
      tableList.push([
        '导出人：' +
          this.userInfo.employeeName +
          '      导出时间：' +
          moment().format('YYYY年MM月DD日')
      ]);
      merges.push({ s: { r: 0, c: 0 }, e: { r: 0, c: 3 } });
      merges.push({
        s: { r: records.length + 2, c: 0 },
        e: { r: records.length + 2, c: 3 }
      });
      //导出插件使用
      downloadXlsx(tableList, '人数使用分析.xlsx', merges, cols, rows);
    },
    handleChangeType({ useType }) {
      if (this.loading) return;
      this.useType = useType;
      this.form.name = '';
      if (useType == '5') {
        this.$nextTick(() => {
          this.renderSubAndFinishedTrendCell();
        });
      } else if (useType == '6') {
        this.MonthlyAdmissionDeparture = null;
        this.$nextTick(() => {
          this.refreshDeptInfoTable();
        });
      } else {
        this.MonthlyAdmissionDeparture = null;
        this.$nextTick(() => {
          this.refreshPeopleInfoTable();
        });
      }
      this.showUserTable = false;
      this.handleGetLoginSate();
    },
    yearHide() {
      this.$refs.yearHide.hidePicker();
    },
    queryDateChange() {
      this.handleGetLoginSate();
      this.refreshPeopleInfoTable();
    },
    queryDateChangeRange() {
      if (
        moment(this.form.queryDateRang[0]) > moment(this.form.queryDateRang[1])
      ) {
        this.$message.warning('开始日期不能大于结束日期');
        this.form.queryDateRang[0] = this.form.queryDateRang[1];
      }
      this.refreshDeptInfoTable();
      this.showUserTable = false;
    },
    handleSearch() {
      let name = this.form.name;
      let filterArr = this.activeTableData.filter(item => {
        return item.userName && item.userName.includes(name);
      });
      this.$refs.table.tableData = (filterArr || []).map((item, i) => {
        return {
          ...item,
          index: i + 1
        };
      });
    },
    handleReset() {
      this.form.name = '';
      this.$refs.table.tableData = deepClone(this.activeTableData);
    },
    async handleGetLoginSate() {
      let res = await $api({
        url: `/ts-basics-bottom/system/login/stat?queryDate=${this.form.queryDate}`,
        method: 'get'
      });
      if (res.success == false) {
        this.$message.error(
          res.message || '获取登录日志统计接口失败,请联系管理员'
        );
        return;
      }
      let { onlines, logins, loginsByPc, loginsByWx, accesses } = res.object;
      this.$root.$emit('inlinePeopleCountChange', onlines);
      this.$set(this.useTypeList[0], 'value', onlines);
      this.$set(this.useTypeList[1], 'value', logins);
      this.$set(this.useTypeList[2], 'value', loginsByPc);
      this.$set(this.useTypeList[3], 'value', loginsByWx);
      this.$set(this.useTypeList[4], 'value', accesses);
    },

    async refreshPeopleInfoTable(searchData = {}) {
      this.loading = true;
      let res = await $api({
        url: '/ts-basics-bottom/system/login/log',
        method: 'get',
        params: {
          type: this.useType,
          queryDate: this.form.queryDate,
          ...searchData
        }
      });
      if (res.success == false) {
        this.$message.error(
          res.message || '当前登录日志列表获取失败，请联系管理员'
        );
        return;
      }

      this.activeTableData = (res.object || []).map((item, i) => {
        return {
          ...item,
          index: i + 1
        };
      });

      this.$refs.table.tableData = deepClone(this.activeTableData);
      this.loading = false;
    },
    // 科室表单
    async refreshDeptInfoTable(searchData = {}) {
      this.loading = true;
      let [startDate, endDate] = this.form.queryDateRang;
      let res = await $api({
        url: '/ts-basics-bottom/system/login/sysUseAnalysisList',
        method: 'get',
        params: {
          ...searchData,
          startDate,
          endDate
        }
      });
      if (res.success == false) {
        this.$message.error(
          res.message || '当前登录日志列表获取失败，请联系管理员'
        );
        return;
      }

      this.activeTableDataDept = (res.object || []).map((item, i) => {
        return {
          ...item,
          index: i + 1
        };
      });

      this.$refs.deptTable.tableData = deepClone(this.activeTableDataDept);
      this.loading = false;
    },
    // 人员表单
    async refreshUserInfoTable(searchData = {}) {
      this.loading = true;
      let [startDate, endDate] = this.form.queryDateRang;
      let params = {
        startDate,
        endDate,
        ...searchData
      };
      let res = await $api({
        url: '/ts-basics-bottom/system/login/sysUseAnalysisDetailList',
        method: 'get',
        params
      });
      if (res.success == false) {
        this.$message.error(
          res.message || '当前登录日志列表获取失败，请联系管理员'
        );
        return;
      }

      this.activeTableDataUser = (res.object || []).map((item, i) => {
        return {
          ...item,
          index: i + 1
        };
      });

      this.$refs.userTable.tableData = deepClone(this.activeTableDataUser);
      this.loading = false;
    },
    setSeries(lineList, dataList) {
      let list = [];
      let color = ['#395CBF', '#E76D24', '#00FF40'];
      lineList.forEach((item, index) => {
        let keyName =
          index == 0
            ? 'loginsCount'
            : index == 1
            ? 'loginsPcNumbers'
            : 'loginsWxNumbers';
        let series = {
          name: item,
          type: 'line',
          itemStyle: {
            normal: {
              color: color[index],
              lineStyle: {
                color: color[index],
                width: 1
              }
            }
          },
          data: dataList[keyName]
        };
        list.push(series);
      });
      return list;
    },
    async renderSubAndFinishedTrendCell() {
      let res = await $api({
        url: '/ts-basics-bottom//system/login/stackedLine',
        method: 'get',
        params: {
          queryYear: this.queryYear
        }
      });
      if (!res.success) {
        this.$message.error(res.message || '获取失败');
        this.loading = false;
        return;
      }
      let dataList = {};
      let dataMonth = [];
      let lineList = [
        '月累计平均每天访问人数',
        '月累计平均每天PC端访问次数',
        '月累计平均每天移动端访问次数'
      ];
      let monthList = [
        '01',
        '02',
        '03',
        '04',
        '05',
        '06',
        '07',
        '08',
        '09',
        '10',
        '11',
        '12'
      ];
      for (let key in res.object) {
        let list = [];
        let monthDataList = [];
        if (key == 'totalEmp') {
          this.totalCount = res.object[key];
        } else {
          monthList.forEach((e, index) => {
            let obj = res.object[key].filter(
              i => i.loginMonth.split('-')[1].indexOf(e) > -1
            );
            if (obj.length > 0) {
              list.push(obj[0].loginNumbers);
              monthDataList.push(this.monthLabel[index]);
            }
          });
          dataMonth = monthDataList;
          dataList[key] = list;
        }
      }
      let options = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          top: '95%',
          data: lineList
        },
        grid: {
          left: '6%',
          right: '8%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: dataMonth,
          axisLabel: {
            textStyle: {
              color: '#1B253A',
              fontStyle: 'normal',
              fontFamily: '微软雅黑',
              fontSize: 12
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            splitNumber: 5,
            axisLabel: {
              textStyle: {
                color: '#a8aab0',
                fontStyle: 'normal',
                fontFamily: '微软雅黑',
                fontSize: 12
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#E5E9ED'
              }
            }
          }
        ],
        series: [...this.setSeries(lineList, dataList)]
      };
      if (!this.MonthlyAdmissionDeparture) {
        this.MonthlyAdmissionDeparture = this.$echarts.init(
          this.$refs.MonthlyAdmissionDeparture
        );
      }
      this.MonthlyAdmissionDeparture.clear();
      this.MonthlyAdmissionDeparture.setOption(options);
      this.loading = false;
    },
    close() {
      this.visible = false;
      this.activeTableData = [];
      this.form = {
        name: '',
        queryDate: moment().format('YYYY-MM-DD'),
        queryDateRang: [
          moment().format('YYYY-MM-DD'),
          moment().format('YYYY-MM-DD')
        ]
      };
    }
  }
};
</script>
<style lang="scss">
.select-date-year-set-style {
  .el-date-picker__header-label {
    cursor: default;
    &:hover {
      color: rgb(96, 98, 102);
    }
  }
}
</style>
<style lang="scss" scoped>
.system-use-info-popover {
  width: 400px !important;
  p {
    margin-bottom: 4px !important;
  }
}
::v-deep {
  .system-use-info {
    width: 1250px;
    margin-top: calc((100vh - 590px) / 2) !important;
    background: #fff;
    .table_flex {
      display: flex;
      width: 100%;
      height: 500px;
    }
    .system-dialog-title {
      display: flex;
      align-items: center;
      .title {
        font-weight: 800;
        font-size: 20px;
        margin-right: 4px;
      }
      .tips-img {
        width: 20px;
        height: 20px;
      }
    }

    .search-container {
      display: flex;
      align-items: center;
      position: relative;
      .el-form-item {
        margin-bottom: 0;
      }
      .ts-btn {
        margin: 0 8px;
      }
      .export {
        position: absolute;
        right: 0px;
      }
      .exportDept {
        position: absolute;
        right: 45%;
      }
      .el-icon-refresh {
        cursor: pointer;
      }
    }

    .el-table--medium td,
    .el-table--medium th {
      padding: 0;
    }
    .ts-btn {
      line-height: 28px;
      min-width: 60px;
      padding: 0 8px;
      height: 30px;
      font-size: 14px;
      border-radius: 2px;
      color: #333;

      &:hover {
        border-color: #eee;
        background-color: #eee;
        color: #333;
      }

      &.primary {
        background-color: $theme-color;
        border-color: $theme-color;
        color: #fff;
        &:hover {
          border-color: $theme-color-hover;
          background-color: $theme-color-hover;
          color: #fff;
        }
      }
    }
    .el-dialog__body {
      height: 502px;
      padding: 0;

      .content {
        height: 100%;
        display: flex;

        .left-type {
          width: 200px;
          height: 100%;
          background: #fff;
          background-color: #5d657d;
          border-right: 1px solid #e2e2e2;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          position: relative;
          // justify-content: space-between;
          .el-form-item__label {
            color: #e3e3e3;
          }
          .el-input__inner {
            background-color: #5d657d !important;
            // border-color: #5d657d;
            color: #e3e3e3;
          }
          .ul-container {
            .type-item {
              flex-shrink: 0;
              padding: 0 20px;
              width: 100%;
              height: 40px;
              display: flex;
              align-items: center;
              color: #333;
              user-select: none;
              overflow: hidden;
              // border-bottom: 1px solid #5d657d;
              border-bottom: 1px solid #e2e2e2;
              cursor: pointer;
              &.active {
                // background-color: mix($theme-color, #fff, 10%);
                background-color: #9498ae;
              }
              // &:hover {
              //   background-color: mix($theme-color, #fff, 10%);
              //   background-color: #04114acc;
              // }
              .type-name {
                flex: 1;
                overflow: hidden;
                -o-text-overflow: ellipsis;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #e3e3e3;
              }
            }
          }
          .tips-container {
            padding: 0 8px 8px 8px;
            // color: rgb(255, 102, 0);
            color: #e3e3e3;
            position: absolute;
            bottom: 0px;
            width: 198px;
          }
        }
        .right-table {
          flex: 1;
          padding-bottom: 8px;
          padding-left: 8px;
          display: flex;
          flex-direction: column;
          .right-table-content {
            display: flex;
            width: 100%;
            height: 100%;
            .cell-echarts {
              width: 100%;
              height: 100%;
            }
          }
          .echart {
            flex-direction: column;
          }
          .title-year {
            display: flex;
            justify-content: center;
            line-height: 30px;
            margin-top: 10px;
            .tips-img {
              width: 20px;
              height: 20px;
              position: relative;
              top: 5px;
              left: 5px;
            }
          }
          .top-search-form {
            padding: 8px;
            .trasen-search-content {
              border-bottom: 0px;
              padding-bottom: 0px;
            }
          }
          .tableContent {
            .table {
              height: 100%;
            }
          }
          .details-span {
            color: #5260ff;
            cursor: pointer;
          }
        }
      }
    }

    .el-dialog__footer {
      border-top: 1px solid #eee;
    }
  }
}
.noData {
  position: relative;
}
.list-no-data-img {
  width: 200px;
  height: 230px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  img {
    width: 200px;
    height: 200px;
  }
  div {
    text-align: center;
  }
}
.list-no-data-imgs {
  position: relative;
  width: calc(45% - 20px);
  margin-left: 10px;
  img {
    position: absolute;
    top: calc(50% - 100px);
    left: calc(50% - 100px);
    width: 200px;
    height: 200px;
  }
  div {
    width: 100%;
    position: absolute;
    top: calc(50% + 100px);
    text-align: center;
  }
}
/deep/ .deptTable {
  width: 58%;
  .is-scrolling-none {
    height: 370px !important;
  }
}
</style>
