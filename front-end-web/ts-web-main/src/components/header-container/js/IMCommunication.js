import { Encrypt } from '@/utils/encrypt.js';

export default {
  computed: {
    hasIMTimelyCommunication() {
      return this.$store.state.common.globalSetting.imSwitch;
    }
  },
  data() {
    return {
      imInformCount: 0
    };
  },
  watch: {
    hasIMTimelyCommunication: {
      handler(val) {
        if (val) {
          this.getUnReadIMMessageCount();
        }
      },
      immediate: true
    }
  },
  methods: {
    handleOpenIMTimelyCommunication() {
      let { employeeNo } = this.userInfo,
        url = `${location.origin}/qiqiim-server/login?account=${Encrypt(
          String(employeeNo).trim()
        ).toString()}`;
      window.open(url);
    },
    getUnReadIMMessageCount() {
      this.getIMTimer && clearTimeout(this.getIMTimer);
      this.getIMTimer = setTimeout(() => {
        this.getUnReadIMMessageCount();
        this.ajax.getUnReadIMMessageCount().then(res => {
          if (!res.success) {
            return;
          }
          this.imInformCount = res.object || 0;
        });
      }, 10000);
    }
  }
};
