<template>
  <div
    class="resizable-box"
    :style="boxStyle"
    @mousedown.stop="startResize($event, 'corner')"
  >
    <!-- 内容区域 -->
    <div class="content">
      222
    </div>

    <!-- 四个调整手柄 -->
    <div
      v-for="(corner, index) in corners"
      :key="index"
      class="resize-handle"
      :class="corner.class"
      :style="corner.style"
      @mousedown.stop="startResize($event, corner.direction)"
    ></div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      width: 300,
      height: 200,
      minWidth: 100,
      minHeight: 100,
      isResizing: false,
      startX: 0,
      startY: 0,
      startWidth: 0,
      startHeight: 0
    };
  },
  computed: {
    boxStyle() {
      return {
        width: `${this.width}px`,
        height: `${this.height}px`,
        position: 'relative'
      };
    },
    corners() {
      return [
        {
          direction: 'tl',
          class: 'top-left',
          style: {
            left: '-5px',
            top: '-5px',
            cursor: 'nwse-resize'
          }
        },
        {
          direction: 'tr',
          class: 'top-right',
          style: {
            right: '-5px',
            top: '-5px',
            cursor: 'nesw-resize'
          }
        },
        {
          direction: 'bl',
          class: 'bottom-left',
          style: {
            left: '-5px',
            bottom: '-5px',
            cursor: 'nesw-resize'
          }
        },
        {
          direction: 'br',
          class: 'bottom-right',
          style: {
            right: '-5px',
            bottom: '-5px',
            cursor: 'nwse-resize'
          }
        }
      ];
    }
  },
  methods: {
    startResize(e, direction) {
      this.isResizing = true;
      this.startX = e.clientX;
      this.startY = e.clientY;
      this.startWidth = this.width;
      this.startHeight = this.height;

      document.addEventListener('mousemove', this.onResize);
      document.addEventListener('mouseup', this.stopResize);
    },
    onResize(e) {
      if (!this.isResizing) return;

      const deltaX = e.clientX - this.startX;
      const deltaY = e.clientY - this.startY;

      let newWidth = this.startWidth + deltaX;
      let newHeight = this.startHeight + deltaY;

      // 根据拖动方向调整计算逻辑
      switch (this.currentDirection) {
        case 'tl':
          newWidth = Math.max(this.minWidth, this.width - deltaX);
          newHeight = Math.max(this.minHeight, this.height - deltaY);
          break;
        case 'tr':
          newWidth = Math.max(this.minWidth, this.width + deltaX);
          newHeight = Math.max(this.minHeight, this.height - deltaY);
          break;
        case 'bl':
          newWidth = Math.max(this.minWidth, this.width - deltaX);
          newHeight = Math.max(this.minHeight, this.height + deltaY);
          break;
        case 'br':
          newWidth = Math.max(this.minWidth, this.width + deltaX);
          newHeight = Math.max(this.minHeight, this.height + deltaY);
          break;
      }

      this.width = newWidth;
      this.height = newHeight;
    },
    stopResize() {
      this.isResizing = false;
      document.removeEventListener('mousemove', this.onResize);
      document.removeEventListener('mouseup', this.stopResize);
    }
  }
};
</script>

<style lang="scss" scoped>
.resizable-box {
  border: 1px solid #ddd;
  overflow: hidden;
  background: #fff;
  z-index: 9999;
}

.resize-handle {
  position: absolute;
  width: 10px;
  height: 10px;
  background: #409eff;
  border: 1px solid #fff;
  border-radius: 50%;
  cursor: move;
  user-select: none;
}

.content {
  padding: 15px;
  box-sizing: border-box;
}

/* 方向指示器 */
.top-left::after {
  content: '↖';
  position: absolute;
  top: 12px;
  left: 12px;
  color: white;
}

.top-right::after {
  content: '↗';
  position: absolute;
  top: 12px;
  left: 4px;
  color: white;
}

.bottom-left::after {
  content: '↙';
  position: absolute;
  top: 4px;
  left: 12px;
  color: white;
}

.bottom-right::after {
  content: '↘';
  position: absolute;
  top: 4px;
  left: 4px;
  color: white;
}
</style>
