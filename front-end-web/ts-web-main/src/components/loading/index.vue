<template>
  <div ref="loadingContent" class="loading-content">
    <img :src="src" class="loading-img" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      src: require('@/assets/img/loading-2.gif')
    };
  },
  mounted() {
    this.$refs.loadingContent.onclick = function(e) {
      e.preventDefault();
      e.stopPropagation();
      e.cancelBubble = true;
      return false;
    };
  }
};
</script>

<style scoped lang="scss">
.loading-content {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.08);
  z-index: 192830129830982011;
  display: flex;
  align-items: center;
  justify-content: center;

  .loading-img {
    width: 32px;
    height: 32px;
  }
}
</style>
