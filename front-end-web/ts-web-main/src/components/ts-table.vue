<template>
  <div class="tableContent">
    <div class="table">
      <el-table
        ref="table"
        :border="border"
        :data="tableData"
        :height="height"
        stripe
        :show-summary="showSummary"
        :summary-method="getSummaries"
        @selection-change="handleSelectionChange"
        @sort-change="sortChangeMethod"
      >
        <el-table-column
          type="selection"
          width="39"
          v-if="showSelection"
          :resizable="false"
        >
        </el-table-column>
        <!-- 序号栏 -->
        <el-table-column
          type="index"
          v-if="showIndex"
          align="center"
          :resizable="false"
          :label="indexLabel"
          :index="computedIndex"
        >
        </el-table-column>

        <template v-for="(item, index) in columns">
          <el-table-column
            :key="index"
            :resizable="item.resizable || false"
            v-bind="item"
          ></el-table-column>
        </template>

        <!-- 待完成 -->
        <!-- <el-table-column
          label="操作"
          :resizable="false"
          v-if="!hiddenActionCell"
        >
          <template slot-scope="scope">
            <slot name="actionRow" :row="scope.row" :cell="scope"></slot>
          </template>
        </el-table-column> -->
      </el-table>

      <el-pagination
        v-if="hasPage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageNo"
        :page-sizes="[20, 40, 60, 80, 100, 200, 500, 1000, 2000]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    border: {
      //是否带有边框
      type: Boolean,
      default: () => {
        return true;
      }
    },
    columns: {
      //列表元素
      type: Array,
      default: () => {
        return [];
      }
    },
    height: {
      //hight属性 配置后可固定表头  父容器最好是 flex：1
      type: [Number, String],
      default: () => {
        return '100%';
      }
    },
    showIndex: {
      //是否展示序号
      type: Boolean,
      default: () => {
        return false;
      }
    },
    indexLabel: {
      //序号列表名
      type: String,
      default: () => {
        return '';
      }
    },
    indexMethod: {
      //序号显示方法
      type: Function,
      default: (pageNo, pageSize, index, row) => {
        let count = (pageNo - 1) * pageSize + index / 1 + 1;
        return count;
      }
    },
    sortChange: {
      type: Function,
      default: (column, order, prop, table) => {
        let searchData = {
          pageNo: this.pageNo
        };
      }
    },
    showSelection: {
      //是否显示选择框
      type: Boolean,
      default: () => {
        return false;
      }
    },
    hiddenActionCell: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    loadType: {
      type: String,
      default: () => {
        return 'inline';
      }
    },
    hasPage: {
      type: Boolean,
      default: () => true
    },
    showSummary: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      localData: [], //本地缓存数据
      tableData: [], // 表格数据
      total: 0, // 表格全部数据个数
      pageSize: 20, // 分页大小
      pageNo: 1, // 当前页码

      // loading: false, //加载动画展示
      // loadingTimer: null, //加载动画定时器，免得太快了。。。
      // requestLoading: false, //是否在请求数据，用来防止定时器过快

      sortLabel: '', //当前排序
      sortProp: '', //当前排序字段的 属性名
      sidx: null, //当前排序字段 排序名
      sord: 'desc' // 当前排序方式， 默认为倒序
    };
  },
  created() {
    //为当前默认排序赋值
    this.columns.forEach(item => {
      if (
        (item.sortable == 'custom' || item.sortable == 'true') &&
        item.defaultSort == true
      ) {
        this.sortLabel = item.label;
        this.sortProp = item.prop;
        this.sidx = item.sortIndex || null;
        this.sord = item.sortType || 'desc';
      }

      if (
        item.showOverflowTooltip != 'true' &&
        item.showOverflowTooltip != 'false'
      ) {
        item.showOverflowTooltip = true;
      }
    });
  },
  mounted() {
    if (this.sidx) {
      let type = this.sord == 'desc' ? 'descending' : 'ascending';
      this.$refs.table.sort(this.sortProp, type);
    } else {
      // this.refreshTable();
    }
  },
  methods: {
    //页面搜索
    refreshTable(data) {
      let searchData = {};
      if (Object.prototype.toString.call(data) != '[object Object]') {
        data = {};
      }

      searchData = { ...data, pageSize: this.pageSize };
      // Object.keys(data).length ? searchData.pageNo = 1 : searchData.pageNo = this.pageNo;
      if (!Object.keys(data).length || !data.pageNo) {
        searchData.pageNo = 1;
        this.pageNo = 1;
      }

      //如果没有排序字段的话添加默认排序字段
      let keys = Object.keys(searchData);
      if (keys.indexOf('sidx') == -1 || !searchData.sidx) {
        searchData.sidx = this.sidx;
        this.sidx ? (searchData.sord = this.sord) : '';
      }

      if (this.loadType == 'local' && this.localData.length) {
        let startIndex = this.pageSize * (this.pageNo - 1),
          endIndex = startIndex + this.pageSize;
        endIndex > this.localData.length - 1
          ? (endIndex = this.localData.length)
          : null;

        this.tableData = this.localData.slice(startIndex, endIndex);
        return;
      }

      //加载动画效果
      // this.loading = true;
      // this.requestLoading = true;
      // this.loadingTimer && clearTimeout(this.loadingTimer);
      // this.loadingTimer = setTimeout(() => {
      //   if (!this.requestLoading) {
      //     this.loading = false;
      //   }
      //   this.loadingTimer = null;
      // }, 600);
      // let _this = this;

      this.$emit('refresh', searchData, (res = {}) => {
        if (!Object.prototype.toString.call(res).length) {
          return;
        }

        if (this.loadType == 'local') {
          this.localData = res.list || [];
          this.refreshTable();
        } else {
          this.tableData = res.list || [];
        }
        this.total = res.total || 0;
        //禁止后端更改表格的页码
        // this.pageNo = res.pageNo || 1;
        // this.pageSize = res.pageSize || 20;

        //关闭加载动画
        // if (!this.loadingTimer) {
        //   this.loading = false;
        // }
        // this.requestLoading = false;
      });

      // clearTimeout(this.loadingTimer);
      // this.requestLoading = false;
      // this.loading = false;
      // this.$message.error('出错啦');
    },

    //--------------------- 事件处理 -----------------------
    //处理pageSize改变
    handleSizeChange(val) {
      this.pageSize = val;
      this.refreshTable();
    },

    //处理pageNo改变
    handleCurrentChange(val) {
      this.pageNo = val;
      this.refreshTable({ pageNo: this.pageNo });
    },
    //处理表格选择改变
    handleSelectionChange(val) {
      this.$emit('selectionChange', val);
    },

    //排序改变
    sortChangeMethod(props) {
      let ref = this.$refs.table,
        { column, order, prop } = props;
      order = { ascending: 'asc', descending: 'desc', none: null }[order];

      let sidx = null;
      for (let item of this.columns) {
        if (
          item.label == column.label &&
          item.prop == prop &&
          (item.sortable == 'custom' || item.sortable == 'true')
        ) {
          sidx = item.sortIndex;
          break;
        }
      }
      let searchData = {
        sidx,
        sord: order,
        pageNo: this.pageNo
      };

      this.sord = order;
      this.sidx = sidx;

      if (!order) {
        searchData.sidx = null;
        this.sidx = null;
      }
      this.refreshTable(searchData);
    },
    //-------------------- 内部方法 -------------------
    computedIndex(index) {
      let pageNo = `${this.pageNo}`,
        pageSize = `${this.pageSize}`,
        row = JSON.parse(JSON.stringify(this.tableData[index]));
      return this.indexMethod(Number(pageNo), Number(pageSize), index, row);
    },
    //获取表格数据
    getTableData() {
      return {
        col: this.columns,
        data: this.tableData
      };
    },
    //获取表格选中的行
    getSelection() {
      return this.$refs.table.selection;
    },
    userTable(status) {
      this.$emit('userTable', {}, status);
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      let value1 = 0;
      let value2 = 0;
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '';
          return;
        }
        if (index === 1) {
          sums[index] = '总计';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          let num = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          if (index == 2) {
            value1 = num;
          }
          if (index == 3) {
            value2 = num;
            sums[index] = (
              <span
                class="details-span"
                onClick={() => {
                  this.userTable(1);
                }}>
                {num}
              </span>
            );
          } else if (index == 4) {
            sums[index] = (
              <span
                class="details-span"
                onClick={() => {
                  this.userTable(2);
                }}>
                {num}
              </span>
            );
          } else if (index == 5) {
            let per = ((value2 / value1) * 100).toFixed(2);
            sums[index] = <span>{per + '%'}</span>;
          } else {
            sums[index] = num + '';
          }
        } else {
          sums[index] = '';
        }
      });
      return sums;
    }
  }
};
</script>

<style scoped lang="scss">
.tableContent {
  height: 100%;
  width: 100%;
  position: relative;
}
.table {
  width: 100%;
  height: calc(100% - 40px);
  position: absolute;
  .el-pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }

  /deep/ .el-table th.gutter {
    display: none !important;
  }
  /deep/::-webkit-scrollbar {
    width: 6px;
    height: 8px;
  }
  /deep/::-webkit-scrollbar-thumb {
    border-radius: 8px;
    height: 50px;
    background: rgba(153, 153, 153, 0.4);
    &:hover {
      background: rgba(153, 153, 153, 0.8);
    }
  }
  /deep/::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    background: #fff;
  }
}
</style>
