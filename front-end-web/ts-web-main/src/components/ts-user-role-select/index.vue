<template>
  <el-dialog
    custom-class="ts-user-group-select"
    :title="title"
    width="600px"
    :show-close="false"
    :visible.sync="visible"
    :append-to-body="appendToBody"
    @close="cancel"
  >
    <div class="container">
      <div class="table-container">
        <div class="search-container">
          <el-input
            placeholder="输入姓名"
            v-model="queryParam.empName"
            suffix-icon="el-icon-search"
            clearable
            @input="searchData()"
          >
          </el-input>
        </div>
        <el-table
          ref="multipleTable"
          :data="dataSource"
          style="width: 100%"
          height="100%"
          :show-overflow-tooltip="true"
          :stripe="true"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
          :row-class-name="tableRowClassName"
          :row-key="rowKey"
        >
          <el-table-column
            v-if="!isRadio"
            :show-overflow-tooltip="true"
            width="36"
            type="selection"
            :reserve-selection="true"
            prop="roleCode"
          ></el-table-column>
          <el-table-column
            label="角色编码"
            :show-overflow-tooltip="true"
            prop="roleCode"
            align="center"
          >
            <template slot-scope="{ row }">
              <span class="text-style">{{ row.roleCode }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="角色名称"
            :show-overflow-tooltip="true"
            prop="roleName"
          >
            <template slot-scope="{ row }">
              <span class="text-style">{{ row.roleName }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <span slot="footer" class="footer">
      <span class="ts-button primary" @click="save">
        确 定
      </span>
      <span class="ts-button" @click="cancel">
        取 消
      </span>
    </span>
  </el-dialog>
</template>

<script>
import { commonUtils } from '@/utils/index.js';
import _ from 'lodash';
const folderIcon = require('@/assets/img/icon_folder.png');
const fileIcon = require('@/assets/img/icon_file.svg');
const queryParam = {
  empName: undefined
};
export default {
  name: 'TsUserRoleSelect',
  data() {
    return {
      field: '',
      visible: false,
      folderIcon,
      fileIcon,
      title: '选择',
      showCheckbox: true,
      appendToBody: false,
      isRadio: false,
      tableRowCode: '',
      /* 查询条件-请不要在queryParam中声明非字符串值的属性 */
      queryParam: JSON.parse(JSON.stringify(queryParam)),
      /* 数据源 */
      dataSource: [],
      dataAll: [],
      /* 分页参数 */
      selectedRowKeys: [] // 选中人员列表
    };
  },
  methods: {
    /**@desc **/
    /**
     * showCheckbox 组织结构是否可以勾选
     * title 标题
     * empList 选中人员
     * deptList 选中组织架构
     * isRadio 单选
     */
    open(
      field,
      {
        showCheckbox = true,
        title = '选择',
        empList = [],
        appendToBody = false,
        isRadio = false
      }
    ) {
      this.field = field;
      this.showCheckbox = showCheckbox;
      this.title = title;
      this.appendToBody = appendToBody;
      this.isRadio = isRadio;
      this.visible = true;
      this.$nextTick(() => {
        // 回显人员勾选
        empList.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      });
      Promise.all([this.loadData(1)]).then(res => {});
    },
    // 遍历数据源
    recursionData(list) {
      for (const key in list) {
        list[key].disabled = true;
        if (commonUtils.arrayLength(list[key].children)) {
          this.recursionData(list[key].children);
        }
      }
    },
    // 部门人员查询
    async loadData(arg) {
      const dataParams = this.getDataParams();
      try {
        let res;
        res = await this.ajax.getRolePlatform(dataParams);
        this.dataSource = res.object || [];
        this.dataAll = this.dataSource;
      } catch (error) {
        throw error;
      } finally {
      }
    },
    searchData() {
      if (this.queryParam.empName == '') {
        this.dataSource = this.dataAll;
      } else {
        this.dataSource = this.dataAll.filter(
          e => e.roleName.indexOf(this.queryParam.empName) > -1
        );
      }
    },
    getDataParams() {
      // 获取查询条件
      var data = Object.assign(this.queryParam);
      data.pageNo = 1;
      data.pageSize = 10000;
      return commonUtils.filterObj(data);
    },
    handleSelectionChange(val) {
      this.selectedRowKeys = val;
    },
    handleRowClick(row, column, event) {
      if (!this.isRadio) return;
      this.tableRowCode = row.id;
      this.selectedRowKeys = [row];
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.id === this.tableRowCode) {
        return 'highlight-row';
      }
      return '';
    },

    rowKey(row) {
      return row.id;
    },
    // 清空选中项
    clearSelect(type) {
      if (type === 'selectedRowKeys') {
        this.$refs.multipleTable.clearSelection();
      }
    },
    // 关闭模态窗
    cancel() {
      this.visible = false;
      this.field = '';
      this.showCheckbox = true;
      this.title = '选择';
      this.appendToBody = false;
      this.$refs.multipleTable.clearSelection();
      this.queryParam = JSON.parse(JSON.stringify(queryParam));
    },
    // 保存
    save() {
      this.$emit('ok', {
        [this.field]: {
          empList: this.selectedRowKeys
        }
      });
      this.cancel();
    }
  }
};
</script>
<style lang="scss" scoped>
.ts-user-group-select {
  /deep/.el-dialog__body {
    padding: 8px 8px;
  }
  .container {
    display: flex;
    justify-content: space-between;
    height: 100%;
    & > div {
      height: 100%;
      box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.3);
      border: 1px solid #e4e4e4;
    }
    .table-container {
      box-sizing: border-box;
    }
    .table-container {
      width: 100%;
      display: flex;
      flex-direction: column;
      /deep/.el-loading-spinner {
        margin-top: 0;
      }
      .search-container {
        padding: 8px;
      }
      .el-pagination {
        display: flex;
        justify-content: flex-end;
      }
      .text-style {
        font-size: 12px;
        color: #333333;
      }
    }
    /deep/::-webkit-scrollbar {
      width: 6px;
      height: 8px;
    }
    /deep/::-webkit-scrollbar-thumb {
      border-radius: 8px;
      height: 50px;
      background: rgba(0, 0, 0, 0.2);
    }
    /deep/::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      background: #fff;
    }
  }
  .footer {
    .primary {
      margin-right: 8px;
    }
  }
}
</style>
<style lang="scss">
.ts-user-group-select .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: $theme-color;
  border-color: rgba(151, 151, 151, 1);
}
.ts-user-group-select .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: $theme-color;
  border-color: rgba(151, 151, 151, 1);
}
.ts-user-group-select .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
  white-space: nowrap;
  display: inline-block;
}
.ts-user-group-select .el-table .el-table__header-wrapper tr,
.ts-user-group-select .el-table .el-table__header-wrapper th {
  background-color: white;
}
.ts-user-group-select .el-table .el-table__header-wrapper tr:hover,
.ts-user-group-select .el-table .el-table__header-wrapper th:hover {
  background-color: white;
}
tr.highlight-row td {
  background-color: $theme-color !important;
}
tr.highlight-row td span {
  color: white !important;
}
</style>
