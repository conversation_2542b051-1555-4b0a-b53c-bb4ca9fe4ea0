<template>
  <el-dialog
    custom-class="ts-user-tree-select"
    :title="title"
    width="600px"
    :show-close="false"
    :visible.sync="visible"
    :append-to-body="appendToBody"
    @close="cancel"
  >
    <div class="container">
      <div class="tree-container">
        <div class="search-container">
          <el-input placeholder="输入部门名称" v-model="filterText"> </el-input>
        </div>
        <el-scrollbar style="flex-grow: 1; width: 100%">
          <el-tree
            ref="deptTree"
            :data="deptTreeList.data"
            :show-checkbox="showCheckbox"
            node-key="id"
            :default-expanded-keys="deptTreeList.defaultExpandedKeys"
            :filter-node-method="filterNode"
            :expand-on-click-node="false"
            :render-content="renderContent"
            @check-change="handleCheckChange"
            @node-click="nodeClick"
            class="dept-tree"
          >
          </el-tree>
        </el-scrollbar>
      </div>
    </div>
    <span slot="footer" class="footer">
      <span class="ts-button primary" @click="save">
        确 定
      </span>
      <span class="ts-button" @click="cancel">
        取 消
      </span>
    </span>
  </el-dialog>
</template>

<script>
import api from '@/api/ajax/hrm.js';
import _ from 'lodash';
const folderIcon = require('@/assets/img/icon_folder.png');
const fileIcon = require('@/assets/img/icon_file.svg');
const queryParam = {
  empCode: undefined,
  empName: undefined,
  empDeptCode: undefined,
  deptCodeSeach: undefined,
  groupId: undefined
};
export default {
  name: 'TsUserDeptSelect',
  data() {
    return {
      ajax: api,
      field: '',
      visible: false,
      folderIcon,
      fileIcon,
      filterText: '',
      title: '选择',
      showCheckbox: true,
      appendToBody: false,
      isRadio: false,
      deptTreeList: {
        data: [],
        defaultExpandedKeys: []
      },
      /* 查询条件-请不要在queryParam中声明非字符串值的属性 */
      queryParam: JSON.parse(JSON.stringify(queryParam)),
      /* 数据源 */
      treeType: 'emp', // 当前选中的树类型
      treeCheck: {
        nodes: [],
        keys: []
      }
    };
  },
  methods: {
    /**@desc **/
    /**
     * showCheckbox 组织结构是否可以勾选
     * title 标题
     * empList 选中人员
     * deptList 选中组织架构
     * isRadio 单选
     */
    open(
      field,
      {
        showCheckbox = true,
        title = '选择',
        empList = [],
        deptList = [],
        appendToBody = false,
        isRadio = false
      }
    ) {
      this.field = field;
      this.showCheckbox = showCheckbox;
      this.title = title;
      this.appendToBody = appendToBody;
      this.isRadio = isRadio;
      this.visible = true;
      this.$nextTick(() => {
        let treeList = [];
        // 回显组织架构勾选
        deptList.forEach(row => {
          treeList.push(row.id);
        });
        this.$refs.deptTree.setCheckedKeys(treeList);
        if (isRadio) {
          this.tableRowCode = empList.length === 1 ? empList[0].empCode : '';
          this.selectedRowKeys = empList;
        }
      });
      Promise.all([this.getTreeList()]).then(res => {});
    },
    renderContent(h, { node, data, store }) {
      return (
        <div class="custom-tree-node">
          <img
            class="icon-file"
            src={data.children ? this.folderIcon : this.fileIcon}
          />
          <span>{data.name}</span>
        </div>
      );
    },
    // 获取组织部门列表
    async getTreeList() {
      try {
        let deptTreeList = await this.ajax.getDeptTreeList();
        deptTreeList = deptTreeList.object || [];
        if (deptTreeList.length > 0)
          this.deptTreeList = {
            data: deptTreeList,
            defaultExpandedKeys: [deptTreeList[0].id]
          };
      } catch (error) {
        throw error;
      }
    },
    // 树搜索
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleCheckChange(data, checked, indeterminate) {
      this.treeCheck.nodes = this.$refs.deptTree.getCheckedNodes();
      this.treeCheck.keys = this.$refs.deptTree.getCheckedKeys();
    },
    // 点击部门事件
    nodeClick(data, checked, indeterminate) {
      this.treeType = 'emp';
      if (this.queryParam.deptCodeSeach !== data.id) {
        this.queryParam.deptCodeSeach = data.id;
      }
    },
    // 关闭模态窗
    cancel() {
      this.visible = false;
      this.field = '';
      this.showCheckbox = true;
      this.title = '选择';
      this.appendToBody = false;
      this.$refs.deptTree.setCheckedKeys([]);
      this.queryParam = JSON.parse(JSON.stringify(queryParam));
      this.filterText = '';
      this.treeType = 'emp'; // 当前选中的树类型
      this.treeCheck = {
        nodes: [],
        keys: []
      };
    },
    // 保存
    save() {
      this.$emit('ok', {
        [this.field]: {
          empList: this.treeCheck.nodes
        }
      });
      this.cancel();
    }
  },
  computed: {
    selectTreeNames() {
      return this.treeCheck.nodes
        .map(item => {
          return item.name;
        })
        .join(', ');
    }
  },
  watch: {
    filterText(val) {
      this.$refs.deptTree.filter(val);
    }
  }
};
</script>
<style lang="scss" scoped>
.ts-user-tree-select {
  /deep/.el-dialog__body {
    padding: 8px 8px;
  }
  .container {
    height: 475px;
    display: flex;
    justify-content: space-between;
    & > div {
      height: 100%;
      box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.3);
      border: 1px solid #e4e4e4;
    }
    .tree-container {
      box-sizing: border-box;
    }
    .tree-container {
      width: 200px;
      display: flex;
      flex-direction: column;
      .search-container {
        padding: 8px;
      }
    }
    /deep/::-webkit-scrollbar {
      width: 6px;
      height: 8px;
    }
    /deep/::-webkit-scrollbar-thumb {
      border-radius: 8px;
      height: 50px;
      background: rgba(0, 0, 0, 0.2);
    }
    /deep/::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      background: #fff;
    }
  }
  .footer {
    .primary {
      margin-right: 8px;
    }
  }
}
/deep/ {
  .dept-tree {
    .el-tree-node__content:hover .custom-tree-node span {
      color: $theme-color;
    }
    .is-current {
      & > .el-tree-node__content {
        background-color: $theme-color-8 !important;
      }
    }
  }
}
</style>
<style lang="scss">
.ts-user-tree-select .custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 14px;
  padding-right: 8px;
}
.ts-user-tree-select .custom-tree-node span {
  font-size: 12px;
  color: #333333;
  line-height: 16px;
}
.ts-user-tree-select .custom-tree-node .icon-file {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.ts-user-tree-select .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: $theme-color;
  border-color: rgba(151, 151, 151, 1);
}
.ts-user-tree-select .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: $theme-color;
  border-color: rgba(151, 151, 151, 1);
}
.ts-user-tree-select .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
  white-space: nowrap;
  display: inline-block;
}
.ts-user-tree-select .el-table .el-table__header-wrapper tr,
.ts-user-tree-select .el-table .el-table__header-wrapper th {
  background-color: white;
}
.ts-user-tree-select .el-table .el-table__header-wrapper tr:hover,
.ts-user-tree-select .el-table .el-table__header-wrapper th:hover {
  background-color: white;
}
tr.highlight-row td {
  background-color: $theme-color !important;
}
tr.highlight-row td span {
  color: white !important;
}
</style>
