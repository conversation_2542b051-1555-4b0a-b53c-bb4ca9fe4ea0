export default [
  {
    path: '/login',
    component: resolve => require([`@/views/login/index.vue`], resolve),
    styleName: 'icon-ji<PERSON><PERSON><PERSON><PERSON>',
    name: '登录'
  },
  {
    path: '/index',
    component: resolve => require([`@/views/index/index.vue`], resolve),
    name: '首页'
  },
  {
    path: '/workSheet/noLoginLargeScreen',
    component: resolve =>
      require([`@/views/noLoginWorkSheetScreen/index.vue`], resolve),
    name: '工单大屏'
  },
  {
    path: '/message',
    component: resolve => require([`@/views/message/index.vue`], resolve),
    name: '消息'
  },
  {
    path: '/comprehensiveQuery/report',
    component: resolve =>
      require([`@/views/comprehensiveQuery/index.vue`], resolve),
    name: '综合查询报表'
  },
  {
    path: '/system/homePage',
    component: resolve => require([`@/views/homePage/index.vue`], resolve),
    name: '首页管理'
  }
];
