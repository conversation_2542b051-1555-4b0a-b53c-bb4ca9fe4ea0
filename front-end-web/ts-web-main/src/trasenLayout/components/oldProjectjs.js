import { cssStr } from '@/trasenLayout/components/odlProjectCss';

export default {
  data() {
    return {
      show: true,
      src: '',
      iframe: '',
      js: {}
    };
  },
  props: {
    leftbg: {
      type: Number,
      default: 0
    }
  },
  created() {
    let isChild = !this.isChildApp(this.$route.path);
    let token = this.$cookies.get('token');
    if (isChild) {
      this.src =
        `/#${this.$route.path}` +
        (this.$route.path == '/index'
          ? `?t=${new Date().getTime()}&inform=${token}`
          : '');
      // setTimeout(() => {
      //   this.show = false;
      // }, 50);
    } else {
      this.src = `/#/index?inform=${token}`;
      // setTimeout(() => {
      //   this.show = true;
      // }, 50);
    }
  },
  methods: {
    /**@desc 模拟点击老项目一级菜单
     * @param {Object} obj**/
    updateFirstMenu(obj) {
      let domStr = `#top-nav li.nav-item a[menu-id="${obj.id}"]`;
      let dom = this.iframe.document.querySelector(domStr);
      if (dom) {
        dom.click();
      }
    },
    closeContextMenu() {
      this.$root.$emit('closeContextMenu');
    },
    /**@desc 删除以及更新路由页面
     * @param {Object} _obj**/
    curdRouter(_obj) {
      let obj = _obj.detail;
      if (obj.type == 'deleteRouter') {
        this.deleteRouter(obj);
      } else if (obj.type == 'updateRouter') {
        this.updateRouter(obj);
      }
    },
    /**@desc 删除老项目单个页面缓存
     * @param {Object} obj**/
    deleteRouter(obj) {
      let domStr = `div.cuttab-box[menu-id="${obj.id}"]`;
      let dom = this.iframe.document.querySelector(domStr);
      if (dom) {
        let colseDom = dom.querySelector('.cuttab-close');
        colseDom.click();
      }
    },
    /**@desc 刷新老项目单个页面**/
    updateRouter(obj) {
      let domHtml = this.iframe.document.querySelector(
        `.content-hide-box[menu-id="${obj.id}"]`
      );

      if (domHtml) {
        let dom = this.iframe.document.querySelector('#cuttab-contextmenu');
        dom.setAttribute('menu-id', obj.id);
        if (dom) {
          let refreshDom = dom.querySelector('#refreshCut');
          refreshDom.click();
        }
      }
    },
    /**@desc 绑定iframe hsah事件 以及其他事件监听**/
    onhashchange() {
      this.$refs.iframe.contentWindow.addEventListener('hashchange', () => {
        let hash = this.iframe.location.hash.slice(1).replace(/\?.+/, '');
        if (hash != this.$route.path && !this.isChildApp(this.$route.path)) {
          this.$router.push(hash);
        }
      });

      //添加前往登录事件
      this.$refs.iframe.contentWindow.addEventListener('goLogin', () => {
        this.$root.$emit('goLogin');
      });

      //添加老项目webSokect监听
      this.$refs.iframe.contentWindow.addEventListener(
        'sendToNewFrameMessage',
        e => {
          let detail = e.detail || {};
          if (detail.type) {
            this.$root.$emit(detail.type, detail.data);
          }
        }
      );
      //一次性添加所有向老项目传输信息的方法
      this.$root.$on('sendMessageToOldFrame', message => {
        let prop = message.detail;
        this.iframe.dispatchEvent(
          new this.iframe.CustomEvent(prop.type, { detail: prop.data })
        );
      });

      /**@desc 等页面加载完成初始化websocket**/
      this.iframe.onLoad = () => {
        this.webSocketClient();
      };

      /**@desc 模拟老项目点击事件外放出来**/
      this.iframe.document
        .querySelector('body')
        .addEventListener('click', this.closeContextMenu);
    },
    /**@desc 监听元素是否加载完成 并注入监听方法**/
    getIframeDom() {
      setTimeout(() => {
        this.iframe = this.$refs.iframe.contentWindow;
        if (this.iframe.document.querySelector('#mainContentBox')) {
          let style = this.$refs.iframe.contentWindow.document.createElement(
            'DIV'
          );
          style.id = 'old-oa-css';
          let styleDom = this.iframe.document.querySelector('div#old-oa-css');
          styleDom && styleDom.remove();

          this.iframe.document.querySelector('body').appendChild(style);
          style.innerHTML = cssStr; //注入页面样式
          this.iframe.document.store = {
            isDingTalkWorkOrder:
              this.$store.state.common.globalSetting.mobilePlatform == 2
          };
          this.onhashchange(); //注入监听方法
        } else {
          this.getIframeDom();
        }
      });
    },
    /**@desc 判断是否在子应用**/
    isChildApp(path) {
      let index = 0;
      this.$store.state.common.qiankuanapps.forEach(item => {
        if (path.indexOf(item.userData.packageName) != -1) {
          index++;
        }
      });
      this.$store.state.common.whiteRouterList.forEach(item => {
        if (item == path) {
          index++;
        }
      });
      this.show = index ? true : false;
      return index;
    },
    /**@desc 更新老项目面包绡**/
    isCollapseFun() {
      let domHtml = this.iframe.document.querySelector(`.toggleLeftMenu`);
      if (domHtml) {
        domHtml.click();
      }
    },
    /**@desc test**/
    jumpOpenDialog(detail) {
      this.iframe.dispatchEvent(
        new this.iframe.CustomEvent('jumpOpenDialog', { detail })
      );
    },

    //来电跳转老项目弹屏
    workSheetWebSocketMessage(detail) {
      let node = this.iframe.document.querySelector(
        '#orderInfoAddFormDiv #pkCustometLogId'
      );
      //如果已有工单正在创建，则不作处理
      if (node) {
        return;
      }

      this.iframe.dispatchEvent(
        new this.iframe.CustomEvent('workSheetWebSocketMessage', { detail })
      );
    }
  },
  beforeDestroy() {
    /**@desc 接收页签自定义事件**/
    window.removeEventListener('updateDataQianKun', this.curdRouter, false);
    window.removeEventListener(
      'webSocketMessage',
      this.webSocketMessage,
      false
    );
    this.$root.$off('isCollapse', this.isCollapse);
    this.$root.$off('updateFirstMenu', this.updateFirstMenu);
    this.$root.$off(
      'workSheetWebSocketMessage',
      this.workSheetWebSocketMessage
    );
    // this.$root.$off('indexDoorChange');
    this.$root.$off('menuMessage');
    this.$root.$off('routerChange');
    // this.$refs.iframe.contentWindow.removeEventListener(
    //   'click',
    //   this.closeContextMenu
    // );
  },
  mounted() {
    this.getIframeDom();
    window.addEventListener('updateDataQianKun', this.curdRouter, false);
    window.addEventListener('webSocketMessage', this.webSocketMessage, false);
    window.addEventListener('jumpOpenDialog', this.jumpOpenDialog, false);
    this.$root.$on('isCollapse', this.isCollapseFun);
    this.$root.$on('updateFirstMenu', this.updateFirstMenu);
    this.$root.$on('workSheetWebSocketMessage', this.workSheetWebSocketMessage); //工单来电弹屏
    //添加index页面门户改变事件
    // this.$root.$on('indexDoorChange', detail => {
    //   this.iframe.dispatchEvent(
    //     new this.iframe.CustomEvent('indexDoorChange', { detail })
    //   );
    // });
    //添加更多菜单点击事件 隐藏左侧菜单
    this.$root.$on('menuMessage', e => {
      let rebackFunc = () => {
        if (!this.iframe) {
          setTimeout(() => {
            rebackFunc();
          }, 50);
          return;
        }
        let dom = this.iframe.document.querySelector('#mainContentBox');
        dom.classList.add('body-content');
      };
      rebackFunc();
    });

    //一次性添加所有向老项目传输信息的方法
    this.$root.$on('OpenOldProjectPageOffice', message => {
      this.iframe.dispatchEvent(
        new this.iframe.CustomEvent('mainMessage', {
          detail: {
            key: message.type,
            value: message.data
          }
        })
      );
    });

    /**@desc 老项目修改路由 */
    this.$root.$on('routerChange', route => {
      this.$router.push(route);
    });

    this.$root.$on('dispatchPoliticalEditDialog', e => {
      this.$router.push(
        '/ts-web-oa/party-member-management/party-member-management'
      );
    });
  },
  watch: {
    $route(to) {
      this.$root.$emit('sendMessageToOldFrame', {
        detail: {
          type: 'mainMessage',
          data: { key: 'routeChange', value: to.fullPath }
        }
      });
      if (!this.isChildApp(to.path)) {
        if (to.path.indexOf('http') >= 0 || to.path.indexOf('https') >= 0) {
          let isHttp = to.fullPath.indexOf('http') >= 0,
            a = document.createElement('a'),
            firstHttpIndex = to.fullPath.indexOf(isHttp ? 'http' : 'https'),
            url = to.fullPath.slice(firstHttpIndex);
          a.target = '_blank';
          a.href = url;
          a.click();
        }
        // this.iframe.location.hash = to.path;
        this.iframe.location.hash = to.fullPath; //jumpOpenDialog修改，可能有问题

        try {
          let params = JSON.parse(sessionStorage.getItem('routeParams'));
          sessionStorage.removeItem('routeParams');
          this.$root.$emit('sendMessageToOldFrame', {
            detail: { type: 'refreshNowPage', data: params }
          });
        } catch (e) {
          console.log(e);
        }
      }
    }
  }
};
