<template>
  <div>
    <div v-if="item.menus && item.menus.length != 0">
      <el-submenu :index="`${item.parentIndex_}`">
        <template slot="title">
          <span
            class="oaicon nav-item-icon "
            :class="item.icon || 'oaicon oa-icon-erjicaidan'"
            v-if="item.icon || isCollapse"
          ></span>
          <span
            slot="title"
            class="item-title"
            :style="isCollapse ? 'margin-left: 5px' : ''"
          >
            {{ item.menuname }}
          </span>
        </template>
        <sidebar-item
          :item="_item"
          :index="`${_item.parentIndex_}`"
          :key="_index"
          v-for="(_item, _index) in item.menus"
        />
      </el-submenu>
    </div>
    <el-menu-item
      @click="menuClick(item)"
      v-else
      :index="`${item.parentIndex_}`"
      class="item-title-menu"
    >
      <i
        class="oaicon nav-item-icon"
        :class="item.icon || 'oaicon oa-icon-erjicaidan'"
        v-if="item.icon || isCollapse"
      ></i>
      <span slot="title">{{ item.menuname }}</span>
    </el-menu-item>
  </div>
</template>

<script>
export default {
  name: 'sidebarItem',
  props: {
    item: {
      //数据对象
      type: Object,
      default: () => {}
    },
    index: {
      //数据索引
      type: [Number, String],
      default: 0
    }
  },
  computed: {
    isCollapse: function() {
      return this.$store.state.common.isCollapse;
    }
  },
  methods: {
    async menuClick(item) {
      if (item.alink.startsWith('http:') || item.alink.startsWith('https:')) {
        if (item.alink.indexOf('TSDesign') > -1) {
          let res = await this.ajax.getSsoToken({
            userCode: this.$store.state.common.userInfo.employeeNo
          });
          this.$router.push('/ts-web-oa/toDesign/report');
          let url = item.alink + '?token=' + res.object;
          window.open(url, '_blank');
        } else {
          window.open(item.alink, '_blank');
        }
      } else {
        if (item.alink === '/personnelReport/monthPersonStatistic') {
          window.open('/view-new/personnelReport/monthPersonStatistic.html');
          return false;
        }
        if (item.alink === '/personnelReport/monthDeptTypeStatistic') {
          window.open('/view-new/personnelReport/monthDeptTypeStatistic.html');
          return false;
        }
        let ddkhNewSearchIcon = 'oa_change_path_ddkh';
        if (item.alink.includes(ddkhNewSearchIcon)) {
          let url = item.alink.replace(ddkhNewSearchIcon, location.host);
          window.open('http://' + url, '_target');
          return false;
        }
        this.$router.push(item.alink);
      }
    }
  }
};
</script>

<style scoped lang="scss">
.iconfont {
  padding-right: 20px;
}
.nav-item-icon {
  font-size: 20px;
  margin-right: 8px;
}
</style>
