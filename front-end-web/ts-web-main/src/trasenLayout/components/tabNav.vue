<template>
  <div class="__common-layout-pageTabs" ref="tabNavContent">
    <!-- <el-scrollbar> -->
    <div
      ref="tabsContent"
      class="__tabs"
      :style="{ '--collapseWidth': isCollapseWidth + 8 + 'px' }"
    >
      <el-scrollbar
        ref="topscroll"
        style="width: 100%; border-radius: 0 0 4px 4px;"
        wrap-class="scroll-wrap"
        view-class="scroll-view"
      >
        <div style="display:flex; padding-right: 16px;">
          <div
            class="__tab-item"
            v-for="item in openedPageRouters"
            :class="{
              '__is-active': item.alink == $route.path
            }"
            :key="item.fullPath"
            @click="onClick(item)"
            @contextmenu.prevent="showContextMenu($event, item)"
          >
            <div>
              {{ item.menuname }}
            </div>
            <span
              v-if="item.alink != homeUrl"
              class="el-icon-error"
              @click.stop="onClose(item)"
              @contextmenu.prevent.stop=""
              :style="openedPageRouters.length <= 1 ? 'width:0;' : ''"
            ></span>
            <span v-else class="el-icon-s-home"></span>
          </div>
        </div>
      </el-scrollbar>
    </div>
    <!-- </el-scrollbar> -->
    <div v-show="contextMenuVisible">
      <ul
        :style="{ left: contextMenuLeft + 'px', top: contextMenuTop + 'px' }"
        class="__contextmenu"
      >
        <li>
          <el-button type="text" @click="reload()" size="mini">
            重新加载
          </el-button>
        </li>
        <li>
          <el-button
            type="text"
            @click="closeOtherLeft"
            :disabled="false"
            size="mini"
            >关闭左边</el-button
          >
        </li>
        <li>
          <el-button
            type="text"
            @click="closeOtherRight"
            :disabled="false"
            size="mini"
            >关闭右边</el-button
          >
        </li>
        <li>
          <el-button type="text" @click="closeOther" size="mini"
            >关闭其他</el-button
          >
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    isCollapseWidth: {
      type: Number,
      default: 160
    },
    blankRouteName: {
      type: String,
      default: 'blank'
    } //空白路由的name值
  },
  data() {
    return {
      homeUrl: '/index',
      contextMenuVisible: false, //右键菜单是否显示
      contextMenuLeft: 0, //右键菜单显示位置
      contextMenuTop: 0, //右键菜单显示位置
      contextMenuTargetPageRoute: null, //右键所指向的菜单路由
      openedPageRouters: [] //已打开的路由页面
    };
  },
  watch: {
    //当路由变更时，执行打开页面的方法
    $route: {
      handler(v) {
        this.openPage(v);
      },
      immediate: true
    }
  },
  mounted() {
    //添加点击关闭右键菜单
    window.addEventListener('click', this.closeContextMenu);
    this.$root.$on('closeContextMenu', this.closeContextMenu);
    //监听事件获取
    this.$root.$on('addTabNav', data => {
      let index = this.openedPageRouters.findIndex(
        // item => item.fullPath == data.fullPath
        item => item.alink == data.alink
      );
      if (index >= 0) {
        return;
      }
      this.openedPageRouters.push(data);
    });
    this.$root.$on('deleteRouteBySub', route => {
      let routes = this.openedPageRouters.find(e => e.alink == route.alink);
      this.onClose(routes);
    });
  },
  beforeDestroy() {
    window.removeEventListener('click', this.closeContextMenu);
    this.$root.$off('closeContextMenu', this.closeContextMenu);
  },
  methods: {
    /**更新激活菜单**/
    updateMenuFun(tabIndex) {
      let activeMenu = '';
      this.openedPageRouters.forEach(item => {
        if (item.alink == this.$route.path) {
          activeMenu = item.parentIndex_;
        }
      });
      this.$set(this.$store.state.common, 'activeMenu', activeMenu); //设激活下级菜单

      //滚动滚动条至指定位置
      if (tabIndex == -1) {
        return;
      }
      setTimeout(() => {
        let tabsScrollContent = this.$refs['tabNavContent'].querySelector(
            '.scroll-wrap.el-scrollbar__wrap'
          ),
          scrollClientWidth = tabsScrollContent.clientWidth,
          scrollWidth = tabsScrollContent.scrollWidth,
          scrollLeft = tabsScrollContent.scrollLeft,
          maxLeft = scrollWidth - scrollClientWidth,
          tab = this.$refs['tabNavContent'].querySelectorAll('.__tab-item')[
            tabIndex
          ],
          tabOffsetLeft = tab.offsetLeft;
        if (scrollClientWidth > scrollWidth) {
          return;
        }
        // 暂时先这样，可以优化一下
        tabsScrollContent.scrollLeft = tabOffsetLeft;
      });
    },
    /**@desc 添加首页**/
    async getHomeRouter(route) {
      let url = sessionStorage.getItem('homeUrl'),
        urlIndex = this.$store.state.common.menuLineList.findIndex(
          item => item.alink == this.homeUrl
        );

      sessionStorage.removeItem('homeUrl');
      if (url && urlIndex >= 0) {
        this.homeUrl = url;
      } else {
        let res = await this.$api({
          url: '/ts-basics-bottom/rolePage/getRolePage',
          method: 'POST'
        });
        if (res.success != false) {
          this.homeUrl = res.object.pageUrl;
        }
      }
      let arr = this.$store.state.common.menuLineList.filter(
        item => item.alink == this.homeUrl
      );
      if (arr.length > 0 && !arr[0].fullPath) {
        arr[0].fullPath = arr[0].alink;
      }
      this.openedPageRouters = arr;
    },
    /**@desc 获取菜单单条数据**/
    getMenuRouter(route) {
      let arr = this.$store.state.common.menuLineList.filter(
        item => item.alink == route.path
      );
      return arr[0] || {};
    },
    isMenuRouter(route) {
      let bool = false;
      let arr = this.$store.state.common.menuLineList.filter(
        item => item.alink == route.path
      );
      if (arr.length != 0) {
        bool = true;
      }
      return bool;
    },
    isWhiteRouter(route) {
      let whiteIndex = this.$store.state.common.whiteRouterList.findIndex(
        item => item.alink == route.path
      );
      return whiteIndex >= 0;
    },
    //打开页面
    async openPage(route) {
      if (this.openedPageRouters.length == 0) {
        await this.getHomeRouter(route);
      }
      if (route.name == this.blankRouteName) {
        return;
      }
      let tabIndex = this.openedPageRouters.findIndex(
        item => item.fullPath == route.fullPath
        // item => item.alink == route.path
      );

      if (tabIndex == -1) {
        let openedPageRoute = this.openedPageRouters.find(
          // alink 基础路径
          item => item.alink == route.path
        );
        //判断页面是否支持不同参数多开页面功能，如果不支持且已存在path值一样的页面路由，那就替换它
        if (!route.meta.canMultipleOpen && openedPageRoute != null) {
          tabIndex = this.openedPageRouters.indexOf(openedPageRoute);

          this.openedPageRouters.splice(tabIndex, 1, {
            ...this.getMenuRouter(route),
            fullPath: route.fullPath
          });
        } else {
          if (this.isMenuRouter(route) || this.isWhiteRouter(route)) {
            this.openedPageRouters.push({
              ...this.getMenuRouter(route),
              fullPath: route.fullPath
            });
            tabIndex = this.openedPageRouters.length - 1;
          }
        }
      }
      this.updateMenuFun(tabIndex);
    },
    //点击页面标签卡时
    onClick(route) {
      if (route.fullPath !== this.$route.fullPath) {
        this.$router.push(route.fullPath);
      }
    },
    //关闭页面标签时
    onClose(route) {
      let index = this.openedPageRouters.indexOf(route);
      this.delPageRoute(route);
      // 如果 alink 基础路径 已经被删完了，那也就没有必要一定 完整路径得一样才跳转上一页面
      let pageNum = this.openedPageRouters.filter(
        item => item.alink == route.alink
      ).length;
      if (route.fullPath === this.$route.fullPath || pageNum == 0) {
        //删除页面后，跳转到上一页面
        this.$router.replace(
          this.openedPageRouters[index == 0 ? 0 : index - 1].fullPath
        );
      }
    },
    //右键显示菜单
    showContextMenu(e, route) {
      this.contextMenuTargetPageRoute = route;
      this.contextMenuLeft = e.screenX - 8;
      this.contextMenuTop = e.layerY;
      this.contextMenuVisible = true;
    },
    //隐藏右键菜单
    closeContextMenu() {
      this.contextMenuVisible = false;
      this.contextMenuTargetPageRoute = null;
    },
    //重载页面
    reload() {
      if (this.contextMenuTargetPageRoute.alink != this.$route.path) {
        this.$router.push(this.contextMenuTargetPageRoute.fullPath);
      }
      this.postEvent({
        type: 'updateRouter',
        ...this.contextMenuTargetPageRoute
      });
    },
    //关闭其他页面
    closeOther() {
      for (let i = 0; i < this.openedPageRouters.length; i++) {
        let r = this.openedPageRouters[i];
        if (
          r !== this.contextMenuTargetPageRoute &&
          r.fullPath != this.homeUrl
        ) {
          this.delPageRoute(r);
          i--;
        }
      }
      if (this.contextMenuTargetPageRoute.fullPath != this.$route.fullPath) {
        this.$router.replace(this.contextMenuTargetPageRoute.fullPath);
      }
    },
    //根据路径获取索引
    getPageRouteIndex(fullPath) {
      for (let i = 0; i < this.openedPageRouters.length; i++) {
        if (this.openedPageRouters[i].fullPath === fullPath) {
          return i;
        }
      }
    },
    //关闭左边页面
    closeOtherLeft() {
      let index = this.openedPageRouters.indexOf(
        this.contextMenuTargetPageRoute
      );
      let currentIndex = this.getPageRouteIndex(this.$route.fullPath);
      if (index > currentIndex) {
        this.$router.replace(this.contextMenuTargetPageRoute.fullPath);
      }
      for (let i = 0; i < index; i++) {
        let r = this.openedPageRouters[i];
        if (r.fullPath != this.homeUrl) {
          this.delPageRoute(r);
          i--;
          index--;
        }
      }
    },
    //关闭右边页面
    closeOtherRight() {
      let index = this.openedPageRouters.indexOf(
        this.contextMenuTargetPageRoute
      );
      let currentIndex = this.getPageRouteIndex(this.$route.fullPath);
      for (let i = index + 1; i < this.openedPageRouters.length; i++) {
        let r = this.openedPageRouters[i];
        this.delPageRoute(r);
        i--;
      }
      if (index < currentIndex) {
        this.$router.replace(this.contextMenuTargetPageRoute.fullPath);
      }
    },
    //删除页面
    delPageRoute(route) {
      let routeIndex = this.openedPageRouters.indexOf(route);
      if (routeIndex >= 0) {
        this.openedPageRouters.splice(routeIndex, 1);
      }
      if (this.openedPageRouters.length == 1) {
        this.$set(this.$store.state.common, 'sideBarWidth', 0);
      }
      this.postEvent({
        ...route,
        type: 'deleteRouter'
      });
    },
    /**@desc**/
    postEvent(obj) {
      window.dispatchEvent(
        new CustomEvent('updateDataQianKun', { detail: obj })
      );
    }
  }
};
</script>
<style lang="scss">
.__common-layout-pageTabs {
  height: 30px;
  box-sizing: border-box;
  .__contextmenu {
    margin: 0;
    border: 1px solid #e4e7ed;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    box-shadow: 1px 1px 3px 0 rgba(0, 0, 0, 0.1);
    li {
      margin: 0;
      padding: 0px 15px;
      &:hover {
        background: #f2f2f2;
        cursor: pointer;
      }
      button {
        color: #2c3e50;
      }
    }
  }
  $c-tab-border-color: #efeff4;
  position: relative;
  &::before {
    content: '';
    // border-bottom: 1px solid $c-tab-border-color;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100%;
    background-color: $c-tab-border-color;
  }
  .__tabs {
    display: flex;
    margin-left: var(--collapseWidth);
    margin-right: 8px;
    background-color: #fff;
    border-radius: 0 0 4px 4px;
    .__tab-item {
      width: 104px;
      min-width: 104px;
      height: 30px;
      padding: 0 8px;
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      white-space: nowrap;
      border: 1px solid $c-tab-border-color;
      border-left: none;
      border-bottom: 0px;
      border-top: 0px;
      line-height: 14px;
      cursor: pointer;
      transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
        padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      &:not(.__is-active):hover {
        color: $theme-color;
      }
      &.__is-active {
        color: $theme-color;
      }
      .el-icon-error {
        overflow: hidden;
        font-size: 18px;
        color: #c2ccdc;
        background-color: #fff;
        vertical-align: text-top;
      }
      div {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .scroll-wrap {
    height: 47px;
    overflow-x: auto;
    overflow-y: hidden;
    background-color: #fff;
  }
  .scroll-view {
    display: inline-block;
  }
}
</style>
