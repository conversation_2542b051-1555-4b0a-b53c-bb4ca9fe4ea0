export default {
  data() {
    return {
      socket: {},
      admin: 'admin',
      websocket_connected_count: 0
    };
  },
  methods: {
    webSocketClient() {
      let that = this;
      if (typeof WebSocket == 'undefined') {
        console.log('您的浏览器不支持WebSocket');
      } else {
        //httprequest请求id
        // var sid = "<%=requestId%>";
        //实现化WebSocket对象，指定要连接的服务器地址与端口  建立连接
        //TODO 正式环境记得改地址
        that.socket = new WebSocket(
          `ws://192.168.81.118:9005/ts-information/messagewebsocket/${this.admin}`
        );
        //打开事件
        that.socket.onopen = function() {
          //socket.send("这是来自客户端的消息" + location.href + new Date());
        };
        //获得消息事件
        that.socket.onmessage = function(evt) {
          var message = evt.data;
          if (message != '链接成功') {
            message = JSON.parse(evt.data);
            window.dispatchEvent(
              new CustomEvent('webSocketMessage', { detail: message })
            );
          }
        };
        //关闭事件
        that.socket.onclose = function(e) {};
        //发生了错误事件
        that.socket.onerror = function() {
          that.websocket_connected_count++;
          if (that.websocket_connected_count <= 5) {
            that.webSocketClient();
          }
          console.log('Socket发生了错误');
          //此时可以尝试刷新页面
        };
      }
    }
  },
  mounted() {
    window.onbeforeunload = () => {
      this.socket.close();
    };
  }
};
