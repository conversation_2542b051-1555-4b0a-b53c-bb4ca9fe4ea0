<template>
  <div class="home">
    <!-- <div
      class="feedback"
      @click="handleShowAnonyMousBox"
      v-if="showFeedBack && $route.path == '/index' && showEdit"
    >
      <img src="@/assets/img/home/<USER>" />
      <i class="el-icon-error" @click="hide"></i>
    </div> -->

    <header-container
      ref="headerContainer"
      v-show="!isFullScreen"
      @OpenNewMessage="handleOpenNewMessage"
      @intelligentAssistantShow="intelligentAssistantShow"
    />
    <div class="header-alert" v-show="headerbg"></div>

    <section
      class="section"
      :style="{ height: `calc(100% - ${headerHeight}px)` }"
    >
      <!-- <div
        class="section-left-box"
        :style="{ width: isCollapseWidth + 'px' }"
        id="section-left-box"
      >
      </div> -->
      <div
        class="section-left"
        :style="{ width: isCollapseWidth + 'px' }"
        v-show="leftbg != 2"
      >
        <div class="left-alert" v-show="leftbg == 1"></div>
        <el-scrollbar class="section-left-scrollbar">
          <div
            class="fold-div"
            :style="{ width: isCollapseWidth - 20 + 'px' }"
            @click="setIsCollapse"
          >
            <i class="oaicon oa-icon-zhankai left-menue-top-icon"></i>
          </div>
          <el-menu
            class="el-menu-vertical-demo"
            :default-active="$store.state.common.activeMenu"
            :unique-opened="true"
            :collapse="isCollapse"
            v-if="menuKey"
          >
            <sidebarItem
              :item="item"
              :index="item.parentIndex_"
              :key="index"
              v-for="(item, index) in $store.state.common.childMenuList"
            />
          </el-menu>
        </el-scrollbar>
      </div>
      <div>
        <div class="section-right">
          <tabNav
            ref="tabNav"
            v-show="!isFullScreen"
            :isCollapseWidth="isCollapseWidth"
          />

          <div
            ref="container"
            class="qiankun-container"
            :style="{ height: `calc(100vh - ${headerHeight + 30}px)` }"
            :class="{ fullScreenHeight: isFullScreen }"
          >
            <oldProject
              ref="oldProject"
              :isCollapseWidth="isCollapseWidth"
              :leftbg="leftbg"
            />
            <div
              :id="item.userData.packageName"
              class="qiankun-container-div"
              :style="{ width: `calc(100% - ${isCollapseWidth}px - 8px)` }"
              :class="[
                index == qiankunIndex ? 'qiankun-container-div-active' : '',
                isFullScreen ? 'fullScreenWidth' : ''
              ]"
              v-for="(item, index) in app"
              :key="index"
            ></div>
            <div
              v-show="showRouterView"
              class="qiankun-container-div qiankun-container-div-active router-view"
              :style="{
                width: isFullScreen
                  ? '100%'
                  : `calc(100% - ${isCollapseWidth}px)`
              }"
              :class="{ 'router-view-full-screen': isFullScreen }"
            >
              <router-view />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 来电弹屏 -->
    <workSheetCallBox
      v-show="showCallingBox"
      :showCallingBox="showCallingBox"
      :workSheetCallData="workSheetCallData"
      @answer="handleAnswer"
      @close="showCallingBox = false"
    ></workSheetCallBox>

    <!-- 来电等待列表 -->
    <workSheetWaitingList
      v-show="workSheetWaitingList.length"
      :workSheetWaitingList="workSheetWaitingList"
    ></workSheetWaitingList>

    <!-- 加载特效 -->
    <loading-box v-show="isLoading" />
    <!-- 消息提醒 -->
    <toast v-if="showToast" v-bind="toastProp" @close="showToast = false" />

    <dialog-version-tips
      v-if="dialogVersionTips"
      v-model="dialogVersionTips"
      :versionInfo="versionInfo"
    />

    <dialog-new-message v-show="dialogNewMessage" v-model="dialogNewMessage" />
    <Anonymous ref="Anonymous" />
    <intelligentAssistant
      ref="intelligentAssistant"
      :visible="visible"
      @intelligentAssistantShow="intelligentAssistantShow"
    />
    <customization
      v-if="showFeedBack && $route.path == '/index'"
      ref="customization"
      @handleShowAnonyMousBox="handleShowAnonyMousBox"
    />
    <!-- <intelligentAssistants ref="intelligentAssistants" /> -->
  </div>
</template>

<script>
import index from './index.js';
import App from './App.js';
import toastWebSocket from './js/toastWebSocket.js';
import Anonymous from '../views/login/components/anonymous.vue';
import intelligentAssistant from '../components/intelligent-assistant/index.vue';
import customization from './components/customization.vue';
// import intelligentAssistants from '../components/intelligent-assistant/indexOne.vue';
export default {
  name: 'layout',
  // mixins: [index, App],
  mixins: [index, App, toastWebSocket],
  components: { Anonymous, intelligentAssistant, customization },
  data() {
    return {
      showEdit: true,
      visible: false
    };
  },
  computed: {
    isFullScreen() {
      return this.$store.state.common.isFullScreen;
    },
    showRouterView() {
      return (
        !(this.qiankunIndex >= 0) &&
        this.$store.state.common.whiteRouterList.findIndex(
          item => item == this.$route.path
        ) >= 0
      );
    },
    showFeedBack() {
      let set = this.$store.state.common.globalSetting;
      return set ? set.orgCode === 'hnnkyy' : false;
    }
  },
  methods: {
    //显示/隐藏意见反馈弹框
    handleShowAnonyMousBox(index) {
      this.$refs.Anonymous.handleToggleShow(index);
    },
    hide() {
      this.showEdit = !this.showEdit;
    },
    intelligentAssistantShow() {
      this.visible = !this.visible;
    }
  }
};
</script>

<style scoped lang="scss">
.fullScreenHeight {
  height: 100vh !important;
}
.fullScreenWidth {
  width: 100% !important;
}
.header-alert {
  position: fixed;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.3);
  height: 74px;
  width: 100%;
  z-index: 100;
}
.left-alert {
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.3);
  height: 100%;
  width: 100%;
  z-index: 100;
}
.home {
  height: 100%;
  width: 100%;
  &::-webkit-scrollbar {
    width: 6px;
    height: 8px;
    background: transparent;
  }

  .feedback {
    width: 100px;
    height: 100px;
    position: absolute;
    top: 70%;
    right: 100px;
    z-index: 1000;
    border-radius: 50%;
    background: #fff;
    box-shadow: inset 0 0 10px 5px #5260ff;
    img {
      margin: 17.5px 17.5px;
      width: 65px;
      height: 65px;
    }
    i {
      position: relative;
      top: -110px;
      left: 80px;
      font-size: 18px;
      color: #aaa;
    }
  }
}
.section {
  position: relative;
  .section-left-box {
    transition: all 0.3s;
    position: absolute;
    left: 0;
    top: 0;
    width: 160px;
    z-index: 100;
    height: 100%;
    border-right: 8px solid #eee;
    cursor: e-resize;
  }
  .section-left {
    transition: all 0.3s;
    position: absolute;
    left: 0;
    top: 0;
    width: 160px;
    z-index: 100;
    height: 100%;
    cursor: default;
    .fold-div {
      padding-left: 20px;
      height: 40px;
      display: flex;
      align-items: center;
      // background-color: $theme-color;
      background-color: $menu-color;
    }

    .section-left-scrollbar {
      height: 100%;
      // background-color: $theme-color;
      background-color: $menu-color;

      /deep/.el-scrollbar__wrap {
        overflow-x: hidden;
      }

      .left-menue-top-icon {
        display: inline-block;
        vertical-align: top;
        font-size: 20px;
        color: #c2ccdc;
      }
    }
    .el-menu-vertical-demo {
      border-right: none;
      width: 100%;
    }
  }

  .section-right {
    height: calc(100vh - 68px);
    width: 100%;
    position: relative;
    transition: all 0.3s;

    .qiankun-container {
      width: 100%;
      position: relative;
      .qiankun-container-div {
        position: fixed;
        right: 200vw;
        top: 200vh;
        width: 100%;
        height: 100%;
        overflow: hidden;

        ::v-deep {
          > div {
            height: 100%;
            width: 100%;

            background-color: transparent;
          }
        }

        &.qiankun-container-div-active {
          position: absolute;
          right: 0;
          top: 0;
        }
      }
    }

    .section-right-top {
      height: 48px;
      width: 100%;
      padding-left: 38px;
    }

    .section-right-view {
      height: calc(100% - 48px);
      width: 100%;
    }
  }
}
.router-fade-enter-active,
.router-fade-leave-active {
  transition: opacity 0.3s;
}
.router-fade-enter,
.router-fade-leave-active {
  opacity: 0;
}
.header-list {
  margin-right: 30px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 400;
  color: #caced3;
}
.header-list-acitve {
  font-size: 16px;
  font-weight: 400;

  background: linear-gradient(-90deg, #3ddcff 0%, #058fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
/deep/ {
  .el-menu {
    background-color: $menu-color;

    .el-menu-item {
      height: 40px;
      line-height: 40px;
      color: $menu-text-color;
      &:hover {
        background-color: $theme-color-hover;
        color: $menu-text-color;
      }

      &.is-active {
        background-color: $menu-isActive-color;
      }
    }
  }

  .el-submenu {
    .el-submenu__title {
      height: 40px;
      line-height: 40px;
      color: $menu-text-color;
      &:hover {
        background-color: $theme-color-hover;
        color: $menu-text-color;
      }
    }

    &.is-active {
      & > .el-submenu__title {
        background-color: $theme-color-highlight;
        color: $menu-text-color;
      }
      .el-menu {
        background-color: rgba($menu-isActive-color, 0.4);
      }
    }
  }

  .el-menu--collapse .el-submenu__title,
  .el-menu--collapse .el-menu-item {
    height: 50px;
    line-height: 50px;
    &.is-active {
      background-color: $menu-isActive-color;
      border: none;
      .nav-item-icon {
        color: #fff;
      }
    }
  }
  .el-menu--collapse .nav-item-icon {
    line-height: 50px;
  }
  .el-menu--collapse .nav-item-icon.oa-icon-erjicaidan {
    margin-left: -6px;
  }
  .el-menu--collapse .el-submenu.is-active {
    color: #fff;
    .el-submenu__title {
      background-color: $menu-isActive-color;
      border: none;
      .nav-item-icon {
        font-weight: 400;
      }
      &:hover {
        background-color: $menu-isActive-color;
      }
    }
    .nav-item-icon {
      color: #fff;
    }
  }
  .nav-item-icon {
    color: #c2ccdc;
  }
}
.router-view {
  box-sizing: border-box;
}
.router-view-full-screen {
  padding: 0;
}
</style>
