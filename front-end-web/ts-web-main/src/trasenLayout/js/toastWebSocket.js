import toast from '../components/toast.vue';

export default {
  components: {
    toast
  },
  data() {
    return {
      showToast: false,
      toastWebSocket: null, //消息提醒webSocket容器
      reConnectTimer: null, //webSocket心跳计时器 60秒重连一次
      reConnectErrorCount: 0, //重连失败次数
      toastProp: {} //消息提示盒子参数
    };
  },
  created() {
    this.connectWebSocket();
    window.addEventListener('beforeunload', () => {
      this.toastWebSocket && this.toastWebSocket.close();
    });
  },
  methods: {
    //连接webSocket
    connectWebSocket() {
      if (typeof WebSocket == 'undefined') {
        console.log('消息提示WebSocket连接失败： 浏览器不支持WebSocket');
        return;
      }

      this.toastWebSocket && this.toastWebSocket.close();
      this.toastWebSocket = new WebSocket(
        `ws://${
          location.host
        }/ts-basics-bottom/messagewebsocket/${this.$cookies.get(
          'sso_domain_user_code'
        )}`
      );

      this.toastWebSocket.onopen = () => {};
      this.toastWebSocket.onclose = () => {};
      this.toastWebSocket.onerror = () => {
        this.reConnectErrorCount++;
        if (this.reConnectErrorCount <= 5) {
          this.connectWebSocket();
        }
      };

      this.toastWebSocket.onmessage = this.handleGetToastMessage;

      this.reConnectTimer && clearInterval(this.reConnectTimer);
      this.reConnectTimer = setInterval(() => {
        if (this.toastWebSocket.readyState == 1) {
          this.toastWebSocket.send('ping');
        } else {
          this.connectWebSocket();
        }
      }, 10000);
    },

    handleGetToastMessage(e) {
      console.log(e);
      let message = e.data;
      if (message != '链接成功') {
        this.showToast = false;
        if (e.data == 'ping') return;
        message = JSON.parse(e.data);
        let content = message.content;
        if (message.title.indexOf('我的日程') != -1) {
          this.toastProp = {
            heading: message.title,
            text: content,
            hideAfter: 3000000,
            textColor: '#03a6ff',
            btns: {
              我知道了: toast => {
                var d = {
                  id: message.id,
                  isRemind: '2'
                };
                this.$api({
                  method: 'post',
                  url: '/ts-oa/schedule/insertOrUpdate',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  data: JSON.stringify(d)
                });
                toast.handleClose();
              },
              查看: toast => {
                if (message.url && message.url.indexOf('noticeId=') >= 0) {
                  let noticeId = message.url.split('noticeId=')[1];
                  this.ajax.saveNoticeBrowser({ noticeId });
                }

                var d = {
                  id: message.id,
                  isRemind: '2'
                };
                this.$api({
                  method: 'post',
                  url: '/ts-oa/schedule/insertOrUpdate',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  data: JSON.stringify(d)
                });

                this.$router.push('/hrm/schudler');
                this.$root.$emit('sendMessageToOldFrame', {
                  detail: {
                    type: 'openDialog',
                    data: {
                      path: '/hrm/schudler/add',
                      title: '日程编辑',
                      data: {
                        id: message.id
                      }
                    }
                  }
                });
                toast.handleClose();
              }
            }
          };
          this.showToast = true;
          this.$store.dispatch('common/getMessageCount');
        } else if (message.title.indexOf('工单来电') != -1) {
          this.handleCallReminder(e);
        } else {
          this.$root.$emit('sendMessageToOldFrame', {
            detail: {
              type: 'operateEvent',
              data: {
                triggerName: 'change',
                eventName: 'indexProcess'
              }
            }
          });

          this.toastProp = {
            heading: message.title,
            text: content,
            hideAfter: 3000000,
            textColor: '#03a6ff',
            position: 'bottom-right'
          };
          if (message.url && message.url.indexOf('&&NoJump') >= 0) {
            this.toastProp.btns = {
              关闭: toast => {
                toast.handleClose();
              }
            };
          } else {
            this.toastProp.btns = {
              查看: toast => {
                if (message.url && message.url.indexOf('noticeId=') >= 0) {
                  let noticeId = message.url.split('noticeId=')[1];
                  this.ajax.saveNoticeBrowser({ noticeId });
                }
                if (message.url.split('?')[1]) {
                  let params = {};
                  message.url
                    .split('?')[1]
                    .split('&')
                    .map(item => {
                      let keyList = item.split('=');
                      params[keyList[0]] = keyList[1];
                    });
                  sessionStorage.setItem('routeParams', JSON.stringify(params));
                }
                this.$router.push(message.url);
                toast.handleClose();
              }
            };
          }

          let messageInfo = message.url && message.url.split('?')[0];
          messageInfo = messageInfo && messageInfo.split('&');
          if (messageInfo) {
            this.handleDifferentiateInformation(messageInfo);
          }

          this.showToast = true;
          this.$store.dispatch('common/getMessageCount');
        }
      }
    },
    handleDifferentiateInformation(infoList) {
      let info = {};
      infoList.map(item => {
        let itemList = item.split('=');
        info[itemList[0]] = itemList[1];
      });

      if (
        info.MessageType == 'WorkOrder' &&
        ['1'].includes(String(info.status))
      ) {
        new Audio(process.env.VUE_APP_BASE_URL + 'OrderToast.mp3').play();
      }
    }
  },
  beforeDestroy() {
    this.toastWebSocket.close();
    this.reConnectTimer && clearInterval(this.reConnectTimer);
  }
};
