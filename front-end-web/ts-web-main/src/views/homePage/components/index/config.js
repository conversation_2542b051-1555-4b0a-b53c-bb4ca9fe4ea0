import config from '@/views/index/config';
export default {
  mixins: [config],
  data() {
    return {
      showColumnList: [
        {
          label: '多栏目',
          value: '2'
        },
        {
          label: '单栏目',
          value: '1'
        }
      ],
      showTypeList: [
        {
          label: '列表模式',
          value: '1'
        },
        {
          label: '图文模式',
          value: '2'
        }
      ],
      medicalTypeList: [
        {
          label: '业务情况',
          value: '1'
        },
        {
          label: '百分制考核情况',
          value: '101'
        }
      ],
      selectDataLabel: {
        userList: {
          dataName: 'readerName',
          showName: 'userStr'
        },
        roleList: {
          dataName: 'readerRoleName',
          showName: 'roleStr'
        },
        orgList: {
          dataName: 'readerOrgName',
          showName: 'orgStr'
        },
        groupList: {
          dataName: 'readerGroupName',
          showName: 'groupStr'
        }
      },
      informationChannellList: [],
      workFlowList: [],
      homeTemplate: []
    };
  },
  methods: {
    // 信息管理栏目内容列表
    getInformationChannellList() {
      this.ajax.informationChannellList().then(res => {
        this.informationChannellList = res.rows || [];
      });
    },
    // 流程类型获取
    getAllDefinitionInfoList() {
      this.ajax.getAllDefinitionInfoList().then(res => {
        this.workFlowList = res.object || [];
      });
    },
    findY() {
      let xList = this.homeTemplate.filter(e => e.x == 0);
      let y = 0;
      let h = 0;
      xList.forEach(i => {
        if (y <= i.y) {
          y = i.y;
          h = i.h;
        }
      });
      return y + h;
    }
  }
};
