<!-- 首页设置-弹窗 -->
<template>
  <div class="home-page-set-box" v-if="visible">
    <div class="content">
      <div class="header">
        <div class="title">{{ title }}</div>
        <img
          class="close-icon"
          src="@/assets/img/new-message/close.svg"
          width="12"
          height="12"
          @click="cancel"
        />
      </div>
      <div class="set-content">
        <div class="left">
          <ts-form ref="ruleForm" :model="form" labelWidth="110px">
            <ts-form-item label="门户标题" prop="title" :rules="rules.required">
              <ts-input
                v-model="form.title"
                :maxlength="10"
                placeholder="请填写10字以内的名称"
              />
            </ts-form-item>
            <ts-form-item label="使用权限" prop="allReader">
              <ts-switch
                v-model="form.allReader"
                :disabled="disabledSwitch"
                :active-value="1"
                :inactive-value="0"
              />
              <span v-if="form.allReader">&nbsp;全员人员</span>
              <div class="jurisdiction" v-else>
                <p>
                  <span @click="handleOpenSelectUser('userList')" class="color"
                    >+开放给人员：</span
                  >
                  <span
                    :title="
                      userList
                        .map(e => {
                          return e.readerName;
                        })
                        .join(',')
                    "
                  >
                    {{ userStr }}
                  </span>
                </p>
                <p>
                  <span @click="handleOpenSelectRole" class="color"
                    >+开放给角色：</span
                  ><span
                    :title="
                      roleList
                        .map(e => {
                          return e.readerRoleName;
                        })
                        .join(',')
                    "
                    >{{ roleStr }}</span
                  >
                </p>
                <p>
                  <span @click="handleOpenSelectTree" class="color"
                    >+开放给部门：</span
                  ><span
                    :title="
                      orgList
                        .map(e => {
                          return e.readerOrgName;
                        })
                        .join(',')
                    "
                    >{{ orgStr }}</span
                  >
                </p>
                <p>
                  <span @click="handleOpenSelectGroup" class="color"
                    >+开放给群组：</span
                  ><span
                    :title="
                      groupList
                        .map(e => {
                          return e.readerGroupName;
                        })
                        .join(',')
                    "
                    >{{ groupStr }}</span
                  >
                </p>
              </div>
            </ts-form-item>
            <ts-form-item label="模块高度一致" prop="fixedHeight">
              <ts-switch
                v-model="form.fixedHeight"
                :active-value="1"
                :inactive-value="0"
                @change="handleFixedHeightChange"
              />
            </ts-form-item>
            <ts-form-item label="首页内容" prop="title">
              <div class="addNewBlock" @click="addLabel" v-if="addLabelShow">
                <i class="fa fa-plus"></i>
              </div>
              <div class="labelDiv" v-else>
                <ts-form ref="ruleForm" :model="formContent" labelWidth="80px">
                  <ts-form-item label="显示内容" prop="title">
                    <ts-select
                      v-model="formContent.elementType"
                      popper-class="noBottom"
                    >
                      <ts-option
                        v-for="(item, index) in labelList"
                        :key="index"
                        :label="item.labelName"
                        :value="item.elementType"
                      ></ts-option>
                    </ts-select>
                  </ts-form-item>
                  <ts-form-item
                    label="栏目数量"
                    prop="elementShow"
                    v-if="formContent.elementType == '6'"
                  >
                    <ts-select
                      v-model="formContent.elementColumn"
                      popper-class="noBottom"
                    >
                      <ts-option
                        v-for="(item, index) in showColumnList"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                      ></ts-option>
                    </ts-select>
                  </ts-form-item>
                  <ts-form-item
                    label="显示模式"
                    prop="elementShow"
                    v-if="formContent.elementType == '6'"
                  >
                    <ts-select
                      v-model="formContent.elementShow"
                      popper-class="noBottom"
                    >
                      <ts-option
                        v-for="(item, index) in showTypeList"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                      ></ts-option>
                    </ts-select>
                  </ts-form-item>
                  <ts-form-item
                    label="栏目内容"
                    prop="elementChannel"
                    v-if="formContent.elementType == '6'"
                  >
                    <ts-select
                      v-model="formContent.elementChannel"
                      clearable
                      multiple
                      filterable
                      popper-class="noBottom"
                    >
                      <ts-option
                        v-for="(item, index) in informationChannellList"
                        :key="index"
                        :label="item.channelName"
                        :value="item.id"
                      ></ts-option>
                    </ts-select>
                  </ts-form-item>
                  <ts-form-item
                    label="数据内容"
                    prop="elementMedicalBusiness"
                    v-if="formContent.elementType == '14'"
                  >
                    <ts-select
                      v-model="formContent.elementMedicalBusiness"
                      popper-class="noBottom"
                    >
                      <ts-option
                        v-for="(item, index) in medicalTypeList"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                      ></ts-option>
                    </ts-select>
                  </ts-form-item>
                  <ts-form-item
                    label="流程类型"
                    prop="elementChannel"
                    v-if="formContent.elementType == '15'"
                  >
                    <ts-select
                      v-model="formContent.elementChannel"
                      clearable
                      filterable
                      popper-class="noBottom"
                    >
                      <ts-option
                        v-for="(item, index) in workFlowList"
                        :key="index"
                        :label="item.workflowName"
                        :value="item.wfDefinitionId"
                      ></ts-option>
                    </ts-select>
                  </ts-form-item>
                  <ts-form-item
                    label="栏目名称"
                    prop="elementName"
                    v-if="formContent.elementType == '15'"
                  >
                    <ts-input
                      v-model="formContent.elementName"
                      :maxlength="50"
                      placeholder="请填写栏目名称"
                    />
                  </ts-form-item>
                </ts-form>
                <div class="footer">
                  <el-button type="primary" class="ts-button" @click="submitAdd"
                    >确定</el-button
                  >
                  <el-button class="ts-button cancel" @click="addLabel"
                    >取消</el-button
                  >
                </div>
              </div>
            </ts-form-item>
          </ts-form>
        </div>
        <div class="right">
          <el-scrollbar
            style="width: 100%;height: 100%;"
            v-if="homeTemplate.length != 0"
          >
            <grid-layout
              :layout.sync="homeTemplate"
              :col-num="colNum"
              :row-height="10"
              :is-draggable="true"
              :is-resizable="false"
              :is-mirrored="false"
              :vertical-compact="true"
              :margin="[8, 8]"
              :use-css-transforms="true"
              @layout-updated="layoutUpdatedEvent"
              v-if="homeTemplate.length > 0"
            >
              <grid-item
                v-for="(item, index) in homeTemplate"
                :x="item.x"
                :y="item.y"
                :w="item.w"
                :h="item.h"
                :i="item.i"
                :key="item.i"
                :is-resizable="item.isResizable"
                :minW="item.minW"
                :minH="item.minH"
                @move="moveEvent"
                @moved="movedEvent"
                @resize="resizeEvent"
                @resized="resizedEvent"
              >
                <div class="grid-item" @click="toshowLabel(item, index)">
                  <i class="el-icon-error" @click="delTemplate(index)"></i>
                  {{ item.labelName }}
                </div>
              </grid-item>
            </grid-layout>
          </el-scrollbar>
        </div>
      </div>
      <div class="footer">
        <el-button class="ts-button" type="primary" @click="submit"
          >保存</el-button
        >
        <el-button class="ts-button cancel" @click="cancel">取消</el-button>
      </div>
    </div>
    <ts-user-dept-select ref="TsUserDeptSelect" @ok="handleOk" />
    <ts-user-group-select ref="TsUserGroupSelect" @ok="handleOk" />
    <ts-user-role-select ref="TsUserRoleSelect" @ok="handleOk" />
    <ts-user-tree-select ref="TsUserTreeSelect" @ok="handleOk" />
  </div>
</template>

<script>
import commonUtils from '@/unit/file.js';
import sort from './sort';
import { GridLayout, GridItem } from 'vue-grid-layout';
import TsUserDeptSelect from '@/components/ts-user-dept-select';
import TsUserGroupSelect from '@/components/ts-user-group-select';
import TsUserRoleSelect from '@/components/ts-user-role-select';
import TsUserTreeSelect from '@/components/ts-user-tree-select';
import config from './config';
import { deepClone } from '@/utils/deepClone';
export default {
  mixins: [config, sort],
  components: {
    GridLayout,
    GridItem,
    TsUserDeptSelect,
    TsUserGroupSelect,
    TsUserRoleSelect,
    TsUserTreeSelect
  },
  data() {
    return {
      isDraggable: false,
      visible: false,
      title: '新建门户',
      form: {
        allReader: 1,
        fixedHeight: 0
      },
      formContent: {},
      rules: {
        required: { required: true, message: '必填' }
      },
      addLabelShow: true,
      SelectType: '',
      userList: [],
      userStr: '',
      roleList: [],
      roleStr: '',
      groupList: [],
      groupStr: '',
      orgList: [],
      orgStr: '',
      defaultSwitch: 1,
      disabledSwitch: false,
      editIndex: null,
      api: 'save'
    };
  },
  methods: {
    async edit(id = null) {
      this.getInformationChannellList();
      this.getAllDefinitionInfoList();
      if (id) {
        this.api = 'update';
        let res = await this.ajax.selectById({ id });
        let data = res.object || {};
        if (data.allReader == 0) {
          if (data.readerName) {
            let users = data.readerName.split(',');
            users.forEach((item, index) => {
              this.userList.push({
                readerName: item,
                readerUser: data.readerUser.split(',')[index]
              });
            });
            this.showStr('userList', 5);
          }
          if (data.readerRoleName) {
            let roles = data.readerRoleName.split(',');
            roles.forEach((item, index) => {
              this.roleList.push({
                readerRoleName: item,
                readerRole: data.readerRole.split(',')[index]
              });
            });
            this.showStr('roleList', 5);
          }
          if (data.readerOrgName) {
            let orgs = data.readerOrgName.split(',');
            orgs.forEach((item, index) => {
              this.orgList.push({
                readerOrgName: item,
                readerOrg: data.readerOrg.split(',')[index]
              });
            });
            this.showStr('orgList', 5);
          }
          if (data.readerGroupName) {
            let orgs = data.readerGroupName.split(',');
            orgs.forEach((item, index) => {
              this.groupList.push({
                readerGroupName: item,
                readerGroup: data.readerGroup.split(',')[index]
              });
            });
            this.showStr('groupList', 5);
          }
        }
        this.disabledSwitch = data.isDefault ? true : false;
        let portalElement = [];
        if (data.portalElement && data.portalElement.length) {
          portalElement = data.portalElement.map(item => {
            let obj = this.labelList.find(
              e => e.elementType == item.elementType
            );
            if (item.elementType == 6 && item.elementChannel) {
              item.elementChannel = item.elementChannel.split(',');
            }
            return {
              labelName: obj?.labelName || '',
              elementChannel: item.elementChannel,
              elementType: item.elementType,
              elementColumn: item.elementColumn,
              elementMedicalBusiness: item.elementMedicalBusiness,
              elementName: item.elementName,
              elementShow: item.elementShow,
              i: item.id,
              w: item.w || item.width,
              h: item.h || item.height,
              x: item.x || item.xpos,
              y: item.y || item.ypos,
              isDraggable: !!item.isDraggable,
              isResizable: !!item.isResizable
            };
          });
        }
        this.homeTemplate = portalElement;
        this.form = {
          id: data.id,
          title: data.title,
          allReader: data.allReader,
          fixedHeight: data.fixedHeight
        };
      } else {
        this.api = 'save';
      }
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate();
      });
    },
    handleFixedHeightChange(val) {
      let arr = deepClone(this.homeTemplate);
      arr.map(item => {
        // 模块高度一致时，统一设置高度为8
        if (!!val) item.h = 8;
        else {
          let obj = this.labelList.find(e => e.elementType == item.elementType);
          item.h = obj.h;
          // 信息管理模块如果是单栏目，盒子高度为6
          if (item.elementType == 6 && item.elementColumn == 1) {
            item.h = 6;
          }
        }
      });
      this.homeTemplate = deepClone(this.resetModuleLocation(arr));
    },
    addLabel() {
      this.editIndex = null;
      this.addLabelShow = !this.addLabelShow;
      if (!this.addLabelShow) this.formContent = {};
    },
    delTemplate(index) {
      this.homeTemplate.splice(index, 1);
    },
    toshowLabel(item, index) {
      if (this.isDraggable) {
        this.isDraggable = false;
        this.editIndex = null;
        this.addLabelShow = true;
        this.formContent = {};
      } else {
        let obj = deepClone(item);
        this.formContent = obj;
        this.editIndex = index;
        this.addLabelShow = false;
      }
    },
    handleOk(res) {
      let { typeApproverData = {} } = res || {},
        { empList = [] } = typeApproverData;
      this[this.SelectType] = [];
      empList.map(item => {
        let obj = null;
        if (this.SelectType == 'userList') {
          obj = {
            readerName: item.empName,
            readerUser: item.empCode
          };
        }
        if (this.SelectType == 'roleList') {
          obj = {
            readerRoleName: item.roleName,
            readerRole: item.id
          };
        }
        if (this.SelectType == 'orgList') {
          obj = {
            readerOrgName: item.name,
            readerOrg: item.id
          };
        }
        if (this.SelectType == 'groupList') {
          obj = {
            readerGroupName: item.groupName,
            readerGroup: item.groupId
          };
        }
        this[this.SelectType].push(obj);
      });
      this.showStr(this.SelectType, 5);
    },
    /**@desc 打开人员选择弹窗 */
    handleOpenSelectUser() {
      this.SelectType = 'userList';
      let empList = [];
      this.userList.map(item => {
        empList.push({
          empName: item.readerName,
          empCode: item.readerUser
        });
      });
      this.$refs.TsUserDeptSelect.open('typeApproverData', {
        showCheckbox: false,
        title: '选择',
        empList,
        deptList: [],
        appendToBody: true,
        isRadio: false
      });
    },
    handleOpenSelectGroup() {
      this.SelectType = 'groupList';
      let empList = [];
      this.groupList.map(item => {
        empList.push({
          groupId: item.readerGroup,
          groupName: item.readerGroupName
        });
      });
      this.$refs.TsUserGroupSelect.open('typeApproverData', {
        showCheckbox: false,
        title: '选择',
        empList,
        deptList: [],
        appendToBody: true,
        isRadio: false
      });
    },
    handleOpenSelectRole() {
      this.SelectType = 'roleList';
      let empList = [];
      this.roleList.map(item => {
        empList.push({
          id: item.readerRole,
          roleName: item.readerRoleName
        });
      });
      this.$refs.TsUserRoleSelect.open('typeApproverData', {
        showCheckbox: false,
        title: '选择',
        empList,
        deptList: [],
        appendToBody: true,
        isRadio: false
      });
    },
    handleOpenSelectTree() {
      this.SelectType = 'orgList';
      let deptList = [];
      this.orgList.map(item => {
        deptList.push({
          id: item.readerOrg,
          name: item.readerOrgName
        });
      });
      this.$refs.TsUserTreeSelect.open('typeApproverData', {
        showCheckbox: true,
        title: '选择',
        empList: [],
        deptList,
        appendToBody: true,
        isRadio: false
      });
    },
    submitAdd() {
      let arr = deepClone(this.homeTemplate);
      let selectObj = this.labelList.find(
        e => e.elementType == this.formContent.elementType
      );
      let editObject = Object.assign({}, this.formContent, selectObj);
      // 信息管理模块如果是多栏目，盒子宽度为2个单元格
      if (editObject.elementType == 6 && this.formContent.elementColumn == 2)
        editObject.w = 2;
      //模块高度一致时，h设置为8
      if (!!this.form.fixedHeight) editObject.h = 8;
      if (this.editIndex != null) {
        arr[this.editIndex] = editObject;
      } else {
        editObject.x = 0;
        editObject.y = this.findY();
        editObject.i = commonUtils.guid();
        arr.push(editObject);
      }
      this.homeTemplate = deepClone(this.resetModuleLocation(arr));
      this.addLabel();
      this.formContent = {};
    },
    moveEvent(i, x, y) {
      // console.log(`移动中-${i} ${x} ${y}`);
      let findItem = this.homeTemplate.find(e => e.i == i);
      findItem.x = x;
      findItem.y = y;
    },
    movedEvent(i, x, y) {
      // console.log(`移动完成-${i} ${x} ${y}`);
    },
    resizeEvent(i, h, w) {
      let findItem = this.homeTemplate.find(e => e.i == i);
      findItem.h = h;
      findItem.w = w;
      // console.log(`缩放中-${i} ${h} ${w}`);
    },
    resizedEvent(i, h, w) {
      // console.log(`缩放完成-${i} ${h} ${w}`);
    },
    layoutUpdatedEvent(newLayout) {
      // console.log(newLayout);
      this.isDraggable = true;
    },
    submit() {
      let submitData = { ...this.selectObject(), ...this.form };
      let portalElement = deepClone(this.homeTemplate);
      portalElement.forEach(e => {
        if (e.elementType == '6') {
          e.elementChannel = e.elementChannel.join(',');
        }
        e.xpos = e.x;
        e.ypos = e.y;
        e.width = e.w;
        e.height = e.h;
        e.isResizable = e.isResizable ? 1 : 0;
        e.isDraggable = e.isDraggable ? 1 : 0;
      });
      submitData.portalElement = portalElement;
      this.ajax.portalThemeSave(submitData, this.api).then(res => {
        if (res.success) {
          this.$message.success(res.message || '操作成功');
          this.cancel();
          this.$emit('ok');
        } else {
          this.$message.error(res.message || '操作失败');
        }
      });
    },
    selectObject() {
      if (this.form.reader) return {};
      let readerName = this.userList
          .map(e => {
            return e.readerName;
          })
          .join(','),
        readerUser = this.userList
          .map(e => {
            return e.readerUser;
          })
          .join(','),
        readerRole = this.roleList
          .map(e => {
            return e.readerRole;
          })
          .join(','),
        readerRoleName = this.roleList
          .map(e => {
            return e.readerRoleName;
          })
          .join(','),
        readerOrg = this.orgList
          .map(e => {
            return e.readerOrg;
          })
          .join(','),
        readerOrgName = this.orgList
          .map(e => {
            return e.readerOrgName;
          })
          .join(','),
        readerGroup = this.groupList
          .map(e => {
            return e.readerGroup;
          })
          .join(','),
        readerGroupName = this.groupList
          .map(e => {
            return e.readerGroupName;
          })
          .join(',');
      return {
        readerUser,
        readerName,
        readerRole,
        readerRoleName,
        readerOrg,
        readerOrgName,
        readerGroup,
        readerGroupName
      };
    },
    showStr(dataList, showNum) {
      let showS = '',
        label =
          dataList == 'userList'
            ? '人'
            : dataList == 'roleList'
            ? '个角色'
            : dataList == 'groupList'
            ? '个群组'
            : dataList == 'orgList'
            ? '个部门'
            : '',
        dataName = this.selectDataLabel[dataList].dataName,
        showName = this.selectDataLabel[dataList].showName;
      if (this[dataList].length > showNum) {
        let str = [];
        this[dataList].slice(0, showNum).forEach(item => {
          str.push(item[dataName]);
        });
        showS = str.join(',') + `等${this[dataList].length}${label}`;
      } else {
        showS = this[dataList]
          .map(e => {
            return e[dataName];
          })
          .join(',');
      }
      this.$set(this, showName, showS);
    },
    cancel() {
      Object.assign(this.$data, this.$options.data());
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.home-page-set-box {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0%;
  top: 0%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  transition: all 0.5s;
  z-index: 999;
  .content {
    display: flex;
    flex-direction: column;
    width: 80%;
    height: 648px;
    overflow: hidden;
    background: #fff;
    border-radius: 2px;
  }
  .ts-button {
    background-color: #5260ff;
    border: 1px #5260ff solid;
    border-radius: 2px;
    color: #fff;
    line-height: 28px;
    min-width: 60px;
    padding: 0 8px;
    height: 30px;
    font-size: 14px;
    &.cancel {
      background: #fff;
      border-color: #ccc;
      color: #333;
    }
  }
  .header {
    box-sizing: border-box;
    border-bottom: 1px solid #e4e4e4;
    padding: 8px 24px;
    color: #666;
    height: 48px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
    .close-icon {
      cursor: pointer;
    }
  }
  .set-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    padding: 16px 24px;
    box-sizing: border-box;
    .left {
      flex: 1;
      /deep/ .ts-form {
        .ts-form-item {
          margin-bottom: 10px;
        }
        .jurisdiction {
          p {
            line-height: 2rem;
            .color {
              cursor: pointer;
              color: #5260ff;
            }
          }
        }
        .addNewBlock {
          width: 100px;
          height: 100px;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px solid #ccc;
          color: #ccc;
          border-radius: 10px;
          i {
            font-size: 34px;
          }
        }
        .labelDiv {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          border-radius: 4px;
          background-color: #fff;
          box-shadow: 0px 2px 4px 0px #0000004d;
          padding-right: 8px;
          padding-top: 8px;
          box-sizing: border-box;
          padding-bottom: 60px;
        }
      }
    }
    .right {
      flex: 2;
      width: 100%;
      height: 100%;
      .grid-item {
        width: 100%;
        height: 100%;
        background-color: #e4e4e4;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        position: relative;
        color: #333;
        cursor: pointer;
        &:hover {
          i {
            display: block;
          }
        }
        i {
          position: absolute;
          top: 10px;
          right: 10px;
          font-size: 18px;
          cursor: pointer;
          display: none;
        }
      }
    }
  }
  .footer {
    box-sizing: border-box;
    text-align: right;
    background-color: #fff;
    padding: 8px 24px;
    border-top: 1px solid #e4e4e4;
  }
}
</style>
