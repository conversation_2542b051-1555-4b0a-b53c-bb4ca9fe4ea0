<!-- 首页设置 -->
<template>
  <div class="index">
    <div class="button">
      <el-button type="primary" @click="add" class="ts-button"
        >新建门户</el-button
      >
    </div>
    <div class="doorList">
      <div class="door" v-for="(item, index) in doorList" :key="index">
        <div class="doorLeft">
          <img src="@/assets/img/index/def_img_2.png" />
        </div>
        <div class="doorRight">
          <div class="right-top">
            <p class="title">{{ item.title }}</p>
            <p v-if="item.isDefault" class="default">默认</p>
          </div>
          <p class="showTitle" :title="`${getRederName(item)}`">
            可见范围: {{ getRederName(item) }}
          </p>
          <div class="right-bottom">
            <i
              class="fa fa-trash-o"
              v-if="index != 0"
              @click="deletePortal(item)"
            ></i>
            <i class="fa fa-pencil-square-o" @click="edit(item)"></i>
          </div>
        </div>
      </div>
    </div>
    <DialogGatewayDetail
      ref="dialogGatewayDetail"
      v-model="dialogNewMessageShow"
      @ok="selectPortalThemeList"
    />
  </div>
</template>

<script>
import DialogGatewayDetail from './dialog-gateway-detail.vue';
export default {
  components: { DialogGatewayDetail },
  data() {
    return {
      dialogNewMessageShow: false,
      doorList: []
    };
  },
  mounted() {
    this.selectPortalThemeList();
  },
  methods: {
    getRederName(item) {
      if (item.allReader == 1) return '全体人员';
      var seeJurisdiction = [];
      if (item.readerName) {
        seeJurisdiction.push(item.readerName);
      }
      if (item.readerRoleName) {
        seeJurisdiction.push(item.readerRoleName);
      }
      if (item.readerOrgName) {
        seeJurisdiction.push(item.readerOrgName);
      }
      if (item.readerGroupName) {
        seeJurisdiction.push(item.readerGroupName);
      }
      return seeJurisdiction.length ? seeJurisdiction.join('、') : '';
    },
    async deletePortal(item) {
      try {
        await this.$confirm(
          `<span>删除后将无法恢复，您确认删除吗？</span>`,
          '提示',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );
        this.ajax.portalThemeDelete({ ...item }).then(res => {
          if (!res.success) this.$message.error('删除失败');
          this.$message.success('删除成功');
          this.selectPortalThemeList();
        });
      } catch (e) {
        console.error(e);
      }
    },
    // 获取门户数据别表
    selectPortalThemeList() {
      let param = {
        authorization: 'N',
        isFindChild: 'N'
      };
      this.ajax.selectPortalThemeList(param).then(res => {
        if (res.success) {
          this.doorList = res.object || [];
        } else {
          this.$message.error(res.message || '获取失败');
        }
      });
    },
    add() {
      this.$refs.dialogGatewayDetail.edit();
    },
    edit(item) {
      this.$refs.dialogGatewayDetail.edit(item.id);
    }
  }
};
</script>

<style lang="scss" scoped>
.index {
  .ts-button {
    background-color: #5260ff;
    border: 1px #5260ff solid;
    border-radius: 2px;
    color: #fff;
    line-height: 28px;
    min-width: 60px;
    padding: 0 8px;
    height: 30px;
    font-size: 14px;
  }
  .doorList {
    margin-top: 10px;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .door {
      width: 31%;
      padding: 8px;
      border: 1px solid #e4e4e4;
      border-radius: 10px;
      margin-right: 8px;
      margin-bottom: 8px;
      display: flex;
      height: 190px;
      .doorLeft {
        flex: 1;
        background-color: #5260ff17;
        border-radius: 10px;
        img {
          width: 31%;
          display: block;
          margin: 0 auto;
          margin-top: 20%;
        }
      }
      .doorRight {
        flex: 1;
        padding-left: 20px;
        position: relative;
        .right-top {
          display: flex;
          justify-content: space-between;
          height: 32px;
          line-height: 32px;
          p {
            margin: 0;
          }
          .title {
            font-size: 18px;
          }
          .default {
            line-height: 32px;
            border-radius: 10px;
            border: 1px solid #ccc;
            padding: 0 16px;
            color: #999;
          }
        }
        .showTitle {
          line-height: 20px;
          font-size: 14px;
          color: rgba(51, 51, 51, 0.7);
          word-break: break-all;
          text-overflow: -o-ellipsis-lastline;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 5;
          line-clamp: 5;
          -webkit-box-orient: vertical;
        }
        .right-bottom {
          position: absolute;
          bottom: 0;
          display: flex;
          flex-direction: row-reverse;
          width: 100%;
          right: 0;
          .fa {
            margin-left: 15px;
            font-size: 18px;
            cursor: pointer;
            color: #888;
          }
        }
      }
    }
  }
}
</style>
