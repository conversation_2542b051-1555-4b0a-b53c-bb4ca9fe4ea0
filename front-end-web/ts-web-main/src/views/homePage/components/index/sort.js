import { deepClone } from '@/utils/deepClone';
export default {
  data() {
    return {
      colArray: [],
      count: 1, //
      colNum: 4
    };
  },
  methods: {
    resetModuleLocation(arr) {
      let newArr = deepClone(arr);
      // 先对模块进行排序，根据行排序，当行相同时，按列排序
      newArr.sort(this.ySort);
      for (var i = 0; i < this.colNum; i++) {
        this.colArray[i] = {
          x: i,
          y: 0
        };
      }
      newArr.map(item => {
        // 计算模块宽度
        this.count = (item.w + 0.5) >>> 0; //取整;
        let data = {};
        if (this.count == 1) {
          this.colArray.sort(this.ySort);
          data = this.draw(item, this.colArray[0].x, this.colArray[0].y);
        } else if (this.count === this.colNum) {
          this.colArray.sort(this.yDownSort);
          data = this.draw(item, 0, this.colArray[0].y);
        } else {
          data = this.placeWide(item);
        }
        item.x = data.x;
        item.y = data.y;
      });
      return newArr;
    },

    placeWide: function($item) {
      var arr = this.colArray.sort(this.xSort);
      for (var i = this.colArray.length - this.count, j = 0; i-- >= 0; ) {
        if (!arr[j]) {
          break;
        }
        arr[j].maxY = this.maxY(j);
        arr[j].diff = Math.abs(arr[j].y - arr[j].maxY);
        j++;
      }
      arr.sort(this.maxSort);
      arr[0].y += arr[0].diff;
      return this.draw($item, arr[0].x, arr[0].y);
    },

    draw: function($item, xNum, yNum) {
      this.colArray.sort(this.xSort);
      for (var i = 0; i < this.count; i++) {
        this.colArray[xNum + i].y = yNum + $item.h;
      }
      return {
        x: xNum,
        y: yNum
      };
    },

    maxY: function(num) {
      var col = 0;
      for (var i = 0; i < this.count; i++) {
        col = this.colArray[num + i].y >= col ? this.colArray[num + i].y : col;
      }
      return col;
    },

    //根据列排序
    xSort: function(b, c) {
      return b.x - c.x;
    },

    //根据行排序，当行相同时，按列排序
    ySort: function(b, c) {
      return b.y === c.y ? b.x - c.x : b.y - c.y;
    },

    //根据行降序
    yDownSort: function(b, c) {
      return c.y - b.y;
    },

    //根据最大值排序
    maxSort: function(b, c) {
      return b.maxY === c.maxY ? b.x - c.x : b.maxY - c.maxY;
    }
  }
};
