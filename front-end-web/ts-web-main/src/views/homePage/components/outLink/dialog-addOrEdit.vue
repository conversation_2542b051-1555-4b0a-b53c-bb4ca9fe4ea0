<!-- 首页设置-弹窗 -->
<template>
  <el-dialog
    custom-class="homePageSet"
    :title="title"
    width="600px"
    :show-close="true"
    :visible.sync="visible"
    :append-to-body="true"
    @close="cancel"
  >
    <div class="setContent">
      <ts-form ref="ruleForm" :model="form" labelWidth="110px">
        <ts-form-item label="图标" prop="icon" :rules="rules.required">
          <base-upload-fileid
            ref="icon"
            v-model="form.icon"
            :limit="1"
            :moduleName="'global'"
            class="systemSetUpload"
          >
            <div class="img-box">
              <img src="@/assets/img/index/defPhoto.png" />
              <span>推荐尺寸60*60</span>
            </div>
          </base-upload-fileid>
        </ts-form-item>
        <ts-form-item label="名称" prop="title" :rules="rules.required">
          <ts-input
            v-model="form.title"
            :maxlength="10"
            placeholder="请填写10字以内的名称"
          />
        </ts-form-item>
        <ts-form-item label="描述" prop="remark">
          <ts-input
            v-model="form.remark"
            :maxlength="200"
            type="textarea"
            placeholder="请填写"
          />
        </ts-form-item>
        <ts-form-item label="跳转地址" prop="outerNetAddress">
          <ts-input
            v-model="form.outerNetAddress"
            :maxlength="50"
            placeholder="请填写"
          />
        </ts-form-item>
        <el-row>
          <el-col :span="12">
            <ts-form-item label="参数名字" prop="parameterName1">
              <ts-input
                v-model="form.parameterName1"
                :maxlength="10"
                placeholder="请填写"
              />
            </ts-form-item>
          </el-col>
          <el-col :span="12">
            <ts-form-item label="参数值" prop="parameterValue1">
              <ts-select
                v-model="form.parameterValue1"
                popper-class="noBottom"
                clearable
              >
                <ts-option label="usercode" value="usercode"></ts-option>
              </ts-select>
            </ts-form-item>
          </el-col>
        </el-row>
      </ts-form>
    </div>
    <div class="footer">
      <el-button class="ts-button" type="primary" @click="submit"
        >保存</el-button
      >
      <el-button class="ts-button cancel" @click="cancel">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { deepClone } from '@/utils/deepClone';
export default {
  data() {
    return {
      visible: false,
      title: '新建门户',
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },
      api: ''
    };
  },
  methods: {
    async edit(rowData = null) {
      if (rowData) {
        let data = deepClone(rowData);
        data.icon = data.icon.split('/').pop();
        this.form = data;
        this.api = 'update';
      } else {
        this.api = 'save';
      }
      this.visible = true;
    },
    async submit() {
      let validate = await this.$refs.ruleForm.validate().catch(res => res);
      if (!validate) {
        return;
      }
      let submitData = deepClone(this.form);
      submitData.icon =
        '/ts-basics-bottom/fileAttachment/downloadFile/' + submitData.icon;
      this.ajax.loginSsoListHomeUpdate(submitData, this.api).then(res => {
        if (res.success) {
          this.$message.success(res.message || '操作成功');
          this.$emit('ok');
          this.cancel();
        } else {
          this.$message.error(res.message || '操作失败');
        }
      });
      this.cancel();
    },
    cancel() {
      this.$refs.ruleForm.clearValidate();
      this.visible = false;
      this.form = {};
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  height: 400px;
}
/deep/ .systemSetUpload {
  .picture-list,
  .picture-item {
    width: 60px !important;
    height: 60px !important;
  }
}
.homePageSet {
  .ts-button {
    background-color: #5260ff;
    border: 1px #5260ff solid;
    border-radius: 2px;
    color: #fff;
    line-height: 28px;
    min-width: 60px;
    padding: 0 8px;
    height: 30px;
    font-size: 14px;
    &.cancel {
      background: #fff;
      border-color: #ccc;
      color: #333;
    }
  }
  .setContent {
    padding-bottom: 50px;
    /deep/ .ts-form {
      .ts-form-item {
        margin-bottom: 10px;
      }
    }
  }
  .img-box {
    display: flex;
    align-items: center;
    img {
      width: 60px;
      height: 60px;
    }
    span {
      margin-left: 10px;
      color: #999;
    }
  }
  .footer {
    width: 100%;
    box-sizing: border-box;
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 8px 15px;
    text-align: right;
    border-top: 1px solid #eee;
    background-color: #fff;
  }
}
</style>
