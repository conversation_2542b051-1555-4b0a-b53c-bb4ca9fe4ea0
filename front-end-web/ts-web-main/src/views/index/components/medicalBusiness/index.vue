<!-- 医疗业务 -->
<template>
  <div class="medicalBusiness">
    <div class="cardHead">
      <div class="headLeft">
        <i class="sign"></i>
        <div class="headTitle">
          {{ config.elementMedicalBusiness == 1 ? '业务情况' : '百分制考核' }}
        </div>
        <div class="shell-search-box">
          <i class="fa fa-angle-left" @click="prevDay"></i>
          <el-date-picker
            v-model="queryDate"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :clearable="false"
            :picker-options="dateOptions"
            placeholder="选择日期"
            @change="queryDateChangeRange"
            style="width: 100px;"
            v-if="config.elementMedicalBusiness == 1"
          />
          <el-date-picker
            v-model="queryDateMonth"
            type="month"
            format="yyyy-MM"
            value-format="yyyy-MM"
            :picker-options="dateOptions"
            :clearable="false"
            placeholder="选择月份"
            @change="queryDateMonthChangeRange"
            style="width: 140px;"
            v-else
          />
          <i class="fa fa-angle-right" @click="nextDay"></i>
        </div>
      </div>
    </div>
    <div
      class="index_content"
      v-loading="loading"
      v-if="config.elementMedicalBusiness == 1"
    >
      <div class="data-wrap">
        <p class="today-title">收入</p>
        <p class="today-data">
          <span class="todayIncome">{{ totalNum.sr }}</span
          >元
        </p>
        <p class="same-period">
          同期：<span class="samePeriodIncome">{{ totalNum.sr_tq }}</span
          >元
        </p>
        <p class="year-over-year">
          同比：<span class="yearOverYear">{{ totalNum.sr_tqzf }}</span
          >%
        </p>
      </div>
      <div class="chart-wrap" ref="line"></div>
      <div class="chart-wrap-w400" ref="circl"></div>
    </div>
    <div class="index_content" v-loading="loading" v-else>
      <div class="percent-col">
        <p>医务科</p>
        <div class="scroll-content">
          <div
            class="indicator-item"
            v-for="(item, index) in indicatorObj[0]"
            :key="index"
          >
            <div class="indicator-title">{{ item.label }}</div>
            <div class="indicator-value-content">
              <span class="indicator-value">{{ dateB[item.prop] }}</span>
              <span class="unit">{{ item.unit || '%' }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="percent-col" style="flex: 3;">
        <p>中医药发展办公室</p>
        <div class="scroll-content tablet-content">
          <div
            class="indicator-item"
            v-for="(item, index) in indicatorObj[1]"
            :key="index"
          >
            <div class="indicator-title">{{ item.label }}</div>
            <div class="indicator-value-content">
              <span class="indicator-value">{{ dateB[item.prop] }}</span>
              <span class="unit">{{ item.unit || '%' }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="percent-col">
        <p>护理部</p>
        <div class="scroll-content">
          <div
            class="indicator-item"
            v-for="(item, index) in indicatorObj[2]"
            :key="index"
          >
            <div class="indicator-title">{{ item.label }}</div>
            <div class="indicator-value-content">
              <span class="indicator-value">{{ dateB[item.prop] }}</span>
              <span class="unit">{{ item.unit || '%' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment, { months } from 'moment';
export default {
  props: {
    config: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      queryDate: moment()
        .subtract(1, 'days')
        .format('YYYY-MM-DD'),
      queryDateMonth: moment()
        .subtract(1, 'months')
        .format('YYYY-MM'),
      totalNum: {},
      pieChart: null,
      lineEchart: null,
      dateB: {},
      indicatorObj: [
        [
          {
            label: '出院人次',
            prop: 'ba_cyrc',
            unit: '人次'
          },
          {
            label: '病例类型(C/D型)',
            prop: 'ba_cdxblzb'
          },
          {
            label: '手术三四级构成比',
            prop: 'ba_ssjssgcb'
          },
          {
            label: '住院基药比例',
            prop: 'zy_jbywbl'
          },
          {
            label: '门诊基药比例',
            prop: 'mz_jbywbl'
          },
          {
            label: '预约诊疗率',
            prop: 'mz_yyzll'
          }
        ],
        [
          {
            label: '门诊中药处方比例',
            prop: 'mz_zycfbl'
          },
          {
            label: '门诊患者使用中医非药物疗法比例',
            prop: 'mz_zyfywlfbl'
          },
          {
            label: '医疗机构中药制剂收入占药品收入比例',
            prop: 'hj_zyzjsrbl'
          },
          {
            label: '门诊散装中药饮片和小包装中药饮片处方比例',
            prop: 'mz_zyypcfbl'
          },
          {
            label: '以中医为主治疗的出院患者比例',
            prop: 'ba_cyhz_zyzlwzbl'
          },
          {
            label: '门诊中医医疗服务项目收入占门诊医疗收入比例',
            prop: 'mz_zyfwxmsrbl'
          },
          {
            label: '门诊患者中药饮片使用率',
            prop: 'mz_zyypsyl'
          },
          {
            label: '中药收入占药品收入比例',
            prop: 'hj_zysrbl'
          },
          {
            label: '住院中医医疗服务项目收入占住院医疗收入比例',
            prop: 'zy_zyfwxmsrbl'
          },
          {
            label: '出院患者中药饮片使用率',
            prop: 'ba_cyhz_zyypsyl'
          },
          {
            label: '中药饮片收入占药品收入比例',
            prop: 'hj_zyypsrbl'
          }
        ],
        [
          {
            label: '门诊中药处方比例',
            prop: 'mz_zycfbl'
          }
        ]
      ],
      dateOptions: {
        disabledDate(time) {
          return time >= Date.now() - 24 * 60 * 60 * 1000;
        }
      }
    };
  },
  computed: {
    ksdm() {
      return this.$cookies
        .get('sso_sysRoleCode')
        .split(',')
        .includes('YZCX_YLDJS')
        ? '0'
        : this.$store.state.common.userInfo.orgId;
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.config.elementMedicalBusiness == 1) {
        this.businessChange();
        // this.pieChart = this.$echarts.init(this.$refs.circl);
        // this.lineEchart = this.$echarts.init(this.$refs.line);
      } else {
        this.queryDateMonthChangeRange(this.queryDateMonth);
      }
    });
  },
  methods: {
    businessChange() {
      this.loading = true;
      this.getIncomeComposition();
      this.getSevenIncomeChart();
      this.getTotalIncome();
      this.loading = false;
    },
    // 饼图数据
    async getIncomeComposition() {
      let param = {
        begdate: this.queryDate,
        enddate: this.queryDate,
        ksdm: this.ksdm
      };
      let res = await this.ajax.getIncomeComposition(param);
      let data = [
        { value: res.object.hj_ypsr, name: '药品' },
        { value: res.object.hj_clsr, name: '材料' },
        { value: res.object.hj_jcsr, name: '检查' },
        { value: res.object.hj_jysr, name: '化验' },
        { value: res.object.hj_fwsr, name: '服务' }
      ];
      this.renderPie(data);
    },
    // 设置饼图数据
    renderPie(data) {
      let options = {
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            let res = `${params.marker}${params.data.name}:
              <br/>${
                params.data.value
                  ? params.data.value.toFixed(2)
                  : params.data.value
              }元<br/>
              占比：${params.percent}%`;
            return res;
          },
          backgroundColor: 'rgba(255,255,255,0.85)',
          borderColor: '#ddd',
          extraCssText: 'box-shadow: 0 0 3px rgba(255, 255, 255, 0.9);'
        },
        legend: {
          show: false
        },
        series: [
          {
            name: '收入构成占比',
            type: 'pie',
            radius: ['45%', '65%'],
            center: ['50%', '50%'],
            label: {
              position: 'outside',
              formatter: '{b}: {d}%',
              color: 'var(--theme-color)'
            },
            data: data
          }
        ]
      };
      if (!this.pieChart) {
        this.pieChart = this.$echarts.init(this.$refs.circl);
      }
      this.pieChart.clear();
      this.pieChart.setOption(options);
    },
    // 折线图数据
    async getSevenIncomeChart() {
      let param = {
        begdate: moment(this.queryDate)
          .subtract(6, 'days')
          .format('YYYY-MM-DD'),
        enddate: this.queryDate,
        ksdm: this.ksdm
      };
      let res = await this.ajax.getSevenIncomeChart(param);
      let weekList = [];
      let data = [];
      for (let i = 0; i < 6; i++) {
        let days = moment(this.queryDate)
          .subtract(i, 'days')
          .format('YYYY-MM-DD');
        weekList.unshift(days);
        let object = { pdate: days, sr: 0 };
        res.object.forEach(item => {
          if (item.pdate == days) {
            object.sr = item.sr;
          }
        });
        data.unshift(object.sr);
      }
      this.renderLine(data, weekList);
    },
    renderLine(data, xData) {
      let options = {
        title: {
          text: '收入趋势',
          top: '10',
          left: 'center'
        },
        grid: {
          top: '50',
          bottom: '40'
        },
        xAxis: {
          type: 'category',
          data: xData
        },
        yAxis: {
          type: 'value',
          show: true,
          axisLine: {
            show: true,
            onZero: true
          }
        },
        series: [
          {
            data: data,
            type: 'line',
            itemStyle: {
              normal: {
                label: {
                  show: true, //开启显示
                  position: 'top', //在上方显示
                  textStyle: {
                    //数值样式
                    color: 'var(--theme-color)',
                    fontSize: 12
                  }
                }
              }
            }
          }
        ]
      };
      if (!this.lineEchart) {
        this.lineEchart = this.$echarts.init(this.$refs.line);
      }
      this.lineEchart.clear();
      this.lineEchart.setOption(options);
    },
    // 业务-数字信息
    async getTotalIncome() {
      let param = {
        begdate: this.queryDate,
        enddate: this.queryDate,
        ksdm: this.ksdm
      };
      let res = await this.ajax.getTotalIncome(param);
      this.totalNum = res.object;
    },
    // 百分比数据
    async getAssessmentIndicators() {
      let param = {
        begdate: moment(this.queryDateMonth)
          .startOf('month')
          .format('YYYY-MM-DD'),
        enddate: moment(this.queryDateMonth)
          .endOf('month')
          .format('YYYY-MM-DD'),
        ksdm: this.ksdm
      };
      this.loading = true;
      let res = await this.ajax.getAssessmentIndicators(param);
      this.loading = false;
      Object.keys(res.object).map(key => {
        let text = Number(res.object[key]);
        let settingItem =
          this.indicatorObj.find(item => item.prop == key) || {};
        if (!isNaN(text)) {
          if (!settingItem.unit) {
            text = parseInt(text * 10000) / 100;
          } else {
            text = parseFloat(text.toFixed(2));
          }
        } else {
          text = '';
        }
        res.object[key] = text;
      });
      this.dateB = res.object;
    },
    prevDay() {
      if (this.config.elementMedicalBusiness == 1) {
        this.queryDate = moment(this.queryDate)
          .subtract(1, 'days')
          .format('YYYY-MM-DD');
        this.queryDateChangeRange(this.queryDate);
      } else {
        this.queryDateMonth = moment(this.queryDateMonth)
          .subtract(1, 'months')
          .format('YYYY-MM');
        this.queryDateMonthChangeRange(this.queryDateMonth);
      }
    },
    nextDay() {
      if (this.config.elementMedicalBusiness == 1) {
        let now = moment()
          .subtract(1, 'days')
          .format('YYYY-MM-DD');
        if (this.queryDate == now) return;
        this.queryDate = moment(this.queryDate)
          .add(1, 'days')
          .format('YYYY-MM-DD');
        this.queryDateChangeRange(this.queryDate);
      } else {
        let now = moment().format('YYYY-MM');
        if (now == this.queryDateMonth) return false;
        this.queryDateMonth = moment(this.queryDateMonth)
          .add(1, 'months')
          .format('YYYY-MM');
        this.queryDateMonthChangeRange(this.queryDateMonth);
      }
    },
    queryDateChangeRange(e) {
      this.queryDate = e;
      this.businessChange();
      this.$forceUpdate();
    },
    queryDateMonthChangeRange(e) {
      this.queryDateMonth = e;
      this.getAssessmentIndicators();
      this.$forceUpdate();
    }
  }
};
</script>

<style lang="scss" scoped>
.medicalBusiness {
  height: 100%;
  background: #fff;
  .cardHead {
    height: 39px;
    background: #fff;
    box-shadow: 0px 1px 0px 0px #e4e4e4;
    font-size: 14px;
    font-family: MicrosoftYaHei-Bold, MicrosoftYaHei;
    font-weight: bold;
    color: rgba(51, 51, 51, 0.5);
    display: flex;
    justify-content: space-between;
    .headLeft {
      display: flex;
      align-items: center;
      .sign {
        width: 4px;
        height: 18px;
        background: var(--theme-color);
        border-radius: 2px;
        display: inline-block;
        margin: 0 6px 0 8px;
      }
      .headTitle {
        line-height: 30px;
        letter-spacing: 2px;
        color: #333333;
        font-size: 15px;
        font-weight: bold;
      }
      /deep/ .el-date-editor {
        .el-input__inner {
          padding: 0 8px !important;
          background-color: #edefff;
          border-color: #edefff;
          color: var(--theme-color);
          font-size: 14px;
          cursor: pointer;
          text-align: center;
        }
        .el-input__prefix {
          display: none;
        }
      }
      .fa {
        margin: 0 4px;
        font-size: 20px;
        cursor: pointer;
        color: #ccc;
        padding: 6px;
      }
    }
  }
  .index_content {
    display: flex;
    align-items: center;
    height: calc(100% - 40px);
    .data-wrap {
      padding-left: 40px;
      width: 200px;
      .today-title {
        color: #333;
        font-size: 18px;
        font-weight: 700;
      }
      .today-data {
        font-size: 14px;
        color: #333;
        .todayIncome {
          font-size: 28px;
          font-weight: 700;
          color: var(--theme-color);
        }
      }
      .same-period,
      .year-over-year {
        font-size: 14px;
        color: #333;
        margin-top: 6px;
        display: flex;
        align-items: center;
        .samePeriodIncome {
          font-size: 14px;
          color: #333;
        }
        .yearOverYear {
          display: flex;
          align-items: center;
        }
      }
    }
    .chart-wrap {
      flex: 1;
      height: 100%;
    }
    .chart-wrap-w400 {
      width: 400px;
      height: 100%;
    }
    .percent-col {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 8px 16px;
      height: 100%;
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      &:not(:last-child)::after {
        content: ' ';
        height: 80%;
        width: 1px;
        background-color: #edefff;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translate(-50%, -50%);
      }
      p {
        font-weight: bold;
        margin-bottom: 8px;
      }
      .scroll-content {
        flex: 1;
        overflow: hidden;
      }
      .indicator-item {
        display: flex;
        padding: 8px 0;
        justify-content: space-between;
        .indicator-title {
          white-space: pre-wrap;
          margin-right: 4px;
          flex: 1;
        }
        .indicator-value-content {
          white-space: nowrap;
          min-width: 50px;
          text-align: right;
        }
        .indicator-value {
          color: var(--theme-color);
          font-weight: bold;
        }
      }
      .tablet-content {
        .indicator-item {
          width: calc((100% - 16px) / 3);
          display: inline-flex;
          padding: 8px;
          box-sizing: border-box;
        }
      }
    }
  }
}
</style>
