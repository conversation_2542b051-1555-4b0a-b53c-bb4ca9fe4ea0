<template>
  <div class="person-dynamics-container">
    <item-head title="人员动态" :showPopover="true">
      <template #operate>
        <SearchDateBar
          :quickDateList="quickDateList"
          :showDateRange="true"
          @refresh="refresh"
        />
      </template>
      <template #popverContent>
        <p>人员动态主要指标计算规则说明：</p>
        <p>1.在职人数：档案信息中除退休、调出、离职、长期病休状态的人数。</p>
        <p>2.在岗人数：</p>
        <p>&nbsp;&nbsp;① 在职人数-离职人数+人员去向人数。</p>
        <p>&nbsp;&nbsp;② 今日排班人数。</p>
      </template>
    </item-head>

    <div class="content">
      <div class="today-updates content-item">
        <div class="title">今日动态</div>

        <div class="data-count">
          <div class="on-the-job update-item">
            <div class="count-module">
              <div class="count">{{ dayDynaData.total_cnt }}</div>
              <div class="label">在职人数</div>
            </div>
            <img src="@/assets/img/home/<USER>" alt="" />
          </div>
          <div class="on-the-post update-item">
            <div class="count-module">
              <div class="count">{{ dayDynaData.on_duty_cnt }}</div>
              <div class="label">在岗人数</div>
            </div>
            <img src="@/assets/img/home/<USER>" alt="" />
          </div>
        </div>
      </div>
      <div class="right-content">
        <div class="abnormal-person content-item">
          <div class="title">{{ `异动人员(${psnTrnsData.total})` }}</div>
          <el-scrollbar
            v-if="psnTrnsData.psnTrnsDataList.length"
            style="width:100%; height: 100%;"
            wrap-style="overflow-x: hidden;"
          >
            <ul class="statistics-container">
              <li
                class="statistics-item"
                v-for="(item, index) in psnTrnsData.psnTrnsDataList"
                :key="index"
              >
                <span class="count">{{ item.cnt }}</span>
                <div class="label">{{ item.cause }}</div>
              </li>
            </ul>
          </el-scrollbar>
          <EmptyImg v-else />
        </div>
        <div class="person-direction content-item">
          <div class="title">{{ `人员去向(${psnDstnData.total})` }}</div>
          <el-scrollbar
            v-if="psnDstnData.psnDstnDataList.length"
            style="width:100%; height: 100%;"
            wrap-style="overflow-x: hidden;"
          >
            <ul class="statistics-container">
              <li
                class="statistics-item"
                v-for="(item, index) in psnDstnData.psnDstnDataList"
                :key="index"
              >
                <span class="count">{{ item.cnt }}</span>
                <div class="label">{{ item.leave_type }}</div>
              </li>
            </ul>
          </el-scrollbar>
          <EmptyImg v-else />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ItemHead from '../eventWarning/item-head.vue';
import SearchDateBar from '../eventWarning/search-date-bar';
import EmptyImg from '../eventWarning/empty-img';
export default {
  components: {
    ItemHead,
    SearchDateBar,
    EmptyImg
  },
  data() {
    return {
      quickDateList: [
        {
          label: '今日',
          value: 1
        },
        {
          label: '本月',
          value: 3
        }
      ],
      dayDynaData: {
        on_duty_cnt: 0,
        total_cnt: 0
      },
      psnTrnsData: {
        total: 0,
        psnTrnsDataList: []
      },
      psnDstnData: {
        total: 0,
        psnDstnDataList: []
      }
    };
  },
  created() {
    this.refresh();
  },
  methods: {
    refresh(params = { type: 1 }) {
      let data = {
        type: params.type
      };
      if (params.dates && params.dates.length) {
        data.start_date = params.dates[0];
        data.end_date = params.dates[1];
      }
      this.ajax.getDayDyna(data).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '数据获取失败');
          return;
        }
        this.dayDynaData = res.object;
      });
      this.ajax.getPsnTrns(data).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '数据获取失败');
          return;
        }
        this.psnTrnsData.total = res.object.totalCnt || 0;
        this.psnTrnsData.psnTrnsDataList = res.object.dataList || [];
      });
      this.ajax.getPsnDstn(data).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '数据获取失败');
          return;
        }
        this.psnDstnData.total = res.object.totalCnt;
        this.psnDstnData.psnDstnDataList = res.object.dataList;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.person-dynamics-container {
  flex: 1;
  height: 100%;
  margin-left: 8px;
  display: flex;
  flex-direction: column;
  .content {
    flex: 1;
    display: flex;
    padding: 4px;
    overflow: hidden;

    .right-content {
      flex: 1;
      display: flex;
    }
    .content-item {
      display: flex;
      flex-direction: column;
      border: 1px solid #eeee;
      border-radius: 4px;

      .title {
        height: 32px;
        line-height: 32px;
        font-size: 13px;
        color: #333333;
        padding: 0 8px;
        border-bottom: 1px solid #eeee;
      }

      &.today-updates {
        width: 140px;

        .data-count {
          flex: 1;
          padding: 8px 12px;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
        }

        .update-item {
          border-radius: 4px;
          padding: 8px;
          color: #fff;
          display: flex;
          justify-content: space-between;
          &.on-the-job {
            background-color: #5a87f0;
          }
          &.on-the-post {
            background-color: #71ca8b;
          }
          img {
            width: 34px;
          }
          .count-module {
            .val {
              font-weight: 700;
            }
            .label {
              color: #fff;
              font-size: 12px;
            }
          }
        }
      }

      .statistics-container {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        .statistics-item {
          text-align: center;
          padding: 10px 0;
          width: 50%;
          .count {
            color: #e5995d;
            font-weight: 700;
          }
          .label {
            font-size: 13px;
            color: #333333;
          }
        }
      }

      &.abnormal-person {
        width: 36%;
        margin: 0 8px;
      }

      &.person-direction {
        flex: 1;
        .statistics-item {
          width: 25%;
        }
      }
    }
  }
}
</style>
