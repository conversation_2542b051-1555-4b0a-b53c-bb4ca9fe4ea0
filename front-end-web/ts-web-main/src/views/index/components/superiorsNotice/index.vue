<!-- 上级通文 -->
<template>
  <div class="superiorsNotice">
    <div class="cardHead">
      <div class="colmuns" ref="colmunListRef">
        <div
          v-for="(col, index) in colmunList"
          :key="index"
          @click="getInformationList(col)"
        >
          <div class="colmun" :class="activeId == col.id ? 'active' : ''">
            <span class="red-icon" v-if="col.noread > 0"></span>
            {{ col.channelName }}
          </div>
        </div>
      </div>
      <i class="el-icon-more headRight" @click="toGo"></i>
    </div>
    <div class="messageListNoimg" v-loading="loading">
      <el-scrollbar
        style="width: 100%;height: 100%;"
        v-if="informationList.length != 0"
        wrap-style="overflow-x: hidden;"
      >
        <div
          class="messageItem"
          v-for="(item, index) in informationList"
          :key="index"
        >
          <p class="info-title one-line">
            <span class="new" v-if="activeId == 1 && item.bid == null"
              >new</span
            >
            <span class="new" v-if="activeId == 2 && item.confirmStatus == 0"
              >new</span
            >
            <span
              class="one-line"
              style="flex: 1;"
              :style="1 == item.titleColor ? 'color:#D50D0D;' : ''"
            >
              {{ activeId == 1 ? item.informationTitle : item.fileTitle }}
            </span>
          </p>
          <p class="date">{{ getTime(item) }}</p>
        </div>
      </el-scrollbar>
      <no-data v-else />
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import noData from '../noData.vue';
export default {
  components: { noData },
  props: {
    config: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      colmunList: [
        {
          channelName: '上级通知',
          id: 1,
          noread: 0,
          api: 'getSuperiorDataList',
          param: {
            informationStatus: 1,
            pageSize: 10,
            pageNo: 1,
            sidx: 't1.CREATE_DATE',
            sord: 'desc'
          }
        },
        {
          channelName: '上级公文',
          id: 2,
          noread: 0,
          api: 'getSuperiorGovDocumentFileList',
          param: {
            pageSize: 10,
            pageNo: 1,
            sidx: 'create_date',
            sord: 'desc'
          }
        }
      ],
      informationList: [],
      activeId: '',
      loading: false
    };
  },
  mounted() {
    this.selectNoreadByChannel();
  },
  methods: {
    // 跳转列表
    toGo() {
      if (this.activeId == 1) {
        this.$router.push('/information/messageRead');
      } else {
        this.$router.push('/govDocument/superiorGovDocument');
      }
    },
    async selectNoreadByChannel() {
      let res = await this.ajax.govSendfileNoreadData();
      this.colmunList[0].noread = res.object.infoNoread;
      this.colmunList[1].noread = res.object.govNoread;
      this.handleCommand(1);
    },
    handleCommand(id) {
      let activeObj = this.colmunList.find(e => e.id == id);
      this.getInformationList(activeObj);
    },
    async getInformationList(item) {
      this.loading = true;
      this.activeId = item.id;
      let res = await this.ajax[item.api](item.param);
      this.loading = false;
      this.informationList = res.rows || [];
    },
    getTime(item) {
      if (!item.createDate) return '';
      if (moment().format('YYYY') == item.createDate.slice(0, 4)) {
        return moment(item.createDate).format('MM-DD');
      } else {
        return moment(item.createDate).format('YYYY-MM-DD');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.superiorsNotice {
  height: 100%;
  background: #fff;
  .cardHead {
    height: 40px;
    background: #fff;
    box-shadow: 0px 1px 0px 0px #e4e4e4;
    border-radius: 4px 4px 0px 0px;
    font-size: 14px;
    color: #333;
    display: flex;
    justify-content: space-between;
    .colmuns {
      display: flex;
      max-width: calc(100% - 30px);
      width: calc(100% - 30px);
      .colmun {
        font-size: 15px;
        padding: 0 8px;
        text-align: center;
        line-height: 40px;
        letter-spacing: 2px;
        text-indent: 5px;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        .red-icon {
          width: 8px;
          height: 8px;
          background: #e24242;
          border-radius: 50%;
          display: inline-block;
        }
        &.active {
          color: var(--theme-color);
          font-weight: 600;
          &::after {
            content: '';
            width: 60px;
            display: block;
            margin: 0 auto;
            border-bottom: 2px solid var(--theme-color);
          }
        }
      }
    }
    .headRight {
      cursor: pointer;
      position: relative;
      top: 4px;
      font-size: 28px;
      color: #33333380;
    }
  }
  .messageListNoimg {
    height: calc(100% - 40px);
    position: relative;
    .messageItem {
      display: flex;
      justify-content: space-between;
      align-items: baseline;
      box-shadow: 0px 0.5px 0px 0px #f4f4f4;
      cursor: pointer;
      position: relative;
      padding: 8px 12px 8px 8px;
      &:hover {
        background-color: #edefff;
        .one-line {
          color: var(--theme-color);
          white-space: unset;
        }
      }
      .one-line {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .info-title {
        flex: 1;
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #333333;
        line-height: 19px;
        display: flex;
        align-items: center;
        .new {
          font-size: 12px;
          color: red;
          font-family: monospace;
          margin-right: 4px;
        }
      }
      .date {
        font-size: 11px;
        color: #999;
        line-height: 16px;
        padding-left: 4px;
      }
    }
  }
}
</style>
