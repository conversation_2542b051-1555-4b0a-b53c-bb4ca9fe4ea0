<template>
  <div class="anonymous-box" id="AnonymousBox">
    <div class="title-top">
      <span class="title">{{ title }}</span>
      <img
        @click="handleToggleShow(index)"
        src="@/assets/img/login/close.svg"
        alt=""
      />
    </div>
    <div class="content">
      <textarea v-model="opinion" maxlength="800" />
      <span v-if="!opinion" class="input-tips">
        {{ placeholder }}
        <span style="font-size: 10px;">(800字以内)</span>
      </span>
      <div class="bottom-button">
        <span class="buttom-tips">{{ tips }}</span>
        <div>
          <span @click="handelResetValue" class="button">重置</span>
          <span @click="handleSubmit" class="button">提交</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      opinion: '',
      title: '',
      placeholder: '',
      tips: '',
      message: '',
      index: null
    };
  },
  methods: {
    handelResetValue() {
      this.opinion = '';
    },
    async handleSubmit() {
      if (!this.opinion) {
        return false;
      }
      let params = { opinion: this.opinion };
      if (this.index == 2) {
        params.boxType = 1;
      }
      const res = await this.ajax.suggestionBoxSave(params);
      if (res.success && res.statusCode === 200) {
        this.$message.success(this.message);
        this.handleToggleShow(this.index);
      }
    },
    handleToggleShow(index = 1) {
      this.opinion = '';
      let classList = AnonymousBox.classList;
      if (this.index == index) {
        if ([...classList].includes('show')) {
          classList.remove('show');
          this.index = null;
        } else {
          classList.add('show');
        }
      } else {
        this.index = index;
        if (![...classList].includes('show')) {
          classList.add('show');
        }
      }
      // 意见
      if (index == 1) {
        this.title = '意见箱';
        this.placeholder = '请在此输入您的意见或建议';
        this.tips = '注:您的意见或者建议都将匿名提交。';
        this.message = '提交成功，感谢您的宝贵建议!';
      }
      // 关怀
      if (index == 2) {
        this.title = '关爱地带';
        this.placeholder = '请在此输入您的关爱需求';
        this.tips = '注:您的关爱需求都将匿名提交。';
        this.message = '提交成功，感谢提供您的关爱需求!';
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.anonymous-box {
  position: absolute;
  right: -350px;
  bottom: 10px;

  width: 350px;
  height: 420px;
  background: #fff;
  box-shadow: 0px 0px 8px #a2a2a2;
  border-radius: 8px;

  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  transition: all 0.3s ease-in-out;
  z-index: 1001;
  &.show {
    right: 10px;
  }
  .title-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    .title {
      font-size: 14px;
      padding: 0;
      margin: 0;
      color: #464646;
    }
    img {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }
  .content {
    width: 100%;
    flex: 1;
    padding: 8px;
    padding-bottom: 40px;
    background: #eee;
    box-sizing: border-box;
    position: relative;
    textarea {
      width: 100%;
      height: 100%;
      border: none;
      resize: none;
      outline: 2px solid transparent;
      outline-offset: 2px;
      transition-duration: 0.3s;
      color: #333;
      padding: 8px;
      border-radius: 4px;
      box-sizing: border-box;
      &:focus {
        box-shadow: 0 0 8px #5260ff;
      }
    }

    .input-tips {
      display: inline-block;
      text-align: center;
      width: 100%;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      font-size: 18px;
      color: #a3a3a4;
    }
  }
  .bottom-button {
    padding-top: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .buttom-tips {
      font-size: 14px;
      color: #464646;
    }
    .button {
      cursor: pointer;
      padding: 4px 8px;
      background: #6d77e8;
      margin-left: 8px;
      color: #fff;
      border-radius: 4px;
      font-size: 12px;
    }
  }
}
</style>
