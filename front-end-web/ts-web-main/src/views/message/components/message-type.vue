<template>
  <div class="message-type">
    <div class="top-search">
      <div class="left">
        <span class="form-title">标题</span>
        <el-input
          style="width:140px"
          v-model="data.subject"
          placeholder="请输入标题关键字"
        ></el-input>
        <el-button class="seacrh-button" size="mini" @click="submitForm"
          >搜索</el-button
        >
        <img @click="resetForm" :src="img" alt="" />
      </div>
      <button
        v-if="isUnReadList"
        class="all-have-read-btn"
        @click="allHaveReadHandle"
      >
        全部置为已读
      </button>
    </div>
    <info-list-item
      v-bind="$attrs"
      v-if="infoList"
      :infoList="infoList"
      @inTheBottomHandle="inTheBottomHandle"
      @haveReadHandle="haveReadHandle"
    ></info-list-item>
  </div>
</template>

<script>
import InfoListItem from './info-list-item';
export default {
  components: {
    InfoListItem
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  created() {
    this.getUnreadList();
  },
  data: () => ({
    infoList: [],
    pressSearchBtn: false
  }),
  computed: {
    isUnReadList() {
      return this.$attrs.type === '0' && this.infoList.length;
    },
    img() {
      return require(`@/assets/img/icon-message-reset.svg`);
    }
  },
  methods: {
    async getUnreadList(searchType) {
      this.openLoadingHandle();
      const type = this.$attrs.type;
      try {
        let data = {
          pageSize: 25,
          readerStatus: type,
          pageNo: this.data.pageNo
        };

        if (this.pressSearchBtn) {
          data.subject = this.data.subject;
        }

        const result = await this.ajax.getMessageList(data);
        const { pageNo, pageCount, rows, totalCount } = result;

        if (searchType === 'search') {
          this.infoList = rows;
        } else {
          this.infoList = [...this.infoList, ...rows] || [];
        }

        let params = {
          type,
          pageNo,
          pageCount,
          totalCount
        };
        this.$emit('getPageNo', params);
      } catch (e) {
        console.log(e, 'e');
      } finally {
        this.closeLoadingHandle();
      }
    },
    // 滚动条到底部 刷新table
    inTheBottomHandle() {
      const type = this.$attrs.type;
      this.$emit('setPageNo', { type, fn: this.getUnreadList });
    },
    // 全部已读
    async allHaveReadHandle() {
      this.openLoadingHandle();
      try {
        const result = await this.ajax.setAllNoticeBrowser();

        const { success, statusCode } = result;
        if (success && statusCode === 200) {
          this.$store.dispatch('common/getMessageCount');
          this.infoList = [];
        }
      } catch (e) {
        console.log(e, 'e');
      } finally {
        this.closeLoadingHandle();
      }
    },
    // 单个已读
    async haveReadHandle(item) {
      this.openLoadingHandle();
      try {
        const { source, toUrl, id } = item;

        // 未读才 发送请求
        switch (this.$attrs.type) {
          case '0':
            const result = await this.ajax.saveNoticeBrowser({ noticeId: id });

            const { success, statusCode } = result;
            if (success && statusCode === 200) {
              this.$store.dispatch('common/getMessageCount');
              const index = this.infoList.findIndex(item => item.id === id);
              this.infoList.splice(index, 1);
            }
            break;
        }

        if (source === '工单管理') {
          this.$router.push(
            '/ts-web-work-order/workSheet/workOrderHomePage/applicantPerson'
          );
          window.dispatchEvent(
            new CustomEvent('qiankunOpenWorkInfoHandle', {
              detail: {
                workNumber: toUrl.split('#').pop()
              }
            })
          );
        } else {
          this.$router.push(toUrl);
        }
      } catch (e) {
        console.log(e, 'e');
      } finally {
        this.closeLoadingHandle();
      }
    },
    openLoadingHandle() {
      this.$root.$emit('sendAjaxMessage', {
        data: 'sendMessage'
      });
    },
    closeLoadingHandle() {
      this.$root.$emit('sendAjaxMessage', {
        data: 'getMessage'
      });
    },
    submitForm() {
      this.data.pageNo = 1;
      this.pressSearchBtn = true;
      this.getUnreadList('search');
    },
    resetForm() {
      this.data.pageNo = 1;
      this.data.subject = '';
      this.pressSearchBtn = false;
      this.getUnreadList('search');
    }
  }
};
</script>

<style lang="scss" scoped>
.message-type {
  width: 100%;
  height: 100%;
}
.top-search {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    display: flex;
    align-items: center;
    > img {
      cursor: pointer;
      border: none;
    }
  }
  .all-have-read-btn {
    padding: 2px 4px;
    border-radius: 2px;
    border: 1px solid $theme-color;
    font-size: 14px;
    color: $theme-color;
    line-height: 19px;
    background: #fff;
    cursor: pointer;
  }
  .form-title {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 20px;
    margin-right: 8px;
  }
  .seacrh-button {
    width: 60px;
    height: 30px;
    background: $theme-color;
    border-radius: 2px;
    color: #fff;
    border: none;
    margin: 0 8px;
  }
}
</style>
