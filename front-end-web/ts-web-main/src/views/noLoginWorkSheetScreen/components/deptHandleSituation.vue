<template>
  <div class="content flex-column flex-col-center">
    <div class="title">科室处理情况</div>
    <div class="line"></div>
    <div class="flex-grow list-content" style="width: 100%;">
      <div class="list-title flex-col-center flex-row-between">
        <template v-for="(item, index) of columns">
          <div
            class="cell"
            :key="index"
            :style="{ width: item.width ? item.width + 'px' : 'auto' }"
          >
            {{ item.name }}
          </div>
        </template>
      </div>
      <template v-for="(data, dataIndex) of deptHandleList">
        <div class="list-row flex-col-center flex-row-between" :key="dataIndex">
          <template v-for="(col, colIndex) of columns">
            <div
              class="cell"
              :key="colIndex"
              :style="{ width: col.width ? col.width + 'px' : 'auto' }"
              :class="col.class ? col.class(data[col.prop], data) : ''"
            >
              {{ data[col.prop] }}
            </div>
          </template>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    deptHandleList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      columns: [
        {
          name: '科室名',
          prop: 'deptName',
          width: 88
        },
        {
          name: '今日建单',
          prop: 'count',
          width: 88
        },
        {
          name: '总计',
          prop: 'sumCount',
          width: 80
        }
      ]
    };
  }
};
</script>

<style lang="scss" scoped>
.content {
  width: 360px;
  height: 428px;
  padding-top: 12px;
}
.title {
  font-weight: 600;
  color: #ffffff;
  line-height: 32px;
  font-size: 24px;
}
.line {
  width: 180px;
  height: 2px;
  background: linear-gradient(
    270deg,
    rgba(54, 229, 255, 0) 0%,
    #36e5ff 52%,
    rgba(54, 229, 255, 0) 100%
  );
  border-radius: 50%;
  margin-top: 4px;
  margin-bottom: 14px;
}
.list-content {
  padding: 0 16px;
}
.list-title {
  margin-bottom: 9px;
  div {
    font-size: 18px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.5);
    line-height: 25px;
  }
}
.list-row,
.list-title {
  padding: 0 4px;
}
.cell {
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  &.warning {
    color: #d1851c;
  }
  &.error {
    color: #ff6068;
  }
}
.list-row {
  height: 50px;
  border-radius: 4px;
  border: 2px solid rgba(0, 160, 233, 0.8);
  margin-bottom: 4px;
  .cell {
    font-size: 22px;
    font-weight: 600;
    color: #ffffff;
    line-height: 30px;
  }
}
.unassign-count {
  margin-bottom: 16px;
  margin-top: 12px;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  line-height: 28px;
  span {
    margin-left: 8px;
    font-weight: 600;
    color: #e7e517;
    line-height: 37px;
    font-size: 26px;
  }
}
</style>
