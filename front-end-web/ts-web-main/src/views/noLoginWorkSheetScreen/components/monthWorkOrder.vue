<template>
  <div class="content flex-column flex-col-center">
    <div class="title">本月工单</div>
    <div class="line"></div>
    <div class="flex-row-between flex-grow" style="width: 100%;">
      <div class="data-item flex-column flex-col-center">
        <div>总建单</div>
        <div>{{ dataList[0] }}</div>
      </div>
      <div class="data-item flex-column flex-col-center">
        <div>未完成</div>
        <div>{{ dataList[1] }}</div>
      </div>
      <div class="data-item flex-column flex-col-center">
        <div>完成率</div>
        <div>{{ dataList[2] }}%</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dataList: {
      type: Array,
      default: () => []
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  padding: 12px 16px;
  width: 460px;
  height: 164px;
}
.title {
  color: #ffffff;
  line-height: 32px;
  font-weight: 600;
  font-size: 24px;
}
.line {
  width: 180px;
  height: 2px;
  border-radius: 50%;
  background: linear-gradient(
    270deg,
    rgba(54, 229, 255, 0) 0%,
    #36e5ff 52%,
    rgba(54, 229, 255, 0) 100%
  );

  margin-bottom: 8px;
  margin-top: 4px;
}
.data-item div {
  &:first-child {
    font-size: 28px;
    color: #88c1ff;
    line-height: 32px;
    font-weight: 600;
  }
  &:last-child {
    color: #ffffff;
    line-height: 58px;
    font-size: 48px;
    font-weight: 600;
  }
}
</style>
