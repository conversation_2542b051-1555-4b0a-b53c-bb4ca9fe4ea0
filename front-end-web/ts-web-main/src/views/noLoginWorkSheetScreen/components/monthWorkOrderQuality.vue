<template>
  <div class="content flex-column flex-col-center">
    <div class="title">本月工单质量</div>
    <div class="line"></div>
    <div class="flex-grow" style="width: 100%;">
      <div
        v-for="(item, index) of dataObject"
        :key="index"
        class="order-quality-item flex-col-center"
      >
        <div class="quality-dept-name">{{ item.deptName }}</div>
        <div class="img-content">
          <div
            class="full-star"
            :style="{
              width: item.deptAvg * 30 + parseInt(item.deptAvg) * 12 + 'px'
            }"
          >
            <img :src="fullStar" />
          </div>

          <img :src="empStar" class="emp-star" />
        </div>
        <div class="quality-count">{{ item.deptAvg }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dataObject: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      fullStar: require('@/assets/img/workSheet/order_quality_full_star.png'),
      empStar: require('@/assets/img/workSheet/order_quality_emp_star.png'),

      rankingList: []
    };
  }
};
</script>

<style lang="scss" scoped>
.content {
  width: 460px;
  height: 350px;
}
.title {
  margin-top: 12px;
  font-size: 24px;
  color: #ffffff;
  line-height: 32px;
  font-weight: 600;
}
.line {
  border-radius: 50%;
  width: 180px;
  height: 2px;
  background: linear-gradient(
    270deg,
    rgba(54, 229, 255, 0) 0%,
    #36e5ff 52%,
    rgba(54, 229, 255, 0) 100%
  );

  margin-top: 4px;
  margin-bottom: 27px;
}
.order-quality-item {
  padding-left: 20px;
  margin-bottom: 20px;
}
.quality-dept-name {
  font-size: 22px;
  font-weight: 600;
  color: #ffffff;
  line-height: 30px;
  width: 132px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.img-content {
  position: relative;
  margin: 0 24px;
  img {
    width: 198px;
    height: 26px;
  }
}
.full-star {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 3;
  overflow: hidden;
  height: 30px;
}
.quality-count {
  font-size: 24px;
  font-weight: bold;
  color: #ff6068;
  line-height: 33px;
}
</style>
