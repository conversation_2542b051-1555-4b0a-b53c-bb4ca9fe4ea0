<template>
  <div class="content flex-column flex-col-center">
    <div class="title">本月提单科室</div>
    <div class="line"></div>
    <div class="flex-center" style="position:relative; width: 100%;">
      <div class="describ-title">TOP前5</div>
      <div class="legend flex-col-center">
        <div class="circle-finished"></div>
        已完成
      </div>
      <div class="legend flex-col-center">
        <div class="circle-unfinished"></div>
        未完成
      </div>
    </div>
    <template v-for="(item, index) of subList">
      <div class="sub-item" :key="index">
        <div class="sub-dept-name">{{ item.name }}</div>
        <div class="flex-col-center sub-line-container">
          <div
            class="unfinished-line flex-center"
            :class="item.finished / item.total == 1 ? 'no-border' : ''"
            :style="{
              width: (item.total / maxSubCount) * 344 + 'px',
              minWidth: !item.finished || !item.unfinished ? '44px' : '88px'
            }"
          >
            <div
              class="finished-line flex-center"
              :style="{
                width: (item.finished / item.total) * 100 + '%',
                minWidth: item.finished ? '44px' : ''
              }"
            >
              {{ item.finished ? item.finished : '' }}
            </div>
            <div
              class="flex-row-center flex-grow unfinished-label"
              :style="{
                minWidth: item.unfinished ? '44px' : ''
              }"
            >
              {{ item.unfinished ? item.unfinished : '' }}
            </div>
          </div>
          共{{ item.total }}个
        </div>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    subList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      maxSubCount: 0
    };
  },
  watch: {
    subList: function(newVal = []) {
      this.maxSubCount = 0;
      newVal.forEach(item => {
        let total = Number(item.total);
        if (total > this.maxSubCount) {
          this.maxSubCount = total;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  width: 460px;
  height: 404px;
  padding: 14px 12px;
  padding-left: 20px;
}
.title {
  font-weight: 600;
  color: #ffffff;
  line-height: 32px;
  font-size: 24px;
}
.line {
  width: 180px;
  height: 2px;
  background: linear-gradient(
    270deg,
    rgba(54, 229, 255, 0) 0%,
    #36e5ff 52%,
    rgba(54, 229, 255, 0) 100%
  );
  border-radius: 50%;
  margin-top: 4px;
  margin-bottom: 8px;
}
.legend {
  font-weight: 600;
  color: #ffffff;
  line-height: 22px;
  font-size: 16px;
  margin-bottom: 4px;
  &:nth-child(2) {
    margin-right: 16px;
  }
}
.circle-finished,
.circle-unfinished {
  height: 12px;
  width: 12px;
  border-radius: 50%;
  border: 1px solid #1fa3ff;
  margin-right: 8px;
  // background-color: transparent;
}
.circle-finished {
  background-color: #1fa3ff;
}
.sub-item {
  width: 100%;
}
.sub-dept-name,
.sub-line-container {
  font-weight: 600;
  color: #ffffff;
  line-height: 28px;
  font-size: 20px;
  white-space: nowrap;
}
.sub-line-container {
  margin-top: 2px;
  margin-bottom: 4px;
}
.unfinished-line {
  width: 344px;
  height: 24px;
  border-radius: 12px;
  border: 1px solid #1fa3ff;
  margin-right: 8px;
  .unfinished-label {
    font-size: 20px;
    line-height: 28px;
  }
}
.finished-line {
  height: 24px;
  border-radius: 12px;
  background-color: #1fa3ff;
  color: #05122b;
  font-size: 20px;
}
.no-border {
  border: none;
}
.describ-title {
  position: absolute;
  left: 0;
  color: #88c1ff;
  font-size: 18px;
  font-weight: 600;
}
</style>
