<template>
  <div ref="largeScreenContainer" class="large-screen-body">
    <div class="large-screen-content flex flex-column" ref="largeScreenBody">
      <div class="screen-title-box flex">
        <div class="screen-refresh flex-grow">
          <i
            class="oa-icon"
            :class="isFullScreen ? 'oa-pc-tuichuquanping' : 'oa-pc-quanping'"
            style="margin-left: -18px; font-size: 22px; cursor: pointer; margin-right: 12px;"
            @click="handleFullScreen"
          >
          </i>
          60s刷新一次数据
        </div>
        <div class="screen-title flex-col-center ">
          <div
            ref="largeScreenTitle"
            class="flex-col-center"
            style="-webkit-background-clip: text; background-clip: text;"
            :style="{ letterSpacing: screenTitleSpace }"
          >
            <img
              :src="screenLogo"
              :style="{
                marginRight: screenTitleSpace,
                marginLeft: screenTitleSpace
              }"
            />
            {{ screenTitle }}
          </div>
        </div>
        <div class="screen-time flex-grow">{{ nowTime }}</div>
      </div>

      <div class="screen-content flex-column flex-grow">
        <div class="screen-col-top flex">
          <month-work-order
            :dataList="thisMonthOrderList"
            class="screen-card"
          />
          <today-work-order
            :todayCreatCount="todayOrderCount"
            :dataList="todayOrderList"
            class="screen-card"
          />
        </div>

        <div class="screen-bottom flex-grow flex">
          <div class="screen-bottom-col-1">
            <month-work-order-quality
              :dataObject="monthOrderQuality"
              class="screen-card"
            />
            <this-month-sub-order-dept
              :subList="monthSubDeptList"
              class="screen-card"
            />
          </div>

          <handing-order
            :handingOrderList="handingOrderList"
            :totalHanding="totalHanding"
            class="screen-card screen-bottom-col-2"
          />

          <div class="screen-bottom-col-1">
            <dept-handle-situation
              :deptHandleList="deptHandleList"
              class="screen-card"
            >
            </dept-handle-situation>
            <dept-create-order
              :deptCreatePercentData="deptCreatePercentData"
              class="screen-card"
            ></dept-create-order>
            <!-- <div class="screen-card technology-board flex-column flex-center">
              <img
                :src="require('@/assets/img/workSheet/trasen_logo.png')"
              />
              <div class="">
                技术支持：
                <span>创星科技</span>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MonthWorkOrder from './components/monthWorkOrder.vue'; //本月工单
import TodayWorkOrder from './components/todayWorkOrder.vue'; //今日工单
import MonthWorkOrderQuality from './components/monthWorkOrderQuality.vue'; //本月工单质量
import ThisMonthSubOrderDept from './components/thisMonthSubOrderDept.vue'; //本月提单科室
import HandingOrder from './components/handingOrder.vue'; //处理中的工单
import DeptHandleSituation from './components/deptHandleSituation.vue'; //科室处理情况
import DeptCreateOrder from './components/deptCreateOrder.vue'; //科室总计建单占比

export default {
  components: {
    MonthWorkOrder,
    TodayWorkOrder,
    MonthWorkOrderQuality,
    ThisMonthSubOrderDept,
    HandingOrder,
    DeptHandleSituation,
    DeptCreateOrder
  },
  data() {
    return {
      screenLogo: '',
      screenTitle: '北海市第二人民医院',
      screenTitleSpace: 0,

      todayOrderCount: 0, //今日建单总数
      todayOrderList: [], //顶部今日工单完成情况
      thisMonthOrderList: [], //左上角本月工单完成情况
      monthSubDeptList: [], //本月提单科室

      monthOrderQuality: [], //本月工单质量
      monthOrderQualityPage: 1, //本月工单质量页码

      handingRefreshTimer: null, //工单刷新
      totalHanding: 0, //总共正在处理的工单
      handingPageNo: 1,
      handingOrderList: [],

      deptHandleList: [], //科室处理情况数据列表
      deptHandlePageNo: 1, //科室处理情况页码

      deptCreatePercentData: [], //科室总计建单占比数据

      pageRefreshTimer: null, //页面刷新定时器
      intervalTimer: null, //右上角时间刷新定时器
      nowTime: '' //右上角当前时间
    };
  },
  created() {
    this.refreshTime();
    this.intervalTimer = setInterval(() => {
      this.refreshTime();
    }, 30000);
    window.addEventListener('resize', this.handleWindowResize);

    this.setRefreshTimer();
    this.refresh();
  },
  async mounted() {
    // setTimeout(() => {
    //   this.computedBodyContent();
    // }, 100);
    await this.getLogoAndHostName();

    this.$nextTick(() => {
      this.computedBodyContent();

      let length = this.screenTitle.length,
        otherWidth = 650 - length * 31 - 31 - 6;
      this.screenTitleSpace = otherWidth / (length + 2) + 'px';
    });
  },
  activated() {
    window.dispatchEvent(new Event('resize'));
  },
  methods: {
    refresh() {
      //刷新本月工单和今日工单
      this.getOrderDetailList();
      this.getMonthOrderQuality();
      this.getMonthSubDept();
      this.getHandingList();
      this.getDeptHandleSituationData();
      this.getDeptCreatePercentData();

      this.handingRefreshTimer && clearInterval(this.handingRefreshTimer);
      this.handingRefreshTimer = setInterval(() => {
        if (
          this.$route.path != '/workSheet/noLoginLargeScreen' &&
          this.$route.path != '/workSheet/wholeHospitalLargeScreen'
        ) {
          return;
        }
        this.getHandingList(1);
        this.getDeptHandleSituationData();
        this.getMonthOrderQuality();
      }, 15100);
      //大于60s确保不会存在连续刷新的情况
    },
    //-------------------------- 接口请求 ---------------------------------
    async getLogoAndHostName() {
      await this.ajax.getSafeGlobalSetting().then(res => {
        if (!res.success) {
          return;
        }
        this.screenLogo =
          '/ts-basics-bottom/fileAttachment/downloadFile/' +
          res.object.hospitalLogo;
        this.screenTitle = res.object.webTitle || '';
      });
    },
    //获取工单处理数据
    getOrderDetailList() {
      this.$axios({
        url:
          '/ts-worksheet/leaderBigScreenStatistics/getKeyDataIndicatorsOfWorkOrder',
        method: 'get',
        timeout: 60000
      }).then(response => {
        let res = response.data || {};
        if (!res.success) {
          this.$message.error(res.message || '服务器出错了，请稍后再试');
          return;
        }

        this.thisMonthOrderList = [
          res.object.monthjd,
          res.object.monthwwc,
          Math.round(res.object.monthwcl)
        ];

        this.todayOrderList = [];
        for (let i = 1; i < 9; i++) {
          this.todayOrderList.push(Number(res.object[i]));
        }

        let finishedCount = this.todayOrderList[4] + this.todayOrderList[5];
        this.todayOrderList[5] = finishedCount;
        this.todayOrderList.splice(4, 1);

        this.todayOrderCount = res.object.dayjrjd;
        this.todayUnAssign = res.object.wzpgdsl;
      });
    },
    //获取本月工单质量
    getMonthOrderQuality() {
      this.$api({
        url:
          '/ts-worksheet/leaderBigScreenStatistics/getEvaluationGroupByDeptAverageScore',
        method: 'get',
        params: {
          pageNo: this.monthOrderQualityPage,
          pageSize: 6
        }
      }).then(res => {
        if (res.success == false) {
          return;
        }

        this.monthOrderQuality = res.rows || [];
        this.monthOrderQualityPage < res.pageCount
          ? this.monthOrderQualityPage++
          : (this.monthOrderQualityPage = 1);
      });
    },
    //获取本月提单科室
    getMonthSubDept() {
      this.$api({
        url: '/ts-worksheet/leaderBigScreenStatistics/getDeptCountTopDatas',
        method: 'get',
        params: {
          sidx: 'total desc, wsTotal',
          sord: 'desc',
          pageNo: 1,
          pageSize: 5
        }
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '服务器出错了，请稍后再试');
          return;
        }

        this.monthSubDeptList = [];
        (res.rows || []).forEach(item => {
          this.monthSubDeptList.push({
            name: item.name,
            finished: item.wsTotal,
            unfinished: item.wwcTotal,
            total: item.total
          });
        });
      });
    },
    //获取正在处理工单
    getHandingList(type) {
      if (this.totalHanding && this.totalHanding < 13 && type) {
        return;
      }

      this.$axios({
        url: '/ts-worksheet/leaderBigScreenStatistics/wsSheetScreenPageList',
        method: 'get',
        timeout: 60000,
        params: {
          pageNo: this.handingPageNo,
          pageSize: 12
        }
      }).then(response => {
        let res = response.data || { success: false };
        if (res.success == false) {
          this.$message.error(res.message || '服务台报错，请稍后再试');
          return;
        }
        this.totalHanding = res.totalCount;
        this.handingPageNo =
          this.handingPageNo == res.pageCount ? 1 : this.handingPageNo + 1;

        this.handingOrderList = res.rows || [];
      });
    },
    //获取科室处理情况
    getDeptHandleSituationData() {
      this.$axios({
        url: '/ts-worksheet/leaderBigScreenStatistics/getDayGroupByDept',
        method: 'get',
        params: {
          pageNo: this.deptHandlePageNo,
          pageSize: 6
        }
      }).then(response => {
        const res = response.data || { success: false };
        if (res.success == false) {
          return;
        }
        this.deptHandlePageNo < res.pageCount
          ? this.deptHandlePageNo++
          : (this.deptHandlePageNo = 1);
        this.deptHandleList = res.rows || [];
      });
    },
    //获取科室总计建单占比
    getDeptCreatePercentData() {
      this.$axios({
        url: '/ts-worksheet/leaderBigScreenStatistics/getDayGroupByDept',
        method: 'get',
        params: {
          pageNo: 1,
          pageSize: 9999
        }
      }).then(response => {
        const res = response.data || { success: false };
        if (res.success == false) {
          return;
        }
        this.deptCreatePercentData = (res.rows || []).map(item => {
          return {
            name: item.deptName,
            value: item.sumCount
          };
        });
      });
    },
    //-------------------------- 事件处理 ---------------------------------
    /**@desc 主动请求全屏 */
    handleFullScreen() {
      if (this.isFullScreen) {
        document.exitFullscreen();
        return;
      }
      this.$refs.largeScreenContainer.requestFullscreen();
    },
    handleWindowResize() {
      this.$set(
        this.$store.state.common,
        'isFullScreen',
        window.screen.height === window.innerHeight
      );
      const largeScreenBody = document.getElementsByClassName(
        'large-screen-body'
      )[0];
      if (window.screen.height === window.innerHeight) {
        this.$set(this.$store.state.common, 'sideBarWidth', 0);
        largeScreenBody.style.height = '100vh';
      } else {
        this.$set(this.$store.state.common, 'sideBarWidth', 160);
        largeScreenBody.style.height = '100%';
      }

      this.windowResizeTimer && window.clearTimeout(this.windowResizeTimer);
      this.windowResizeTimer = window.setTimeout(() => {
        this.computedBodyContent();
      }, 200);
    },

    // ----------------------- 其他方法 ----------------------------
    computedBodyContent() {
      let containWidth =
          this.$refs.largeScreenContainer.clientWidth ||
          this.$refs.largeScreenContainer.offsetWidth,
        containHeight =
          this.$refs.largeScreenContainer.clientHeight ||
          this.$refs.largeScreenContainer.offsetHeight;

      let widthRadio = containWidth / 1920,
        heightRadio = containHeight / 1080,
        radio = widthRadio < heightRadio ? widthRadio : heightRadio;
      this.$refs.largeScreenBody.setAttribute(
        'style',
        `transform: translate(-50%, -50%) scale(${radio});
                -webkit-transform:  translate(-50%, -50%) scale(${radio});
            `
      );
    },
    //设置定时刷新
    setRefreshTimer() {
      this.pageRefreshTimer && setTimeout(this.pageRefreshTimer);
      this.pageRefreshTimer = setTimeout(() => {
        this.setRefreshTimer();
        if (
          this.$route.path != '/workSheet/noLoginLargeScreen' &&
          this.$route.path != '/workSheet/wholeHospitalLargeScreen'
        ) {
          return;
        }
        this.refresh();
      }, 60000);
    },
    refreshTime() {
      this.nowTime =
        this.$dayjs().format('YYYY-MM-DD HH:mm') +
        ' 星期' +
        ['日', '一', '二', '三', '四', '五', '六'][this.$dayjs().day()];
    },
    clearCache() {
      this.intervalTimer && clearInterval(this.intervalTimer);
      this.pageRefreshTimer && clearInterval(this.pageRefreshTimer);
      this.handingRefreshTimer && clearInterval(this.handingRefreshTimer);
      window.removeEventListener('resize', this.handleWindowResize);
    }
  },
  beforeDestroy() {
    this.clearCache();
  },
  computed: {
    isFullScreen() {
      return this.$store.state.common.isFullScreen;
    }
  }
};
</script>

<style lang="scss" scoped>
.large-screen-body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  background-image: url('../../assets/img/workSheet/backgroundImg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  -moz-background-size: 100% 100%;

  .large-screen-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transform-origin: center;
    width: 1920px;
    height: 1080px;

    padding-top: 15px;
  }

  .screen-title-box {
    width: 1920px;
    height: 60px;
    margin-bottom: 27px;
    position: relative;
    color: #fff;
    font-weight: 600;
    color: #ffffff;
    line-height: 33px;

    // background-image: url('../../../assets/img/workSheet/title_background_image.png');
    // background-repeat: no-repeat;
    // background-size: 100% 100%;
    // -moz-background-size: 100% 100%;

    .screen-title {
      width: 690px;
      padding: 0 20px;
      background-image: url(../../assets/img/workSheet/title_bg_img.png);
      background-size: 100% 100%;
      > div {
        font-size: 31px;
        font-weight: 600;
        color: transparent;
        line-height: 50px;
        height: 60px;
        background-image: linear-gradient(180deg, #2fb1ff 0%, #23f9ff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      img {
        width: 31px;
        height: 31px;
        border-radius: 50%;
      }
      .dot {
        background-color: #e7e517;
        border-radius: 50%;
        width: 6px;
        height: 6px;
      }
    }
    .screen-refresh {
      padding-left: 20px;
      line-height: 70px;
      height: 50px;
      background-image: url('../../assets/img/workSheet/title_background_left.png');
      background-size: 100% 100%;
    }
    .screen-time {
      display: flex;
      justify-content: flex-end;
      padding-right: 20px;
      font-size: 26px;
      height: 50px;
      line-height: 70px;
      background-image: url('../../assets/img/workSheet/title_background_right.png');
      background-size: 100% 100%;
    }
  }
}

.screen-col-top {
  height: 164px;
  margin-bottom: 20px;
  > div:first-child {
    margin-right: 20px;
  }
}
.screen-card {
  background: rgba(11, 57, 109, 0.3);
  box-shadow: 0px 0px 10px 0px #103062 inset;
}
.screen-content {
  padding: 0 20px;
}
.screen-bottom-col-1 > div:not(:last-child) {
  margin-bottom: 20px;
}
.screen-bottom-col-2 {
  margin: 0 20px;
}
.technology-board {
  height: 85px;
  width: 360px;
  img {
    width: 200px;
  }
  div {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.4);
    line-height: 25px;
    font-size: 18px;
    span {
      letter-spacing: 10px;
      font-weight: 600;
      line-height: 25px;
      font-size: 18px;
    }
  }
}
/deep/ * {
  box-sizing: border-box;
}
</style>
