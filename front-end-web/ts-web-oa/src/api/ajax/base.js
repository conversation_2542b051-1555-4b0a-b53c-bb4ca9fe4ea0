import { $api } from '@/api/ajax';
import { service } from '@/api/config';

// 获取科室树
const getTree = function() {
  return $api({
    url: `${service.tsBasics()}/organization/getTree`,
    method: 'post'
  });
};

// 获取科室树的人
const getOrgGroupUser = function(params) {
  return $api({
    url: '/ts-basics-bottom/employee/orgGroup/getOrgGroupUser',
    method: 'get',
    params: params
  });
};

// 根据code来查询人
const employeeSelectEmpByUserCode = function(data) {
  return $api({
    url: `${service.tsOa()}/employee/selectEmpByUserCode`,
    method: 'get',
    data
  });
};

const getEmployeeDetailByCode = function(id) {
  return $api({
    url: `${service.tsBasics()}/employee/getEmployeeDetailByCode/` + id,
    method: 'post'
  });
};

// 获取常用联系人
const customEmployeeBaseGetCommContact = function(data) {
  return $api({
    url: `${service.tsBasics()}/api/customEmployeeBase/getCommContact`,
    method: 'post',
    data: data
  });
};

// 保存记录常用联系人
const customEmployeeBaseSaveContact = function(data) {
  return $api({
    url: `${service.tsBasics()}/api/customEmployeeBase/saveContact`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  });
};

// 获取人员
const employeeListStatusAll = function(params, data) {
  return $api({
    url: '/ts-oa/employee/list',
    method: 'post',
    params: params,
    data: data
  });
};

// 获取群组
const getOrgGroupTree = function(params) {
  return $api({
    url: `/ts-basics-bottom/employee/orgGroup/getOrgGroupTree`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params
  });
};

// 获取字典
const getDictItem = function(data) {
  return $api({
    url: `${service.tsBasics()}/dictItem/list`,
    method: 'post',
    data
  });
};
const getDict = function(funUrl) {
  return $api({
    url: `/ts-hrms/dict/combobox/${funUrl}`,
    method: 'post'
  });
};
const getEmployeeList = function(data) {
  return $api({
    url: `${service.tsOa()}/employee/list?empStatus=1`,
    method: 'post',
    data
  });
};
const uploadFile = function(data, moduleName = 'oa') {
  return $api({
    url: `${service.tsBasics()}/fileAttachment/upload?moduleName=${moduleName}`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  });
};
// ts-document
const uploadFileDocument = function(data, moduleName = '') {
  return $api({
    url: `/ts-oa/attachment/fileUpload?module=${moduleName}`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  });
};
// 获取附件
const getFileAttachmentByBusinessId = function(params) {
  return $api({
    url: `${service.tsBasics()}/fileAttachment/getFileAttachmentByBusinessId`,
    method: 'get',
    params: params
  });
};
// 获取附件 (根据fileId)
const getFileAttachmentSelectByIds = function(params) {
  return $api({
    url: `/ts-document/attachment/selectByIds`,
    method: 'get',
    params: params
  });
};
// 删除附件
const deleteFileId = function(params) {
  return $api({
    url: `${service.tsBasics()}/fileAttachment/deleteFileId`,
    method: 'get',
    params: params
  });
};
/**@desc 起草 查看流程 */
const getWorkflowParams = function(data) {
  return $api({
    url: `${service.tsOa()}/api/contractRecord/getWorkflowParams`,
    method: 'get',
    data
  });
};
/**@desc 获取数据字典 */
const getDataByDataLibrary = function(data) {
  return $api({
    url: `${service.tsBasics()}/dictItem/getDictItemByTypeCode?typeCode=${data}`,
    method: 'get'
  });
};
/**@desc 获取系统调用字典 */
const getCommErrorLogs = function(data) {
  return $api({
    url: `${service.tsBasics()}/api/CommErrorLogs/list`,
    method: 'get',
    data
  });
};
// 登录成功使用
const getAllGlobalSetting = function() {
  return $api({
    url: `${service.tsBasics()}/globalSetting/getAllGlobalSetting`,
    method: 'get'
  });
};

//获取人员列表
const customEmployeeBaseList = function(data) {
  return $api({
    url: service.tsBasics() + '/api/customEmployeeBase/list',
    method: 'post',
    data
  });
};

//获取科室组织架构
const getDeptOrgTree = function(data) {
  return $api({
    url: service.tsBasics() + '/api/hrmsOrg/list',
    method: 'get',
    data
  });
};

// 获取机构树形列表
const getOrgTree = function(data) {
  return $api({
    url: service.tsBasics() + '/api/hrmsOrg/orgTree',
    method: 'get',
    data
  });
};

export {
  getTree,
  getOrgGroupUser,
  employeeListStatusAll,
  employeeSelectEmpByUserCode,
  getEmployeeDetailByCode,
  customEmployeeBaseGetCommContact,
  customEmployeeBaseSaveContact,
  getOrgGroupTree,
  getDictItem,
  getDict,
  getEmployeeList,
  uploadFile,
  uploadFileDocument,
  getFileAttachmentByBusinessId,
  getFileAttachmentSelectByIds,
  deleteFileId,
  getWorkflowParams,
  getDataByDataLibrary,
  getCommErrorLogs,
  getAllGlobalSetting,
  customEmployeeBaseList,
  getDeptOrgTree,
  getOrgTree
};

export default {
  getTree,
  getOrgGroupUser,
  employeeListStatusAll,
  employeeSelectEmpByUserCode,
  getEmployeeDetailByCode,
  customEmployeeBaseGetCommContact,
  customEmployeeBaseSaveContact,
  getOrgGroupTree,
  getDictItem,
  getDict,
  getEmployeeList,
  uploadFile,
  uploadFileDocument,
  getFileAttachmentByBusinessId,
  getFileAttachmentSelectByIds,
  deleteFileId,
  getWorkflowParams,
  getDataByDataLibrary,
  getCommErrorLogs,
  getAllGlobalSetting,
  customEmployeeBaseList,
  getDeptOrgTree,
  getOrgTree
};
