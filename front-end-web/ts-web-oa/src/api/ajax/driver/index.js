import { $api } from '@/api/ajax';
import { service } from '@/api/config';

export const vehicleDriverSave = function(data) {
  return $api({
    url: `${service.tsOa()}/api/vehicleDriver/save`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  });
};

export const vehicleDriverUpdate = function(data) {
  return $api({
    url: `${service.tsOa()}/api/vehicleDriver/update`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  });
};

export const vehicleDriverDelete = function(id) {
  return $api({
    url: `${service.tsOa()}/api/vehicleDriver/delete/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  });
};

export const vehicleDriverList = function(params) {
  return $api({
    url: `${service.tsOa()}/api/vehicleDriver/list`,
    method: 'get',
    params
  });
};

// 获取司机详情
export const selectVehicleDriverById = function(params) {
  return $api({
    url: `${service.tsOa()}/api/vehicleDriver/selectVehicleDriverById`,
    method: 'get',
    params
  });
};

// 司机事故记录新增
export const vehicleDriverAccidentSave = function(data) {
  return $api({
    url: `${service.tsOa()}/api/vehicleDriverAccident/save`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  });
};

// 司机统计
export const getVehicleDriverListStatistics = function() {
  return $api({
    url: `${service.tsOa()}/api/vehicleDriver/getVehicleDriverListStatistics`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  });
};

// 司机事故记录编辑
export const vehicleDriverAccidentUpdate = function(data) {
  return $api({
    url: `${service.tsOa()}/api/vehicleDriverAccident/update`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  });
};

// 司机事故记录删除
export const vehicleDriverAccidentDelete = function(id) {
  return $api({
    url: `${service.tsOa()}/api/vehicleDriverAccident/delete/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  });
};

// 司机违章记录新增
export const vehicleDriverViolationSave = function(data) {
  return $api({
    url: `${service.tsOa()}/api/vehicleDriverViolation/save`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  });
};

// 司机违章记录编辑
export const vehicleDriverViolationUpdate = function(data) {
  return $api({
    url: `${service.tsOa()}/api/vehicleDriverViolation/update`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  });
};

// 司机违章记录删除
export const vehicleDriverViolationDelete = function(id) {
  return $api({
    url: `${service.tsOa()}/api/vehicleDriverViolation/delete/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  });
};

export default {};
