import { $api } from '@/api/ajax';
import { service } from '@/api/config';

export default {
  // 获取人员分数列表
  getPersonnelScoreList(params) {
    return $api({
      url: `${service.tsInformation()}/api/messageCustomContent/list`,
      method: 'get',
      params
    });
  },
  savePersonnelScore(data) {
    return $api({
      url: `${service.tsInformation()}/api/messageCustomContent/save`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },
  sendPersonnelScoreMessage(data) {
    return $api({
      url: `${service.tsInformation()}/api/messageCustomContent/sendMessage`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },
  importPersonnelScore(data) {
    return $api({
      url: `${service.tsInformation()}/api/messageCustomContent/excelImportAssess`,
      method: 'post',
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      data
    });
  }
};
