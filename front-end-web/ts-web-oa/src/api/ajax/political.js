import { $postJson, $api } from '@/api/ajax';
import { service } from '@/api/config';

export default {
  /**@desc  查询巡检列表-树形结构
   **/
  politicalListTree(data) {
    return $api({
      url: `${service.tsOa()}/api/political/listTree`,
      method: 'post',
      data
    });
  },
  /**@desc  新增巡检
   **/
  savePolitical(data) {
    return $postJson(`${service.tsOa()}/api/politicalType/save`, data);
  },
  /**@desc  修改巡检
   **/
  updatePolitical(data) {
    return $postJson(`${service.tsOa()}/api/politicalType/update`, data);
  },
  /**@desc  删除巡检
   **/
  deletePoliticalType(id) {
    return $postJson(`${service.tsOa()}/api/politicalType/delete/${id}`);
  },
  /**@desc  巡查设置列表
   **/
  politicalList(params) {
    return $api({
      url: `${service.tsOa()}/api/political/list`,
      method: 'get',
      params
    });
  },
  /**@desc  删除设置
   **/
  deletePolitical(id) {
    return $api({
      url: `${service.tsOa()}/api/political/delete/${id}`,
      method: 'post'
    });
  },
  /**@desc  活动设置分类列表
   **/
  politicalTypeTree(params) {
    return $api({
      url: `${service.tsOa()}/api/politicalType/listTree`,
      method: 'post',
      params
    });
  },
  /**@desc  活动设置分类列表
   **/
  politicalTypeList(params) {
    return $api({
      url: `${service.tsOa()}/api/politicalType/list`,
      method: 'get',
      params
    });
  },
  /**@desc  新增
   **/
  politicalSave(data) {
    return $postJson(`${service.tsOa()}/api/political/save`, data);
  },
  /**@desc  修改
   **/
  politicalUpdate(data) {
    return $postJson(`${service.tsOa()}/api/political/update`, data);
  },
  /**@desc  巡查详情
   **/
  getpoliticalDetails(id) {
    return $api({
      url: `${service.tsOa()}/api/political/${id}`,
      method: 'get'
    });
  },
  /**@desc  列表
   **/
  getPoliticalStartList(params) {
    return $api({
      url: `${service.tsOa()}/api/politicalStart/list`,
      method: 'get',
      params
    });
  },
  /**@desc  发起巡查
   **/
  savePoliticalStart(data) {
    return $postJson(`${service.tsOa()}/api/politicalStart/save`, data);
  },
  /**@desc  编辑巡查
   **/
  updatePoliticalStart(data) {
    return $postJson(`${service.tsOa()}/api/politicalStart/update`, data);
  },
  /**@desc  删除巡查
   **/
  deletePoliticalStart(id) {
    return $postJson(`${service.tsOa()}/api/politicalStart/delete/${id}`);
  },
  // 获取附件
  getPoliticalStartDetails(id) {
    return $api({
      url: `${service.tsOa()}/api/politicalStart/selectById/${id}`,
      method: 'get'
    });
  }
};
