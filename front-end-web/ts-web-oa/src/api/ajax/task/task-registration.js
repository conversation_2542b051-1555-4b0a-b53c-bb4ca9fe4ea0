import { $api, $get, $upload } from '@/api/ajax';
import { service } from '@/api/config';

export default {
  /**@desc 获取任务登记列表 */
  getTaskRegistrationList(params = {}) {
    return $get(`${service.tsOa()}/api/superviseRegister/list`, params);
  },
  //获取医院页面设置信息
  getGlobalSetting() {
    return $get(`${service.tsBasics()}/globalSetting/getAllGlobalSetting`);
  },
  /**@desc 保存任务登记配置 */
  saveTaskRegistrtion(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseRegister/save`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  /**@desc 更新任务登记配置 */
  updateTaskRegistrtion(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseRegister/update`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  /**@desc 催办 */
  saveUrgentTask(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseRegister/urgent`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  /**@desc 转办 */
  saveTransferTask(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseRegister/transfer`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  /**@desc 批示 */
  saveApproveTask(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseRegister/approve`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  /**@desc 撤销 */
  saveCancelTask(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseRegister/cancel`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  /**@desc 验收 */
  saveCheckTask(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseRegister/check`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  /**@desc 删除任务 */
  deleteTask(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseRegister/delete/${data.id}`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  /**@desc 终止 */
  saveCloseTask(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseRegister/close`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  /**@desc 延期 */
  saveDelayTask(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseRegister/delay`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  /**@desc 办理 */
  saveHandleTask(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseRegister/handle`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  /**@desc 反馈 */
  saveFeedbackTask(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseRegister/feedback`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  /**@desc 反馈详情 */
  getFeedbackDetails(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseRegister/feedbackDetails`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  /**@desc 督办详情 */
  getTaskDetail(params) {
    return $get(`${service.tsOa()}/api/superviseRegister/${params.id}`);
  },

  /**@desc 操作记录 */
  getSuperviseLogsList(params) {
    return $get(
      `${service.tsOa()}/api/superviseLogs/getSuperviseLogsList`,
      params
    );
  },

  /**@desc 任务类型 */
  getSuperviseTypeList(params) {
    return $get(
      `${service.tsOa()}/api/superviseType/getSuperviseTypeList`,
      params
    );
  },

  /**@desc 下载文件 */
  downloadFile(fileId, fileName) {
    return $upload({
      url: `${service.tsBasics()}/fileAttachment/downloadFile/${fileId}`,
      requestMethod: 'get'
    })
      .then(data => {
        if (!data || data.size === 0) {
          Vue.prototype['$message'].warning('File download failed');
          return;
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), fileName);
        } else {
          let url = window.URL.createObjectURL(new Blob([data]));
          let link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', fileName);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link); //下载完成移除元素
          window.URL.revokeObjectURL(url); //释放掉blob对象
        }
      })
      .catch(err => {});
  }
};
