import { $api, $get, $post } from '@/api/ajax';
import { service } from '@/api/config';

export default {
  /**@desc 获取基础配置列表 */
  taskSettingList(params = {}) {
    return $get(
      `${service.tsOa()}/api/superviseType/getSuperviseTypeList`,
      params
    );
  },

  /**@desc 获取基础配置列表(分页) */
  taskSettingListByPage(params) {
    return $get(`${service.tsOa()}/api/superviseType/list`, params);
  },

  /**@desc 保存基础配置 */
  saveSetting(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseType/save`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  /**@desc 修改基础配置 */
  updateSetting(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseType/update`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  },

  /**@desc 删除基础配置 */
  deleteSetting(data) {
    return $api({
      url: `${service.tsOa()}/api/superviseType/delete/${data.id}`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      data
    });
  }
};
