export default {
  //赤霄模块
  sso: 'user', //登录模块,
  grafanaGo: '/grafana-web', //grafana登录
  chixiaoApi: '',
  grafanaChixiao: 'grafanas',
  basics: '/ts-basics-bottom',
  hrms: '/ts-hrms',
  oa: '/ts-oa',
  workflow: '/ts-workflow'
};
let indexConfig = window[process.env.VUE_APP_BASE_URL];
let mode = 0;
let config = {};
let _config = {};
if (process.env.NODE_ENV == 'development') {
  config = indexConfig[mode].config;
} else {
  let configArr = indexConfig.filter(item => {
    if (item.matchPath == '*') {
      return true;
    } else {
      if (location.href.indexOf(item.matchPath) != -1) {
        return true;
      }
    }
    return false;
  });
  config = configArr[0].config;
}
for (let key in config) {
  _config[key] = function() {
    if (Object.prototype.toString.call(config[key]) == '[object Object]') {
      //如果是对象 则进行处理
      if (config[key].base) {
        return `${config[key].base || ''}${config[key].url || ''}`;
      }
      return `${config.base || ''}${config[key].base || ''}${config[key].url ||
        ''}`;
    } else if (
      Object.prototype.toString.call(config[key]) == '[object String]'
    ) {
      return config[key];
    }
  };
  _config[`_${key}`] = config[key];
}

export const service = _config;
