<template>
  <div ref="content" class="base-action-cell-content">
    <span
      class="visible-action-item"
      v-for="(item, index) of showActionList"
      :key="index"
      @click="handleActionClick(item)"
      >{{ item.label }}</span
    >
    <template v-if="hiddenActionList.length">
      <el-popover
        ref="popover"
        placement="bottom-end"
        trigger="hover"
        :close-delay="0"
        popper-class="base-table-action-cell-popover"
        :popper-options="{
          boundariesElement: 'viewport',
          removeOnDestroy: true
        }"
      >
        <span slot="reference">
          <i class="more-action-icon layui-icon layui-icon-more-vertical"></i>
        </span>
        <div
          class="hidden-action-item"
          v-for="(item, index) of hiddenActionList"
          :key="index"
          @click="handleActionClick(item)"
        >
          <div class="action-icon">
            <i :class="item.icon"></i>
          </div>
          {{ item.label }}
        </div>
      </el-popover>
    </template>
  </div>
</template>

<script>
import styleOptions from '@/assets/css/var.scss';

export default {
  props: {
    actions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      lock: null, //是否在计算宽度中
      showActionList: [],
      hiddenActionList: []
    };
  },
  mounted() {},
  watch: {
    actions: {
      handler() {
        this.refresh();
      },
      immediate: true
    }
  },
  methods: {
    handleActionClick(action) {
      this.$emit('action-select', action.event, action);
    },
    refresh(key) {
      if (this.lock && key != this.lock) {
        return;
      }
      this.lock = Math.random()
        .toString(36)
        .substr(2);

      let content = this.$refs.content;
      if (!content) {
        setTimeout(() => {
          this.refresh(this.lock);
        }, 50);
        return;
      }
      let interval = Number(styleOptions['theme-interval'].replace('px', '')),
        width = content.clientWidth + interval,
        newShowActionList = [],
        newHiddenActionList = [];

      for (let index = 0; index < this.actions.length; index++) {
        let action = this.actions[index],
          actionWidth = action.label.length * 14;

        if (index < this.actions.length - 1) {
          //如果 index 小于操作长度，即后面还有操作的时候， 默认存在更多按钮，如若都放的下则继续轮询
          if (width - interval * 2 - actionWidth - 16 >= 0) {
            width = width - interval - actionWidth;
          } else {
            newShowActionList = this.actions.slice(0, index);
            newHiddenActionList = this.actions.slice(index);
            break;
          }
        } else {
          //如果 能容纳下最后一个按钮
          if (width - interval - actionWidth >= 0) {
            newShowActionList = this.actions;
          } else {
            newShowActionList = this.actions.slice(0, index);
            newHiddenActionList = this.actions.slice(index);
          }
        }
      }
      this.hiddenActionList = newHiddenActionList;
      this.showActionList = newShowActionList;

      this.lock = null;
    }
  }
};
</script>

<style lang="scss" scoped>
.base-action-cell-content span + span {
  margin-left: $theme-interval;
}
.more-action-icon {
  cursor: pointer;
  &:hover {
    color: $theme-color;
  }
}
.visible-action-item {
  cursor: pointer;
  color: $theme-color;
  &:hover {
    text-decoration: underline;
    opacity: 0.8;
  }
}
</style>

<style lang="scss">
.base-table-action-cell-popover {
  margin-top: 2px !important;
  padding: 0;
  min-width: unset;
  .popper__arrow {
    display: none;
  }
  .hidden-action-item {
    padding-left: $theme-interval;
    padding-right: 20px;
    height: 27px;
    line-height: 27px;
    cursor: pointer;
    &:hover {
      background-color: mix(#fff, $theme-color, 92%);
    }
  }
  .action-icon {
    height: 27px;
    width: 27px;
    line-height: 27px;
    text-align: center;
    display: inline-block;
    margin-right: 5px;
    i {
      font-size: 16px;
    }
  }
}
</style>
