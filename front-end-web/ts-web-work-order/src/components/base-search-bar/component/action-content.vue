<template>
  <div ref="content" class="action-content flex">
    <el-button
      v-for="(item, index) of showActionList"
      :key="index"
      :class="{ 'trasen-perpul': item.isImportant }"
      v-bind="item.prop"
      @click="item.click()"
    >
      {{ item.label }}
    </el-button>
  </div>
</template>

<script>
export default {
  name: 'action-content',
  props: {
    actions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      showActionList: [],
      hiddenActionList: []
    };
  },
  watch: {
    actions: {
      handler: function(val) {
        this.computedActions();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    computedActions() {
      this.$nextTick(() => {
        if (!this.$refs.content) {
          this.computedActions();
          return;
        }

        let width = 0,
          contentWidth = this.$refs.content.clientWidth;
        this.actions.forEach(item => {
          let length = item.label.length,
            btnWidth = length * 14 + 16;
          item.icon ? btnWidth + 16 : null;

          btnWidth < 60 ? (btnWidth = 60) : null;

          width += btnWidth;

          // width <
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.action-content {
  width: 100%;
}
</style>
