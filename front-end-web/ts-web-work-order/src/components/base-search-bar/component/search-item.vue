<script>
export default {
  name: 'search-item',
  props: {
    prop: {
      type: Object,
      default: () => {}
    },
    value: {}
  },
  data() {
    return {
      domValue: '',
      valueChangeTimer: null
    };
  },
  created() {
    this.domValue = this.value;
  },
  render(h, context) {
    let prop = this.prop,
      Tag =
        this.prop.element
          .split('-')
          .map(item => item.substring(0, 1).toUpperCase() + item.substring(1))
          .join('') || 'div';

    //渲染子组件-暂时这么做吧
    let childNodeList = [];
    if (prop.childNodeList && prop.childNodeList.length) {
      prop.childNodeList.forEach((dom, domIndex) => {
        let ChildTag =
          dom.element
            .split('-')
            .map(item => item.substring(0, 1).toUpperCase() + item.substring(1))
            .join('') || 'div';
        childNodeList.push(
          <ChildTag
            key={dom[dom.key] || domIndex}
            {...{ attrs: dom }}></ChildTag>
        );
      });
    }
    return (
      <Tag
        ref="componentContent"
        vModel={this.domValue}
        {...{ attrs: prop.elementProp, on: prop.event }}
        class={prop.elementClass}>
        {...childNodeList}
      </Tag>
    );
  },
  watch: {
    domValue(val) {
      this.valueChangeTimer && clearTimeout(this.valueChangeTimer);
      this.valueChangeTimer = setTimeout(() => {
        this.$emit('input', val);
      }, 250);
    },
    value(val) {
      this.domValue = this.value;
    }
  }
};

// export default {
//   props: {
//     prop: {
//       type: Object,
//       default: () => {}
//     }
//   },
//   data() {
//     return {
//       value: '',
//       valueChangeTimer: null
//     };
//   },
//   watch: {
//     value: function() {
//       this.valueChangeTimer && clearTimeout(this.valueChangeTimer);
//       this.valueChangeTimer = setTimeout(() => {
//         this.$emit('change', this.value);
//       }, 200);
//     }
//   },
//   methods: {}
// };
</script>

<style lang="scss" scoped>
.search-describe {
  font-size: 14px;
  margin-right: 8px;
}
</style>
