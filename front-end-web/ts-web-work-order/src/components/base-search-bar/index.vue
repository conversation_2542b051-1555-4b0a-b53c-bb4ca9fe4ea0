<template>
  <div class="trasen-search-content flex-row-between">
    <div class="flex left-search-content">
      <template v-for="(item, index) of inlineFormList">
        <div :key="index" :class="item.contentClass" class="search-item">
          <div class="search-lable">{{ item.label }}</div>
          <slot :name="item.value">
            <search-item v-model="form[item.value]" :prop="item"></search-item>
          </slot>
        </div>
      </template>
      <el-button class="trasen-perpul search-btn" @click="handleSearchClick">
        搜索
      </el-button>
      <div
        v-if="hiddenFormList.length"
        class="more-search-content flex-center"
        :class="{ 'active-search-list': shoeMoreSearch }"
        @click="shoeMoreSearch = !shoeMoreSearch"
      >
        <!-- <el-popover
          placement="top-start"
          width="400"
          title="标题"
          trigger="hover"
        > -->
        <div slot="reference" class="oaicon oa-icon-search_list"></div>
        <div class="toast-content"></div>
        <!-- </el-popover> -->
      </div>
      <div
        class="el-icon-refresh-right reset-btn flex-center"
        @click="handleResetSearchForm"
      ></div>

      <div
        class="hidden-search-content"
        :class="{ 'active-content': shoeMoreSearch }"
      >
        <div class="hidden-search-title flex-col-center">
          <div class="oaicon oa-icon-search_list"></div>
          更多查询条件
        </div>
        <div class="search-list-content flex-wrap">
          <el-row
            v-for="(item, index) of hiddenFormList"
            :key="index"
            :style="{ width: 100 / columns + '%' }"
            class="flex-col-center search-list-item"
          >
            <!-- <el-col
              :span="item.labelWidth || labelWidth"
              :style="{ textAlign: item.align || 'right' }"
              class="label-col"
            >
              {{ item.label }}
            </el-col> -->
            <span class="label-title">{{ item.label }}</span>
            <el-col
              :span="elementCol | computedElementCol(item, labelWidth)"
              class="element-col"
            >
              <search-item
                v-model="form[item.value]"
                :prop="item"
              ></search-item>
            </el-col>
          </el-row>
        </div>
        <div class="hidden-search-bottom flex-row-center">
          <el-button class="trasen-perpul" @click="handleSearchClick">
            搜索
          </el-button>
          <el-button @click="handleResetSearchForm">重置</el-button>
          <el-button @click="shoeMoreSearch = false">关闭</el-button>
        </div>
      </div>
    </div>

    <div class="flex-grow flex-end">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script>
import indexJs from './js/index';
import searchItem from './component/search-item.vue';

export default {
  name: 'base-search-bar',
  mixins: [indexJs],
  components: {
    searchItem
  },
  props: {
    /**@desc 搜索条件列表 */
    formList: {
      type: Array,
      default: () => []
    },
    /**@desc 隐藏搜索项文字描述占比 */
    labelWidth: {
      type: Number,
      default: () => 4
    },
    /**@desc 隐藏搜索项输出框描述占比 */
    elementCol: {
      type: Number,
      default: () => 20
    },
    /**@desc 隐藏搜索项列数 */
    columns: {
      type: Number,
      default: () => 2
    },
    /**@desc 操作列表 */
    actions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {},
      inlineFormList: [],
      hiddenFormList: [],
      shoeMoreSearch: false
    };
  },
  watch: {
    formList: {
      handler: function() {
        this.reComputeFormList();
      },
      immediate: true,
      deep: true
    }
  },
  filters: {
    computedElementCol(elementCol, item, labelWidth) {
      return (
        item.elementCol || elementCol || 24 - (item.labelWidth || labelWidth)
      );
    }
  },
  methods: {
    //重新计算搜索表单的顺序
    reComputeFormList() {
      let newInlineFormList = [],
        hiddenFormList = [];

      newInlineFormList = this.formList.slice(0, 3);
      this.inlineFormList = newInlineFormList;
      this.hiddenFormList = this.formList.slice(3);
    },
    //重置
    handleResetSearchForm() {
      this.form = {};
      this.$emit('change', this.form);
    },
    handleSearchClick() {
      this.shoeMoreSearch = false;
      this.$emit('change', this.form);
    }
  }
};
</script>

<style lang="scss" scoped>
.trasen-search-content {
  position: relative;
  z-index: 999;
  padding-bottom: 8px;
  border-bottom: 1px solid $theme-border-color;
}
.left-search-content,
.search-btn,
.reset-btn,
.more-search-content,
.search-item,
.search-lable {
  margin-right: $theme-interval;
}
.search-btn,
.reset-btn,
.more-search-content,
.search-item {
  height: 30px;
}
/deep/.search-item .el-date-editor {
  width: 205px;
}
.more-search-content {
  cursor: pointer;
  width: 40px;
  background-color: #fff;
  border: 1px solid #eee;
  color: #333;
  height: 30px;
  line-height: 30px;
  border-radius: 2px;
  box-sizing: border-box;
  text-align: center;
  margin-left: $theme-interval;
  &:hover,
  &.active-search-list {
    color: $theme-color !important;
    border-color: $theme-color !important;
  }
  .oaicon.oa-icon-search_list {
    font-size: 25px;
    &:hover {
      opacity: 0.8;
    }
  }
}
.hidden-search-content {
  position: absolute;
  top: calc(100% + 1px);
  width: 100%;
  background: #fff;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
  max-height: 0;
  transition: all 0.3s;
  border-radius: 0 0 4px 4px;
  padding: 0 $box-padding-16;
  overflow: hidden;
  &.active-content {
    max-height: 100vh;
  }
}
.hidden-search-title {
  height: 44px;
  font-size: 16px;
  border-bottom: 1px solid $theme-border-color;
  .oa-icon-search_list {
    margin-right: $theme-interval;
    font-size: 16px;
  }
}
.search-list-content {
  padding: $box-padding-16 0;
  padding-bottom: $theme-interval;
}
.search-list-item {
  min-height: 32px;
  margin-bottom: $theme-interval;
}
.label-col {
  padding-right: $theme-interval;
}
.hidden-search-content .search-list-item .element-col > * {
  width: 100%;
}
.hidden-search-bottom {
  border-top: 1px solid $theme-border-color;
  padding: $theme-interval 0;
}
.reset-btn {
  width: 30px;
  height: 30px;
  font-size: 16px;
  cursor: pointer;
  &:hover {
    color: $theme-color;
  }
}
.label-title {
  width: 100px;
  flex-shrink: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: right;
  margin-right: 8px;
}
</style>
