<template>
  <div class="ts-button flex-center" @click="click" :class="`${type}`">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'index',
  props: {
    type: {
      type: String,
      default: 'primary'
    }
  },
  methods: {
    click(e) {
      this.$emit('click', e);
    }
  }
};
</script>

<style scoped lang="scss">
.ts-button {
  height: 30px;
  border-radius: 4px;
  border: 1px solid #32a4ff;
  padding: 0 10px;
  cursor: pointer;
  transition: all 0.3s;
}
.primary {
  color: $--color-primary;
  border-color: $--color-primary;
  &:hover {
    color: $--color-primary-hover;
    border-color: $--color-primary-hover;
  }
  &:active {
    color: $--color-primary-hover;
    border-color: $--color-primary-hover;
  }
}
.success {
  color: $--color-success;
  border-color: $--color-success;
  &:hover {
    color: $--color-success-hover;
    border-color: $--color-success-hover;
  }
  &:active {
    color: $--color-success-hover;
    border-color: $--color-success-hover;
  }
}
</style>
