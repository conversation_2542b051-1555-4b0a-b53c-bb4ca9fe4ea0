<template>
  <span :title="content">
    {{ val }}
  </span>
</template>

<script>
export default {
  name: 'index',
  props: {
    content: {
      type: String,
      default: () => ''
    }
  },
  computed: {
    val() {
      if (this.content && this.content.length > 15) {
        return this.content.slice(0, 15) + '...';
      } else {
        return this.content;
      }
    }
  }
};
</script>

<style scoped></style>
