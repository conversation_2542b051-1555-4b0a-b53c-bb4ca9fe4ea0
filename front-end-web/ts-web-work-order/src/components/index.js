import ElementUi from '@trasen/trasen-element-ui/lib';
import inputTree from '@/components/input-tree/inputTree.vue';
import elementuiExtend from '@/extends/index.js';
import inputSelect from '@/components/input-select/inputSelect.vue'; //可搜索选择下拉框
import BaseTable from './base-table/index.vue';

export default {
  /**@desc 这个文件主要用于初始化各种当前项目公用组件**/
  install(Vue) {
    Vue.use(ElementUi, { size: 'small' });

    Vue.use(elementuiExtend); //初始化ElementUi框架

    Vue.component('InputTree', inputTree);
    Vue.component('InputSelect', inputSelect);
    Vue.component('BaseTable', BaseTable);
  }
};
