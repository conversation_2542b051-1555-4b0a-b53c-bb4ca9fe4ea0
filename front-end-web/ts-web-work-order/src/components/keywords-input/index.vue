<template>
  <div class="flex-start keywords-input">
    <el-input
      v-model="value"
      class="input"
      @input="input"
      :placeholder="placeholder"
      @keyup.enter.native="queryClick"
    >
    </el-input>
    <ts-button @click="queryClick" class="keyword-border">查询</ts-button>
  </div>
</template>

<script>
export default {
  name: 'index',
  data() {
    return {
      value: ''
    };
  },
  watch: {
    keywords() {
      this.value = this.keywords;
    }
  },
  props: {
    keywords: {
      type: [String],
      default: ''
    },
    buttonText: {
      type: [String],
      default: '查询'
    },
    placeholder: {
      type: [String],
      default: ''
    }
  },
  methods: {
    /**@desc 上传更新 keywords**/
    input(e) {
      this.$emit('input', e);
    },
    /**@desc 点击查询**/
    queryClick() {
      this.$emit('click');
    }
  },
  mounted() {
    this.value = this.keywords;
  }
};
</script>

<style scoped lang="scss">
.input {
  /deep/ {
    .el-input__inner {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }
}
.keywords-input {
  //width: 300px;
}
.keyword-border {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
</style>
