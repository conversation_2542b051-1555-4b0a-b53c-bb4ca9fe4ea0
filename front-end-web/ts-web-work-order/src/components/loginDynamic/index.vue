<template>
  <!-- 整个动图画布 -->
  <pixi-renderer :x="0" :y="0" :width="943" :height="600" :transparent="true">
    <!-- 背景图，带上连线 -->
    <pixi-sprite name="bg" :src="srcArr.bg" :x="40" :y="89"></pixi-sprite>
    <!-- 从左到右 第一排元素 -->
    <pixi-container name="module3" :x="411" :y="12" :width="160" :height="175">
      <pixi-sprite
        name="module3bg"
        :x="0"
        :y="0"
        :src="srcArr.module3"
      ></pixi-sprite>
      <!-- module3 半月形上下移动 -->
      <pixi-sprite
        name="module31"
        :x="54"
        :y="16"
        :src="srcArr.module3_3"
        :animationOptions="module3WalkPath1"
      ></pixi-sprite>
      <pixi-sprite
        name="module32"
        :x="24"
        :y="31"
        :src="srcArr.module3_1"
        :animationOptions="module3WalkPath2"
      ></pixi-sprite>
      <pixi-sprite
        name="module33"
        :x="90"
        :y="39"
        :src="srcArr.module3_2"
        :animationOptions="module3WalkPath3"
      ></pixi-sprite>
      <!-- module3 半月形上下移动 -->
    </pixi-container>
    <pixi-container name="module2" :x="129" :y="73" :width="237" :height="214">
      <pixi-sprite
        name="module2Bg"
        :x="0"
        :y="0"
        :src="srcArr.module2"
      ></pixi-sprite>
      <!-- module2上下动画 -->
      <pixi-sprite
        name="module21"
        :x="143"
        :y="46"
        :anchorX="0.5"
        :anchorY="0.5"
        :src="srcArr.module2_1"
        :animationOptions="module2Slide"
      ></pixi-sprite>
      <!-- module2上下动画 -->

      <!-- module2 滑块上下滚动-->
      <!--  114 76 114 118 -->
      <pixi-sprite
        name="module22"
        :x="114"
        :y="76"
        :src="srcArr.light1"
        :animationOptions="module2SlideGroup1"
      ></pixi-sprite>
      <pixi-sprite
        name="module23"
        :x="119"
        :y="120"
        :src="srcArr.light2"
        :animationOptions="module2SlideGroup2"
      ></pixi-sprite>
      <pixi-sprite
        name="module24"
        :x="124"
        :y="81"
        :src="srcArr.light3"
        :animationOptions="module2SlideGroup3"
      ></pixi-sprite>
      <pixi-sprite
        name="module25"
        :x="128"
        :y="126"
        :src="srcArr.light4"
        :animationOptions="module2SlideGroup4"
      ></pixi-sprite>
      <pixi-sprite
        name="module26"
        :x="133"
        :y="84"
        :src="srcArr.light1"
        :animationOptions="module2SlideGroup5"
      ></pixi-sprite>
      <!-- module2 滑块上下滚动 -->
    </pixi-container>
    <pixi-container name="module1" :x="10" :y="132" :width="164" :height="224">
      <pixi-sprite
        name="module1Bg"
        :x="0"
        :y="100"
        :src="srcArr.module1"
      ></pixi-sprite>
      <!-- module1上下动画 -->
      <pixi-sprite
        name="module11"
        :x="68"
        :y="57"
        :src="srcArr.module1_1"
        :animationOptions="module1Slide"
      ></pixi-sprite>
      <!-- module1上下动画 -->
    </pixi-container>
    <!-- 从左到右 第二排 -->
    <pixi-container name="module7" :x="541" :y="47" :width="212" :height="254">
      <pixi-sprite
        name="module7Bg"
        :x="0"
        :y="100"
        :src="srcArr.module7"
      ></pixi-sprite>
      <pixi-sprite
        name="module71"
        :x="57"
        :y="59"
        :src="srcArr.module7_1"
        :animationOptions="module7Slide"
      ></pixi-sprite>
    </pixi-container>
    <pixi-container name="module6" :x="315" :y="81" :width="244" :height="343">
      <pixi-sprite
        name="module6Bg"
        :x="0"
        :y="0"
        :src="srcArr.module6"
      ></pixi-sprite>
      <!-- 移动 + 缩放 + 透明度 -->
      <!-- 起点：120 188 -->
      <pixi-sprite
        name="module61"
        :x="139"
        :y="128"
        :anchorX="0.5"
        :anchorY="0.5"
        :src="srcArr.square1"
        :animationOptions="module6Group1"
      ></pixi-sprite>
      <pixi-sprite
        name="module62"
        :x="129"
        :y="104"
        :anchorX="0.5"
        :anchorY="0.5"
        :src="srcArr.square2"
        :animationOptions="module6Group2"
      ></pixi-sprite>
      <pixi-sprite
        name="module63"
        :x="98"
        :y="112"
        :anchorX="0.5"
        :anchorY="0.5"
        :src="srcArr.square7"
        :animationOptions="module6Group3"
      ></pixi-sprite>
      <pixi-sprite
        name="module64"
        :x="102"
        :y="152"
        :anchorX="0.5"
        :anchorY="0.5"
        :src="srcArr.square4"
        :animationOptions="module6Group4"
      ></pixi-sprite>
      <pixi-sprite
        name="module65"
        :x="139"
        :y="165"
        :anchorX="0.5"
        :anchorY="0.5"
        :src="srcArr.square5"
        :animationOptions="module6Group5"
      ></pixi-sprite>
      <pixi-sprite
        name="module66"
        :x="116"
        :y="114"
        :anchorX="0.5"
        :anchorY="0.5"
        :src="srcArr.square6"
        :animationOptions="module6Group6"
      ></pixi-sprite>
      <pixi-sprite
        name="module67"
        :x="119"
        :y="157"
        :anchorX="0.5"
        :anchorY="0.5"
        :src="srcArr.square3"
        :animationOptions="module6Group7"
      ></pixi-sprite>
      <pixi-sprite
        name="module68"
        :x="122"
        :y="170"
        :anchorX="0.5"
        :anchorY="0.5"
        :src="srcArr.square8"
        :animationOptions="module6Group8"
      ></pixi-sprite>

      <pixi-sprite
        name="module69"
        :src="srcArr.module6_1"
        :x="126"
        :y="145"
        :anchorX="0.5"
        :anchorY="0.5"
        :animationOptions="module6Slide"
      ></pixi-sprite>
    </pixi-container>
    <pixi-container name="module5" :x="157" :y="265" :width="134" :height="146">
      <pixi-sprite
        name="module5bg"
        :x="0"
        :y="0"
        :src="srcArr.module5"
      ></pixi-sprite>
      <pixi-sprite
        name="module51"
        :x="45"
        :y="14"
        :src="srcArr.module5_3"
        :animationOptions="module5WalkPath1"
      ></pixi-sprite>
      <pixi-sprite
        name="module52"
        :x="21"
        :y="26"
        :src="srcArr.module5_1"
        :animationOptions="module5WalkPath2"
      ></pixi-sprite>
      <pixi-sprite
        name="module53"
        :x="75"
        :y="32"
        :src="srcArr.module5_2"
        :animationOptions="module5WalkPath3"
      ></pixi-sprite>
    </pixi-container>
    <pixi-container name="module4" :x="0" :y="343" :width="164" :height="210">
      <pixi-sprite
        name="module4bg"
        :x="0"
        :y="0"
        :src="srcArr.module4"
      ></pixi-sprite>
      <pixi-sprite
        name="module41"
        :x="93"
        :y="36"
        :anchorX="0.5"
        :anchorY="0.5"
        :scaleX="0.8"
        :scaleY="0.8"
        :src="srcArr.module4_1"
        :animationOptions="module4FlipY"
      ></pixi-sprite>
    </pixi-container>
    <!-- 从左到右 第三排 -->
    <pixi-container
      name="module10"
      :x="699"
      :y="148"
      :width="244"
      :height="211"
    >
      <pixi-sprite
        name="module10bg"
        :x="0"
        :y="0"
        :src="srcArr.module10"
      ></pixi-sprite>
      <!-- module10上下动画 -->
      <pixi-sprite
        name="module101"
        :x="94"
        :y="46"
        :anchorX="0.5"
        :anchorY="0.5"
        :src="srcArr.module10_1"
        :animationOptions="module10Slide"
      ></pixi-sprite>
      <!-- module10上下动画 -->
      <!-- module10 滑块上下滚动-->
      <!--  114 76 114 118 -->
      <pixi-sprite
        name="module102"
        :x="65"
        :y="78"
        :src="srcArr.light5"
        :animationOptions="module10SlideGroup1"
      ></pixi-sprite>
      <pixi-sprite
        name="module103"
        :x="70"
        :y="120"
        :src="srcArr.light2"
        :animationOptions="module10SlideGroup2"
      ></pixi-sprite>
      <pixi-sprite
        name="module104"
        :x="75"
        :y="83"
        :src="srcArr.light3"
        :animationOptions="module10SlideGroup3"
      ></pixi-sprite>
      <pixi-sprite
        name="module105"
        :x="80"
        :y="126"
        :src="srcArr.light4"
        :animationOptions="module10SlideGroup4"
      ></pixi-sprite>
      <pixi-sprite
        name="module106"
        :x="84"
        :y="88"
        :src="srcArr.light2"
        :animationOptions="module10SlideGroup5"
      ></pixi-sprite>
      <!-- module10 滑块上下滚动 -->
    </pixi-container>
    <pixi-container name="module9" :x="567" :y="340" :width="164" :height="94">
      <pixi-sprite
        name="module9bg"
        :x="0"
        :y="0"
        :src="srcArr.module9"
      ></pixi-sprite>
      <!-- 圆环 类似电池充电动画 -->
      <pixi-sprite
        name="module91"
        :src="srcArr.module9_1"
        :x="37"
        :y="11"
        :animationOptions="module9SlideGroup1"
      ></pixi-sprite>
      <pixi-sprite
        name="module92"
        :src="srcArr.module9_2"
        :x="37"
        :y="11"
        :animationOptions="module9SlideGroup2"
      ></pixi-sprite>
      <pixi-sprite
        name="module93"
        :src="srcArr.module9_3"
        :x="37"
        :y="11"
        :animationOptions="module9SlideGroup3"
      ></pixi-sprite>
      <pixi-sprite
        name="module94"
        :src="srcArr.module9_1"
        :x="37"
        :y="11"
        :animationOptions="module9SlideGroup4"
      ></pixi-sprite>
      <pixi-sprite
        name="module95"
        :src="srcArr.module9_2"
        :x="37"
        :y="11"
        :animationOptions="module9SlideGroup5"
      ></pixi-sprite>
      <pixi-sprite
        name="module96"
        :src="srcArr.module9_3"
        :x="37"
        :y="11"
        :animationOptions="module9SlideGroup6"
      ></pixi-sprite>
      <pixi-sprite
        name="module97"
        :src="srcArr.module9_1"
        :x="37"
        :y="11"
        :animationOptions="module9SlideGroup7"
      ></pixi-sprite>
      <pixi-sprite
        name="module98"
        :src="srcArr.module9_2"
        :x="37"
        :y="11"
        :animationOptions="module9SlideGroup8"
      ></pixi-sprite>
      <pixi-sprite
        name="module99"
        :src="srcArr.module9_3"
        :x="37"
        :y="11"
        :animationOptions="module9SlideGroup9"
      ></pixi-sprite>
      <pixi-sprite
        name="module97"
        :x="100"
        :y="40"
        :src="srcArr.module9_4"
      ></pixi-sprite>
      <!-- 圆环 类似电池充电动画 -->
    </pixi-container>
    <pixi-container name="module8" :x="181" :y="396" :width="258" :height="187">
      <pixi-sprite
        name="module8bg"
        :x="0"
        :y="0"
        :src="srcArr.module8"
      ></pixi-sprite>
      <pixi-sprite
        name="module81"
        :x="124"
        :y="58"
        :anchorX="0.5"
        :anchorY="0.5"
        :alpha="0"
        :scaleX="0.1"
        :scaleY="0.1"
        :src="srcArr.square9"
        :animationOptions="module8Group1"
      ></pixi-sprite>
      <pixi-sprite
        name="module82"
        :x="132"
        :y="62"
        :anchorX="0.5"
        :anchorY="0.5"
        :alpha="0"
        :scaleX="0.1"
        :scaleY="0.1"
        :src="srcArr.square10"
        :animationOptions="module8Group2"
      ></pixi-sprite>
      <pixi-sprite
        name="module83"
        :x="140"
        :y="66"
        :anchorX="0.5"
        :anchorY="0.5"
        :alpha="0"
        :scaleX="0.1"
        :scaleY="0.1"
        :src="srcArr.square11"
        :animationOptions="module8Group3"
      ></pixi-sprite>
      <pixi-sprite
        name="module84"
        :x="130"
        :y="55"
        :anchorX="0.5"
        :anchorY="0.5"
        :alpha="0"
        :scaleX="0.1"
        :scaleY="0.1"
        :src="srcArr.square12"
        :animationOptions="module8Group4"
      ></pixi-sprite>
      <pixi-sprite
        name="module85"
        :x="140"
        :y="60"
        :anchorX="0.5"
        :anchorY="0.5"
        :alpha="0"
        :scaleX="0.1"
        :scaleY="0.1"
        :src="srcArr.square13"
        :animationOptions="module8Group5"
      ></pixi-sprite>
    </pixi-container>
    <!-- 从左到右 第四排 -->
    <pixi-container
      name="module11"
      :x="450"
      :y="363"
      :width="227"
      :height="237"
    >
      <pixi-sprite
        name="module11bg"
        :x="0"
        :y="0"
        :src="srcArr.module11"
      ></pixi-sprite>
      <!-- module11上下动画 -->
      <pixi-sprite
        name="module111"
        :x="88"
        :y="29"
        :src="srcArr.module11_1"
        :animationOptions="module11Slide"
      ></pixi-sprite>
      <!-- module11上下动画 -->
      <!-- module11 滑块上下滚动-->
      <pixi-sprite
        name="module112"
        :x="103"
        :y="87"
        :src="srcArr.light3"
        :animationOptions="module11SlideGroup1"
      ></pixi-sprite>
      <pixi-sprite
        name="module113"
        :x="108"
        :y="125"
        :src="srcArr.light4"
        :animationOptions="module11SlideGroup2"
      ></pixi-sprite>
      <pixi-sprite
        name="module114"
        :x="113"
        :y="81"
        :src="srcArr.light5"
        :animationOptions="module11SlideGroup3"
      ></pixi-sprite>
      <pixi-sprite
        name="module115"
        :x="117"
        :y="118"
        :src="srcArr.light6"
        :animationOptions="module11SlideGroup4"
      ></pixi-sprite>
      <pixi-sprite
        name="module116"
        :x="122"
        :y="76"
        :src="srcArr.light7"
        :animationOptions="module11SlideGroup5"
      ></pixi-sprite>
      <!-- module11 滑块上下滚动 -->
    </pixi-container>
    <pixi-container
      name="module12"
      :x="671"
      :y="349"
      :width="228"
      :height="237"
    >
      <pixi-sprite
        name="module12bg"
        :x="0"
        :y="0"
        :src="srcArr.module12"
      ></pixi-sprite>
      <!-- module12上下动画 -->
      <pixi-sprite
        name="module121"
        :x="88"
        :y="29"
        :src="srcArr.module12_1"
        :animationOptions="module12Slide"
      ></pixi-sprite>
      <!-- module12上下动画 -->
      <!-- module12 滑块上下滚动-->
      <pixi-sprite
        name="module122"
        :x="103"
        :y="87"
        :src="srcArr.light5"
        :animationOptions="module11SlideGroup1"
      ></pixi-sprite>
      <pixi-sprite
        name="module123"
        :x="108"
        :y="125"
        :src="srcArr.light6"
        :animationOptions="module11SlideGroup2"
      ></pixi-sprite>
      <pixi-sprite
        name="module124"
        :x="113"
        :y="81"
        :src="srcArr.light7"
        :animationOptions="module11SlideGroup3"
      ></pixi-sprite>
      <pixi-sprite
        name="module125"
        :x="117"
        :y="118"
        :src="srcArr.light8"
        :animationOptions="module11SlideGroup4"
      ></pixi-sprite>
      <pixi-sprite
        name="module126"
        :x="122"
        :y="76"
        :src="srcArr.light1"
        :animationOptions="module11SlideGroup5"
      ></pixi-sprite>
      <!-- module11 滑块上下滚动 -->
    </pixi-container>
  </pixi-renderer>
</template>

<script>
import resource from './resource/index';
export default {
  name: 'loginDynamic',
  data() {
    return {
      srcArr: resource,
      // 小人玩电脑，图表动画
      module1Slide: [
        {
          animationType: 'slide', // 动画类型：移动动画
          finalXPosition: 68, // 终点x坐标，相对于父container
          finalYPosition: 47, // 终点y坐标，相对于父container
          yoyo: true, // 是否起点终点之间来回循环动画
          durationInFrames: 60 // 动画执行时长，60帧=1s
        }
      ],
      // 闪电图标上下移动
      module2Slide: [
        {
          animationType: 'slide',
          finalXPosition: 143,
          finalYPosition: 26,
          yoyo: true,
          durationInFrames: 60
        }
        // {
        //   animationType: 'flipYAndWalkPath',
        //   endSkewY: 6,
        //   flipYFrames: 40,
        //   waypoints: [
        //     [143, 46],
        //     [143, 26],
        //     [143, 46]
        //   ],
        //   slideFrames: 60
        // }
      ],
      // 滑块组上下移动
      module2SlideGroup1: [
        {
          animationType: 'slide',
          finalXPosition: 114,
          finalYPosition: 118,
          yoyo: true,
          durationInFrames: 120
        }
      ],
      module2SlideGroup2: [
        {
          animationType: 'slide',
          finalXPosition: 119,
          finalYPosition: 78,
          yoyo: true,
          durationInFrames: 126
        }
      ],
      module2SlideGroup3: [
        {
          animationType: 'slide',
          finalXPosition: 124,
          finalYPosition: 123,
          yoyo: true,
          durationInFrames: 129
        }
      ],
      module2SlideGroup4: [
        {
          animationType: 'slide',
          finalXPosition: 128,
          finalYPosition: 83,
          yoyo: true,
          durationInFrames: 132
        }
      ],
      module2SlideGroup5: [
        {
          animationType: 'slide',
          finalXPosition: 133,
          finalYPosition: 128,
          yoyo: true,
          durationInFrames: 111
        }
      ],
      // 三个半月形图表，上下多个路径点移动
      module3WalkPath1: [
        {
          animationType: 'walkPath', // 动画类型：路径点移动
          waypoints: [
            [54, 16],
            [54, 0],
            [54, 16],
            [54, 26],
            [54, 16]
          ], // 路径点设置[x, y]
          durationInFrames: 30, // 动画执行时长，60帧=1s
          delayBeforeLoop: 500, // 动画循环之前等待，ms为单位
          loop: true // 是否循环动画
        }
      ],
      module3WalkPath2: [
        {
          animationType: 'walkPath',
          waypoints: [
            [24, 31],
            [24, 11],
            [24, 31],
            [24, 41],
            [24, 31]
          ],
          durationInFrames: 20,
          delayBeforeLoop: 500,
          loop: true
        }
      ],
      module3WalkPath3: [
        {
          animationType: 'walkPath',
          waypoints: [
            [90, 39],
            [90, 19],
            [90, 39],
            [90, 49],
            [90, 39]
          ],
          durationInFrames: 40,
          delayBeforeLoop: 500,
          loop: true
        }
      ],
      // 小人工作台图表上下移动
      module7Slide: [
        {
          animationType: 'slide',
          finalXPosition: 57,
          finalYPosition: 39,
          yoyo: true,
          durationInFrames: 60
        }
      ],
      // 连续淡入淡出 + 缩放 动画组
      module6Group1: [
        // {
        //   animationType: 'walkPath',
        //   waypoints: [
        //     [120, 188],
        //     [139, 128]
        //   ],
        //   durationInFrames: 120,
        //   // delayBeforeLoop: 300,
        //   loop: true
        // },
        // {
        //   animationType: 'fadeIn',
        //   endAlpha: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 300,
        //   loop: true
        // },
        // {
        //   animationType: 'scale',
        //   endScaleX: 1,
        //   endScaleY: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 300,
        //   loop: true
        // }
        {
          animationType: 'pulse', // 动画类型：连续淡入淡出
          durationInFrames: 40
        },
        {
          animationType: 'breathe', // 动画类型: 来回缩放
          endScaleX: 1,
          endScaleY: 1,
          durationInFrames: 40
        }
      ],
      module6Group2: [
        // {
        //   animationType: 'walkPath',
        //   waypoints: [
        //     [120, 188],
        //     [129, 104]
        //   ],
        //   durationInFrames: 120,
        //   // delayBeforeLoop: 400,
        //   loop: true
        // },
        // {
        //   animationType: 'fadeIn',
        //   endAlpha: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 400,
        //   loop: true
        // },
        // {
        //   animationType: 'scale',
        //   endScaleX: 1,
        //   endScaleY: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 400,
        //   loop: true
        // }
        {
          animationType: 'pulse',
          durationInFrames: 50
        },
        {
          animationType: 'breathe',
          endScaleX: 1,
          endScaleY: 1,
          durationInFrames: 50
        }
      ],
      module6Group3: [
        // {
        //   animationType: 'walkPath',
        //   waypoints: [
        //     [120, 188],
        //     [98, 112]
        //   ],
        //   durationInFrames: 120,
        //   // delayBeforeLoop: 500,
        //   loop: true
        // },
        // {
        //   animationType: 'fadeIn',
        //   endAlpha: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 500,
        //   loop: true
        // },
        // {
        //   animationType: 'scale',
        //   endScaleX: 1,
        //   endScaleY: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 500,
        //   loop: true
        // }
        {
          animationType: 'pulse'
        },
        {
          animationType: 'breathe',
          endScaleX: 1,
          endScaleY: 1
        }
      ],
      module6Group4: [
        // {
        //   animationType: 'walkPath',
        //   waypoints: [
        //     [120, 188],
        //     [102, 152]
        //   ],
        //   durationInFrames: 120,
        //   // delayBeforeLoop: 300,
        //   loop: true
        // },
        // {
        //   animationType: 'fadeIn',
        //   endAlpha: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 300,
        //   loop: true
        // },
        // {
        //   animationType: 'scale',
        //   endScaleX: 1,
        //   endScaleY: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 300,
        //   loop: true
        // }
        {
          animationType: 'pulse',
          durationInFrames: 70
        },
        {
          animationType: 'breathe',
          endScaleX: 1,
          endScaleY: 1,
          durationInFrames: 70
        }
      ],
      module6Group5: [
        // {
        //   animationType: 'walkPath',
        //   waypoints: [
        //     [120, 188],
        //     [139, 165]
        //   ],
        //   durationInFrames: 120,
        //   // delayBeforeLoop: 400,
        //   loop: true
        // },
        // {
        //   animationType: 'fadeIn',
        //   endAlpha: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 400,
        //   loop: true
        // },
        // {
        //   animationType: 'scale',
        //   endScaleX: 1,
        //   endScaleY: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 400,
        //   loop: true
        // }
        {
          animationType: 'pulse',
          durationInFrames: 80
        },
        {
          animationType: 'breathe',
          endScaleX: 1,
          endScaleY: 1,
          durationInFrames: 80
        }
      ],
      module6Group6: [
        // {
        //   animationType: 'walkPath',
        //   waypoints: [
        //     [120, 188],
        //     [116, 114]
        //   ],
        //   durationInFrames: 120,
        //   // delayBeforeLoop: 500,
        //   loop: true
        // },
        // {
        //   animationType: 'fadeIn',
        //   endAlpha: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 500,
        //   loop: true
        // },
        // {
        //   animationType: 'scale',
        //   endScaleX: 1,
        //   endScaleY: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 500,
        //   loop: true
        // }
        {
          animationType: 'pulse',
          durationInFrames: 90
        },
        {
          animationType: 'breathe',
          endScaleX: 1,
          endScaleY: 1,
          durationInFrames: 90
        }
      ],
      module6Group7: [
        // {
        //   animationType: 'walkPath',
        //   waypoints: [
        //     [120, 188],
        //     [119, 157]
        //   ],
        //   durationInFrames: 120,
        //   // delayBeforeLoop: 300,
        //   loop: true
        // },
        // {
        //   animationType: 'fadeIn',
        //   endAlpha: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 300,
        //   loop: true
        // },
        // {
        //   animationType: 'scale',
        //   endScaleX: 1,
        //   endScaleY: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 300,
        //   loop: true
        // }
        {
          animationType: 'pulse',
          durationInFrames: 100
        },
        {
          animationType: 'breathe',
          endScaleX: 1,
          endScaleY: 1,
          durationInFrames: 100
        }
      ],
      module6Group8: [
        // {
        //   animationType: 'walkPath',
        //   waypoints: [
        //     [120, 188],
        //     [122, 170]
        //   ],
        //   durationInFrames: 120,
        //   // delayBeforeLoop: 400,
        //   loop: true
        // },
        // {
        //   animationType: 'fadeIn',
        //   endAlpha: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 400,
        //   loop: true
        // },
        // {
        //   animationType: 'scale',
        //   endScaleX: 1,
        //   endScaleY: 1,
        //   durationInFrames: 120,
        //   easingType: 'smoothstep',
        //   yoyo: false,
        //   // delayTimeBeforeRepeat: 400,
        //   loop: true
        // }
        {
          animationType: 'pulse',
          durationInFrames: 110
        },
        {
          animationType: 'breathe',
          endScaleX: 1,
          endScaleY: 1,
          durationInFrames: 110
        }
      ],
      // 圆饼图上下移动
      module6Slide: [
        {
          animationType: 'slide',
          finalXPosition: 126,
          finalYPosition: 130,
          yoyo: true,
          durationInFrames: 66
        }
      ],
      // 三个半月形图表，上下多个路径点移动
      module5WalkPath1: [
        {
          animationType: 'walkPath',
          waypoints: [
            [45, 14],
            [45, -6],
            [45, 14],
            [45, 24],
            [45, 14]
          ],
          durationInFrames: 30,
          delayBeforeLoop: 500,
          loop: true
        }
      ],
      module5WalkPath2: [
        {
          animationType: 'walkPath',
          waypoints: [
            [21, 26],
            [21, 6],
            [21, 26],
            [21, 32],
            [21, 26]
          ],
          durationInFrames: 20,
          delayBeforeLoop: 500,
          loop: true
        }
      ],
      module5WalkPath3: [
        {
          animationType: 'walkPath',
          waypoints: [
            [75, 32],
            [75, 12],
            [75, 32],
            [75, 42],
            [75, 32]
          ],
          durationInFrames: 40,
          delayBeforeLoop: 500,
          loop: true
        }
      ],
      // 云朵图标上下移动+y轴翻转
      module4FlipY: [
        // {
        //   animationType: 'flipY',
        //   endSkewY: 3,
        //   yoyo: true,
        //   durationInFrames: 30,
        //   delayBeforeRepeat: 300
        // }
        {
          animationType: 'flipYAndWalkPath', // 动画类型：翻转+路径点移动，先翻转，完成后再移动
          endSkewY: 3, // 最终翻转的偏移量
          flipYFrames: 40, // 翻转动画时长
          waypoints: [
            [93, 36],
            [93, 21],
            [93, 36]
          ], // 移动路径点
          slideFrames: 40 // 移动动画时长
        }
      ],
      // 闪电图标上下移动
      module10Slide: [
        {
          animationType: 'slide',
          finalXPosition: 94,
          finalYPosition: 26,
          yoyo: true,
          durationInFrames: 70
        }
        // {
        //   animationType: 'flipYAndWalkPath',
        //   endSkewY: 6,
        //   flipYFrames: 40,
        //   waypoints: [
        //     [94, 46],
        //     [94, 26],
        //     [94, 46]
        //   ],
        //   slideFrames: 60
        // }
      ],
      // 滑块组上下移动
      module10SlideGroup1: [
        {
          animationType: 'slide',
          finalXPosition: 65,
          finalYPosition: 118,
          yoyo: true,
          durationInFrames: 120
        }
      ],
      module10SlideGroup2: [
        {
          animationType: 'slide',
          finalXPosition: 70,
          finalYPosition: 80,
          yoyo: true,
          durationInFrames: 126
        }
      ],
      module10SlideGroup3: [
        {
          animationType: 'slide',
          finalXPosition: 75,
          finalYPosition: 122,
          yoyo: true,
          durationInFrames: 129
        }
      ],
      module10SlideGroup4: [
        {
          animationType: 'slide',
          finalXPosition: 80,
          finalYPosition: 84,
          yoyo: true,
          durationInFrames: 132
        }
      ],
      module10SlideGroup5: [
        {
          animationType: 'slide',
          finalXPosition: 84,
          finalYPosition: 128,
          yoyo: true,
          durationInFrames: 111
        }
      ],
      // 圆环，类似电池充电动画
      module9SlideGroup1: [
        {
          animationType: 'slide',
          finalXPosition: 102,
          finalYPosition: 48,
          loop: true,
          // delayTimeBeforeRepeat: 500,
          durationInFrames: 120
        },
        {
          animationType: 'fadeOut',
          easingType: 'linear',
          loop: true,
          durationInFrames: 120
        }
      ],
      module9SlideGroup2: [
        {
          animationType: 'slideDelayBefore',
          finalXPosition: 102,
          finalYPosition: 48,
          loop: true,
          delayBefore: 200,
          durationInFrames: 120
        },
        {
          animationType: 'fadeOut',
          easingType: 'linear',
          loop: true,
          delayBefore: 200,
          durationInFrames: 120
        }
      ],
      module9SlideGroup3: [
        {
          animationType: 'slideDelayBefore',
          finalXPosition: 102,
          finalYPosition: 48,
          loop: true,
          delayBefore: 400,
          durationInFrames: 120
        },
        {
          animationType: 'fadeOut',
          easingType: 'linear',
          loop: true,
          delayBefore: 400,
          durationInFrames: 120
        }
      ],
      module9SlideGroup4: [
        {
          animationType: 'slideDelayBefore',
          finalXPosition: 102,
          finalYPosition: 48,
          loop: true,
          delayBefore: 600,
          durationInFrames: 120
        },
        {
          animationType: 'fadeOut',
          easingType: 'linear',
          loop: true,
          delayBefore: 600,
          durationInFrames: 120
        }
      ],
      module9SlideGroup5: [
        {
          animationType: 'slideDelayBefore',
          finalXPosition: 102,
          finalYPosition: 48,
          loop: true,
          delayBefore: 800,
          durationInFrames: 120
        },
        {
          animationType: 'fadeOut',
          easingType: 'linear',
          loop: true,
          delayBefore: 800,
          durationInFrames: 120
        }
      ],
      module9SlideGroup6: [
        {
          animationType: 'slideDelayBefore',
          finalXPosition: 102,
          finalYPosition: 48,
          loop: true,
          delayBefore: 1000,
          durationInFrames: 120
        },
        {
          animationType: 'fadeOut',
          easingType: 'linear',
          loop: true,
          delayBefore: 1000,
          durationInFrames: 120
        }
      ],
      module9SlideGroup7: [
        {
          animationType: 'slideDelayBefore',
          finalXPosition: 102,
          finalYPosition: 48,
          loop: true,
          delayBefore: 1200,
          durationInFrames: 120
        },
        {
          animationType: 'fadeOut',
          easingType: 'linear',
          loop: true,
          delayBefore: 1200,
          durationInFrames: 120
        }
      ],
      module9SlideGroup8: [
        {
          animationType: 'slideDelayBefore',
          finalXPosition: 102,
          finalYPosition: 48,
          loop: true,
          delayBefore: 1400,
          durationInFrames: 120
        },
        {
          animationType: 'fadeOut',
          easingType: 'linear',
          loop: true,
          delayBefore: 1400,
          durationInFrames: 120
        }
      ],
      module9SlideGroup9: [
        {
          animationType: 'slideDelayBefore',
          finalXPosition: 102,
          finalYPosition: 48,
          loop: true,
          delayBefore: 1600,
          durationInFrames: 120
        },
        {
          animationType: 'fadeOut',
          easingType: 'linear',
          loop: true,
          delayBefore: 1600,
          durationInFrames: 120
        }
      ],
      // 移动 + 缩放 + 淡入 动画组
      module8Group1: [
        {
          animationType: 'walkPath',
          waypoints: [
            [124, 58],
            [140, 40]
          ],
          durationInFrames: 60,
          delayBeforeLoop: 300,
          loop: true
        },
        {
          animationType: 'fadeIn', // 淡入动画
          endAlpha: 1, // 最终的不透明度值
          durationInFrames: 60, // 动画时长，60帧=1s
          easingType: 'smoothstep', // 缓动类型：动画执行速率
          yoyo: false, // 是否起始值，结束值之间来回执行动画
          delayTimeBeforeRepeat: 300, // 动画结束后等待时长，再继续下一次动画 ms为单位
          loop: true // 是否循环的从起始值执行到结束值
        },
        {
          animationType: 'scale', // 缩放动画
          endScaleX: 1, // 最终x轴缩放值
          endScaleY: 1, // 最终y轴缩放值
          durationInFrames: 60,
          easingType: 'smoothstep',
          yoyo: false,
          delayTimeBeforeRepeat: 300,
          loop: true
        }
      ],
      module8Group2: [
        {
          animationType: 'walkPath',
          waypoints: [
            [132, 62],
            [116, 50]
          ],
          durationInFrames: 80,
          delayBeforeLoop: 300,
          loop: true
        },
        {
          animationType: 'fadeIn',
          endAlpha: 1,
          durationInFrames: 80,
          easingType: 'smoothstep',
          yoyo: false,
          delayTimeBeforeRepeat: 300,
          loop: true
        },
        {
          animationType: 'scale',
          endScaleX: 1,
          endScaleY: 1,
          durationInFrames: 80,
          easingType: 'smoothstep',
          yoyo: false,
          delayTimeBeforeRepeat: 300,
          loop: true
        }
      ],
      module8Group3: [
        {
          animationType: 'walkPath',
          waypoints: [
            [140, 66],
            [150, 60]
          ],
          durationInFrames: 60,
          delayBeforeLoop: 500,
          loop: true
        },
        {
          animationType: 'fadeIn',
          endAlpha: 1,
          durationInFrames: 60,
          easingType: 'smoothstep',
          yoyo: false,
          delayTimeBeforeRepeat: 500,
          loop: true
        },
        {
          animationType: 'scale',
          endScaleX: 1,
          endScaleY: 1,
          durationInFrames: 60,
          easingType: 'smoothstep',
          yoyo: false,
          delayTimeBeforeRepeat: 500,
          loop: true
        }
      ],
      module8Group4: [
        {
          animationType: 'walkPath',
          waypoints: [
            [130, 55],
            [160, 30]
          ],
          durationInFrames: 80,
          delayBeforeLoop: 400,
          loop: true
        },
        {
          animationType: 'fadeIn',
          endAlpha: 1,
          durationInFrames: 80,
          easingType: 'smoothstep',
          yoyo: false,
          delayTimeBeforeRepeat: 400,
          loop: true
        },
        {
          animationType: 'scale',
          endScaleX: 1,
          endScaleY: 1,
          durationInFrames: 80,
          easingType: 'smoothstep',
          yoyo: false,
          delayTimeBeforeRepeat: 400,
          loop: true
        }
      ],
      module8Group5: [
        {
          animationType: 'walkPath',
          waypoints: [
            [140, 60],
            [120, 35]
          ],
          durationInFrames: 80,
          delayBeforeLoop: 500,
          loop: true
        },
        {
          animationType: 'fadeIn',
          endAlpha: 1,
          durationInFrames: 80,
          easingType: 'smoothstep',
          yoyo: false,
          delayTimeBeforeRepeat: 500,
          loop: true
        },
        {
          animationType: 'scale',
          endScaleX: 1,
          endScaleY: 1,
          durationInFrames: 80,
          easingType: 'smoothstep',
          yoyo: false,
          delayTimeBeforeRepeat: 500,
          loop: true
        }
      ],
      // 闪电图标上下移动
      module11Slide: [
        {
          animationType: 'slide',
          finalXPosition: 88,
          finalYPosition: 9,
          yoyo: true,
          durationInFrames: 80
        }
      ],
      // 闪电图标上下移动
      module12Slide: [
        {
          animationType: 'slide',
          finalXPosition: 88,
          finalYPosition: 9,
          yoyo: true,
          durationInFrames: 90
        }
      ],
      // module11 module12 共用一套 滑块组上下移动 动画
      module11SlideGroup1: [
        {
          animationType: 'slide',
          finalXPosition: 103,
          finalYPosition: 126,
          yoyo: true,
          durationInFrames: 120
        }
      ],
      module11SlideGroup2: [
        {
          animationType: 'slide',
          finalXPosition: 108,
          finalYPosition: 83,
          yoyo: true,
          durationInFrames: 126
        }
      ],
      module11SlideGroup3: [
        {
          animationType: 'slide',
          finalXPosition: 113,
          finalYPosition: 122,
          yoyo: true,
          durationInFrames: 129
        }
      ],
      module11SlideGroup4: [
        {
          animationType: 'slide',
          finalXPosition: 117,
          finalYPosition: 78,
          yoyo: true,
          durationInFrames: 132
        }
      ],
      module11SlideGroup5: [
        {
          animationType: 'slide',
          finalXPosition: 122,
          finalYPosition: 116,
          yoyo: true,
          durationInFrames: 111
        }
      ]
    };
  },
  mounted() {},
  methods: {}
};
</script>

<style lang="scss" scoped></style>
