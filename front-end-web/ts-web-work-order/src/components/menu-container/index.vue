<template>
  <div class="ct-container">
    <div
      class="ct-header"
      :style="{
        height: `${headerHeight}px`,
        width: `calc(100% - ${paddingleftRight * 2}px)`,
        marginLeft: `${paddingleftRight}px`
      }"
    >
      <div
        :style="{
          height: `${headerHeight - 20}px`,
          width: `100%`
        }"
      >
        <slot name="header"></slot>
      </div>

      <div
        :style="{
          height: `20px`,
          width: `calc(100% - ${paddingleftRight * 2}px)`,
          marginLeft: `${paddingleftRight}px`
        }"
      ></div>
    </div>
    <div
      class="ct-body"
      :style="{
        height: `calc(100% - ${bodyHeight}px)`,
        width: `calc(100% - ${paddingbody * 2}px)`,
        marginLeft: `${paddingbody}px`
      }"
    >
      <div class="ct-body-container">
        <slot name="body"></slot>
      </div>
    </div>
    <div
      class="ct-footer flex-start"
      :style="{
        height: `${footerHeight}px`,
        width: `calc(100% - ${paddingleftRight * 2}px)`,
        marginLeft: `${paddingleftRight}px`
      }"
    >
      <slot name="footer"></slot>
    </div>
    <div><slot name="dialog"></slot></div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    headerHeight: {
      type: [Number, String],
      default: 78
    },
    footerHeight: {
      type: [Number, String],
      default: 50
    },
    paddingleftRight: {
      type: [Number, String],
      default: 20
    },
    paddingbody: {
      type: [Number, String],
      default: 20
    }
  },

  computed: {
    bodyHeight() {
      let body = parseInt(this.headerHeight) + parseInt(this.footerHeight);
      return body;
    }
  }
};
</script>

<style lang="scss" scoped>
.ct-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 3;
  flex-direction: column;
  background: $--bankuai-bg;
  position: relative;
}
.ct-header {
  width: 100%;
  overflow: hidden;
}
.ct-body {
  display: flex;
  flex: 1;
  width: 100%;
  position: relative;
  overflow: hidden;
}
.ct-body-container {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  overflow: hidden;
}
.ct-footer {
  width: 100%;
}
</style>
