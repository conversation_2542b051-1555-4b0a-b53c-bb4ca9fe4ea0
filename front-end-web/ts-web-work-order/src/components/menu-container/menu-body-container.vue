<template>
  <div
    :style="{
      width: `calc(100% - ${paddingleftRight * 2}px)`,
      marginLeft: `${paddingleftRight}px`
    }"
  >
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'menu-body-container',
  props: {
    paddingleftRight: {
      type: [Number, String],
      default: 20
    }
  }
};
</script>

<style scoped lang="scss">
.ct-header {
  width: 100%;
  overflow: hidden;
}
</style>
