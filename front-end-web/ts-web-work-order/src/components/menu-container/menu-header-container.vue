<template>
  <div
    class="ct-header"
    :style="{
      height: `${headerHeight}px`,
      width: `calc(100% - ${paddingleftRight * 2}px)`,
      marginLeft: `${paddingleftRight}px`
    }"
  >
    <div
      class="menu-header"
      :style="{
        height: `${headerHeight - 20}px`,
        width: `100%`
      }"
    >
      <slot></slot>
    </div>

    <div
      :style="{
        height: `20px`,
        width: `calc(100% - ${paddingleftRight * 2}px)`,
        marginLeft: `${paddingleftRight}px`
      }"
    ></div>
  </div>
</template>

<script>
export default {
  name: 'menu-header-container',
  props: {
    headerHeight: {
      type: [Number, String],
      default: 78
    },
    paddingleftRight: {
      type: [Number, String],
      default: 20
    }
  }
};
</script>

<style scoped lang="scss">
.ct-header {
  width: 100%;
  overflow: hidden;
}
.menu-header {
  border-bottom: 1px solid $--border-table;
  color: $basic-white;
}
</style>
