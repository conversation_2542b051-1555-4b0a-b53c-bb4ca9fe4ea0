/**
 * 设置单一的补间动画
 * @param {*} animationOptions 设置动画所需配置
 * animationOptions中包含以下参数：
 * animationType:
 *  1. slide: 直线滑动补间动画；以下是所需参数
 *      anySprite		需要移动的精灵, 必选参数，下面注释中会省略
 *      finalXPosition		滑动结束时 x 坐标
 *      finalYPosition		滑动结束时 y 坐标
 *      durationInFrames	60	补间需要的帧数，也就是动画应该持续多长时间；必选参数，下面注释中会省略
 *      easingType	"smoothstep"	缓动类型 （
 *         Linear：精灵从开始到停止保持匀速运动。
 *         Smoothstep，smoothstepSquared，smoothstepCubed。加速精灵并以非常自然的方式减慢速度。
 *         acceleration， accelerationCubed。逐渐加速精灵并突然停止。如果要更加平滑的加速效果，请使用 sine，sineSquared 或 sineCubed。
 *         Deceleration：deceleration，decelerationCubed。突然加速精灵并逐渐减慢它。要获得更加平滑的减速效果，请使用inverseSine，inverseSineSquared或inverseSineCubed。
 *         Bounce：bounce 10 -10 ，这将使精灵到达起点和终点时略微反弹，更改乘数10和 -10，可以改变效果。
 *       ）必选参数，下面注释中会省略
 *      yoyo	false	用于确定精灵是否应在补间的起点和终点之间来回移动。必选参数，下面注释中会省略
 *      delayTimeBeforeRepeat	0	一个以毫秒为单位的数字，用于确定精灵 yoyo 之前的延迟时间。
 *  2. followCurve：沿贝塞尔曲线移动；以下是所需参数
 *       curve		贝塞尔曲线数组, 最少四个坐标点数据
 *       delayTimeBeforeRepeat	0	一个以毫秒为单位的数字，用于确定精灵 yoyo 之前的延迟时间。
 *    slide 和 followCurve 方法适用于简单的来回动画效果，但你也可以结合它们以使精灵遵循更复杂的路径。
 *  3. walkPath： 沿指定点的路径移动；以下是所需参数
 *       waypoints		坐标点的二维数组
 *       loop	false	用于确定精灵在到达结尾时是否从头开始
 *       delayBetweenSections	0	一个以毫秒为单位的数字，用于确定精灵在移动到路径的下一部分之前应该等待的时间。
 *  4. walkCurve： 遵循一系列连接的贝塞尔曲线移动；以下是参数
 *       curvedWaypoints		贝塞尔曲线的坐标点的数组
 *       delayBeforeContinue	0	一个以毫秒为单位的数字，用于确定精灵yoyo之前的延迟时间。
 *  5. 其他内置补间效果：
 *      fadeOut fadeIn: fadeOut 方法使精灵逐渐变得透明，fadeIn 方法使精灵从透明逐渐显现。这两个方法需要的参数是一样的。
 *         anySprite		需要产生效果的精灵
 *         durationInFrames	60	持续的帧数
 *      pulse: 使用 pulse 方法可以使精灵以稳定的速率连续淡入淡出。
 *         anySprite		需要产生效果的精灵
 *         durationInFrames	60	淡入淡出应该持续的帧数，也就是持续时间
 *         minAlpha	0	精灵可以减少到的最小的透明度值
 *      scale: 缩放
 *         anySprite		需要产生效果的精灵
 *         endScaleX	0.5	x 轴缩放的比例
 *         endScaleY	0.5	y 轴缩放的比例
 *         durationInFrames	60	持续时间，以帧为单位
 *      breathe： 如果你希望缩放效果来回 yoyo，请使用 breathe 方法。它是一种缩放效果，使精灵看起来好像在呼吸。
 *         anySprite		需要产生效果的精灵
 *         endScaleX	0.5	x 轴缩放的比例
 *         endScaleY	0.5	y 轴缩放的比例
 *         durationInFrames	60	持续时间，以帧为单位
 *         yoyo	true	是否轮流反向播放
 *         delayBeforeRepeat	0	一个以毫秒为单位的数字，用于确定精灵 yoyo 之前的延迟时间。
 *      strobe: 使用 strobe 方法通过快速改变精灵比例，使精灵看起来像闪光灯一样闪烁。参数：只需要传入一个精灵作为参数即可。
 *      wobble: 使用 wobble 方法可以使精灵像果冻一样摆动。参数：只需要传入一个精灵作为参数即可。
 *
 *    以上动画参数中产生效果的精灵参数一律不传，因为再调用这个方法时，已传递精灵实例过来
 *
 * 具体配置动画参数教程可参考学习：https://segmentfault.com/a/1190000018190147
 * @param {*} charmInstance charm实例
 * @param {*} spriteInstance 设置动画的精灵实例
 */
export function setSingleAnimation(
  animationOptions,
  charmInstance,
  spriteInstance
) {
  let animationPixie = null;
  if (!animationOptions.animationType) {
    console.log('动画类型未设置');
    return;
  }
  let durationInFrames = 60;
  let endScaleX = 0.5;
  let endScaleY = 0.5;
  let yoyo = false;
  switch (animationOptions.animationType) {
    case 'slide':
      if (
        animationOptions.finalXPosition == undefined ||
        animationOptions.finalYPosition == undefined
      ) {
        console.log(animationOptions);
        console.log('滑动结束坐标未设置');
      } else {
        const x = animationOptions.finalXPosition;
        const y = animationOptions.finalYPosition;
        let durationInFrames = 60;
        let easingType = 'smoothstep';
        let yoyo = false;
        let loop = false;
        let delayTimeBeforeRepeat = 0;
        if (animationOptions.durationInFrames)
          durationInFrames = animationOptions.durationInFrames;
        if (animationOptions.easingType)
          easingType = animationOptions.easingType;
        if (animationOptions.yoyo) yoyo = animationOptions.yoyo;
        if (animationOptions.loop) loop = animationOptions.loop;
        if (animationOptions.delayTimeBeforeRepeat)
          delayTimeBeforeRepeat = animationOptions.delayTimeBeforeRepeat;

        animationPixie = charmInstance.slide(
          spriteInstance,
          x,
          y,
          durationInFrames,
          easingType,
          yoyo,
          loop,
          delayTimeBeforeRepeat
        );
      }
      break;
    case 'slideDelayBefore':
      if (
        animationOptions.finalXPosition == undefined ||
        animationOptions.finalYPosition == undefined
      ) {
        console.log('滑动结束坐标未设置');
      } else {
        const x = animationOptions.finalXPosition;
        const y = animationOptions.finalYPosition;
        let durationInFrames = 60;
        let easingType = 'smoothstep';
        let yoyo = false;
        let loop = false;
        let delayTimeBeforeRepeat = 0;
        let delayBefore = 0;
        if (animationOptions.durationInFrames)
          durationInFrames = animationOptions.durationInFrames;
        if (animationOptions.easingType)
          easingType = animationOptions.easingType;
        if (animationOptions.yoyo) yoyo = animationOptions.yoyo;
        if (animationOptions.loop) loop = animationOptions.loop;
        if (animationOptions.delayTimeBeforeRepeat)
          delayTimeBeforeRepeat = animationOptions.delayTimeBeforeRepeat;
        if (animationOptions.delayBefore)
          delayBefore = animationOptions.delayBefore;

        animationPixie = charmInstance.slideDelayBefore(
          spriteInstance,
          x,
          y,
          durationInFrames,
          easingType,
          yoyo,
          loop,
          delayTimeBeforeRepeat,
          delayBefore
        );
      }
      break;
    case 'followCurve':
      if (
        animationOptions.curve == undefined ||
        animationOptions.curve.length === 0
      ) {
        console.log('贝塞尔曲线数组未设置');
      } else {
        const curve = animationOptions.curve;
        let durationInFrames = 60;
        let easingType = 'smoothstep';
        let yoyo = false;
        let delayTimeBeforeRepeat = 0;
        if (animationOptions.durationInFrames)
          durationInFrames = animationOptions.durationInFrames;
        if (animationOptions.easingType)
          easingType = animationOptions.easingType;
        if (animationOptions.yoyo) yoyo = animationOptions.yoyo;
        if (animationOptions.delayTimeBeforeRepeat)
          delayTimeBeforeRepeat = animationOptions.delayTimeBeforeRepeat;

        animationPixie = charmInstance.followCurve(
          spriteInstance,
          curve,
          durationInFrames,
          easingType,
          yoyo,
          delayTimeBeforeRepeat
        );
      }
      break;
    case 'walkPath':
      if (
        animationOptions.waypoints == undefined ||
        animationOptions.waypoints.length === 0
      ) {
        console.log('坐标点数组未设置');
      } else {
        const waypoints = animationOptions.waypoints;
        if (animationOptions.delayBefore) {
          setTimeout(() => {
            let durationInFrames = 60;
            let easingType = 'smoothstep';
            let loop = false;
            let yoyo = false;
            let delayBetweenSections = 0;
            let delayBeforeLoop = 0;
            if (animationOptions.durationInFrames)
              durationInFrames = animationOptions.durationInFrames;
            if (animationOptions.easingType)
              easingType = animationOptions.easingType;
            if (animationOptions.yoyo) yoyo = animationOptions.yoyo;
            if (animationOptions.loop) loop = animationOptions.loop;
            if (animationOptions.delayBetweenSections)
              delayBetweenSections = animationOptions.delayBetweenSections;
            if (animationOptions.delayBeforeLoop)
              delayBeforeLoop = animationOptions.delayBeforeLoop;

            animationPixie = charmInstance.walkPath(
              spriteInstance,
              waypoints,
              durationInFrames,
              easingType,
              loop,
              yoyo,
              delayBetweenSections,
              delayBeforeLoop
            );
          }, animationOptions.delayBefore);
        } else {
          let durationInFrames = 60;
          let easingType = 'smoothstep';
          let loop = false;
          let yoyo = false;
          let delayBetweenSections = 0;
          let delayBeforeLoop = 0;
          if (animationOptions.durationInFrames)
            durationInFrames = animationOptions.durationInFrames;
          if (animationOptions.easingType)
            easingType = animationOptions.easingType;
          if (animationOptions.yoyo) yoyo = animationOptions.yoyo;
          if (animationOptions.loop) loop = animationOptions.loop;
          if (animationOptions.delayBetweenSections)
            delayBetweenSections = animationOptions.delayBetweenSections;
          if (animationOptions.delayBeforeLoop)
            delayBeforeLoop = animationOptions.delayBeforeLoop;

          animationPixie = charmInstance.walkPath(
            spriteInstance,
            waypoints,
            durationInFrames,
            easingType,
            loop,
            yoyo,
            delayBetweenSections,
            delayBeforeLoop
          );
        }
      }
      break;
    case 'walkCurve':
      if (
        animationOptions.curvedWaypoints == undefined ||
        animationOptions.curvedWaypoints.length === 0
      ) {
        console.log('多条贝塞尔曲线数组未设置');
      } else {
        const curvedWaypoints = animationOptions.curvedWaypoints;
        let durationInFrames = 60;
        let easingType = 'smoothstep';
        let yoyo = false;
        let delayBeforeContinue = 0;
        if (animationOptions.durationInFrames)
          durationInFrames = animationOptions.durationInFrames;
        if (animationOptions.easingType)
          easingType = animationOptions.easingType;
        if (animationOptions.yoyo) yoyo = animationOptions.yoyo;
        if (animationOptions.delayBeforeContinue)
          delayBeforeContinue = animationOptions.delayBeforeContinue;

        animationPixie = charmInstance.walkCurve(
          spriteInstance,
          curvedWaypoints,
          durationInFrames,
          easingType,
          yoyo,
          delayBeforeContinue
        );
      }
      break;
    case 'fadeOut':
      // let durationInFrames = 60;
      let easingType = 'sine';
      let loop = false;
      let delayTimeBeforeRepeat = 0;
      let delayBefore = 0;
      if (animationOptions.durationInFrames)
        durationInFrames = animationOptions.durationInFrames;
      if (animationOptions.easingType) easingType = animationOptions.easingType;
      if (animationOptions.yoyo) yoyo = animationOptions.yoyo;
      if (animationOptions.loop) loop = animationOptions.loop;
      if (animationOptions.delayTimeBeforeRepeat)
        delayTimeBeforeRepeat = animationOptions.delayTimeBeforeRepeat;
      if (animationOptions.delayBefore)
        delayBefore = animationOptions.delayBefore;
      animationPixie = charmInstance.fadeOut(
        spriteInstance,
        animationOptions.endAlpha ? animationOptions.endAlpha : 0,
        durationInFrames,
        easingType,
        yoyo,
        loop,
        delayTimeBeforeRepeat,
        delayBefore
      );
      break;
    case 'fadeIn':
      if (animationOptions.endAlpha == undefined) {
        console.log('未设置最终透明度');
      } else {
        let durationInFrames = 60;
        let easingType = 'sine';
        let yoyo = false;
        let loop = false;
        let delayTimeBeforeRepeat = 0;
        let delayBefore = 0;
        if (animationOptions.durationInFrames)
          durationInFrames = animationOptions.durationInFrames;
        if (animationOptions.easingType)
          easingType = animationOptions.easingType;
        if (animationOptions.yoyo) yoyo = animationOptions.yoyo;
        if (animationOptions.loop) loop = animationOptions.loop;
        if (animationOptions.delayTimeBeforeRepeat)
          delayTimeBeforeRepeat = animationOptions.delayTimeBeforeRepeat;
        if (animationOptions.delayBefore)
          delayBefore = animationOptions.delayBefore;
        animationPixie = charmInstance.fadeIn(
          spriteInstance,
          animationOptions.endAlpha,
          durationInFrames,
          easingType,
          yoyo,
          loop,
          delayTimeBeforeRepeat,
          delayBefore
        );
      }
      break;
    case 'pulse':
      let minAlpha = 0;
      if (animationOptions.durationInFrames)
        durationInFrames = animationOptions.durationInFrames;
      if (animationOptions.minAlpha) minAlpha = animationOptions.minAlpha;
      animationPixie = charmInstance.pulse(
        spriteInstance,
        durationInFrames,
        minAlpha
      );
      break;
    case 'scale':
      if (
        animationOptions.endScaleX == undefined ||
        animationOptions.endScaleY == undefined
      ) {
        console.log('未设置缩放比例');
      } else {
        let endScaleX = animationOptions.endScaleX;
        let endScaleY = animationOptions.endScaleY;
        let durationInFrames = 60;
        let easingType = 'sine';
        let yoyo = false;
        let loop = false;
        let delayTimeBeforeRepeat = 0;
        let delayBefore = 0;
        if (animationOptions.durationInFrames)
          durationInFrames = animationOptions.durationInFrames;
        if (animationOptions.easingType)
          easingType = animationOptions.easingType;
        if (animationOptions.yoyo) yoyo = animationOptions.yoyo;
        if (animationOptions.loop) loop = animationOptions.loop;
        if (animationOptions.delayTimeBeforeRepeat)
          delayTimeBeforeRepeat = animationOptions.delayTimeBeforeRepeat;
        if (animationOptions.delayBefore)
          delayBefore = animationOptions.delayBefore;
        animationPixie = charmInstance.scale(
          spriteInstance,
          endScaleX,
          endScaleY,
          durationInFrames,
          easingType,
          yoyo,
          loop,
          delayTimeBeforeRepeat,
          delayBefore
        );
      }
      break;
    case 'breathe':
      yoyo = true;
      let delayBeforeRepeat = 0;
      if (animationOptions.durationInFrames)
        durationInFrames = animationOptions.durationInFrames;
      if (animationOptions.endScaleX) endScaleX = animationOptions.endScaleX;
      if (animationOptions.endScaleY) endScaleY = animationOptions.endScaleY;
      if (animationOptions.yoyo) yoyo = animationOptions.yoyo;
      if (animationOptions.delayBeforeRepeat)
        delayBeforeRepeat = animationOptions.delayBeforeRepeat;
      animationPixie = charmInstance.breathe(
        spriteInstance,
        endScaleX,
        endScaleY,
        durationInFrames,
        yoyo,
        delayBeforeRepeat
      );
      break;
    case 'strobe':
      animationPixie = charmInstance.strobe(spriteInstance);
      break;
    case 'wobble':
      animationPixie = charmInstance.wobble(spriteInstance);
      break;
    case 'rotation':
      if (animationOptions.endRotation == undefined) {
        console.log('未设置旋转角度');
      } else {
        const endRotation = animationOptions.endRotation;
        let durationInFrames = 60;
        let easingType = 'smoothstep';
        let yoyo = false;
        let loop = false;
        let delayBeforeRepeat = 0;
        let delayBefore = 0;
        if (animationOptions.durationInFrames)
          durationInFrames = animationOptions.durationInFrames;
        if (animationOptions.easingType)
          easingType = animationOptions.easingType;
        if (animationOptions.yoyo) yoyo = animationOptions.yoyo;
        if (animationOptions.loop) loop = animationOptions.loop;
        if (animationOptions.delayBeforeRepeat)
          delayBeforeRepeat = animationOptions.delayBeforeRepeat;
        if (animationOptions.delayBefore)
          delayBefore = animationOptions.delayBefore;

        animationPixie = charmInstance.rotation(
          spriteInstance,
          endRotation,
          durationInFrames,
          easingType,
          yoyo,
          loop,
          delayBeforeRepeat,
          delayBefore
        );
      }
      break;
    case 'flipY':
      if (animationOptions.endSkewY == undefined) {
        console.log('未设置翻转角度');
      } else {
        const endSkewY = animationOptions.endSkewY;
        let durationInFrames = 60;
        let easingType = 'smoothstep';
        let yoyo = false;
        let loop = false;
        let delayBeforeRepeat = 0;
        let delayBefore = 0;
        if (animationOptions.durationInFrames)
          durationInFrames = animationOptions.durationInFrames;
        if (animationOptions.easingType)
          easingType = animationOptions.easingType;
        if (animationOptions.yoyo) yoyo = animationOptions.yoyo;
        if (animationOptions.loop) loop = animationOptions.loop;
        if (animationOptions.delayBeforeRepeat)
          delayBeforeRepeat = animationOptions.delayBeforeRepeat;
        if (animationOptions.delayBefore)
          delayBefore = animationOptions.delayBefore;

        animationPixie = charmInstance.flipY(
          spriteInstance,
          endSkewY,
          durationInFrames,
          easingType,
          yoyo,
          loop,
          delayBeforeRepeat,
          delayBefore
        );
      }
      break;
    case 'flipYAndWalkPath':
      if (animationOptions.endSkewY == undefined) {
        console.log('未设置翻转角度');
      } else if (
        animationOptions.waypoints == undefined ||
        animationOptions.waypoints.length === 0
      ) {
        console.log('坐标点数组未设置');
      } else {
        const endSkewY = animationOptions.endSkewY;
        const waypoints = animationOptions.waypoints;
        let flipYFrames = 60;
        let slideFrames = 60;
        let flipYType = 'smoothstep';
        let slideType = 'smoothstep';
        if (animationOptions.flipYFrames)
          flipYFrames = animationOptions.flipYFrames;
        if (animationOptions.flipYType) flipYType = animationOptions.flipYType;
        if (animationOptions.slideFrames)
          slideFrames = animationOptions.slideFrames;
        if (animationOptions.slideType) slideType = animationOptions.slideType;

        animationPixie = charmInstance.flipYAndWalkPath(
          spriteInstance,
          endSkewY,
          flipYFrames,
          flipYType,
          waypoints,
          slideFrames,
          slideType
        );
      }
      break;
    case 'flipX':
      if (animationOptions.endSkewX == undefined) {
        console.log('未设置翻转角度');
      } else {
        const endSkewX = animationOptions.endSkewX;
        let durationInFrames = 60;
        let easingType = 'smoothstep';
        let yoyo = false;
        let loop = false;
        let delayBeforeRepeat = 0;
        let delayBefore = 0;
        if (animationOptions.durationInFrames)
          durationInFrames = animationOptions.durationInFrames;
        if (animationOptions.easingType)
          easingType = animationOptions.easingType;
        if (animationOptions.yoyo) yoyo = animationOptions.yoyo;
        if (animationOptions.loop) loop = animationOptions.loop;
        if (animationOptions.delayBeforeRepeat)
          delayBeforeRepeat = animationOptions.delayBeforeRepeat;
        if (animationOptions.delayBefore)
          delayBefore = animationOptions.delayBefore;

        animationPixie = charmInstance.flipX(
          spriteInstance,
          endSkewX,
          durationInFrames,
          easingType,
          yoyo,
          loop,
          delayBeforeRepeat,
          delayBefore
        );
      }
      break;
    default:
      charmInstance.testWaitQueue();
      break;
  }
  return animationPixie;
}
/**
 * 设置组合动画
 * @param animationOptions 动画配置项数组
 * @param charmInstance 动画库实例对象
 * @param spriteInstance 精灵实例对象
 */
export function setCombinedAnimation(
  animationOptions,
  charmInstance,
  spriteInstance
) {
  if (Array.isArray(animationOptions) && animationOptions.length > 0) {
    animationOptions.map(item => {
      setSingleAnimation(item, charmInstance, spriteInstance);
    });
  } else {
    console.log('动画配置有误');
  }
}
