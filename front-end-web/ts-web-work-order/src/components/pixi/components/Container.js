import DisplayObject from './DisplayObject.js';
import { Container } from 'pixi.js';
/**
 * 容器是容纳子项的通用显示对象。它还增加了对高级渲染功能（如遮罩和过滤）的内置支持。
 * 它是所有显示对象的基类，它们充当其他对象（包括Graphics和Sprite）的容器。
 */
export default {
  mixins: [DisplayObject],
  props: {
    height: Number,
    width: Number,
    // 确定是否可以单击/触摸displayObject的子级将其设置为false允许PixiJS绕过递归hitTest函数
    interactiveChildren: Boolean
  },
  computed: {
    instance: () => new Container()
  },
  watch: {
    instance: {
      handler(instance) {
        if (this.width) instance.width = this.width;
        if (this.height) instance.height = this.height;
        if (this.interactiveChildren)
          instance.interactiveChildren = this.interactiveChildren;
      },
      immediate: true
    },
    width: function(width) {
      this.instance.width = width;
    },
    height: function(height) {
      this.instance.height = height;
    },
    interactiveChildren: function(interactiveChildren) {
      this.instance.interactiveChildren = interactiveChildren;
    }
  },
  // 拿到标签内的内容，以div的方式放到dom
  render(h) {
    return this.$slots.default ? h('div', this.$slots.default) : undefined;
  }
};
