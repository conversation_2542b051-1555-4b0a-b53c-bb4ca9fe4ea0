import Container from './Container.js';
import { Sprite, Graphics } from 'pixi.js';
import {
  setSingleAnimation,
  setCombinedAnimation
} from '../animation/animation.js';
/**
 *  Sprite对象是渲染到屏幕上的所有纹理对象的基础
 *  可以直接从图像创建精灵，如下所示：
 *  let sprite = PIXI.Sprite.from('assets/image.png');
 *  创建精灵的更有效方法是使用PIXI.Spritesheet，因为在渲染到屏幕时效率低下时交换基本纹理。
 *  PIXI.Loader.shared.add("assets/spritesheet.json").load(setup);
 *  function setup() {
 *  let sheet = PIXI.Loader.shared.resources["assets/spritesheet.json"].spritesheet;
 *  let sprite = new PIXI.Sprite(sheet.textures["image.png"]);
 *  ...
 *  }
 */
export default {
  mixins: [Container],
  props: {
    // 锚点设置精灵的原点。默认值为(0,0)这意味着子画面的原点位于左上方, 将锚点设置为(0.5,0.5)表示精灵的原点居中。
    anchorX: Number,
    anchorY: Number,
    // 应用于精灵的混合模式。应用值PIXI.BLEND_MODES.NORMAL重置混合模式。
    blendMode: Number,
    // buttonMode
    // pluginName
    // shader
    // texture
    // 应用于小精灵的色调。这是一个十六进制值。值为0xFFFFFF将消除任何色调效果。
    tint: Number,
    src: String, // 图片路径
    animationOptions: Array, // 动画配置项
    isHover: Boolean, // 是否有hover效果
    hoverType: {
      type: String,
      default: 'float' // 默认悬浮效果
    },
    isLine: Boolean, // 是否有连接线
    lineStartPosition: Object, // 连接线起始点{ x: 0, y: 0 }
    isDrag: Boolean, // 是否可拖拽
    dragBorder: Object // 拖拽边界，画布在页面的位置
  },
  computed: {
    instance() {
      return this.src ? Sprite.fromImage(this.src) : new Sprite();
    }
  },
  data() {
    return {
      animationPixieOver: null,
      animationPixieOut: null,
      recordXPosition: this.x, // 记录x的值，用于拖拽
      recordYPosition: this.y, // 记录y的值，用于拖拽
      graphics: new Graphics()
    };
  },
  watch: {
    instance: {
      handler(instance) {
        if (this.tint) instance.text = this.tint;
        if (this.blendMode) instance.blendMode = this.blendMode;
        if (this.anchorX || this.anchorY)
          instance.anchor.set(this.anchorX || 0, this.anchorY || 0);
        // 是否有单一动画配置项
        if (this.animationOptions) {
          setCombinedAnimation(this.animationOptions, this.charm, instance);
        }
        // 是否有悬浮效果配置项
        if (this.isHover) {
          instance.interactive = true;
          instance.on('mouseover', this.onMouseover);
          instance.on('mouseout', this.onMouseout);
        }
        // 是否需要连线
        if (this.isLine) {
          this.drawLine();
        }
        // 是否支持拖拽
        if (this.isDrag) {
          instance.interactive = true;
          instance
            .on('pointerdown', this.onDragStart)
            .on('pointermove', this.onDragMove)
            .on('pointerup', this.onDragEnd)
            .on('pointerupoutside', this.onDragEnd);
        }
      },
      immediate: true
    },
    tint: function(tint) {
      this.instance.tint = tint;
    },
    blendMode: function(blendMode) {
      this.instance.blendMode = blendMode;
    },
    anchorX: function(anchorX) {
      this.instance.anchor.x = anchorX;
    },
    anchorY: function(anchorY) {
      this.instance.anchor.y = anchorY;
    },
    recordXPosition: function(x) {
      this.instance.x = x;
    },
    recordYPosition: function(y) {
      this.instance.y = y;
    }
  },
  methods: {
    // 画线方法，设置的点到实例的x,y
    drawLine() {
      this.graphics.lineStyle(2, 0xff3300, 1);
      this.graphics.moveTo(this.lineStartPosition.x, this.lineStartPosition.y);
      this.graphics.lineTo(this.instance.x, this.instance.y);
      this.graphics.x = 0;
      this.graphics.y = 0;
      this.app.stage.addChild(this.graphics);
    },
    // 鼠标移入监听
    onMouseover(event) {
      if (this.animationPixieOut) {
        this.animationPixieOut.pause();
        this.animationPixieOut = null;
      }
      if (this.hoverType === 'float') {
        const options = {
          animationType: 'slide',
          finalXPosition: this.x,
          finalYPosition: this.y - 10,
          durationInFrames: 30
        };
        this.animationPixieOver = setSingleAnimation(
          options,
          this.charm,
          this.instance
        );
      } else {
        // 其他效果，例如更换背景图片
      }
    },
    // 鼠标移出监听
    onMouseout(event) {
      if (this.animationPixieOver) {
        this.animationPixieOver.pause();
        this.animationPixieOver = null;
      }
      if (this.hoverType === 'float') {
        const options = {
          animationType: 'slide',
          finalXPosition: this.x,
          finalYPosition: this.y,
          durationInFrames: 30
        };
        this.animationPixieOut = setSingleAnimation(
          options,
          this.charm,
          this.instance
        );
      } else {
        // 其他效果，例如更换背景图片
      }
    },
    // 开始拖拽
    onDragStart(event) {
      this.dragging = true;
      this.data = event.data;
      // 鼠标点击位置和精灵位置的偏移量，用于移动计算
      this.diff = {
        x: event.data.global.x - this.recordXPosition,
        y: event.data.global.y - this.recordYPosition
      };
      this.graphics.clear();
    },
    // 拖拽移动中
    onDragMove() {
      if (this.dragging) {
        const newPosition = this.data.getLocalPosition(this.$parent.instance);
        // 拖拽中保证精灵不超过背景区域
        this.recordXPosition = Math.min(
          Math.max(this.dragBorder.left || 0, newPosition.x - this.diff.x),
          this.dragBorder.right
        );
        this.recordYPosition = Math.min(
          Math.max(this.dragBorder.top || 0, newPosition.y - this.diff.y),
          this.dragBorder.bottom
        );
      }
    },
    // 拖拽完成，松开鼠标或抬起手指
    onDragEnd() {
      if (this.dragging) {
        this.dragging = false;
        this.data = null;
        this.drawLine();
      }
    }
  }
};
