<template>
  <div class="quill-editor">
    <quill-editor
      ref="myTextEditor"
      v-model="content"
      :options="editorOption"
      style="height:300px;"
    ></quill-editor>
  </div>
</template>

<script>
import 'quill/dist/quill.core.css';
import 'quill/dist/quill.snow.css';
import 'quill/dist/quill.bubble.css';

import { quillEditor } from 'vue-quill-editor';

export default {
  components: {
    quillEditor
  },
  data() {
    return {
      content: '',
      editorOption: {
        placeholder: '请输入应用介绍'
      }
    };
  },
  methods: {
    onEditorChange({ editor, html, text }) {
      this.content = html;
    }
  },
  mounted() {},
  name: 'index'
};
</script>

<style scoped lang="scss">
.quill-editor {
  padding: 17px 0 25px 0;
}

/deep/ .ql-editor.ql-blank::before {
  color: $--table-general-color-hover;
  font-size: 14px;
}
</style>
