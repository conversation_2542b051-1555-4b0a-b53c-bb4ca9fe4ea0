<template>
  <table ref="tableDataBox" class="table-data" style="table-layout: fixed">
    <tbody>
      <tr v-for="(row, index) in itemDatas" :key="index">
        <template v-for="item in row">
          <template>
            <td
              v-show="!item.labelHidden"
              :key="item.label + item.prop"
              :colspan="item.labelColspan || 1"
              :rowspan="item.rowspan || 1"
              :style="{ width: item.labelWidth || labelWidth + 'px' }"
              class="label-class"
            >
              <template v-if="item.labelSlot">
                <slot :name="item.labelSlot" :item="item" />
              </template>
              <template v-else> {{ item.label }} </template>
            </td>

            <td
              v-if="item.prop || item.slot"
              :key="item.label || index"
              :colspan="item.colspan || 1"
              :rowspan="item.rowspan || 1"
              :style="{ width: item.propWidth || 'auto' }"
            >
              <template v-if="item.slot">
                <slot :name="item.slot" :item="item" :form-datas="formDatas" />
              </template>

              <template v-else-if="item.defaultVal">
                {{ item.defaultVal }}
              </template>

              <!-- normal -->
              <template v-else>
                <span
                  :style="{ color: item.activeColor || '' }"
                  :class="item.activeClass || ''"
                >
                  {{
                    item.prop
                      ? formDatas[item.prop]
                        ? formDatas[item.prop]
                        : '--'
                      : '--'
                  }}</span
                >
              </template>
            </td>
          </template>
        </template>
      </tr>
    </tbody>
  </table>
</template>

<script>
export default {
  props: {
    itemDatas: {
      type: Array,
      default: () => []
    },
    formDatas: {
      type: Object,
      default: () => {}
    },
    labelWidth: {
      type: Number,
      default: 105
    }
  }
};
</script>

<style lang="scss" scoped>
.table-data {
  tr {
    width: 100%;
    .tr-title {
      padding-left: 10px;
      box-sizing: border-box;
      text-align: left;
      font-weight: 700;
    }
  }
}

.table-data tr > td {
  line-height: 16px !important;
}

.table-data {
  width: 100%;
  border-spacing: 0;
  border-collapse: collapse;

  tr > td {
    height: 40px;
    border: 1px solid #cecece;

    &:nth-child(2n-1) {
      width: 105px;
      padding-right: 12px;
      background-color: #f5f7fa;
      text-align: right;
      line-height: 1;
    }

    &:nth-child(2n) {
      padding-left: 12px;
    }
  }
}
</style>
