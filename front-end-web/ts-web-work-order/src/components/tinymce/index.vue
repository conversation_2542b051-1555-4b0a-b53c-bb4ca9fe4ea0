<template>
  <div ref="content">
    <editor :init="init" :api-Key="apiKey" v-model="content"></editor>
  </div>
</template>

<script>
import tinymce from 'tinymce';
import editor from '@tinymce/tinymce-vue';
import 'tinymce/themes/silver';
import 'tinymce/plugins/quickbars';
import 'tinymce/plugins/table';
import 'tinymce/plugins/link';
import 'tinymce/plugins/code';
import 'tinymce/plugins/image';
import 'tinymce/plugins/advlist';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/media';
import 'tinymce/plugins/paste';
import 'tinymce/icons/default/index';
import '@/assets/tinymce/langs/zh_CN';

export default {
  components: {
    editor: editor
  },
  props: {
    content: {
      type: String,
      default: () => {
        return '';
      }
    }
  },
  data() {
    return {
      ed: {},
      token: '',
      apiKey: 'uehtdbja2gm3onzytlv9a2k9cer030r0yyb0vm1lrugxr425',
      init: {
        // selector: '#tinymceContent',
        auto_focus: true,
        elementpath: false,
        statusbar: false,
        language: 'zh_CN',
        // language_url: '/assets/tinymce/langs/zh_CN.js',
        skin_url: '/assets/tinymce/skins/ui/oxide/content',
        plugins: 'quickbars link table code image advlist lists media paste',
        menubar: '',
        menu: {},
        toolbar: [
          'undo redo newdocument | bold italic underline strikethrough backcolor forecolor formatselect | fontselect | fontsizeselect | lineheight ',
          'cut copy paste | link image  media  | table  | alignleft aligncenter alignright alignjustify | outdent indent | bullist numlist | code'
        ],
        table_clone_elements: 'p',
        table_grid: false,
        width: '100%',
        height: '100%',
        auto_focus: true,
        paste_enable_default_filters: false,
        setup: function(editor) {},
        images_upload_handler: this.imageUploadHandler,
        paste_postprocess: function(plugin, args) {
          let res = args.node.querySelectorAll('img');
          var flag = false;
          for (let i = 0; i < res.length; i++) {
            if (res[i].src.indexOf('file:') != -1) {
              flag = true;
            }
          }
          if (flag) {
            this.$api({
              method: 'get',
              url: 'http://localhost:26789/file/readfile'
            })
              .then(res => {
                this.updateImgBase64();
              })
              .catch(res => {
                this.$confirm(
                  '<div style="padding:10px">粘贴WORD图文模式，您需要先安装一个插件<a style="color:blue" href="/static/wordPasterPlug/trasenWordPaster.zip">点击下载</a>  <br><div>',
                  '提示',
                  {
                    showCancelButton: false,
                    dangerouslyUseHTMLString: true,
                    showConfirmButton: false
                  }
                );
              });
          }
        },
        file_picker_types: 'file media',
        convert_urls: false, //这个参数加上去就可以了
        media_alt_source: false,
        media_filter_html: false,
        powerpaste_word_import: 'merge', // 参数可以是propmt, merge, clear
        powerpaste_html_import: 'merge', // propmt, merge, clear
        powerpaste_allow_local_images: true, //允许带图片
        paste_data_images: true,
        file_picker_callback: this.handleFilePickerCallBack,
        init_instance_callback: function(editor) {
          //页面初始化事件
          this.ed = editor;
        }
      }
    };
  },
  mounted() {
    let cookies = document.cookie.split(';');
    cookies.forEach(item => {
      if (item.indexOf('token') >= 0) {
        this.token = item.split('=')[1] || '';
      }
    });

    let content = this.$refs.content,
      tinymceIframe = content.querySelector('.tox-edit-area__iframe'),
      tinymceBody =
        tinymceIframe &&
        tinymceIframe.contentWindow.document.querySelector('head');

    let cssList = ['content.min.css', 'skin.min.css'];
    cssList.forEach(item => {
      let linkDom = document.createElement('link');
      linkDom.rel = 'stylesheet';
      linkDom.style = 'text/css';
      linkDom.href = location.origin + '/container/' + item;
      tinymceBody.append(linkDom);
    });
  },
  methods: {
    handleFilePickerCallBack(cb, value, meta) {
      if (meta.filetype == 'media') {
        //创建一个隐藏的type=file的文件选择input
        let input = document.createElement('input');
        input.setAttribute('type', 'file');
        input.onchange = () => {
          var file = input.files[0];
          this.fileUpload(file, cb);
        };
        //触发点击
        input.click();
      }
    },
    //图片上传
    async imageUploadHandler(blobInfo, success, failure) {
      let formData = new FormData();
      formData.append('file', blobInfo.blob());

      let formAxios = this.$axios.create({
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      formAxios({
        url: '/ts-basics-bottom/fileAttachment/upload?moduleName=workDesk',
        data: formData,
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        if (res.data) {
          if (res.data.success == false) {
            failure(
              '图片上传失败' +
                (res.data.message ? ': ' + res.data.message : '！')
            );
            return;
          }
          success(res.data.object[0].filePath);
        } else {
          failure('图片上传失败');
        }
      });

      // this.$api({
      //   url: '/ts-basics-bottom/fileAttachment/upload?moduleName=workDesk',
      //   method: 'post',
      //   data: formData,
      //   async: false,
      //   headers: {
      //     'Content-Type': 'multipart/form-data;'
      //   }
      // }).then(res => {
      //   if (res.success == false) {
      //     failure('图片上传失败' + (res.message ? ': ' + res.message : '！'));
      //     return;
      //   }
      //   success(res.object[0].filePath);
      // });
    },
    //文件上传
    fileUpload(file, cb) {
      var formData = new FormData();
      //假设接口接收参数为file,值为选中的文件
      formData.append('file', file);
      this.$api({
        url: '/ts-document/attachment/fileUpload?module=workDesk&fillupf=2',
        method: 'post',
        // contentType: false,
        // processData: false,
        headers: {
          'Content-Type': ''
        },
        data: formData
      }).then(res => {
        if (
          Object.prototype.toString.call(res) == '[object Error]' ||
          !res.location
        ) {
          this.$message({
            type: 'error',
            customClass: 'tinymce-toast-message-box',
            message: '出错啦，请稍后再试'
          });
          cb();
          return;
        }
        cb(res.location);
      });
    },
    //上传Base64图片
    updateImgBase64() {
      let res = this.ed.iframeElement.contentWindow.document.querySelectorAll(
        'img'
      );
      let ajax = [];
      for (let i = 0; i < res.length; i++) {
        if (res[i].src.indexOf('file:') != -1) {
          ajax.push(
            this.$axios.get(
              `http://localhost:26789/file/readfile?img=${res[i].src}&dataIndex=${i}`
            )
          );
          // ajax.push($.get(`http://localhost:26789/file/readfile?img=${res[i].src}&dataIndex=${i}`));
        }
      }
      if (ajax.length != 0) {
        Promise.all(ajax).then(_res => {
          _res.forEach(_item => {
            res[_item.dataIndex]['data-src'] = res[_item.dataIndex]['src'];
            res[_item.dataIndex]['src'] = _item.base64;
          });
          this.updateImg();
        });
      }
    },
    /**@desc 提交前替换图片请求服务器资源**/
    updateImg() {
      let res = this.ed.iframeElement.contentWindow.document.querySelectorAll(
        'img'
      );
      let ajax = [];
      let dom = [];
      let url = location.origin + '/ts-document/attachment/';
      for (let i = 0; i < res.length; i++) {
        if (res[i].src.indexOf('data:image/png;base64,') != -1) {
          dom.push(res[i]);
          ajax.push(
            this.$axios.post(`${url}imageBase64Upload`, {
              token: this.token,
              module: 'richfile',
              fillupf: '2',
              imageBase64: res[i].src.split('data:image/png;base64,')[1]
            })
            // $.post(`${url}imageBase64Upload`, {
            //     token: this.token,
            //     module: 'richfile',
            //     fillupf: '2',
            //     imageBase64: res[i].src.split('data:image/png;base64,')[1],
            // })
          );
        }
      }
      if (ajax.length != 0) {
        Promise.all(ajax).then(_res => {
          _res.forEach((_item, index) => {
            if (_item.success) {
              dom[index].src = `${_item.object.location}`;
              dom[index].removeAttribute('data-mce-src');
            }
          });
          //submit();
        });
      }
    },

    //获取数据
    getData() {
      return this.content;
    }
  }
};
</script>

<style src="../../assets/tinymce/skins/ui/oxide/skin.css"></style>
<style src="../../assets/tinymce/skins/ui/oxide/content.css"></style>

<style lang="scss">
.tinymce-toast-message-box {
  z-index: 5050 !important;
}
</style>
