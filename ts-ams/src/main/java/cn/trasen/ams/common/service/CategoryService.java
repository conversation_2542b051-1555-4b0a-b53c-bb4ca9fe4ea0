package cn.trasen.ams.common.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.common.model.Category;
import cn.trasen.homs.core.model.TreeModel;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName CategoryService
 * @Description TODO
 * @date 2025年3月6日 下午3:31:39
 */
public interface CategoryService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2024年5月13日 下午5:03:17
     * <AUTHOR>
     */
    Integer save(Category record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2024年5月13日 下午5:03:17
     * <AUTHOR>
     */
    Integer update(Category record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2024年5月13日 下午5:03:17
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return Category
     * @Title selectById
     * @Description 根据ID查询
     * @date 2024年5月13日 下午5:03:17
     * <AUTHOR>
     */
    Category selectById(String id);

    Boolean hasSon(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<Category>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2024年5月13日 下午5:03:17
     * <AUTHOR>
     */
    DataSet<Category> getDataSetList(Page page, Category record);

    List<TreeModel> getTreeList(Integer level, String sysType);

    List<TreeModel> getTreeListByIds(List<String> ids);

    List<Category> getList(Page page, Category record);

    List<Category> getListByIds(List<String> ids);

    Map fetchAllCategoryName2Map(String sysType);
}
