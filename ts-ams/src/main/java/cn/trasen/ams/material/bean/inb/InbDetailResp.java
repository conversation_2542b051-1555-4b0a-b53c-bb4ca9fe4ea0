package cn.trasen.ams.material.bean.inb;

import cn.trasen.ams.material.model.Inb;
import cn.trasen.ams.material.model.InbDtl;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.material.bean.inb
 * @className: InbInsertReq
 * @author: chenbin
 * @description: 物资入库单创建编辑请求体
 * @date: 2025/7/31 09:54
 * @version: 1.0
 */
@Data

public class InbDetailResp {
    
    @ApiModelProperty(value = "入库单主数据")
    private Inb inb;

    @ApiModelProperty(value = "入库单明细数据")
    private List<InbDtlResp> InbDtlList;
}
