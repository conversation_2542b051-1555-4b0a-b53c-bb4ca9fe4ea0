package cn.trasen.ams.material.controller;

import cn.trasen.ams.material.bean.inb.InbDetailResp;
import cn.trasen.ams.material.bean.inb.InbDtlResp;
import cn.trasen.ams.material.bean.inb.InbInsertReq;
import cn.trasen.ams.material.model.InbDtl;
import cn.trasen.ams.material.service.InbDtlService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.Inb;
import cn.trasen.ams.material.service.InbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InbController
 * @Description TODO
 * @date 2025年7月31日 上午9:49:53
 */
@RestController
@Api(tags = "InbController")
public class InbController {

    private transient static final Logger logger = LoggerFactory.getLogger(InbController.class);

    @Autowired
    private InbService inbService;

    @Autowired
    private InbDtlService inbDtlService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveInb
     * @Description 新增
     * @date 2025年7月31日 上午9:49:53
     * <AUTHOR>
     */
    @ApiOperation(value = "物资入库单新增", notes = "物资入库单新增")
    @PostMapping("/api/material/inb/save")
    public PlatformResult<String> saveInb(@RequestBody InbInsertReq record) {
        try {
            inbService.insert(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateInb
     * @Description 编辑
     * @date 2025年7月31日 上午9:49:53
     * <AUTHOR>
     */
    @ApiOperation(value = "物资入库单编辑", notes = "物资入库单编辑")
    @PostMapping("/api/material/inb/update")
    public PlatformResult<String> updateInb(@RequestBody InbInsertReq record) {
        try {
            inbService.edit(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<Inb>
     * @Title selectInbById
     * @Description 根据ID查询
     * @date 2025年7月31日 上午9:49:53
     * <AUTHOR>
     */
    @ApiOperation(value = "物资入库单详情", notes = "物资入库单详情")
    @GetMapping("/api/material/inb/{id}")
    public PlatformResult<InbDetailResp> selectInbById(
            @PathVariable String id,
            @RequestParam(value = "direction", defaultValue = "current") String direction) {
        try {
            // 根据direction参数获取对应的入库单ID
            String targetId = inbService.getTargetInbId(id, direction);
            if (targetId == null) {
                return PlatformResult.failure("没有找到" + getDirectionDesc(direction) + "的记录");
            }

            InbDetailResp record = new InbDetailResp();
            Inb inb = inbService.selectById(targetId);
            List<InbDtlResp> inbDtlList = inbDtlService.getInbDtlExtListByInbId(inb.getId());

            record.setInb(inb);
            record.setInbDtlList(inbDtlList);

            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * 获取方向描述
     * @param direction 方向参数
     * @return 描述文本
     */
    private String getDirectionDesc(String direction) {
        switch (direction) {
            case "prev":
                return "上一条";
            case "next":
                return "下一条";
            case "current":
            default:
                return "当前";
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteInbById
     * @Description 根据ID删除
     * @date 2025年7月31日 上午9:49:53
     * <AUTHOR>
     */
    @ApiOperation(value = "物资入库单删除", notes = "物资入库单删除")
    @PostMapping("/api/material/inb/delete/{id}")
    public PlatformResult<String> deleteInbById(@PathVariable String id) {
        try {
            inbService.remove(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param inbIdList
     * @return PlatformResult<String>
     * @Title batchDeleteInb
     * @Description 批量删除入库单
     * @date 2025年8月1日 下午2:30:00
     * <AUTHOR>
     */
    @ApiOperation(value = "物资入库单批量删除", notes = "物资入库单批量删除")
    @PostMapping("/api/material/inb/batch/delete")
    public PlatformResult<String> batchDeleteInb(@RequestBody List<String> inbIdList) {
        try {
            // 参数验证
            if (CollectionUtils.isEmpty(inbIdList)) {
                return PlatformResult.failure("删除的入库单ID列表不能为空");
            }
            // 调用批量删除服务
            inbService.batchRemove(inbIdList);
            return PlatformResult.success("成功删除 " + inbIdList.size() + " 条入库单记录");
        } catch (Exception e) {
            logger.error("批量删除入库单失败: {}", e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<Inb>
     * @Title selectInbList
     * @Description 查询列表
     * @date 2025年7月31日 上午9:49:53
     * <AUTHOR>
     */
    @ApiOperation(value = "物资入库单列表", notes = "物资入库单列表")
    @GetMapping("/api/material/inb/list")
    public DataSet<Inb> selectInbList(Page page, Inb record) {
        return inbService.getDataSetList(page, record);
    }

    // 批量审核

    // 批量取消
}
