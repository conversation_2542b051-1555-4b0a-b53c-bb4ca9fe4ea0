package cn.trasen.ams.material.controller;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.model.Brand;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.MSku;
import cn.trasen.ams.material.service.MSkuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MSkuController
 * @Description TODO
 * @date 2025年7月24日 上午8:51:19
 */
@RestController
@Api(tags = "MSkuController")
public class MSkuController {

    private transient static final Logger logger = LoggerFactory.getLogger(MSkuController.class);

    @Autowired
    private MSkuService mSkuService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveMSku
     * @Description 新增
     * @date 2025年7月24日 上午8:51:19
     * <AUTHOR>
     */
    @ApiOperation(value = "物资字典新增", notes = "物资字典新增")
    @PostMapping("/api/material/sku/save")
    public PlatformResult<String> saveMSku(@Validated @RequestBody MSku record) {
        try {
            mSkuService.save(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateMSku
     * @Description 编辑
     * @date 2025年7月24日 上午8:51:19
     * <AUTHOR>
     */
    @ApiOperation(value = "物资字典编辑", notes = "物资字典编辑")
    @PostMapping("/api/material/sku/update")
    public PlatformResult<String> updateMSku(@Validated @RequestBody MSku record) {
        try {
            mSkuService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<MSku>
     * @Title selectMSkuById
     * @Description 根据ID查询
     * @date 2025年7月24日 上午8:51:19
     * <AUTHOR>
     */
    @ApiOperation(value = "物资字典详情", notes = "物资字典详情")
    @GetMapping("/api/material/sku/{id}")
    public PlatformResult<MSku> selectMSkuById(@PathVariable String id) {
        try {
            MSku record = mSkuService.selectById(id);
            mSkuService.dataFmt(record);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteMSkuById
     * @Description 根据ID删除
     * @date 2025年7月24日 上午8:51:19
     * <AUTHOR>
     */
    @ApiOperation(value = "物资字典删除", notes = "物资字典删除")
    @PostMapping("/api/material/sku/delete/{id}")
    public PlatformResult<String> deleteMSkuById(@PathVariable String id) {
        try {
            mSkuService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<MSku>
     * @Title selectMSkuList
     * @Description 查询列表
     * @date 2025年7月24日 上午8:51:19
     * <AUTHOR>
     */
    @ApiOperation(value = "物资字典列表", notes = "物资字典列表")
    @RequestMapping(value = "/api/material/sku/list", method = {RequestMethod.GET, RequestMethod.POST})
    public DataSet<MSku> selectMSkuList(Page page, MSku record) {
        return mSkuService.getDataSetList(page, record);
    }

    @ApiOperation(value = "物资字典列表导出", notes = "导出")
    @PostMapping(value = "/api/material/sku/export")
    public ResponseEntity<byte[]> export(@RequestBody MSku record) throws IOException {

        try {

            List<MSku> exportList = mSkuService.getListNoPage(record);
            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath("template/mskuExportTpl.xlsx"));

            params.setColForEach(true);
            Map<String, Object> map = new HashMap<>();
            map.put("list", exportList);
            // 获取当前年月日 字符串
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            try {
                workbook.write(outputStream);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                outputStream.close();
                workbook.close();
            }

            byte[] contents = outputStream.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFilename = new String("物资字典导出数据.xlsx".getBytes("UTF-8"), "ISO8859-1");
            headers.setContentDispositionFormData("attachment", encodedFilename);
            headers.setContentLength(contents.length);

            return new ResponseEntity<>(contents, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }
}
