package cn.trasen.ams.material.controller;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.material.bean.warehouseRela.WarehouseRelaVsEmpResp;
import cn.trasen.ams.material.constant.WarehouseRelaConst;
import cn.trasen.ams.material.service.WarehouseRelaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.common.model.Warehouse;
import cn.trasen.ams.common.service.WarehouseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName WarehouseController
 * @Description TODO
 * @date 2025年7月18日 下午3:56:24
 */
@RestController
@Api(tags = "WarehouseController")
@Valid
public class WarehouseMController {

    private transient static final Logger logger = LoggerFactory.getLogger(WarehouseMController.class);

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private WarehouseRelaService warehouseRelaService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveWarehouse
     * @Description 新增
     * @date 2025年7月18日 下午3:56:24
     * <AUTHOR>
     */
    @ApiOperation(value = "物资仓库新增", notes = "物资仓库新增")
    @PostMapping("/api/material/warehouse/save")
    public PlatformResult<String> saveWarehouse(@Validated @RequestBody Warehouse record) {
        try {
            record.setSysType(CommonConst.SYS_TYPE_WZ);
            warehouseService.save(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateWarehouse
     * @Description 编辑
     * @date 2025年7月18日 下午3:56:24
     * <AUTHOR>
     */
    @ApiOperation(value = "物资仓库编辑", notes = "物资仓库编辑")
    @PostMapping("/api/material/warehouse/update")
    public PlatformResult<String> updateWarehouse(@Validated @RequestBody Warehouse record) {
        try {
            record.setSysType(CommonConst.SYS_TYPE_WZ);
            warehouseService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<Warehouse>
     * @Title selectWarehouseById
     * @Description 根据ID查询
     * @date 2025年7月18日 下午3:56:24
     * <AUTHOR>
     */
    @ApiOperation(value = "物资仓库详情", notes = "物资仓库详情")
    @GetMapping("/api/material/warehouse/{id}")
    public PlatformResult<Warehouse> selectWarehouseById(@PathVariable String id) {
        try {
            Warehouse record = warehouseService.selectById(id);
            warehouseService.dataFmt(record);
            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteWarehouseById
     * @Description 根据ID删除
     * @date 2025年7月18日 下午3:56:24
     * <AUTHOR>
     */
    @ApiOperation(value = "物资仓库删除", notes = "物资仓库删除")
    @PostMapping("/api/material/warehouse/delete/{id}")
    public PlatformResult<String> deleteWarehouseById(@PathVariable String id) {
        try {
            warehouseService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<Warehouse>
     * @Title selectWarehouseList
     * @Description 查询列表
     * @date 2025年7月18日 下午3:56:24
     * <AUTHOR>
     */
    @ApiOperation(value = "物资仓库列表", notes = "物资仓库列表")
    @GetMapping("/api/material/warehouse/list")
    public DataSet<Warehouse> selectWarehouseList(Page page, Warehouse record) {
        record.setSysType(CommonConst.SYS_TYPE_WZ);
        return warehouseService.getDataSetList(page, record);
    }


    /**
     * @param empId:
     * @param page:
     * @return PlatformResult
     * <AUTHOR>
     * @description 根据人员获取物资仓库列表
     * @date 2025/7/24 18:24
     */
    @ApiOperation(value = "根据人员获取物资仓库列表", notes = "根据人员获取物资仓库列表")
    @GetMapping("/api/material/warehouse/listByEmp/{empId}")
    public PlatformResult selectWarehouselistByEmp(@PathVariable String empId, Page page) {
        try {
            return PlatformResult.success(warehouseRelaService.selectWarehouseByEmpId(page, empId));
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "根据物资分类获取物资仓库列表", notes = "根据物资分类获取物资仓库列表")
    @GetMapping("/api/material/warehouse/listByCategory/{categoryId}")
    public PlatformResult selectWarehouseRelaEmpList(@PathVariable String categoryId, Page page) {
        try {
            return PlatformResult.success(warehouseRelaService.selectWarehouseByCategoryId(page, categoryId));
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "根据仓库ID获取物资分类树", notes = "根据仓库ID获取物资分类树")
    @GetMapping("/api/material/warehouse/categoryTree/{whId}")
    public PlatformResult selectCategoryTree(@PathVariable String whId) {
        try {
            return PlatformResult.success(warehouseRelaService.getCateTreeList(whId));
        } catch (Exception e) {
            return PlatformResult.failure(e.getMessage());
        }
    }
}
