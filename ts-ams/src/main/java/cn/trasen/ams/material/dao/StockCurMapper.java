package cn.trasen.ams.material.dao;

import cn.trasen.ams.material.model.StockCur;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface StockCurMapper extends Mapper<StockCur> {

    /**
     * 根据MD5列表查询已存在的库存记录
     * @param md5List MD5列表
     * @return 已存在的库存记录列表
     */
    List<StockCur> selectByMd5List(@Param("md5List") List<String> md5List);

    /**
     * 批量插入库存记录
     * @param stockCurList 库存记录列表
     */
    void batchInsert(@Param("stockCurList") List<StockCur> stockCurList);

    /**
     * 批量更新库存记录
     * @param stockCurList 库存记录列表
     */
    void batchUpdate(@Param("stockCurList") List<StockCur> stockCurList);
}