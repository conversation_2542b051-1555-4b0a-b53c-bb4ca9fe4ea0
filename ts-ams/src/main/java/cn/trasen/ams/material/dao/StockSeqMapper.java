package cn.trasen.ams.material.dao;

import cn.trasen.ams.material.model.StockSeq;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface StockSeqMapper extends Mapper<StockSeq> {

    /**
     * 批量插入库存流水记录
     * @param stockSeqList 库存流水记录列表
     */
    void batchInsert(@Param("stockSeqList") List<StockSeq> stockSeqList);

    /**
     * 根据订单ID删除库存流水记录
     * @param ordId 订单ID
     */
    void deleteByOrdId(@Param("ordId") String ordId);
}