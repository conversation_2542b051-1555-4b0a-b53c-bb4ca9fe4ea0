<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.material.dao.InbMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.material.model.Inb">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="flow_no" jdbcType="VARCHAR" property="flowNo" />
    <result column="wh_id" jdbcType="VARCHAR" property="whId" />
    <result column="supply_id" jdbcType="VARCHAR" property="supplyId" />
    <result column="purch_id" jdbcType="VARCHAR" property="purchId" />
    <result column="purch_name" jdbcType="VARCHAR" property="purchName" />
    <result column="purch_date" jdbcType="TIMESTAMP" property="purchDate" />
    <result column="inv_no" jdbcType="VARCHAR" property="invNo" />
    <result column="inv_date" jdbcType="TIMESTAMP" property="invDate" />
    <result column="inb_date" jdbcType="TIMESTAMP" property="inbDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_consume" jdbcType="CHAR" property="isConsume" />
    <result column="apply_dept_id" jdbcType="VARCHAR" property="applyDeptId" />
    <result column="apply_dept_name" jdbcType="VARCHAR" property="applyDeptName" />
    <result column="apply_user_id" jdbcType="VARCHAR" property="applyUserId" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="stat" jdbcType="CHAR" property="stat" />
    <result column="return_stat" jdbcType="CHAR" property="returnStat" />
    <result column="total_amt" jdbcType="DECIMAL" property="totalAmt" />
    <result column="print_stat" jdbcType="CHAR" property="printStat" />
    <result column="doer_id" jdbcType="VARCHAR" property="doerId" />
    <result column="doer_name" jdbcType="VARCHAR" property="doerName" />
    <result column="doer_time" jdbcType="TIMESTAMP" property="doerTime" />
    <result column="cancel_user_id" jdbcType="VARCHAR" property="cancelUserId" />
    <result column="cancel_user_name" jdbcType="VARCHAR" property="cancelUserName" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_date" jdbcType="DATE" property="createDate" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <!-- 关联字段 -->
    <result column="whName" jdbcType="VARCHAR" property="whName" />
    <result column="supplyName" jdbcType="VARCHAR" property="supplyName" />
  </resultMap>
  <select id="getList" resultType="cn.trasen.ams.material.model.Inb" parameterType="cn.trasen.ams.material.model.Inb">
      SELECT
      t1.*,
      t2.name as wh_name,
      t3.name as supply_name,
      t5.id as mtd_code_id,
      t5.name as mtd_code_name
      FROM m_inb t1
      LEFT JOIN c_warehouse t2 ON t1.wh_id = t2.id AND t2.is_deleted = 'N'
      LEFT JOIN c_supplier t3 ON t1.supply_id = t3.id AND t3.is_deleted = 'N'
      LEFT JOIN m_mtd_code_rela t4 ON t1.id = t4.model_id and t4.model_type = '0' AND t4.is_deleted = 'N'
      LEFT JOIN m_mtd_code t5 ON t5.id = t4.mtd_code_id
      WHERE t1.is_deleted = 'N'
      <!-- 动态查询条件 -->
      <if test="flowNo != null and flowNo != ''">
          AND t1.flow_no LIKE CONCAT('%', #{flowNo}, '%')
      </if>

      <if test="mtdCodeId != null and mtdCodeId != ''">
          AND t5.mtd_code_id = #{mtdCodeId}
      </if>

      <if test="whName != null and whName != ''">
          AND t2.name = #{whName}
      </if>
      <if test="supplyName != null and supplyName != ''">
          AND t3.name = #{supplyName}
      </if>
      <if test="purchName != null and purchName != ''">
          AND t1.purch_name LIKE CONCAT('%', #{purchName}, '%')
      </if>
      <if test="invNo != null and invNo != ''">
          AND t1.inv_no LIKE CONCAT('%', #{invNo}, '%')
      </if>
      <if test="stat != null and stat != ''">
          AND t1.stat = #{stat}
      </if>
      <if test="returnStat != null and returnStat != ''">
          AND t1.return_stat = #{returnStat}
      </if>
      <if test="isConsume != null and isConsume != ''">
          AND t1.is_consume = #{isConsume}
      </if>
      <if test="applyDeptId != null and applyDeptId != ''">
          AND t1.apply_dept_id = #{applyDeptId}
      </if>
      <if test="doerName != null and doerName != ''">
          AND t1.doer_name LIKE CONCAT('%', #{doerName}, '%')
      </if>
      <if test="createUser != null and createUser != ''">
          AND t1.create_user = #{createUser}
      </if>

      <!-- 日期范围查询 -->

      <if test="purchDateQuery">
          AND t1.purch_date BETWEEN
          SUBSTRING_INDEX(#{purchDateQuery}, ',', 1)
          AND
          SUBSTRING_INDEX(#{purchDateQuery}, ',', -1)
      </if>
      <if test="inbDateQuery">
          AND t1.inb_date BETWEEN
          SUBSTRING_INDEX(#{inbDateQuery}, ',', 1)
          AND
          SUBSTRING_INDEX(#{inbDateQuery}, ',', -1)
      </if>

      ORDER BY t1.create_date DESC
  </select>

  <!-- 获取上一条记录ID -->
  <select id="getPrevId" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT t1.id
    FROM m_inb t1
    WHERE t1.is_deleted = 'N'
      AND (
        (t1.stat &lt; (SELECT stat FROM m_inb WHERE id = #{currentId} AND is_deleted = 'N'))
        OR (
          t1.stat = (SELECT stat FROM m_inb WHERE id = #{currentId} AND is_deleted = 'N')
          AND t1.create_date &gt; (SELECT create_date FROM m_inb WHERE id = #{currentId} AND is_deleted = 'N')
        )
      )
    ORDER BY t1.stat DESC, t1.create_date ASC
    LIMIT 1
  </select>

  <!-- 获取下一条记录ID -->
  <select id="getNextId" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT t1.id
    FROM m_inb t1
    WHERE t1.is_deleted = 'N'
      AND (
        (t1.stat &gt; (SELECT stat FROM m_inb WHERE id = #{currentId} AND is_deleted = 'N'))
        OR (
          t1.stat = (SELECT stat FROM m_inb WHERE id = #{currentId} AND is_deleted = 'N')
          AND t1.create_date &lt; (SELECT create_date FROM m_inb WHERE id = #{currentId} AND is_deleted = 'N')
        )
      )
    ORDER BY t1.stat ASC, t1.create_date DESC
    LIMIT 1
  </select>
</mapper>