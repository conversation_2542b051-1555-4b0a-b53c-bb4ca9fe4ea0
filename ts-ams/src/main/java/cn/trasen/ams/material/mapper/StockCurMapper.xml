<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.material.dao.StockCurMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.material.model.StockCur">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="md5" jdbcType="VARCHAR" property="md5" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="wh_id" jdbcType="VARCHAR" property="whId" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="num" jdbcType="INTEGER" property="num" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>

  <!-- 根据MD5列表查询已存在的库存记录 -->
  <select id="selectByMd5List" resultMap="BaseResultMap">
    SELECT
      id, md5, sku_id, wh_id, batch_no, num,
      create_date, create_user, create_user_name,
      dept_id, dept_name, update_date, update_user, update_user_name,
      sso_org_code, sso_org_name, is_deleted
    FROM m_stock_cur
    WHERE md5 IN
    <foreach collection="md5List" item="md5" open="(" separator="," close=")">
      #{md5,jdbcType=VARCHAR}
    </foreach>
    AND is_deleted = 'N'
  </select>

  <!-- 批量插入库存记录 -->
  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO m_stock_cur
    (
      id, md5, sku_id, wh_id, batch_no, num,
      create_date, create_user, create_user_name,
      dept_id, dept_name, update_date, update_user, update_user_name,
      sso_org_code, sso_org_name, is_deleted
    )
    VALUES
    <foreach collection="stockCurList" item="item" index="index" separator=",">
    (
      #{item.id,jdbcType=VARCHAR},
      #{item.md5,jdbcType=VARCHAR},
      #{item.skuId,jdbcType=VARCHAR},
      #{item.whId,jdbcType=VARCHAR},
      #{item.batchNo,jdbcType=VARCHAR},
      #{item.num,jdbcType=INTEGER},
      #{item.createDate,jdbcType=TIMESTAMP},
      #{item.createUser,jdbcType=VARCHAR},
      #{item.createUserName,jdbcType=VARCHAR},
      #{item.deptId,jdbcType=VARCHAR},
      #{item.deptName,jdbcType=VARCHAR},
      #{item.updateDate,jdbcType=TIMESTAMP},
      #{item.updateUser,jdbcType=VARCHAR},
      #{item.updateUserName,jdbcType=VARCHAR},
      #{item.ssoOrgCode,jdbcType=VARCHAR},
      #{item.ssoOrgName,jdbcType=VARCHAR},
      #{item.isDeleted,jdbcType=CHAR}
    )
    </foreach>
  </insert>

  <!-- 批量更新库存记录 -->
  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="stockCurList" item="item" index="index" separator=";">
      UPDATE m_stock_cur
      SET
        sku_id = #{item.skuId,jdbcType=VARCHAR},
        wh_id = #{item.whId,jdbcType=VARCHAR},
        batch_no = #{item.batchNo,jdbcType=VARCHAR},
        num = num + #{item.num,jdbcType=INTEGER},
        update_date = #{item.updateDate,jdbcType=TIMESTAMP},
        update_user = #{item.updateUser,jdbcType=VARCHAR},
        update_user_name = #{item.updateUserName,jdbcType=VARCHAR},
        dept_id = #{item.deptId,jdbcType=VARCHAR},
        dept_name = #{item.deptName,jdbcType=VARCHAR},
        sso_org_code = #{item.ssoOrgCode,jdbcType=VARCHAR},
        sso_org_name = #{item.ssoOrgName,jdbcType=VARCHAR}
      WHERE md5 = #{item.md5,jdbcType=VARCHAR}
      AND is_deleted = 'N'
    </foreach>
  </update>
</mapper>