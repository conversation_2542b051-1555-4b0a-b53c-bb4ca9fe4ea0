package cn.trasen.ams.material.model;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.validator.dict.DictExistValid;
import cn.trasen.ams.common.validator.pk.PkExistValid;
import cn.trasen.ams.material.constant.OrdConst;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;

@Table(name = "m_inb")
@Setter
@Getter
public class Inb {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;


    @Transient
    @PkExistValid(table = "m_mtd_code", message = "方式码不正确")
    @ApiModelProperty(value = "Q:方式码ID")
    private String mtdCodeId;

    /**
     * 方式码名称
     */
    @Transient
    @ApiModelProperty(value = "方式码名称")
    private String mtdCodeName;

    /**
     * 流水号
     */
    @Column(name = "flow_no")
    @ApiModelProperty(value = "Q:流水号")
    private String flowNo;

    /**
     * 仓库ID
     */
    @NotNull(message = "仓库ID不能为空")
    @PkExistValid(table = "c_warehouse", message = "仓库不正确")
    @Column(name = "wh_id")
    @ApiModelProperty(value = "仓库ID")
    private String whId;

    @Transient
    @ApiModelProperty(value = "Q:仓库名称")
    private String whName;


    /**
     * 供应商ID
     */
    @PkExistValid(table = "c_supplier", message = "供应商不正确")
    @NotNull(message = "供应商ID不能为空")
    @Column(name = "supply_id")
    @ApiModelProperty(value = "供应商ID")
    private String supplyId;

    @Transient
    @ApiModelProperty(value = "Q:供应商名称")
    private String supplyName;

    /**
     * 采购人
     */
    @Column(name = "purch_id")
    @ApiModelProperty(value = "采购人")
    private String purchId;

    /**
     * 采购人名称
     */
    @Column(name = "purch_name")
    @ApiModelProperty(value = "Q:采购人名称")
    private String purchName;

    /**
     * 采购日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "purch_date")
    @ApiModelProperty(value = "采购日期")
    private Date purchDate;

    @Transient
    @ApiModelProperty(value = "Q:采购日期查询")
    private String purchDateQuery;

    /**
     * 发票号
     */
    @Column(name = "inv_no")
    @ApiModelProperty(value = "发票号")
    private String invNo;

    /**
     * 开票日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "inv_date")
    @ApiModelProperty(value = "开票日期")
    private Date invDate;


    /**
     * 入库日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "inb_date")
    @ApiModelProperty(value = "入库日期")
    private Date inbDate;

    @Transient
    @ApiModelProperty(value = "Q:入库日期查询")
    private String inbDateQuery;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否直销
     */
    @DictExistValid(code = CommonConst.YES_OR_NO, message = "是否直销不正确")
    @Column(name = "is_consume")
    @ApiModelProperty(value = "是否直销")
    private String isConsume;

    @Transient
    @ApiModelProperty(value = "是否直销显示")
    private String isConsumeShow;

    /**
     * 申请科室
     */
    @Column(name = "apply_dept_id")
    @ApiModelProperty(value = "申请科室")
    private String applyDeptId;

    /**
     * 申请科室名称
     */
    @Column(name = "apply_dept_name")
    @ApiModelProperty(value = "申请科室名称")
    private String applyDeptName;

    /**
     * 领用人
     */
    @Column(name = "apply_user_id")
    @ApiModelProperty(value = "领用人")
    private String applyUserId;

    /**
     * 领用人名称
     */
    @Column(name = "apply_user_name")
    @ApiModelProperty(value = "领用人名称")
    private String applyUserName;

    /**
     * 单据状态 1登记 2 已确认
     */
    @ApiModelProperty(value = "Q:单据状态 1登记 2 已确认")
    private String stat;

    @Transient
    @ApiModelProperty(value = "单据状态显示")
    private String statShow;

    /**
     * 退货状态 1 部分退货 2 完全退货
     */

    @Column(name = "return_stat")
    @ApiModelProperty(value = "退货状态 1 部分退货 2 完全退货")
    private String returnStat;


    @Transient
    @ApiModelProperty(value = "退货状态显示")
    private String returnStatShow;

    /**
     * 入库单总价格
     */
    @Column(name = "total_amt")
    @ApiModelProperty(value = "入库单总价格")
    private BigDecimal totalAmt;

    /**
     * 打印状态 0 未打印 1 已打印
     */
    @Column(name = "print_stat")
    @ApiModelProperty(value = "打印状态 0 未打印 1 已打印")
    private String printStat;

    @Transient
    @ApiModelProperty(value = "打印状态显示")
    private String printStatShow;

    /**
     * 审核人ID
     */
    @Column(name = "doer_id")
    @ApiModelProperty(value = "审核人ID")
    private String doerId;

    /**
     * 审核人名称
     */
    @Column(name = "doer_name")
    @ApiModelProperty(value = "审核人名称")
    private String doerName;

    /**
     * 审核人名称
     */
    @Column(name = "doer_time")
    @ApiModelProperty(value = "审核人名称")
    private Date doerTime;

    /**
     * 撤销人ID
     */
    @Column(name = "cancel_user_id")
    @ApiModelProperty(value = "撤销人ID")
    private String cancelUserId;

    /**
     * 撤销人名称
     */
    @Column(name = "cancel_user_name")
    @ApiModelProperty(value = "撤销人名称")
    private String cancelUserName;

    /**
     * 撤销时间
     */
    @Column(name = "cancel_time")
    @ApiModelProperty(value = "撤销时间")
    private Date cancelTime;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;


}