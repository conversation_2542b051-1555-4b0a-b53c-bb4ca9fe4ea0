package cn.trasen.ams.material.model;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.validator.dict.DictExistValid;
import cn.trasen.ams.common.validator.pk.PkExistValid;
import cn.trasen.ams.material.constant.MSkuConst;
import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;

@Table(name = "m_sku")
@Setter
@Getter
public class MSku {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    @Transient
    @ApiModelProperty(value = "Q:忽略ID列表")
    private List<String> ignoreIdList;

    @Transient
    private List<String> idList;

    /**
     * 厂家ID
     */
    @PkExistValid(table = "c_manufacturer", message = "生产厂家不正确")
    @NotNull(message = "生产厂家不能为空")
    @Column(name = "manufacturer_id")
    @ApiModelProperty(value = "厂家ID")
    private String manufacturerId;

    @Transient
    @ApiModelProperty(value = "厂家名称")
    private String manufacturerName;

    /**
     * 供应商ID
     */
    @PkExistValid(table = "c_supplier", message = "供应商不正确")
    @Column(name = "supply_id")
    @ApiModelProperty(value = "供应商ID")
    private String supplyId;

    @Transient
    @ApiModelProperty(value = "供应商名称")
    private String supplyName;


    /**
     * 物资分类ID
     */

    @PkExistValid(table = "c_category", message = "Q:物资分类不正确")
    @NotNull(message = "物资分类不能为空")
    @Column(name = "category_id")
    @ApiModelProperty(value = "物资分类ID")
    private String categoryId;

    @Transient
    @ApiModelProperty(value = "物资分类ID列表")
    private List<String> categoryIdList;

    @Transient
    @ApiModelProperty(value = "物资分类名称")
    private String categoryName;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 级别
     */
    @DictExistValid(code = MSkuConst.SKU_LEVEL, message = "级别不正确")
    @ApiModelProperty(value = "级别")
    private String level;

    @Transient
    @ApiModelProperty(value = "级别显示")
    private String levelShow;

    /**
     * 物资编码
     */
    @Column(name = "flow_no")
    @ApiModelProperty(value = "物资编码")
    private String flowNo;

    /**
     * 物资名称
     */
    @NotNull(message = "物资名称不能为空")
    @ApiModelProperty(value = "Q:物资名称")
    private String name;

    /**
     * 规格型号
     */
    @NotNull(message = "规格型号不能为空")
    @ApiModelProperty(value = "规格型号")
    private String model;

    /**
     * 单位
     */
    @NotNull(message = "单位不能为空")
    @DictExistValid(code = MSkuConst.SKU_UNIT, message = "单位不正确")
    @ApiModelProperty(value = "单位")
    private String unit;

    @Transient
    @ApiModelProperty(value = "单位显示")
    private String unitShow;

    /**
     * 最小单位
     */
    @Column(name = "min_unit")
    @DictExistValid(code = MSkuConst.SKU_MINI_UNIT, message = "最小单位不正确")
    @ApiModelProperty(value = "最小单位")
    private String minUnit;

    @Transient
    @ApiModelProperty(value = "最小单位显示")
    private String minUnitShow;

    /**
     * 单位系数
     */
    @Column(name = "unit_coefficient")
    @ApiModelProperty(value = "单位系数")
    private Integer unitCoefficient;

    /**
     * 参考单价
     */
    @Column(name = "price")
    @NotNull(message = "参考单价不能为空")
    @ApiModelProperty(value = "参考单价")
    private BigDecimal price;

    /**
     * 财务科目
     */
    @Column(name = "account_subject")
    @DictExistValid(code = MSkuConst.ACCOUNT_SUBJECT, message = "财务科目不正确")
    @ApiModelProperty(value = "财务科目")
    private String accountSubject;

    @Transient
    @ApiModelProperty(value = "财务科目显示")
    private String accountSubjectShow;

    /**
     * 状态
     */
    @ApiModelProperty(value = "Q:状态")
    private String status;

    @Transient
    @ApiModelProperty(value = "状态显示")
    private String statusShow;

    /**
     * 注册证编号
     */
    @Column(name = "reg_no")
    @ApiModelProperty(value = "注册证编号")
    private String regNo;

    /**
     * 注册附件
     */
    @Column(name = "reg_file")
    @ApiModelProperty(value = "注册附件")
    private String regFile;

    /**
     * 物资图片
     */
    @Column(name = "img_file")
    @ApiModelProperty(value = "物资图片")
    private String imgFile;

    /**
     * 库房ID
     */
    @PkExistValid(table = "c_warehouse", message = "库房不正确")
    @Column(name = "warehouse_id")
    @ApiModelProperty(value = "库房ID")
    private String warehouseId;

    @Transient
    @ApiModelProperty(value = "库房名称")
    private String warehouseName;

    /**
     * 采购类型
     */
    @Column(name = "purchase_type")
    @DictExistValid(code = MSkuConst.PURCHASE_TYPE, message = "采购类型不正确")
    @ApiModelProperty(value = "采购类型")
    private String purchaseType;

    @Transient
    @ApiModelProperty(value = "采购类型显示")
    private String purchaseTypeShow;


    /**
     * 集采类型
     */
    @Column(name = "jclx_type")
    @DictExistValid(code = MSkuConst.JCLX, message = "集采类型不正确")
    @ApiModelProperty(value = "集采类型")
    private String jclxType;

    @Transient
    @ApiModelProperty(value = "集采类型显示")
    private String jclxTypeShow;

    /**
     * 是否高值
     */
    @DictExistValid(code = CommonConst.YES_OR_NO, message = "是否高值不正确")
    @Column(name = "is_high_value")
    @ApiModelProperty(value = "是否高值")
    private String isHighValue;

    @Transient
    @ApiModelProperty(value = "是否高值显示")
    private String isHighValueShow;

    /**
     * 高值分类
     */
    @Column(name = "high_value_cate")
    @DictExistValid(code = MSkuConst.HIGH_VALUE_CATE, message = "高值分类不正确")
    @ApiModelProperty(value = "高值分类")
    private String highValueCate;

    @Transient
    @ApiModelProperty(value = "高值分类显示")
    private String highValueCateShow;

    /**
     * 保质期（月） quality guarantee period
     */
    @ApiModelProperty(value = "保质期（月）")
    private BigDecimal qgp;

    /**
     * 是否收费
     */
    @DictExistValid(code = CommonConst.YES_OR_NO, message = "是否收费不正确")
    @Column(name = "is_charge")
    @ApiModelProperty(value = "是否收费")
    private String isCharge;

    @Transient
    @ApiModelProperty(value = "是否收费显示")
    private String isChargeShow;
    /**
     * 医保类别
     */
    @Column(name = "mi_type")
    @DictExistValid(code = MSkuConst.MI_TYPE, message = "医保类别不正确")
    @ApiModelProperty(value = "医保类别")
    private String miType;

    @Transient
    @ApiModelProperty(value = "医保类别显示")
    private String miTypeShow;

    /**
     * 医保编码 medical insurance
     */
    @Column(name = "mi_no")
    @ApiModelProperty(value = "医保编码")
    private String miNo;

    /**
     * HIS 收费编码
     */
    @Column(name = "his_charge_no")
    @ApiModelProperty(value = "HIS 收费编码")
    private String hisChargeNo;

    /**
     * HIS 收费名称
     */
    @Column(name = "his_charge_name")
    @ApiModelProperty(value = "HIS 收费名称")
    private String hisChargeName;

    @ApiModelProperty(value = "首拼")
    private String sp;

    @ApiModelProperty(value = "全拼")
    private String qp;

    /**
     * 来源
     */
    @DictExistValid(code = MSkuConst.SKU_ORIGIN, message = "来源不正确")
    @ApiModelProperty(value = "来源")
    private String origin;

    @Transient
    @ApiModelProperty(value = "来源显示")
    private String originShow;

    /**
     * 使用方式
     */
    @Column(name = "use_type")
    @DictExistValid(code = MSkuConst.SKU_USE_TYPE, message = "使用方式不正确")
    @ApiModelProperty(value = "使用方式")
    private String useType;

    @Transient
    @ApiModelProperty(value = "使用方式显示")
    private String useTypeShow;

    /**
     * storage location 货位编码
     */
    @Column(name = "sl_no")
    @ApiModelProperty(value = "货位编码")
    private String slNo;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;
}