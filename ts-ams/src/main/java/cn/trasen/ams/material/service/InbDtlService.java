package cn.trasen.ams.material.service;

import cn.trasen.ams.material.model.Inb;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.InbDtl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InbDtlService
 * @Description TODO
 * @date 2025年7月31日 上午9:50:10
 */
public interface InbDtlService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年7月31日 上午9:50:10
     * <AUTHOR>
     */
    Integer save(InbDtl record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年7月31日 上午9:50:10
     * <AUTHOR>
     */
    Integer update(InbDtl record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年7月31日 上午9:50:10
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return InbDtl
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年7月31日 上午9:50:10
     * <AUTHOR>
     */
    InbDtl selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<InbDtl>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年7月31日 上午9:50:10
     * <AUTHOR>
     */
    DataSet<InbDtl> getDataSetList(Page page, InbDtl record);

    void batchInsert(List<InbDtl> inbDtlList);

    void deleteByInbId(String inbId);

    void _deleteByInbId_(String inbId);

    List<InbDtl> getInbDtlListByInbId(String inbId);

    void dataFmt(InbDtl record);
}
