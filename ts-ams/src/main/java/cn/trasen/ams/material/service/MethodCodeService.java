package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.MethodCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MethodCodeService
 * @Description TODO
 * @date 2025年7月25日 下午2:45:01
 */
public interface MethodCodeService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年7月25日 下午2:45:01
     * <AUTHOR>
     */
    Integer save(MethodCode record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年7月25日 下午2:45:01
     * <AUTHOR>
     */
    Integer update(MethodCode record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年7月25日 下午2:45:01
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return MethodCode
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年7月25日 下午2:45:01
     * <AUTHOR>
     */
    MethodCode selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<MethodCode>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年7月25日 下午2:45:01
     * <AUTHOR>
     */
    DataSet<MethodCode> getDataSetList(Page page, MethodCode record);

    void dataFmt(MethodCode record);

    String genFlowNo(String methodCodeId);

    MethodCode selectByModelId(String modelType, String modelId);
}
