package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.MtdCodeRela;

/**
 * @ClassName MtdCodeRelaService
 * @Description TODO
 * @date 2025年7月31日 上午9:50:23
 * <AUTHOR>
 * @version 1.0
 */
public interface MtdCodeRelaService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025年7月31日 上午9:50:23
	 * <AUTHOR>
	 */
	Integer save(MtdCodeRela record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025年7月31日 上午9:50:23
	 * <AUTHOR>
	 */
	Integer update(MtdCodeRela record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025年7月31日 上午9:50:23
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MtdCodeRela
	 * @date 2025年7月31日 上午9:50:23
	 * <AUTHOR>
	 */
	MtdCodeRela selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MtdCodeRela>
	 * @date 2025年7月31日 上午9:50:23
	 * <AUTHOR>
	 */
	DataSet<MtdCodeRela> getDataSetList(Page page, MtdCodeRela record);

	void deleteByModelId(String modelType,String modelId);

	void _deleteByModelId_(String modelType,String modelId);

	MtdCodeRela selectByModelId(String modelType,String modelId);

}
