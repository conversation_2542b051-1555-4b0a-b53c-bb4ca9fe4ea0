package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.StockSeq;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName StockSeqService
 * @Description TODO
 * @date 2025年7月31日 上午9:50:48
 */
public interface StockSeqService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年7月31日 上午9:50:48
     * <AUTHOR>
     */
    Integer save(StockSeq record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年7月31日 上午9:50:48
     * <AUTHOR>
     */
    Integer update(StockSeq record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年7月31日 上午9:50:48
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return StockSeq
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年7月31日 上午9:50:48
     * <AUTHOR>
     */
    StockSeq selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<StockSeq>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年7月31日 上午9:50:48
     * <AUTHOR>
     */
    DataSet<StockSeq> getDataSetList(Page page, StockSeq record);

    void batchInsert(List<StockSeq> stockSeqList);

    /**
     * 根据订单ID删除库存流水记录
     * @param ordId 订单ID
     */
    void deleteByOrdId(String ordId);
}
