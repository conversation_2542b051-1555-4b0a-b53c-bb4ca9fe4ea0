package cn.trasen.ams.material.service;

import cn.trasen.ams.common.model.Warehouse;
import cn.trasen.ams.material.bean.warehouseRela.WarehouseRelaInsertReq;
import cn.trasen.ams.material.bean.warehouseRela.WarehouseRelaVsEmpResp;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.WarehouseRela;
import cn.trasen.homs.core.model.TreeModel;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName WarehouseRelaService
 * @Description TODO
 * @date 2025年7月22日 上午10:19:12
 */
public interface WarehouseRelaService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年7月22日 上午10:19:12
     * <AUTHOR>
     */
    Integer save(WarehouseRela record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年7月22日 上午10:19:12
     * <AUTHOR>
     */
    Integer update(WarehouseRela record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年7月22日 上午10:19:12
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return WarehouseRela
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年7月22日 上午10:19:12
     * <AUTHOR>
     */
    WarehouseRela selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<WarehouseRela>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年7月22日 上午10:19:12
     * <AUTHOR>
     */
    DataSet<WarehouseRela> getDataSetList(Page page, WarehouseRela record);

    List<WarehouseRela> getListNoPage(WarehouseRela record);

    void updateWarehouseRela(WarehouseRelaInsertReq record);

    void appendWarehouseRela(WarehouseRelaInsertReq record);

    void deleteWarehouseRela(WarehouseRelaInsertReq record);

    List<WarehouseRelaVsEmpResp> selectWarehouseRelaEmpList(WarehouseRelaVsEmpResp record);

    DataSet<Warehouse> selectWarehouseByEmpId(Page page, String empId);

    DataSet<Warehouse> selectWarehouseByCategoryId(Page page, String categoryId);

    List<TreeModel> getCateTreeList(Integer level, String whId, String sysType);
}
