package cn.trasen.ams.material.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.material.bean.inb.InbDtlResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.InbDtlMapper;
import cn.trasen.ams.material.model.InbDtl;
import cn.trasen.ams.material.service.InbDtlService;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InbDtlServiceImpl
 * @Description TODO
 * @date 2025年7月31日 上午9:50:10
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class InbDtlServiceImpl implements InbDtlService {

    private static final Logger logger = LoggerFactory.getLogger(InbDtlServiceImpl.class);

    @Autowired
    private InbDtlMapper mapper;

    @Transactional(readOnly = false)
    @Override
    public Integer save(InbDtl record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(InbDtl record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        InbDtl record = new InbDtl();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public InbDtl selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<InbDtl> getDataSetList(Page page, InbDtl record) {
        Example example = new Example(InbDtl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<InbDtl> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public void batchInsert(List<InbDtl> inbDtlList) {

        // 填充插入时候的必要值
        if (CollectionUtils.isEmpty(inbDtlList)) {
            logger.warn("批量插入入库单明细时，传入的列表为空，操作被忽略.");
            return;
        }
        for (InbDtl inbDtl : inbDtlList) {
            if (inbDtl.getId() == null) {
                inbDtl.setId(IdGeneraterUtils.nextId());
            }
            inbDtl.setCreateDate(new Date());
            inbDtl.setUpdateDate(new Date());
            inbDtl.setIsDeleted("N");
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user != null) {
                inbDtl.setCreateUser(user.getUsercode());
                inbDtl.setCreateUserName(user.getUsername());
                inbDtl.setUpdateUser(user.getUsercode());
                inbDtl.setUpdateUserName(user.getUsername());
                inbDtl.setSsoOrgCode(user.getCorpcode());
                inbDtl.setSsoOrgName(user.getOrgName());
                inbDtl.setDeptId(user.getDeptId());
                inbDtl.setDeptName(user.getDeptname());
            }
        }

        mapper.batchInsert(inbDtlList);
    }

    @Transactional(readOnly = false)
    @Override
    public void deleteByInbId(String inbId) {
        Example example = new Example(InbDtl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inbId", inbId);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        InbDtl record = new InbDtl();
        record.setIsDeleted("Y");
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        mapper.updateByExampleSelective(record, example);
    }

    @Transactional(readOnly = false)
    @Override
    public void _deleteByInbId_(String inbId) {
        // 物理删除
        Assert.hasText(inbId, "入库单ID不能为空.");
        Example example = new Example(InbDtl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inbId", inbId);

        // 执行物理删除
        int deletedCount = mapper.deleteByExample(example);

        // 记录删除结果
        if (deletedCount > 0) {
            logger.info("成功删除入库单 {} 的 {} 条明细记录", inbId, deletedCount);
        }
    }

    @Override
    public List<InbDtl> getInbDtlListByInbId(String inbId) {
        Example example = new Example(InbDtl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inbId", inbId);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<InbDtl> records = mapper.selectByExample(example);
        return records;
    }

    @Override
    public List<InbDtlResp> getInbDtlExtListByInbId(String inbId) {
        return mapper.getInbDtlExtListByInbId(inbId);
    }

    @Override
    public void dataFmt(InbDtl record) {

    }
}
