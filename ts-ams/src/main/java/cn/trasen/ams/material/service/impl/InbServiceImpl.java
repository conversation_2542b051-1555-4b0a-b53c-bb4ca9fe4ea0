package cn.trasen.ams.material.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.trasen.BootComm.utils.MD5;
import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.constant.WarehouseConst;
import cn.trasen.ams.common.model.Warehouse;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.WarehouseService;
import cn.trasen.ams.material.bean.inb.InbInsertReq;
import cn.trasen.ams.material.constant.MethodCodeConst;
import cn.trasen.ams.material.constant.OrdConst;
import cn.trasen.ams.material.model.*;
import cn.trasen.ams.material.service.*;
import com.alibaba.fastjson.JSON;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.InbMapper;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InbServiceImpl
 * @Description TODO
 * @date 2025年7月31日 上午9:49:53
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class InbServiceImpl implements InbService {

    @Autowired
    private InbMapper mapper;

    @Autowired
    private MethodCodeService methodCodeService;

    @Autowired
    private BatchService batchService;

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private InbDtlService inbDtlService;

    @Autowired
    private MtdCodeRelaService mtdCodeRelaService;

    @Autowired
    private StockCurService stockCurService;

    @Autowired
    private StockSeqService stockSeqService;
    @Autowired
    private DictService dictService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(Inb record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Inb record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }


    /**
     * @param record:
     * @return void
     * <AUTHOR>
     * @description 编辑和删除的准备工作
     * @date 2025/7/31 16:02
     */
    @Transactional(readOnly = false)
    public void prepare(InbInsertReq record) {
        Inb inb = record.getInb();
        List<InbDtl> inbDtlList = record.getInbDtlList();
        Warehouse warehouse = warehouseService.selectById(inb.getWhId());
        MethodCode methodCode = methodCodeService.selectById(inb.getMtdCodeId());

        // 新增
        if (inb.getId() == null) {
            // 根据方式码进行流水号生成
            String flowNo = methodCodeService.genFlowNo(inb.getMtdCodeId());
            inb.setFlowNo(flowNo);
            inb.setId(IdGeneraterUtils.nextId());
            // 默认状态
            inb.setStat(OrdConst.ORD_STAT_REGED);
            inb.setPrintStat(CommonConst.NO);
            inb.setReturnStat(CommonConst.NO);

        } else {
            //更新
            // 清除之前明细数据
            inbDtlService._deleteByInbId_(inb.getId());
            // 清空批次信息
            batchService._deleteByInbId_(inb.getId());
            // 清空批次码的关系
            mtdCodeRelaService._deleteByModelId_(OrdConst.ORD_TYPE_RK, inb.getId());
        }
        // 计算总价格
        BigDecimal totalAmt = BigDecimal.ZERO;
        List<Batch> batchList = new ArrayList<>();
        for (InbDtl inbDtl : inbDtlList) {
            inbDtl.setInbId(inb.getId());
            inbDtl.setTotalAmt(inbDtl.getPrice().multiply(new BigDecimal(inbDtl.getNum())));
            totalAmt = totalAmt.add(inbDtl.getTotalAmt());
            // 如果是批次管理，则需要为每一条明细生成批次
            if (WarehouseConst.STK_MTD_BATCH.equals(warehouse.getStkMtd())) {
                // 生成批次号
                String batchNo = batchService.genFlowNo();
                inbDtl.setBatchNo(batchNo);
                // 准备批次需要的信息
                Batch batch = new Batch();
                batch.setInbId(inb.getId());
                batch.setBatchNo(batchNo);
                batch.setProdNo(inbDtl.getProdNo());
                batch.setProdDate(inbDtl.getProdDate());
                batch.setExpireDate(inbDtl.getExpireDate());

                batchList.add(batch);
            }
        }
        // 设置入库单的总金额
        inb.setTotalAmt(totalAmt);

        // 插入批次信息
        if (batchList.size() > 0) {
            batchService.batchInsert(batchList);
        }

        //插入与方式码的关系
        MtdCodeRela mtdCodeRela = new MtdCodeRela();
        mtdCodeRela.setMtdCodeId(inb.getMtdCodeId());
        mtdCodeRela.setModelType(OrdConst.ORD_TYPE_RK);
        mtdCodeRela.setModelId(inb.getId());
        mtdCodeRela.setMtdCodeJson(JSON.toJSONString(methodCode));

        mtdCodeRelaService.save(mtdCodeRela);

    }

    @Transactional(readOnly = false)
    @Override
    public void insert(InbInsertReq record) {

        // 准备部分
        prepare(record);

        Inb inb = record.getInb();
        List<InbDtl> inbDtlList = record.getInbDtlList();

        // 写入入库单
        save(inb);

        // 写入入库单详情
        inbDtlService.batchInsert(inbDtlList);

    }

    @Transactional(readOnly = false)
    @Override
    public void edit(InbInsertReq record) {
        // 准备部分
        prepare(record);

        Inb inb = record.getInb();
        List<InbDtl> inbDtlList = record.getInbDtlList();
        // 写入入库单
        update(inb);
        // 写入入库单详情
        inbDtlService.batchInsert(inbDtlList);
    }

    @Override
    public void remove(String inbId) {
        Assert.hasText(inbId, "入库单ID不能为空.");
        Inb inb = selectById(inbId);

        
        if (OrdConst.ORD_STAT_CHECKED.equals(inb.getStat())) {
            throw new IllegalArgumentException("已确认的入库单不能删除，请先回滚确认。");
        }

        deleteById(inbId);
        // 删除入库单明细
        inbDtlService.deleteByInbId(inbId);
        // 删除批次信息
        batchService.deleteByInbId(inbId);
        // 删除与方式码的关系
        mtdCodeRelaService.deleteByModelId(OrdConst.ORD_TYPE_RK, inbId);
    }

    @Transactional(readOnly = false)
    @Override
    public void rollbackConfirm(String inbId) {
        Assert.hasText(inbId, "入库单ID不能为空.");

        Inb inb = selectById(inbId);
        if (inb == null) {
            throw new IllegalArgumentException("入库单不存在");
        }

        // 检查入库单状态，只有已确认的单据才能回滚
        if (!OrdConst.ORD_STAT_CHECKED.equals(inb.getStat())) {
            throw new IllegalArgumentException("只有已确认的入库单才能进行回滚操作");
        }

        if (!OrdConst.ORD_RETURN_STAT_NO_RETURN.equals(inb.getReturnStat())) {
            throw new IllegalArgumentException("已发生退货的入库单不能进行回滚操作");
        }

        // 获取入库单明细
        List<InbDtl> inbDtlList = inbDtlService.getInbDtlListByInbId(inbId);
        if (CollectionUtils.isEmpty(inbDtlList)) {
            throw new IllegalArgumentException("入库单明细数据缺失，无法完成回滚");
        }

        // 获取方式码信息
        MethodCode methodCode = methodCodeService.selectByModelId(OrdConst.ORD_TYPE_RK, inbId);
        if (methodCode == null) {
            throw new IllegalArgumentException("当前入库单没有对应的入库方式，无法完成回滚");
        }

        // 构造库存回滚数据（与确认时相反的操作）
        List<StockCur> stockCurList = new ArrayList<>();

        inbDtlList.forEach(inbDtl -> {
            StockCur stockCur = new StockCur();
            stockCur.setSkuId(inbDtl.getSkuId());
            stockCur.setWhId(inb.getWhId());
            stockCur.setBatchNo(inbDtl.getBatchNo());

            // 回滚操作：与确认时相反
            if (MethodCodeConst.METHOD_CODE_TYPE_INC.equals(methodCode.getType())) {
                // 原来是加，现在要减
                stockCur.setNum(inbDtl.getNum() * -1);
            } else {
                // 原来是减，现在要加
                stockCur.setNum(inbDtl.getNum());
            }

            stockCur.setMd5(MD5.string2MD5(inbDtl.getSkuId() + inb.getWhId() + inbDtl.getBatchNo()));
            stockCurList.add(stockCur);
        });

        // 回滚库存
        stockCurService.updateStock(stockCurList);

        // 删除库存流水记录
        stockSeqService.deleteByOrdId(inbId);

        // 更新入库单状态为登记状态
        inb.setStat(OrdConst.ORD_STAT_REGED);
        inb.setPrintStat(CommonConst.NO);
        inb.setReturnStat(CommonConst.NO);
        update(inb);

    }

    @Transactional(readOnly = false)
    @Override
    public void rollbackBatchConfirm(List<String> inbIdList) {
        if (inbIdList == null || inbIdList.isEmpty()) {
            return;
        }

        // 逐个处理每个入库单的回滚
        for (String inbId : inbIdList) {
            try {
                rollbackConfirm(inbId);
            } catch (Exception e) {
                // 记录错误但继续处理其他单据
                throw new RuntimeException("入库单 " + inbId + " 回滚失败: " + e.getMessage(), e);
            }
        }
    }

    @Transactional(readOnly = false)
    @Override
    public void confirm(String inbId) {
        Inb inb = selectById(inbId);
        // 更新实时库存
        List<InbDtl> inbDtlList = inbDtlService.getInbDtlListByInbId(inbId);
        // 构造库存更新需要的数据
        if (CollectionUtils.isEmpty(inbDtlList)) {
            throw new IllegalArgumentException("入库单明细数据确实，无法完成确认");
        }
        MethodCode methodCode = methodCodeService.selectByModelId(OrdConst.ORD_TYPE_RK, inbId);

        if (methodCode == null) {
            throw new IllegalArgumentException("当前入库单没有对应的入库方式，无法完成确认");
        }


        List<StockCur> stockCurList = new ArrayList<>();
        List<StockSeq> stockSeqList = new ArrayList<>();

        inbDtlList.forEach(inbDtl -> {
            // 组装更新库存的数据
            StockCur stockCur = new StockCur();
            stockCur.setSkuId(inbDtl.getSkuId());
            stockCur.setWhId(inb.getWhId());
            stockCur.setBatchNo(inbDtl.getBatchNo());
            if (MethodCodeConst.METHOD_CODE_TYPE_INC.equals(methodCode.getType())) {
                // 加
                stockCur.setNum(inbDtl.getNum());
            } else {
                // 减
                stockCur.setNum(inbDtl.getNum() * -1);
            }
            stockCur.setMd5(MD5.string2MD5(inbDtl.getSkuId() + inb.getWhId() + inbDtl.getBatchNo()));
            stockCurList.add(stockCur);

            // 组装时序库存的记录数据
            StockSeq stockSeq = new StockSeq();
            stockSeq.setOrdId(inbId);
            stockSeq.setOrdDtlId(inbDtl.getId());
            stockSeq.setOrdType(OrdConst.ORD_TYPE_RK);
            stockSeq.setSkuId(inbDtl.getSkuId());
            stockSeq.setWhId(inb.getWhId());
            stockSeq.setBatchNo(inbDtl.getBatchNo());
            stockSeq.setNum(inbDtl.getNum());
            stockSeqList.add(stockSeq);

        });

        // 更新实时库存
        stockCurService.updateStock(stockCurList);
        // 添加库存时序记录
        stockSeqService.batchInsert(stockSeqList);

        // 更新入库单状态为已确认
        inb.setStat(OrdConst.ORD_STAT_CHECKED);
        update(inb);

    }


    @Transactional(readOnly = false)
    @Override
    public void batchConfirm(List<String> inbIdList) {
        if (inbIdList == null || inbIdList.isEmpty()) {
            return;
        }

        // 逐个处理每个入库单的确认
        for (String inbId : inbIdList) {
            try {
                confirm(inbId);
            } catch (Exception e) {
                // 记录错误但继续处理其他单据
                throw new RuntimeException("入库单 " + inbId + " 确认失败: " + e.getMessage(), e);
            }
        }
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        Inb record = new Inb();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Inb selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        Inb inb = mapper.selectByPrimaryKey(id);
        dataFmt(inb);
        return inb;
    }

    @Override
    public DataSet<Inb> getDataSetList(Page page, Inb record) {
        List<Inb> records = mapper.getList(page, record);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public void dataFmt(Inb record) {
        record.setStatShow(dictService.cgetNameByValue(OrdConst.ORD_STAT, record.getStat()));
        record.setReturnStatShow(dictService.cgetNameByValue(OrdConst.ORD_RETURN_STAT, record.getReturnStat()));
        record.setPrintStatShow(dictService.cgetNameByValue(CommonConst.YES_OR_NO, record.getPrintStat()));
    }
}
