package cn.trasen.ams.material.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.constant.DataModifyLogConst;
import cn.trasen.ams.common.service.DataModifyLogService;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.SerialNoGenService;
import cn.trasen.ams.common.util.CommonUtil;
import cn.trasen.ams.material.constant.MSkuConst;
import cn.trasen.ams.material.service.WarehouseRelaService;
import cn.trasen.homs.core.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.MSkuMapper;
import cn.trasen.ams.material.model.MSku;
import cn.trasen.ams.material.service.MSkuService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MSkuServiceImpl
 * @Description TODO
 * @date 2025年7月24日 上午8:51:19
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MSkuServiceImpl implements MSkuService {

    @Autowired
    private MSkuMapper mapper;

    @Autowired
    private SerialNoGenService serialNoGenService;

    @Autowired
    private DictService dictService;

    @Autowired
    private DataModifyLogService dataModifyLogService;

    @Autowired
    private WarehouseRelaService warehouseRelaService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(MSku record) {

        autoFillColumn(record);
        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }

        // 记录修改日志
        dataModifyLogService.writeLog("m_sku", record.getId(), record.getFlowNo(),
                record.getName(), DataModifyLogConst.CREATE, record, null);

        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(MSku record) {

        MSku o = selectById(record.getId());

        autoFillColumn(record);
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }

        // 记录修改日志
        dataModifyLogService.writeLog("m_sku", o.getId(), o.getFlowNo(),
                o.getName(), DataModifyLogConst.UPDATE, o, record);

        return mapper.updateByPrimaryKeySelective(record);
    }


    private void autoFillColumn(MSku record) {
        if (StringUtil.isEmpty(record.getId())) {
            record.setStatus(CommonConst.YES);
            record.setFlowNo(genFlowNo());
        }

        if (!StringUtil.isEmpty(record.getName())) {
            record.setSp(CommonUtil.toPinyinFirst(record.getName()));
            record.setQp(CommonUtil.toPinyinFull(record.getName()));
        }
    }

    private String genFlowNo() {
        return serialNoGenService.genByDate("WZZD");
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        MSku record = new MSku();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }

        MSku o = selectById(record.getId());

        dataModifyLogService.writeLog("m_sku", o.getId(), o.getFlowNo(),
                o.getName(), DataModifyLogConst.DELETE, o, null);

        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public MSku selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<MSku> getDataSetList(Page page, MSku record) {

        // 如果传入了warehouseId，并且categoryId 为空 则通过仓库ID查询对应的物资分类ID，表示查这个库房下面的所有物资字典
        if (!StringUtil.isEmpty(record.getWarehouseId()) && StringUtil.isEmpty(record.getCategoryId())) {
            List<String> cateIdList = warehouseRelaService.getCateIdListByWhId(record.getWarehouseId());
            if (cateIdList != null && !cateIdList.isEmpty()) {
                record.setCategoryIdList(cateIdList);
            } else {
                record.setCategoryIdList(Collections.emptyList());
            }
        }

        List<MSku> records = mapper.getList(page, record);
        // dataFmt
        records.forEach(this::dataFmt);

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public List<MSku> getListNoPage(MSku record) {
        List<MSku> mSkusList = mapper.getListNoPage(record);
        mSkusList.forEach(this::dataFmt);
        return mSkusList;
    }

    public void dataFmt(MSku record) {
        record.setLevelShow(dictService.cgetNameByValue(MSkuConst.SKU_LEVEL, record.getLevel()));
        record.setUnitShow(dictService.cgetNameByValue(MSkuConst.SKU_UNIT, record.getUnit()));
        record.setMinUnitShow(dictService.cgetNameByValue(MSkuConst.SKU_MINI_UNIT, record.getMinUnit()));
        record.setAccountSubjectShow(dictService.cgetNameByValue(MSkuConst.ACCOUNT_SUBJECT, record.getAccountSubject()));
        record.setStatusShow(dictService.cgetNameByValue(CommonConst.YES_OR_NO, record.getStatus()));
        record.setPurchaseTypeShow(dictService.cgetNameByValue(MSkuConst.PURCHASE_TYPE, record.getPurchaseType()));
        record.setJclxTypeShow(dictService.cgetNameByValue(MSkuConst.JCLX, record.getJclxType()));
        record.setIsHighValueShow(dictService.cgetNameByValue(CommonConst.YES_OR_NO, record.getIsHighValue()));
        record.setHighValueCateShow(dictService.cgetNameByValue(MSkuConst.HIGH_VALUE_CATE, record.getHighValueCate()));
        record.setIsChargeShow(dictService.cgetNameByValue(MSkuConst.HIGH_VALUE_CATE, record.getIsCharge()));
        record.setMiTypeShow(dictService.cgetNameByValue(MSkuConst.MI_TYPE, record.getMiType()));
        record.setOriginShow(dictService.cgetNameByValue(MSkuConst.SKU_ORIGIN, record.getOrigin()));
        record.setUseTypeShow(dictService.cgetNameByValue(MSkuConst.SKU_USE_TYPE, record.getUseType()));
        record.setIsChargeShow(dictService.cgetNameByValue(CommonConst.YES_OR_NO, record.getIsCharge()));
    }
}
