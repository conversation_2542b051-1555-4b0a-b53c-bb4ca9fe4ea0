package cn.trasen.ams.material.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.model.Warehouse;
import cn.trasen.ams.common.service.DictService;
import cn.trasen.ams.common.service.RedisService;
import cn.trasen.ams.common.service.SerialNoGenService;
import cn.trasen.ams.common.service.WarehouseService;
import cn.trasen.ams.common.util.CommonUtil;
import cn.trasen.ams.material.constant.MethodCodeConst;
import cn.trasen.ams.material.model.MtdCodeRela;
import cn.trasen.ams.material.service.MtdCodeRelaService;
import cn.trasen.homs.bean.base.HrmsOrganizationResp;
import cn.trasen.homs.core.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.MethodCodeMapper;
import cn.trasen.ams.material.model.MethodCode;
import cn.trasen.ams.material.service.MethodCodeService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MethodCodeServiceImpl
 * @Description TODO
 * @date 2025年7月25日 下午2:45:01
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MethodCodeServiceImpl implements MethodCodeService {

    @Autowired
    private MethodCodeMapper mapper;

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private DictService dictService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private SerialNoGenService serialNumberService;

    @Autowired
    private MtdCodeRelaService mtdCodeRelaService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(MethodCode record) {

        autoFillColumn(record);

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }

        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(MethodCode record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    private void autoFillColumn(MethodCode record) {
        if (StringUtil.isEmpty(record.getId())) {
            record.setStatus(CommonConst.YES);
        }
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        MethodCode record = new MethodCode();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public MethodCode selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<MethodCode> getDataSetList(Page page, MethodCode record) {
        Example example = new Example(MethodCode.class);
        Example.Criteria criteria = example.createCriteria();

        // method 查询
        if (!StringUtil.isEmpty(record.getMethod())) {
            criteria.andEqualTo("method", record.getMethod());
        }
        // name 查询
        if (!StringUtil.isEmpty(record.getName())) {
            criteria.andLike("name", "%" + record.getName() + "%");
        }
        // 状态查询
        if (!StringUtil.isEmpty(record.getStatus())) {
            criteria.andEqualTo("status", record.getStatus());
        }

        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");

        // order by
        example.setOrderByClause("seq_no,create_date desc");
        List<MethodCode> records = mapper.selectByExampleAndRowBounds(example, page);

        // dataFmt
        records.forEach(this::dataFmt);

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }


    public void dataFmt(MethodCode record) {
        // 状态翻译
        record.setMethodShow(dictService.cgetNameByValue(MethodCodeConst.RECEIPT_METHOD, record.getMethod()));
        record.setTypeShow(dictService.cgetNameByValue(MethodCodeConst.METHOD_CODE_TYPE, record.getType()));
        record.setStatusShow(dictService.cgetNameByValue(CommonConst.YES_OR_NO, record.getStatus()));
        record.setFlowNoNeedYearShow(dictService.cgetNameByValue(CommonConst.YES_OR_NO, record.getFlowNoNeedYear()));
        record.setFlowNoNeedMonthShow(dictService.cgetNameByValue(CommonConst.YES_OR_NO, record.getFlowNoNeedMonth()));
        record.setFlowNoNeedDayShow(dictService.cgetNameByValue(CommonConst.YES_OR_NO, record.getFlowNoNeedDay()));
        // 仓库翻译
        List<String> warehouseIdList = CommonUtil.string2List(record.getWarehouseIdSet());
        // 循环warehouseIdList

        List<String> warehouseNameList = new ArrayList<>();
        warehouseIdList.forEach(warehouseId -> {
            try {
                Warehouse warehouse = (Warehouse) redisService.fetch("warehouseDetail" + warehouseId, () -> {
                    return warehouseService.selectById(warehouseId);
                }, 300);
                if (warehouse != null) {
                    warehouseNameList.add(warehouse.getName());
                }
            } catch (Exception e) {
                throw new RuntimeException("仓库翻译出错");
            }
        });

        record.setWarehouseNameSet(CommonUtil.list2String(warehouseNameList));

    }

    @Override
    public String genFlowNo(String methodCodeId) {
        // 这里实际上只需要关注给
        MethodCode methodCode = selectById(methodCodeId);
        // 根据配置生成日期模板
        String tpl = "";
        if (CommonConst.YES.equals(methodCode.getFlowNoNeedYear())) {
            tpl += "yyyy";
        }
        if (CommonConst.YES.equals(methodCode.getFlowNoNeedMonth())) {
            tpl += "MM";
        }
        if (CommonConst.YES.equals(methodCode.getFlowNoNeedDay())) {
            tpl += "dd";
        }
        if (StringUtil.isEmpty(tpl)) {
            tpl = "yyyyMMdd";
        }
        int length = 4;

        try {
            length = Integer.parseInt(methodCode.getFlowNoLen());
        } catch (Exception e) {
            throw new RuntimeException("方式码流水号长度配置错误");
        }

        return serialNumberService.genByDate(methodCode.getFlowNoPrefix(), tpl, length);
    }

    @Override
    public MethodCode selectByModelId(String modelType, String modelId) {
        MtdCodeRela mtdCodeRela = mtdCodeRelaService.selectByModelId(modelType, modelId);
        if (mtdCodeRela == null) {
            return null;
        }
        // 根据方式码ID查询方式码
        return selectById(mtdCodeRela.getMtdCodeId());
    }
}
