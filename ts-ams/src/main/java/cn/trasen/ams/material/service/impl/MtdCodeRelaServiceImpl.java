package cn.trasen.ams.material.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.MtdCodeRelaMapper;
import cn.trasen.ams.material.model.MtdCodeRela;
import cn.trasen.ams.material.service.MtdCodeRelaService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MtdCodeRelaServiceImpl
 * @Description TODO
 * @date 2025年7月31日 上午9:50:23
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MtdCodeRelaServiceImpl implements MtdCodeRelaService {

    @Autowired
    private MtdCodeRelaMapper mapper;

    @Transactional(readOnly = false)
    @Override
    public Integer save(MtdCodeRela record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(MtdCodeRela record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        MtdCodeRela record = new MtdCodeRela();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public MtdCodeRela selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<MtdCodeRela> getDataSetList(Page page, MtdCodeRela record) {
        Example example = new Example(MtdCodeRela.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<MtdCodeRela> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Transactional(readOnly = false)
    @Override
    public void deleteByModelId(String modelType, String modelId) {
        // 逻辑删除
        Assert.hasText(modelType, "模型类型不能为空.");
        Assert.hasText(modelId, "模型ID不能为空.");

        Example example = new Example(MtdCodeRela.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("modelType", modelType);
        criteria.andEqualTo("modelId", modelId);
        criteria.andEqualTo("isDeleted", "N");

        // 构造更新对象
        MtdCodeRela updateRecord = new MtdCodeRela();
        updateRecord.setIsDeleted("Y");
        updateRecord.setUpdateDate(new Date());

        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            updateRecord.setUpdateUser(user.getUsercode());
            updateRecord.setUpdateUserName(user.getUsername());
        }

        // 执行逻辑删除
        mapper.updateByExampleSelective(updateRecord, example);
    }

    @Transactional(readOnly = false)
    @Override
    public void _deleteByModelId_(String modelType, String modelId) {
        //  物理删除
        Example example = new Example(MtdCodeRela.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("modelId", modelId);
        criteria.andEqualTo("modelType", modelType);
        mapper.deleteByExample(example);
    }

    @Override
    public MtdCodeRela selectByModelId(String modelType, String modelId) {
        Example example = new Example(MtdCodeRela.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("modelId", modelId);
        criteria.andEqualTo("modelType", modelType);
        return mapper.selectOneByExample(example);
    }
}
