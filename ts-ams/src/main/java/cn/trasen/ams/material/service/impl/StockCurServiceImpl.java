package cn.trasen.ams.material.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.StockCurMapper;
import cn.trasen.ams.material.model.StockCur;
import cn.trasen.ams.material.service.StockCurService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName StockCurServiceImpl
 * @Description TODO
 * @date 2025年7月31日 上午9:50:36
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class StockCurServiceImpl implements StockCurService {

	@Autowired
	private StockCurMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(StockCur record) {

		if(record.getId() == null){
			record.setId(IdGeneraterUtils.nextId());
		}

		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(user.getCorpcode());
			record.setSsoOrgName(user.getOrgName());
			record.setDeptId(user.getDeptId());
			record.setDeptName(user.getDeptname());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(StockCur record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		StockCur record = new StockCur();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public StockCur selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<StockCur> getDataSetList(Page page, StockCur record) {
		Example example = new Example(StockCur.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<StockCur> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public void updateStock(List<StockCur> stockCurList) {
		if (stockCurList == null || stockCurList.isEmpty()) {
			return;
		}

		// 先提取所有的md5，通过md5定位到哪些行已经存在数据，进行更新操作
		// 否则执行批量插入操作

		// 1. 提取所有MD5值
		List<String> md5List = stockCurList.stream()
			.map(StockCur::getMd5)
			.filter(md5 -> md5 != null && !md5.trim().isEmpty())
			.distinct()
			.collect(java.util.stream.Collectors.toList());

		if (md5List.isEmpty()) {
			// 如果没有MD5值，直接批量插入
			prepareForInsert(stockCurList);
			mapper.batchInsert(stockCurList);
			return;
		}

		// 2. 查询已存在的记录
		List<StockCur> existingRecords = mapper.selectByMd5List(md5List);
		Set<String> existingMd5Set = existingRecords.stream()
			.map(StockCur::getMd5)
			.collect(java.util.stream.Collectors.toSet());

		// 3. 分离需要更新和插入的记录
		List<StockCur> recordsToUpdate = new ArrayList<>();
		List<StockCur> recordsToInsert = new ArrayList<>();

		for (StockCur stockCur : stockCurList) {
			if (stockCur.getMd5() != null && existingMd5Set.contains(stockCur.getMd5())) {
				// 需要更新的记录
				prepareForUpdate(stockCur);
				recordsToUpdate.add(stockCur);
			} else {
				// 需要插入的记录
				prepareForInsert(Arrays.asList(stockCur));
				recordsToInsert.add(stockCur);
			}
		}

		// 4. 执行批量更新
		if (!recordsToUpdate.isEmpty()) {
			mapper.batchUpdate(recordsToUpdate);
		}

		// 5. 执行批量插入
		if (!recordsToInsert.isEmpty()) {
			mapper.batchInsert(recordsToInsert);
		}
	}

	/**
	 * 为插入操作准备数据
	 * @param stockCurList 库存记录列表
	 */
	private void prepareForInsert(List<StockCur> stockCurList) {
		Date now = new Date();
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();

		for (StockCur stockCur : stockCurList) {
			if (stockCur.getId() == null) {
				stockCur.setId(IdGeneraterUtils.nextId());
			}

			stockCur.setCreateDate(now);
			stockCur.setUpdateDate(now);
			stockCur.setIsDeleted("N");

			if (user != null) {
				stockCur.setCreateUser(user.getUsercode());
				stockCur.setCreateUserName(user.getUsername());
				stockCur.setUpdateUser(user.getUsercode());
				stockCur.setUpdateUserName(user.getUsername());
				stockCur.setSsoOrgCode(user.getCorpcode());
				stockCur.setSsoOrgName(user.getOrgName());
				stockCur.setDeptId(user.getDeptId());
				stockCur.setDeptName(user.getDeptname());
			}
		}
	}

	/**
	 * 为更新操作准备数据
	 * @param stockCur 库存记录
	 */
	private void prepareForUpdate(StockCur stockCur) {
		Date now = new Date();
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();

		stockCur.setUpdateDate(now);

		if (user != null) {
			stockCur.setUpdateUser(user.getUsercode());
			stockCur.setUpdateUserName(user.getUsername());
			stockCur.setSsoOrgCode(user.getCorpcode());
			stockCur.setSsoOrgName(user.getOrgName());
			stockCur.setDeptId(user.getDeptId());
			stockCur.setDeptName(user.getDeptname());
		}
	}
}
