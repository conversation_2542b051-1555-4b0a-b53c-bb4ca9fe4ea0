package cn.trasen.ams.material.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import cn.trasen.ams.common.constant.WarehouseConst;
import cn.trasen.ams.common.model.Category;
import cn.trasen.ams.common.model.Warehouse;
import cn.trasen.ams.common.service.CategoryService;
import cn.trasen.ams.common.service.WarehouseService;
import cn.trasen.ams.material.bean.warehouseRela.WarehouseRelaInsertReq;
import cn.trasen.ams.material.bean.warehouseRela.WarehouseRelaVsEmpResp;
import cn.trasen.ams.material.constant.WarehouseRelaConst;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.WarehouseRelaMapper;
import cn.trasen.ams.material.model.WarehouseRela;
import cn.trasen.ams.material.service.WarehouseRelaService;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName WarehouseRelaServiceImpl
 * @Description TODO
 * @date 2025年7月22日 上午10:19:12
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class WarehouseRelaServiceImpl implements WarehouseRelaService {

    @Autowired
    private WarehouseRelaMapper mapper;

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private CategoryService categoryService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(WarehouseRela record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(WarehouseRela record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        WarehouseRela record = new WarehouseRela();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public WarehouseRela selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<WarehouseRela> getDataSetList(Page page, WarehouseRela record) {
        Example example = new Example(WarehouseRela.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<WarehouseRela> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    @Override
    public List<WarehouseRela> getListNoPage(WarehouseRela record) {
        Example example = new Example(WarehouseRela.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("warehouseId", record.getWarehouseId());
        criteria.andEqualTo("modelType", record.getModelType());
        return mapper.selectByExample(example);
    }

    @Transactional(readOnly = false)
    public void deleteWarehouseRela(String warehouseId, String modelType) {
        Example example = new Example(WarehouseRela.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("warehouseId", warehouseId);
        criteria.andEqualTo("modelType", modelType);
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        mapper.deleteByExample(example);
    }

    @Transactional(readOnly = false)
    @Override
    public void updateWarehouseRela(WarehouseRelaInsertReq record) {

        if (StringUtil.isEmpty(record.getWarehouseId())) {
            throw new RuntimeException("仓库ID必填");
        }

        if (StringUtil.isEmpty(record.getModelType())) {
            throw new RuntimeException("没有指定关系类型");
        }

        // 删除之前的关系
        deleteWarehouseRela(record.getWarehouseId(), record.getModelType());

        // 如果没有关系ID集合则不需要新增
        if (record.getRelaIdList() == null || record.getRelaIdList().isEmpty()) {
            return;
        }
        // 写入新关系
        for (String modelId : record.getRelaIdList()) {

            WarehouseRela rela = new WarehouseRela();
            rela.setWarehouseId(record.getWarehouseId());
            rela.setModelType(record.getModelType());
            rela.setModelId(modelId);
            save(rela);

        }
    }

    @Transactional(readOnly = false)
    @Override
    public void appendWarehouseRela(WarehouseRelaInsertReq record) {
        // 查询当前仓库和类型下的关系
        // 如果没有关系则直接新增
        // 如果有关系则不变
        // 可以把存在的关系的relaId做一个集合 然后循环新传入的relaIdList 如果不在关系列表中，则新增，否则continue
        if (StringUtil.isEmpty(record.getWarehouseId())) {
            throw new RuntimeException("仓库ID必填");
        }
        if (StringUtil.isEmpty(record.getModelType())) {
            throw new RuntimeException("没有指定关系类型");
        }
        if (CollectionUtils.isEmpty(record.getRelaIdList())) {
            throw new RuntimeException("关联的ID集合不能为空");
        }
        // 查询当前仓库和类型下的关系
        WarehouseRela getListNoPageReq = new WarehouseRela();
        getListNoPageReq.setWarehouseId(record.getWarehouseId());
        getListNoPageReq.setModelType(record.getModelType());

        List<WarehouseRela> relaList = getListNoPage(getListNoPageReq);
        // 把存在的关系的relaId做一个集合 然后循环新传入的relaIdList 如果不在关系列表中，则新增，否则continue
        // 优化：先将已存在的modelId放入Set，避免每次循环都stream
        Set<String> existModelIds = relaList.stream().map(WarehouseRela::getModelId).collect(Collectors.toSet());
        for (String modelId : record.getRelaIdList()) {
            if (!existModelIds.contains(modelId)) {
                WarehouseRela rela = new WarehouseRela();
                rela.setWarehouseId(record.getWarehouseId());
                rela.setModelType(record.getModelType());
                rela.setModelId(modelId);
                save(rela);
            }
        }
    }

    @Transactional(readOnly = false)
    @Override
    public void deleteWarehouseRela(WarehouseRelaInsertReq record) {

        if (StringUtil.isEmpty(record.getWarehouseId())) {
            throw new RuntimeException("仓库ID必填");
        }
        if (StringUtil.isEmpty(record.getModelType())) {
            throw new RuntimeException("没有指定关系类型");
        }
        if (CollectionUtils.isEmpty(record.getRelaIdList())) {
            throw new RuntimeException("关联的ID集合不能为空");
        }
        // 根据 warehouseId 和 modelType 和 relaIdList in 进行软删除
        Example example = new Example(WarehouseRela.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("warehouseId", record.getWarehouseId());
        criteria.andEqualTo("modelType", record.getModelType());
        criteria.andIn("modelId", record.getRelaIdList());
        criteria.andEqualTo("isDeleted", "N");

        WarehouseRela update = new WarehouseRela();
        update.setIsDeleted("Y");
        update.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            update.setUpdateUser(user.getUsercode());
            update.setUpdateUserName(user.getUsername());
        }
        mapper.updateByExampleSelective(update, example);
    }

    @Override
    public List<WarehouseRelaVsEmpResp> selectWarehouseRelaEmpList(WarehouseRelaVsEmpResp record) {
        return mapper.selectWarehouseRelaEmpList(record);
    }

    @Override
    public DataSet<Warehouse> selectWarehouseByEmpId(Page page, String empId) {
        // 根据员工ID查询仓库
        if (StringUtil.isEmpty(empId)) {
            throw new RuntimeException("员工ID不能为空");
        }
        Example example = new Example(WarehouseRela.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("modelType", WarehouseRelaConst.MODEL_TYPE_EMP);
        criteria.andEqualTo("modelId", empId);
        criteria.andEqualTo("isDeleted", "N");
        List<WarehouseRela> relaList = mapper.selectByExample(example);
        List<String> warehouseIds = relaList.stream().map(WarehouseRela::getWarehouseId).collect(Collectors.toList());
        return warehouseService.getDataSetByIdList(page, warehouseIds);

    }

    @Override
    public DataSet<Warehouse> selectWarehouseByCategoryId(Page page, String categoryId) {
        // 根据分类ID查询仓库
        if (StringUtil.isEmpty(categoryId)) {
            throw new RuntimeException("分类ID不能为空");
        }
        Example example = new Example(WarehouseRela.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("modelType", WarehouseRelaConst.MODEL_TYPE_CATEGORY);
        criteria.andEqualTo("modelId", categoryId);
        criteria.andEqualTo("isDeleted", "N");
        List<WarehouseRela> relaList = mapper.selectByExample(example);
        List<String> warehouseIds = relaList.stream().map(WarehouseRela::getWarehouseId).collect(Collectors.toList());

        return warehouseService.getDataSetByIdList(page, warehouseIds);
    }

    @Override
    public List<TreeModel> getCateTreeListByWhId(String whId) {
        WarehouseRela rela = new WarehouseRela();
        rela.setWarehouseId(whId);
        rela.setModelType(WarehouseRelaConst.MODEL_TYPE_CATEGORY);
        List<WarehouseRela> warehouseRelaList = getListNoPage(rela);

        List<String> categoryIdList = warehouseRelaList.stream()
                .map(WarehouseRela::getModelId)
                .collect(Collectors.toList());

        return categoryService.getTreeListByIds(categoryIdList);
    }

    // 根据仓库ID查询所有分类ID
    @Override
    public List<String> getCateIdListByWhId(String whId) {
        WarehouseRela rela = new WarehouseRela();
        rela.setWarehouseId(whId);
        rela.setModelType(WarehouseRelaConst.MODEL_TYPE_CATEGORY);
        List<WarehouseRela> warehouseRelaList = getListNoPage(rela);

        List<String> categoryIdList = warehouseRelaList.stream()
                .map(WarehouseRela::getModelId)
                .collect(Collectors.toList());
        return categoryIdList;
    }

}
