appconfig:
  OAOpenGatewayRibbon:
    enabled: false
  domain: "http://testmoa.trasen.cn/"
  wxDomain: "http://testmoa.trasen.cn/"
  login: "http://testmoa.trasen.cn/"
  wxLoginUrl: "http://testmoa.trasen.cn/mobile-container/login"
  whiteUrlList:
    - "/static/"
    - "/swagger"
    - "/springfox-swagger-ui"
    - "/v2/api-docs"
    - "/static/"
    - "/favicon.ico"
    - "/doc.html"
    - "/webjars"
    - "/swagger-ui.html"
    - "/swagger-resources"
    - "/api/ToaCheckRoute/savetest"
    - "/globalSetting/getGlobalSetting"
    - "/boardroomElectronicScreen/handle"
    - "/boardroomElectronicScreen/test"
    - "/boardroomElectronicScreen/img/"
    - "/product/catalog/update/buyCount/"
    - "/hardware/productInfo/update/"
    - "/employee/valdiateUser"
    - "/employee/resetPassword"
    - "/employee/getEmployeeListByModel"
    - "/employee/selectEmpByUserCode"
    - "/employee/getEmployeeByBirthday"
    - "/employee/getEmployeeByHiredate"
    - "/poserver.zz"
    - "/loginseal.zz"
    - "/adminseal.zz"
    - "/sealimage.zz"
    - "/attachView/richfile"
    - "/govSendfile/openSendFileForm"
    - "/preview/"
    - "/druid/"
    - "/attachment/selectInternalByIds"
    - "/thpsSysetm/clearWxuserid"
    - "/thpsSysetm/updateWxuserid"
    - "/boardRoomES/"
    - "/thpsSystem/syncRoleUserData"
    - "/thpsSystem/modifyUserPwd"
    - "/thpsSystem/modifyUserStatus"
    - "/contractTemplate/openTemplateFileForm"
    - "/contractRecord/openContractFileForm"
  whiteParamsList:
    - "imageBase64"
logging:
  config: classpath:logback-custom.xml

#datasource:
#  name: mysql

server:
  port: 9021
  servlet:
    context-path: /ts-ams
  tomcat:
    uri-encoding: UTF-8
    #最小线程数
    min-spare-threads: 500
    #最大线程数
    max-threads: 2500
    #最大链接数
    max-connections: 6500
    #最大等待队列长度
    accept-count: 1000
    #请求头最大长度kbf
    max-http-header-size: 1048576
    #请请求体最大长度kb
    #max-http-post-size: 2097152
#    max-http-post-size: -1
registry:
  # type: nacos
  type: eureka
  #Eureka注册中心配置
eureka:
  client:
    service-url:
      defaultZone: http://**************:8761/eureka
      #register-with-eureka: false
      #fetch-registry: true
      enabled: true
  instance:
    ip-address: ${spring.cloud.client.ip-address}
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    prefer-ip-address: true

spring:
  application:
    name: ts-ams
  cloud:
    nacos:
      config:
        server-addr: 127.0.0.1:8848 #配置中心地址
        file-extension: yml #指定yaml格式的配置
        auto-refresh: false # 是否启用动态刷新配置
        encode: utf-8 # 编码
        enabled: false
      discovery:
        server-addr: 127.0.0.1:8848 #服务注册中心地址
        enabled: false
    sentinel:
      transport:
        dashboard: 127.0.0.1:8718
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    #driver-class-name: com.kingbase8.Driver
    #url: ********************************************************************
    #username: system
    #password: Trasen_123
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************************************
    username: root
    password: 123456
    druid:
      initialSize: 1
      min-idle: 1
      max-active: 20
      keep-alive: true
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 60000
      validation-query: select 1
      validation-query-timeout: 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      use-global-data-source-stat: true
      remove-abandoned: true
      remove-abandoned-timeout: 1800
      pool-prepared-statements: true
      max-open-prepared-statements: 50
      #aop-patterns: cn.trasen.*
      filters: stat,slf4j
      stat-view-servlet:
        enabled: true
        login-username: xtbg
        login-password: 123456@Xtbg
      web-stat-filter: #web监控
        enabled: true
        url-pattern: /*
        exclusions: '*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*'
      filter:
        stat: #sql监控
          slow-sql-millis: 1000
          log-slow-sql: true
          enabled: true
          db-type: mysql
        wall: #防火墙
          enabled: false
          multi-statement-allow: true
          db-type: mysql
        config:
          drop-table-allow: false

  freemarker:
    checkTemplateLocation: false
  redis:
    host: **************
    password: 123456
    pool:
      max-active: 8
      max-idle: 8
      max-wait: -1
      min-idle: 0
    port: 6379
    timeout: 0
  #  jackson:
  #    serialization:
  #      # 某些类对象无法序列化的时候，是否报错
  #      fail_on_empty_beans: true
  #    deserialization:
  #       # json对象中有不存在的属性时候，是否报错
  #      fail_on_unknown_properties: true
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
      location: D:\Data\tmp
feign:
  client:
    config:
      default:
        connectTimeout: 10000 # 连接超时时间，单位为毫秒
        readTimeout: 10000 # 读取超时时间，单位为毫秒

springfox:
  base-package: cn.trasen
  description: 数字化运营平台-协同办公系统接口平台
  enable: true
  service-url: http://localhost:9109/ts-oa
  title: 协同办公接口管理
  version: 1.0.1

mybatis:
  config-location: classpath:mybatis-config.xml
  mapper-locations: classpath*:**/mapper/*Mapper.xml

moralityStatDate: 2021-04

QR_CODE_VALID_TIME: 60


sso:
  defaultPrikey: MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAseG6INcVyA1vr4PpSr7tojShqTPdq86NnHTjU6QEqpLhKH8hQJdwDRW1tlG4svFPf7kOvy8c3+x4vZc0xywdEwIDAQABAkAT4QIIAYFxpe7BUqCTtdqgsfkPC7jOJns07Osqwb2zwh6pU9KUyk0O5yp6CNfHTGcH3THnYbI1nTyzKqqJnfQBAiEA2uUSg22bat6O5UIlEjVQrsB6hcecwRC63rwRSV7JUYECIQDQCOUjvq44CDWsesT8DVmpIGhHGCmVBHt7gyc0qRpQkwIgHnbdIb+Cbtg0qQGQqT0UUo3lP3MthM0wRMmF2mE/wYECIEg7Pmw50b3sw84eVnT5oa8CbJJ6xj1ScBmDgUJckKF3AiBQeY8dS/XSvcETlq/KVA4nF8ksV2Yx1sFdA4K+1F8+rg==
  defaultPubkey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALHhuiDXFcgNb6+D6Uq+7aI0oakz3avOjZx041OkBKqS4Sh/IUCXcA0VtbZRuLLxT3+5Dr8vHN/seL2XNMcsHRMCAwEAAQ==


parentDeptCode: ''
parentPDCode: ''
ssoDbType: mysql
subUser: 0
syncSystemThpsUserSwitch: 0
#tsSystemUrl: ******************************************************************
#tsSystemDriver: com.kingbase8.Driver
#tsSystemUsername: system
#tsSystemPwd: Trasen_123

tsSystemDriver: com.mysql.jdbc.Driver
tsSystemUrl: *************************************************************************************************************************************************************************************
tsSystemUsername: root
tsSystemPwd: 123456


corpCode: BHRMYY
deptRoleList: '[{fieldId:"clerkid",fieldName:"clerkname",roleName:"科室秘书",isSyncSubordinate:"0"},{fieldId:"headnurseid",fieldName:"headnursename",roleName:"护士长",isSyncSubordinate:"0"},{fieldId:"directorofdepartmentid",fieldName:"directorofdepartmentname",roleName:"科室主任",isSyncSubordinate:"0"},{fieldId:"medicaldirectorid",fieldName:"medicaldirectorname",roleName:"医疗主任",isSyncSubordinate:"0"},{fieldId:"departmentheadid",fieldName:"departmentheadname",roleName:"部门领导",isSyncSubordinate:"0"},{fieldId:"managementleadid",fieldName:"managementleadname",roleName:"分管领导",isSyncSubordinate:"0"},{fieldId:"directleadershipid",fieldName:"directleadershipname",roleName:"直接领导",isSyncSubordinate:"0"},{fieldId:"departmentephorid",fieldName:"departmentephorname",roleName:"部门长",isSyncSubordinate:"0"}]'

roleCode: PTYH
salaryPath: F:/javaCode/TS-OA/web/view/personal
roleId:

#企业微信考勤打卡配置
wxClockSwitch: 0
wechat:
  cp:
    agentId:
    corpId:
    secret:

#企业微信部门人员同步
syncWeixinSwitch: 0
syncWeixinSwitchSecret:


wx:
  loginPage: http://testmoa.trasen.cn/mobile-container/login
  loginUrl: http://testmoa.trasen.cn/mobile-container/ts-mobile-oa/
  switch: 1


filePathUrl: http://127.0.0.1:9088/ts-oa/attachment/downloadFile/
fileSystem: /tmp/trasen/data/attachment
fileSystemPath: /tmp/trasen/data/attachment
forbiddenType: .dll.exe.jar.bat
maxFileSize: 104857600


previewURL: http://**************:9088/ts-preview/addTask

posyspath: D:/home/<USER>/data/lic
popassword: 111111


ribbon:
  NFLoadBalancerRuleClassName: com.netflix.loadbalancer.ZoneAvoidanceRule #配置规则 随机
  ConnectTimeout: 5000 #请求连接超时时间
  ReadTimeout: 10000 #请求处理的超时时间


#北海道闸接口
roadGateUrl:

#人员同步集成平台开发
syncIntegrationPlatformSwitch: 0
syncIntegrationPlatformUrl:


#经费管理流程
fund-entry-workflow-no:
fund-use-workflow-no:
fund-changes-workflow-no:
fund-reimbursement-workflow-no:
fund-supporting-apply-workflow-no:
#经费财务科负责人code用于推送消息
fund-finance-section:

env: "dev"
#sso免登陆接口获取token地址
form-external-ssoUrl: http://192.168.18.228:9099
notify-url-header:
