/*=============================================================*/
/* Table: COMM_ERROR_LOGS                                       */
/* 功能模块:  系统异常日志				                               */
/* 提交人: lijr                                                  */
/* 提交时间:20243-04-21                                           */
/*=============================================================*/
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.comm_error_logs 
    ( 
        ID            VARCHAR(36) NOT NULL comment '唯一标识', 
        SERVICE_NAME  VARCHAR(50) comment '微服务名称', 
        CLASS_NAME    VARCHAR(100) comment '类名称', 
        METHOD_NAME   VARCHAR(100) comment '方法名称', 
        EXCEPTION_MSG VARCHAR(500) comment '异常信息', 
        STACK_TRACE   TEXT comment '异常详细信息', 
        CREATE_TIME   DATETIME comment '异常产生时间', 
        PRIMARY KEY (ID) 
    ) 
    ENGINE=InnoDB COMMENT='系统异常日志采集';

CREATE TABLE IF NOT EXISTS
    `ts_wo`.comm_error_logs 
    ( 
        ID            VARCHAR(36) NOT NULL comment '唯一标识', 
        SERVICE_NAME  VARCHAR(50) comment '微服务名称', 
        CLASS_NAME    VARCHAR(100) comment '类名称', 
        METHOD_NAME   VARCHAR(100) comment '方法名称', 
        EXCEPTION_MSG VARCHAR(500) comment '异常信息', 
        STACK_TRACE   TEXT comment '异常详细信息', 
        CREATE_TIME   DATETIME comment '异常产生时间', 
        PRIMARY KEY (ID) 
    ) 
    ENGINE=InnoDB COMMENT='系统异常日志采集';

        
/*=============================================================*/
/* Table: wf_instance_snapshot                                       */
/* 功能模块:  流程审批快照表				                               */
/* 提交人: 陈彬                                                  */
/* 提交时间:20243-05-09                                         */
/*=============================================================*/ 
CREATE TABLE IF NOT EXISTS `ts_base_oa`.`wf_instance_snapshot` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `wf_instance_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '流程实例ID',
  `wf_task_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '流程任务ID',
  `main_form_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '流程主表数据',
  `son_form_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '流程子表单数据',
  `create_date` datetime DEFAULT NULL,
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `sso_org_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `sso_org_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `is_deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_committee` (
     `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
     `employee_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工ID',
     `employee_name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工姓名',
     `post_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '党内职务id',
     `post` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '党内职务',
     `forhowlong` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任期',
     `files_id` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件id',
     `remark` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
     `enterprise_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业ID',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者ID',
     `create_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者姓名',
     `update_date` datetime DEFAULT NULL COMMENT '更新时间',
     `update_user` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者ID',
     `update_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者姓名',
     `org_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组织机构ID',
     `org_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组织机构名称',
     `is_deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除标识: Y=是; N=否;',
     `sso_org_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构名称',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='党委班子表';

CREATE TABLE IF NOT EXISTS `ts_base_oa`.`hrms_committee_employee` (
     `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
     `committee_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '党委班子表id',
     `employee_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工ID',
     `employee_name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工姓名',
     `post_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '党内职务id',
     `post` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '党内职务',
     `input_date` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任职日期',
     `output_date` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '离任日期',
     `files_id` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件id',
     `remark` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
     `enterprise_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业ID',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `create_user` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者ID',
     `create_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者姓名',
     `update_date` datetime DEFAULT NULL COMMENT '更新时间',
     `update_user` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者ID',
     `update_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者姓名',
     `org_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组织机构ID',
     `org_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组织机构名称',
     `is_deleted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除标识: Y=是; N=否;',
     `sso_org_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构编码',
     `sso_org_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构名称',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='党委成员表';

/***
 * custom_code 科室自定义编码（设备管理）
 */
set @exist := (select count(1) from information_schema.columns 
		where table_name = 'comm_organization' and COLUMN_NAME = 'custom_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`comm_organization` ADD `custom_code` VARCHAR(36)  NULL  DEFAULT NULL  COMMENT ''科室自定义编码（设备管理）'' ',
    'select ''INFO: custom_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'comm_error_logs' and COLUMN_NAME = 'org_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`comm_error_logs` ADD `org_code` VARCHAR(50)  NULL  DEFAULT NULL  COMMENT ''机构编码'' ',
    'select ''INFO: org_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'comm_error_logs' and COLUMN_NAME = 'org_code' and table_schema = 'ts_wo');
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_wo`.`comm_error_logs` ADD `org_code` VARCHAR(50)  NULL  DEFAULT NULL  COMMENT ''机构编码'' ',
    'select ''INFO: org_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'comm_error_logs' and COLUMN_NAME = 'is_sync' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`comm_error_logs` ADD `is_sync` VARCHAR(1)  NULL  DEFAULT NULL  COMMENT ''传输标志Y/N'' ',
    'select ''INFO: custom_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'comm_error_logs' and COLUMN_NAME = 'is_sync' and table_schema = 'ts_wo');
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_wo`.`comm_error_logs` ADD `is_sync` VARCHAR(1)  NULL  DEFAULT NULL  COMMENT ''传输标志Y/N'' ',
    'select ''INFO: custom_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


set @exist := (select count(1) from information_schema.columns 
		where table_name = 'wf_definition_info' and COLUMN_NAME = 'share_org_user_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`wf_definition_info` ADD `share_org_user_code` VARCHAR(1000)  NULL  DEFAULT NULL  COMMENT ''共享机构'' ',
    'select ''INFO: custom_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'wf_definition_info' and COLUMN_NAME = 'share_org_user_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`wf_definition_info` ADD `share_org_user_name` VARCHAR(1000)  NULL  DEFAULT NULL  COMMENT ''共享机构名称'' ',
    'select ''INFO: custom_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'zt_gov_sendfile' and COLUMN_NAME = 'main_file' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`zt_gov_sendfile` ADD `main_file` VARCHAR(100)  NULL  DEFAULT NULL  COMMENT ''正文附件'' ',
    'select ''INFO: custom_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'hrms_employee' and COLUMN_NAME = 'carNo' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_employee` ADD `carNo` VARCHAR(100)  NULL  DEFAULT NULL  COMMENT ''车牌号'' ',
    'select ''INFO: custom_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_employee' and COLUMN_NAME = 'job_attributes' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_employee` ADD `job_attributes` VARCHAR(80)  NULL  DEFAULT NULL  COMMENT ''岗位属性'' ',
                   'select ''INFO: custom_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_committee' and COLUMN_NAME = 'sso_org_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_committee` ADD `sso_org_code` VARCHAR(64)  NULL  DEFAULT NULL  COMMENT ''机构编码'' ',
                   'select ''INFO: custom_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_committee' and COLUMN_NAME = 'sso_org_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_committee` ADD `sso_org_name` VARCHAR(64)  NULL  DEFAULT NULL  COMMENT ''机构名称'' ',
                   'select ''INFO: custom_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_committee_employee' and COLUMN_NAME = 'sso_org_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_committee_employee` ADD `sso_org_code` VARCHAR(64)  NULL  DEFAULT NULL  COMMENT ''机构编码'' ',
                   'select ''INFO: custom_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_committee_employee' and COLUMN_NAME = 'sso_org_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_committee_employee` ADD `sso_org_name` VARCHAR(64)  NULL  DEFAULT NULL  COMMENT ''机构名称'' ',
                   'select ''INFO: custom_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_salary_item' and COLUMN_NAME = 'sso_org_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_salary_item` ADD `sso_org_code` VARCHAR(64)  NULL  DEFAULT NULL  COMMENT ''机构编码'' ',
                   'select ''INFO: custom_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_salary_item' and COLUMN_NAME = 'sso_org_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_salary_item` ADD `sso_org_name` VARCHAR(64)  NULL  DEFAULT NULL  COMMENT ''机构名称'' ',
                   'select ''INFO: sso_org_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_personnel_incident' and COLUMN_NAME = 'subsidy_money' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_personnel_incident` ADD `subsidy_money` VARCHAR(64)  NULL  DEFAULT NULL  COMMENT ''津贴补助'' ',
                   'select ''INFO: subsidy_money 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_personnel_incident' and COLUMN_NAME = 'subsidy_money' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_personnel_incident` ADD `retired_money` VARCHAR(64)  NULL  DEFAULT NULL  COMMENT ''退休工资'' ',
                   'select ''INFO: subsidy_money 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_gov_send_user' and COLUMN_NAME = 'form_file_id' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_gov_send_user` ADD `form_file_id` VARCHAR(50)  NULL  DEFAULT NULL  COMMENT ''表单内容附件id'' ',
    'select ''INFO: form_file_id 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/*=============================================================*/
/* Table: COMM_ERROR_LOGS                                       */
/* 功能模块:  任务督办				                               */
/* 提交人: yuechang                                                  */
/* 提交时间:20243-04-30                                           */
/*=============================================================*/
CREATE TABLE IF NOT EXISTS `toa_supervise_logs`  (
  `id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
  `register_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '登记id',
  `task_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务id',
  `log_type` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '日志类型 1登记 2指派 3办理 4验收 5批示 6催办 7延期 8撤销  9终止 10转办  11抄送 12进度反馈',
  `log_remark` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作内容说明',
  `log_file` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '附件',
  `log_date` date NULL DEFAULT NULL COMMENT '操作日期',
  `copy_leader_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '抄送人名称',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
  `update_user` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '督办操作日志表' ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `toa_supervise_register`  (
  `id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
  `register_matter` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '督办事项',
  `register_remark` varchar(3000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '督办描述',
  `register_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
  `register_urgency` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '紧急程度 1普通 2紧急 3非常紧急',
  `complete_date` date NULL DEFAULT NULL COMMENT '计划完成时间',
  `extension_date` date NULL DEFAULT NULL COMMENT '延期完成时间',
  `actual_date` date NULL DEFAULT NULL,
  `register_abel` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标签',
  `register_files` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '附件',
  `register_status` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态 0草稿  1 办理中  2待验收  3待批示 4已完结  5撤销  6终止',
  `assist_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '协办人code',
  `assist_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '协办人姓名',
  `register_user` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `register_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `multiple_dept` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否多部门承办',
  `delay_date` date NULL DEFAULT NULL COMMENT '延期日期',
  `check_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '验收人code',
  `check_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '验收人姓名',
  `approve_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '批示人code',
  `approve_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '批示人姓名',
  `copy_leader_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '抄送人编码',
  `copy_leader_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '抄送人姓名',
  `finish_date` datetime NULL DEFAULT NULL COMMENT '办结时间',
  `approve_time` decimal(11, 2) NULL DEFAULT NULL COMMENT '批示花费时间',
  `handle_time` decimal(11, 2) NULL DEFAULT NULL COMMENT '处理花费时间',
  `check_time` decimal(11, 2) NULL DEFAULT NULL COMMENT '验收花费时间',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
  `update_user` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除标示',
  `task_date` date NULL DEFAULT NULL,
  `task_desc` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `task_file` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务登记表' ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `toa_supervise_task`  (
  `id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
  `register_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '登记id',
  `task_code` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '承办人code',
  `task_name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '承办人姓名',
  `task_dept_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '承办科室编码',
  `task_dept_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '承办科室名称',
  `task_remark` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `task_handle_remark` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '处理说明',
  `task_handle_date` datetime NULL DEFAULT NULL COMMENT '处理时间',
  `copy_leader` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否抄送分管领导',
  `assist_code` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '协办人code',
  `assist_name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '协办人姓名',
  `copy_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '抄送人code',
  `copy_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '抄送人姓名',
  `task_date` date NULL DEFAULT NULL COMMENT '指派日期',
  `task_desc` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '指派说明',
  `task_file` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '附件',
  `task_status` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态 0草稿 1 办理中  2待验收  3待批示 4已完结 ',
  `task_handle_time` decimal(11, 2) NULL DEFAULT NULL COMMENT '处理花费时间',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
  `update_user` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '督办任务表' ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `toa_supervise_type`  (
  `id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
  `type_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型名称',
  `type_status` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态  0停用 1正常',
  `supervise_user_code` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '督办人',
  `supervise_user_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '督办人名称',
  `check_user_code` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '验收人',
  `check_user_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '验收人名称',
  `approve_user_code` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '批示人',
  `approve_user_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '批示人名称',
  `overdue_day` int NULL DEFAULT NULL COMMENT '超期设置（天）',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
  `update_user` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务督办类型' ROW_FORMAT = Dynamic;


INSERT ignore INTO `ts_base_oa`.`comm_dict_type`(`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`) VALUES ('761866161137709056', 'SALARY_TYPE', '薪资类别', '薪资类别', 'HRMS', 'admin', 'admin', SYSDATE(), NULL, NULL, NULL, 'N', NULL, '432816904746987520', '445897456614420480', '信息科');
INSERT ignore INTO `ts_base_oa`.`comm_dict_item`(`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`) VALUES ('762120019143356416', '761866161137709056', 'jbgz', '基本工资', '', '', 1, 'HRMS', 'admin', 'admin',SYSDATE(), NULL, NULL, NULL, 'N', NULL, '432816904746987520', '445897456614420480', '信息科', '1', 0, '1');
INSERT ignore INTO `ts_base_oa`.`comm_dict_item`(`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`) VALUES ('762120356470255616', '761866161137709056', 'jxgz', '绩效工资', '', '', 2, 'HRMS', 'admin', 'admin',SYSDATE(), NULL, NULL, NULL, 'N', NULL, '432816904746987520', '445897456614420480', '信息科', '2', 0, '1');

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_supervise_register' and COLUMN_NAME = 'is_overdue' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_supervise_register` ADD `is_overdue` VARCHAR(2)  NULL  DEFAULT NULL  COMMENT ''是否超期 0未超期  1即将超期  2已超期'' ',
    'select ''INFO: is_overdue 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


/*=============================================================*/
/* Table: civil                                       */
/* 功能模块:  民政对象脚本				                               */
/* 提交人: wch                                                  */
/* 提交时间:2024-06-01                                         */
/*=============================================================*/ 
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_admission_information
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主表id',
        placement_institutions VARCHAR(100) COMMENT '转安置机构',
        type VARCHAR(50) COMMENT '类型',
        placement_date VARCHAR(50) COMMENT '安置日期',
        in_date VARCHAR(50) COMMENT '入院日期',
        ward VARCHAR(50) COMMENT '病区',
        bed VARCHAR(50) COMMENT '床位',
        county VARCHAR(255) COMMENT '区县',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='入院信息';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_account_handover
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        account_management_id VARCHAR(36) COMMENT '账号管理ID',
        name VARCHAR(50) COMMENT '姓名',
        id_card VARCHAR(100) COMMENT '身份证号',
        bank VARCHAR(50) COMMENT '开户行',
        account VARCHAR(100) COMMENT '账号',
        account_balance DECIMAL(32,2) COMMENT '账户余额',
        registration_date DATE COMMENT '登记日期',
        deliverer VARCHAR(50) COMMENT '交付人',
        recipient VARCHAR(50) COMMENT '接收人',
        certifier VARCHAR(50) COMMENT '证明人',
        handover_date DATE COMMENT '交接日期',
        remarks VARCHAR(100) COMMENT '备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        type INT COMMENT '类型1接收账号，2退还账号',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='账户移交记录';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_account_management
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主键',
        name VARCHAR(50) COMMENT '姓名',
        id_card VARCHAR(100) COMMENT '身份证号',
        bank VARCHAR(50) COMMENT '开户行',
        account VARCHAR(100) COMMENT '账号',
        account_balance DECIMAL(32,2) COMMENT '账户余额',
        registration_date DATE COMMENT '登记日期',
        deliverer VARCHAR(50) COMMENT '交付人',
        recipient VARCHAR(50) COMMENT '接收人',
        certifier VARCHAR(50) COMMENT '证明人',
        handover_date DATE COMMENT '交接日期',
        remarks VARCHAR(100) COMMENT '备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        status INT COMMENT '状态:1代管中，2已退还',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='账户管理';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_annual_budget
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        YEAR VARCHAR(50) COMMENT '年度',
        entry_code VARCHAR(100) COMMENT '项目编码',
        entry_name VARCHAR(100) COMMENT '项目名称',
        subject_code VARCHAR(100) COMMENT '科目编码',
        subject VARCHAR(100) COMMENT '科目',
        budget_amount DECIMAL(32,2) COMMENT '预算金额',
        remark VARCHAR(2000) COMMENT '备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        files VARCHAR(500) COMMENT '附件',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='年度预算';

CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_authority
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        person_id VARCHAR(1000) COMMENT '人员id',
        person_name VARCHAR(1000) COMMENT '人员名称',
        group_id VARCHAR(1000) COMMENT '内容组id',
        group_name VARCHAR(1000) COMMENT '内容组名称',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='权限设置';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_bill_maintenance
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        purchase_date DATE COMMENT '购入日期',
        NUMBER INT COMMENT '数量',
        begin_coding VARCHAR(50) COMMENT '起始号码',
        end_codeing VARCHAR(50) COMMENT '迄止号码',
        requisition_name VARCHAR(50) COMMENT '领用人',
        requisition_dept VARCHAR(50) COMMENT '领用部门',
        requisition_date DATE COMMENT '领用时间',
        payback_name VARCHAR(50) COMMENT '缴回人',
        payback_date DATE COMMENT '缴回日期',
        invoice_amount DECIMAL(32,2) COMMENT '开票金额',
        verification_date DATE COMMENT '核销日期',
        bill_name VARCHAR(50) COMMENT '票据管理人',
        remarks VARCHAR(1000) COMMENT '备注',
        status VARCHAR(10) COMMENT '状态:1有效,2作废',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci;
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_change_expenditure
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        manage_id VARCHAR(36) NOT NULL COMMENT '管理对象主键ID',
        serial_number VARCHAR(50) COMMENT '流水号',
        purchase_date DATE COMMENT '采购日期',
        goods_id VARCHAR(36) COMMENT '物资主键ID',
        goods_name VARCHAR(100) COMMENT '物品名称',
        goods_name_pym VARCHAR(100) COMMENT '物品名称拼音码',
        price DECIMAL(32,2) COMMENT '单价',
        NUMBER INT COMMENT '数量',
        total_price DECIMAL(32,2) COMMENT '总价',
        Recipient VARCHAR(50) COMMENT '领用人',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        status VARCHAR(10) COMMENT '状态1正常,2已作废',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='零钱支出';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_change_ledger
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        manage_id VARCHAR(36) NOT NULL COMMENT '管理对象主键ID',
        bill_number VARCHAR(50) COMMENT '单据号',
        serial_number VARCHAR(50) COMMENT '流水号',
        register_date DATE COMMENT '登记时间',
        amount_collected DECIMAL(32,2) COMMENT '收款金额',
        expenditure_amount DECIMAL(32,2) COMMENT '支出金额',
        balance_amount DECIMAL(32,2) COMMENT '结存金额',
        ledger_items VARCHAR(2000) COMMENT '台账事项',
        relatives_autograph VARCHAR(100) COMMENT '家属签名',
        type VARCHAR(10) COMMENT '类型:1收入，2支出',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        status VARCHAR(10) COMMENT '状态1正常,2已作废',
        review_status VARCHAR(10) COMMENT '审核状态1待审核,2已审核',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='零钱台账';

CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_change_manage
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        bill_number VARCHAR(50) COMMENT '单据号',
        personnel_id VARCHAR(36) COMMENT '民政对象人员主键',
        name VARCHAR(50) COMMENT '姓名',
        pym VARCHAR(50) COMMENT '姓名拼音码',
        identity_number VARCHAR(50) COMMENT '身份证号',
        object_type VARCHAR(10) COMMENT '对象类型(1民政对象，2区县寄养对象，3家属寄养对象)',
        ward VARCHAR(50) COMMENT '所属病区',
        ward_text VARCHAR(100) COMMENT '所属病区名称',
        change_giver VARCHAR(50) COMMENT '零钱给予者',
        transaction_type VARCHAR(10) COMMENT '交易类型:1现金,2扫码',
        change_amount DECIMAL(32,2) COMMENT '零钱金额',
        change_balance DECIMAL(32,2) COMMENT '零钱余额',
        register_date DATE COMMENT '登记时间',
        payee_name VARCHAR(50) COMMENT '收款人',
        prover_name VARCHAR(50) COMMENT '证明人',
        files VARCHAR(50) COMMENT '附件',
        remarks VARCHAR(1000) COMMENT '备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        status VARCHAR(10) COMMENT '状态1正常,2已作废',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='零钱登记管理（对象）';

CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_change_register
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        manage_id VARCHAR(36) NOT NULL COMMENT '管理对象主键ID',
        bill_number VARCHAR(50) COMMENT '单据号',
        personnel_id VARCHAR(36) COMMENT '民政对象人员主键',
        name VARCHAR(50) COMMENT '姓名',
        pym VARCHAR(50) COMMENT '姓名拼音码',
        identity_number VARCHAR(50) COMMENT '身份证号',
        object_type VARCHAR(10) COMMENT '对象类型(1民政对象，2区县寄养对象，3家属寄养对象)',
        ward VARCHAR(50) COMMENT '所属病区',
        ward_text VARCHAR(100) COMMENT '所属病区名称',
        change_giver VARCHAR(50) COMMENT '零钱给予者',
        transaction_type VARCHAR(10) COMMENT '交易类型:1现金,2扫码',
        change_amount DECIMAL(32,2) COMMENT '零钱金额',
        change_balance DECIMAL(32,2) COMMENT '零钱余额',
        register_date DATE COMMENT '登记时间',
        payee_name VARCHAR(50) COMMENT '收款人',
        prover_name VARCHAR(50) COMMENT '证明人',
        files VARCHAR(50) COMMENT '附件',
        remarks VARCHAR(1000) COMMENT '备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        status VARCHAR(10) COMMENT '状态1正常,2已作废',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='零钱登记';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_change_return
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        manage_id VARCHAR(36) NOT NULL COMMENT '管理对象主键ID',
        dept_name VARCHAR(100) COMMENT '科室名称',
        dept_code VARCHAR(50) COMMENT '科室代码',
        return_date DATE COMMENT '退还时间',
        change_balance DECIMAL(32,2) COMMENT '当前余额',
        refund_amount DECIMAL(32,2) COMMENT '退还金额',
        payee VARCHAR(50) COMMENT '领款人',
        bookkeeper VARCHAR(50) COMMENT '记账人',
        custodian VARCHAR(50) COMMENT '代管责任人',
        relationship VARCHAR(50) COMMENT '与患者关系',
        remarks VARCHAR(1000) COMMENT '备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        status VARCHAR(10) COMMENT '状态1正常,2已作废',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='零钱退还';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_field
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        group_name VARCHAR(255) COMMENT '分组名称',
        group_id VARCHAR(36) COMMENT '分组id',
        show_name VARCHAR(255) COMMENT '显示名称',
        field_name VARCHAR(255) COMMENT '字段名称',
        field_type VARCHAR(50) COMMENT '字段类型',
        dict_source VARCHAR(50) COMMENT '字典来源',
        field_length INT COMMENT '字段长度',
        data_format VARCHAR(50) COMMENT '时间格式',
        prompt_text VARCHAR(255) COMMENT '提示文字',
        data_source INT COMMENT '数据来源(1手动录入2常用字段 3 公式编辑4数据字典)',
        is_must INT COMMENT '是否必填',
        is_multiple INT COMMENT '是否多条',
        is_remove_duplicate INT COMMENT '是否去重(0 否 1 是)',
        is_only INT COMMENT '是否只读 (0:否 1 是)',
        is_allow_deleted INT COMMENT '是否允许删除(0否 1 是)',
        is_hide INT COMMENT '是否隐藏字段(0:不是 1 是)',
        is_disabled INT COMMENT '是否停用(0 否 1 是)',
        seq INT COMMENT '排序',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(100) COMMENT '创建人',
        create_user_name VARCHAR(100) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(100) COMMENT '更新人',
        update_user_name VARCHAR(100) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='民政对象自定义字段';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_field_group
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        group_type INT COMMENT '分组类型(1民政对象，2区县寄样对象，3家属寄样对象)',
        group_name VARCHAR(255) COMMENT '分组名称',
        is_allow_deleted INT COMMENT '是否允许删除(0否 1 是)',
        is_disabled INT COMMENT '是否停用(0 否 1 是)',
        seq INT COMMENT '排序',
        table_name VARCHAR(100) COMMENT '表名',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        is_hospitalized INT COMMENT '是否入院办理分组(1是)',
        is_discharge INT COMMENT '是否离院办理分组(1是)',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='民政对象自定义分组表';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_funding_availability
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        YEAR VARCHAR(50) COMMENT '年度',
        entry_code VARCHAR(100) COMMENT '项目编码',
        entry_name VARCHAR(100) COMMENT '项目名称',
        file_number VARCHAR(100) COMMENT '文号',
        funding_source VARCHAR(255) COMMENT '资金来源',
        receive_date DATE COMMENT '到位日期',
        receive_amount DECIMAL(32,2) COMMENT '到位金额',
        remark VARCHAR(2000) COMMENT '备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        files VARCHAR(500) COMMENT '附件',
        budget_id VARCHAR(36) COMMENT '预算ID',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='资金到位情况';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_funding_expenditure
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        YEAR VARCHAR(50) COMMENT '年度',
        entry_code VARCHAR(100) COMMENT '项目编码',
        entry_name VARCHAR(100) COMMENT '项目名称',
        subject_code VARCHAR(100) COMMENT '科目编码',
        subject VARCHAR(100) COMMENT '科目',
        expend_reason VARCHAR(255) COMMENT '支出事由',
        expend_amount DECIMAL(32,2) COMMENT '支出金额',
        expend_date DATE COMMENT '支出时间',
        expend_reimburser VARCHAR(50) COMMENT '支出报销人',
        pay_date DATE COMMENT '支付日期',
        remark VARCHAR(2000) COMMENT '备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        budget_id VARCHAR(36) COMMENT '预算ID',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='资金支出情况';

CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_goods_management
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        goods_name VARCHAR(100) COMMENT '物品名称',
        goods_name_pym VARCHAR(100) COMMENT '物品名称拼音码',
        model VARCHAR(50) COMMENT '规格型号',
        unit VARCHAR(50) COMMENT '单位',
        inventory_number INT COMMENT '库存数量',
        price DECIMAL(32,2) COMMENT '单价',
        supplier_name VARCHAR(200) COMMENT '供应商名称',
        supplier_name_pym VARCHAR(100) COMMENT '供应商名称拼音码',
        purchase_date DATE COMMENT '采购日期',
        deliverer VARCHAR(50) COMMENT '采购人',
        remarks VARCHAR(1000) COMMENT '备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='物品管理';

CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_guarantee_apply
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主表ID',
        apply_date DATE COMMENT '申请日期',
        NUMBER VARCHAR(100) COMMENT '编号',
        name VARCHAR(50) COMMENT '姓名',
        ALIAS VARCHAR(50) COMMENT '别名',
        sex VARCHAR(50) COMMENT '性别',
        birthday VARCHAR(50) COMMENT '出生年月',
        in_date DATE COMMENT '入院时间',
        marriage_status VARCHAR(50) COMMENT '婚姻状况',
        health_status VARCHAR(50) COMMENT '健康状况',
        self_care VARCHAR(50) COMMENT '自理能力',
        educational_level VARCHAR(50) COMMENT '文化程度',
        identity_number VARCHAR(100) COMMENT '身份证号',
        address VARCHAR(255) COMMENT '家庭住址',
        reason VARCHAR(2000) COMMENT '入院理由',
        type INT COMMENT '类型1新增，2核减',
        status INT COMMENT '状态1审核中，2已完成',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='保障申请新增核减';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_message_set
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        expiration_user VARCHAR(1000) COMMENT '证件到期提醒人员code',
        expiration_user_name VARCHAR(1000) COMMENT '证件到期提醒人员名称',
        guarantee_user VARCHAR(1000) COMMENT '保障标准提醒人员code',
        guarantee_user_name VARCHAR(1000) COMMENT '保障标准提醒人员名称',
        withdrawal_user VARCHAR(1000) COMMENT '支取提醒人员code',
        withdrawal_user_name VARCHAR(1000) COMMENT '支取提醒人员名称',
        account_user VARCHAR(1000) COMMENT '代管账号提醒人员code',
        account_user_name VARCHAR(1000) COMMENT '代管账号提醒人员名称',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='消息提醒人员设置';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_personnel_info
    (
        personnel_id VARCHAR(36) NOT NULL COMMENT '主键',
        name VARCHAR(100) COMMENT '姓名',
        sex VARCHAR(20) COMMENT '性别',
        race VARCHAR(50) COMMENT '名族',
        identity_number VARCHAR(100) COMMENT '身份证号',
        birthday DATE COMMENT '出生日期',
        province VARCHAR(100) COMMENT '省',
        city VARCHAR(100) COMMENT '市',
        city_code VARCHAR(100) COMMENT '地址编码',
        county VARCHAR(100) COMMENT '区县',
        address VARCHAR(255) COMMENT '详细地址',
        Issuance VARCHAR(255) COMMENT '签发机构',
        migration_date DATE COMMENT '迁入日期',
        valid_period VARCHAR(100) COMMENT '有效期限',
        effective_start_time VARCHAR(50) COMMENT '有效开始日期',
        effective_end_time VARCHAR(50) COMMENT '有效结束日期',
        processing_date DATE COMMENT '办证日期',
        read_date DATE COMMENT '读证日期',
        object_type INT COMMENT '对象类型(1民政对象，2区县寄养对象，3家属寄养对象)',
        head_feil VARCHAR(100) COMMENT '头像',
        id_card_front VARCHAR(100) COMMENT '身份证正面',
        id_card_back VARCHAR(100) COMMENT '身份证反面',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        pym VARCHAR(50) COMMENT '姓名拼音码',
        PRIMARY KEY (personnel_id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='民政对象自定义主表人员信息';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_withdrawal_detail
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        withdrawal_records_id VARCHAR(36) COMMENT '支取缴存ID',
        type VARCHAR(100) COMMENT '类型',
        amount DECIMAL(32,2) COMMENT '金额',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='支取缴存明细';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_withdrawal_plan
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        account_management_id VARCHAR(36) COMMENT '账号管理ID',
        name VARCHAR(50) COMMENT '姓名',
        id_card VARCHAR(100) COMMENT '身份证号',
        bank VARCHAR(50) COMMENT '开户行',
        account VARCHAR(100) COMMENT '账号',
        proposer VARCHAR(50) COMMENT '提出人',
        planned_amount DECIMAL(32,2) COMMENT '计划支取金额',
        planned_date DATE COMMENT '计划支取日期',
        remarks VARCHAR(1000) COMMENT '备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        files VARCHAR(500) COMMENT '附件',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='支取计划';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_affairs_withdrawal_records
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        account_management_id VARCHAR(36) COMMENT '账号管理ID',
        name VARCHAR(50) COMMENT '姓名',
        id_card VARCHAR(100) COMMENT '身份证号',
        bank VARCHAR(50) COMMENT '开户行',
        account VARCHAR(100) COMMENT '账号',
        withdrawal_date DATE COMMENT '支取日期',
        withdrawal_amount DECIMAL(32,2) COMMENT '支取金额',
        pre_payment_balance DECIMAL(32,2) COMMENT '支前余额',
        post_payment_balance DECIMAL(32,2) COMMENT '支后余额',
        operator VARCHAR(50) COMMENT '经办人',
        certifier VARCHAR(50) COMMENT '证明人',
        files VARCHAR(500) COMMENT '附件',
        remarks VARCHAR(1000) COMMENT '备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='支取缴存记录';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_archiva_files
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主表id',
        archive_category VARCHAR(100) COMMENT '档案类别',
        file_type VARCHAR(100) COMMENT '文件类型',
        file_address VARCHAR(255) COMMENT '文件地址',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='档案文件';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_archival_information
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主表id',
        archival_number VARCHAR(50) COMMENT '档案编号',
        cabinet_number VARCHAR(50) COMMENT '柜子编号',
        box_number VARCHAR(50) COMMENT '盒子编号',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='档案信息';

CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_contract_information
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主表id',
        signing_date VARCHAR(50) COMMENT '签订日期',
        due_date VARCHAR(50) COMMENT '到期日期',
        nursing_expenses VARCHAR(100) COMMENT '护理费标准',
        board_expenses VARCHAR(100) COMMENT '伙食费标准',
        other VARCHAR(2000) COMMENT '其他',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='合同信息';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_county_contacts
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主表id',
        contact_name VARCHAR(50) COMMENT '联系人姓名',
        telephone VARCHAR(50) COMMENT '电话',
        remarks VARCHAR(1000) COMMENT '备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='区县联系人';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_departure_information
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主表id',
        departure_type VARCHAR(50) COMMENT '离院类型',
        death_date VARCHAR(50) COMMENT '死亡日期',
        cremation_date VARCHAR(50) COMMENT '火化日期',
        address VARCHAR(100) COMMENT '住址',
        disease_diagnosis VARCHAR(100) COMMENT '疾病诊断',
        death_reason VARCHAR(100) COMMENT '死亡原因',
        report_doctor VARCHAR(50) COMMENT '报告医生',
        attachments VARCHAR(100) COMMENT '附件',
        remarks VARCHAR(1000) COMMENT '备注',
        departure_date VARCHAR(50) COMMENT '离院日期',
        NUMBER VARCHAR(50) COMMENT '编号',
        origin_name VARCHAR(50) COMMENT '原籍姓名',
        origin_id_card VARCHAR(50) COMMENT '原籍身份证号',
        origin_address VARCHAR(100) COMMENT '原籍地址',
        recipient_name VARCHAR(50) COMMENT '接收姓名',
        recipient_phone VARCHAR(50) COMMENT '接收电话',
        recipient_unit_address VARCHAR(100) COMMENT '接收单位或地址',
        recipient_remarks VARCHAR(1000) COMMENT '接收备注',
        handling_name VARCHAR(50) COMMENT '经办姓名',
        handling_phone VARCHAR(50) COMMENT '经办电话',
        handling_remarks VARCHAR(1000) COMMENT '经办备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        handling_relationship VARCHAR(100) COMMENT '经办关系',
        handling_id_card VARCHAR(50) COMMENT '经办身份证号',
        handling_sex VARCHAR(10) COMMENT '经办性别',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='离院信息';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_disability_certificate
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主表id',
        NUMBER VARCHAR(100) COMMENT '残疾证号',
        issue_date VARCHAR(50) COMMENT '签发日期',
        effective_end_time VARCHAR(50) COMMENT '有效截止日期',
        disability_categories VARCHAR(50) COMMENT '残疾类别',
        disability_level VARCHAR(50) COMMENT '残疾等级',
        relocation_location VARCHAR(255) COMMENT '迁入地点',
        approval_authority VARCHAR(100) COMMENT '批准机关',
        migration_date VARCHAR(50) COMMENT '迁入日期',
        remarks VARCHAR(1000) COMMENT '备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='残疾证信息';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_family_information
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主表id',
        family_name VARCHAR(50) COMMENT '家属姓名',
        relationship VARCHAR(50) COMMENT '关系',
        telephone VARCHAR(50) COMMENT '电话',
        remarks VARCHAR(1000) COMMENT '备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='家属信息';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_hardship_protection
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主表id',
        hardship_date VARCHAR(50) COMMENT '特困日期',
        hardship_standard VARCHAR(50) COMMENT '特困标准',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        remarks VARCHAR(1000) COMMENT '备注',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='特困保障';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_medical_insurance
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主表id',
        insurance_date VARCHAR(50) COMMENT '参保时间',
        category VARCHAR(50) COMMENT '医保类别',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='医保信息';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_nursing_information
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主表id',
        nursing_date VARCHAR(50) COMMENT '护理日期',
        nursing_level VARCHAR(50) COMMENT '护理级别',
        nursing_standard VARCHAR(50) COMMENT '护理标准费',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        remarks VARCHAR(1000) COMMENT '备注',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='护理信息';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_orphan_protection
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主表id',
        orphan_date VARCHAR(50) COMMENT '孤儿日期',
        orphan_standard VARCHAR(50) COMMENT '孤儿待遇标准',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='孤儿保障';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_residence_information
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主表id',
        householder_name VARCHAR(100) COMMENT '户主姓名',
        name VARCHAR(100) COMMENT '姓名',
        householder_nature VARCHAR(100) COMMENT '户主性质',
        householder_relationship VARCHAR(100) COMMENT '户主关系',
        native_place VARCHAR(100) COMMENT '籍贯',
        address VARCHAR(255) COMMENT '住址',
        migration_date VARCHAR(50) COMMENT '迁入日期',
        change_reason VARCHAR(255) COMMENT '变动原因',
        relocation_location VARCHAR(255) COMMENT '迁入地点',
        marital_status VARCHAR(50) COMMENT '婚姻状况',
        agent VARCHAR(50) COMMENT '代办人',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='户籍信息';
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.civil_social_security
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        personnel_id VARCHAR(36) COMMENT '人员主表id',
        lnsured_date VARCHAR(50) COMMENT '参保日期',
        payment_status VARCHAR(50) COMMENT '缴费状态',
        payment_method VARCHAR(50) COMMENT '缴费方式',
        collection_date VARCHAR(50) COMMENT '领取日期',
        claim_criteria VARCHAR(50) COMMENT '领取标准',
        Insured_place VARCHAR(50) COMMENT '参保地',
        transfer_place VARCHAR(50) COMMENT '转出地',
        transfer_date VARCHAR(50) COMMENT '转入日期',
        NUMBER VARCHAR(100) COMMENT '社保卡号',
        bank_name VARCHAR(100) COMMENT '开户行',
        date_opened VARCHAR(50) COMMENT '开户日期',
        remarks VARCHAR(1000) COMMENT '备注',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(50) COMMENT '创建人',
        create_user_name VARCHAR(50) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(50) COMMENT '更新人',
        update_user_name VARCHAR(50) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='社保信息';
	
	
/*=============================================================*/
/* Table: evaluation                                       */
/* 功能模块:  360绩效考评				                               */
/* 提交人: wch                                                  */
/* 提交时间:2024-06-01                                         */
/*=============================================================*/ 

CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.toa_evaluation_master
    (
        id VARCHAR(50) NOT NULL COMMENT '主键',
        title VARCHAR(400) COMMENT '标题',
        remark VARCHAR(1500) COMMENT '描述',
        release_num INT COMMENT '答卷数量',
        status VARCHAR(10) COMMENT '状态    0草稿   1运行中   2已结束  5删除',
        exam_user VARCHAR(4000) COMMENT '被考评人',
        exam_user_name VARCHAR(4000) COMMENT '被考评人名称',
        appraiser VARCHAR(4000) COMMENT '考评人',
        appraiser_name VARCHAR(4000) COMMENT '考评人名称',
        release_date DATETIME COMMENT '发布时间',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(100) COMMENT '创建人',
        create_user_name VARCHAR(100) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(100) COMMENT '更新人',
        update_user_name VARCHAR(100) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        reportduty_url VARCHAR(2000),
        ismsg CHAR(1),
        content VARCHAR(1000),
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci;
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.toa_evaluation_release
    (
        id VARCHAR(50) NOT NULL COMMENT '主键',
        user_code VARCHAR(50) COMMENT '考评人',
        user_name VARCHAR(100) COMMENT '考评人名称',
        release_status INT COMMENT '发布状态  0待提交   1已提交   5删除',
        set_id VARCHAR(50) COMMENT '设置表id',
        master_id VARCHAR(50) COMMENT '主表ID',
        release_user VARCHAR(50) COMMENT '发布人',
        release_user_name VARCHAR(100) COMMENT '发布人名称',
        release_date DATETIME COMMENT '发布时间',
        source VARCHAR(5) COMMENT '来源',
        title VARCHAR(400) COMMENT '标题',
        remark VARCHAR(2000) COMMENT '描述',
        exam_user_name VARCHAR(100) COMMENT '被考评人名称',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(100) COMMENT '创建人',
        create_user_name VARCHAR(100) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(100) COMMENT '更新人',
        update_user_name VARCHAR(100) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci;
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.toa_evaluation_reportduty
    (
        id VARCHAR(50) NOT NULL COMMENT '主键',
        reportduty_title VARCHAR(50) COMMENT '文件标题',
        reportduty_url VARCHAR(200) COMMENT '文件路径',
        duty_name VARCHAR(100) COMMENT '职务名称',
        reportduty_user VARCHAR(50) COMMENT '述职报告人',
        reportduty_user_name VARCHAR(50) COMMENT '述职报告人名称',
        master_id VARCHAR(50) COMMENT '主表id',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(100) COMMENT '创建人',
        create_user_name VARCHAR(100) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(100) COMMENT '更新人',
        update_user_name VARCHAR(100) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci;
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.toa_evaluation_result
    (
        id VARCHAR(50) NOT NULL COMMENT '主键',
        set_id VARCHAR(50) COMMENT '题目id',
        master_id VARCHAR(50) COMMENT '主表id',
        release_id VARCHAR(50) COMMENT '发布id',
        result_user VARCHAR(50) COMMENT '被考评人',
        result_user_name VARCHAR(50) COMMENT '被考评人名称',
        resultu_type INT COMMENT '题目类型    1单选    2多选   3下拉框  4填空',
        result_value VARCHAR(1000) COMMENT '考评值',
        totalscore DOUBLE COMMENT '总分数',
        avgscore DOUBLE COMMENT '平均分',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(100) COMMENT '创建人',
        create_user_name VARCHAR(100) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(100) COMMENT '更新人',
        update_user_name VARCHAR(100) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci;
	
CREATE TABLE IF NOT EXISTS
    `ts_base_oa`.toa_evaluation_set
    (
        id VARCHAR(50) NOT NULL COMMENT '主键',
        option_title VARCHAR(200) COMMENT '题目标题',
        option_remark VARCHAR(1000) COMMENT '题目描述',
        option_type INT COMMENT '题目类型    1单选    2多选   3下拉框  4填空',
        option_value VARCHAR(1000) COMMENT '题目选项值',
        option_score VARCHAR(1000) COMMENT '题目选项值对应分数',
        master_id VARCHAR(50) COMMENT '关联主表id',
        option_sort INT COMMENT '题目排序',
        option_required INT COMMENT '标题',
        select_option VARCHAR(1000) COMMENT '下拉框选项值',
        select_score VARCHAR(1000) COMMENT '下拉框选项分数',
        create_date DATETIME COMMENT '创建日期',
        create_user VARCHAR(100) COMMENT '创建人',
        create_user_name VARCHAR(100) COMMENT '创建人姓名',
        update_date DATETIME COMMENT '更新日期',
        update_user VARCHAR(100) COMMENT '更新人',
        update_user_name VARCHAR(100) COMMENT '更新人姓名',
        is_deleted CHAR(1) COMMENT '是否删除',
        sso_org_code VARCHAR(50) COMMENT '机构编码',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_notice' and COLUMN_NAME = 'clear_user' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_notice` ADD `clear_user` VARCHAR(2000)  NULL  DEFAULT NULL  COMMENT ''删除人员'' ',
    'select ''INFO: clear_user 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_sys_setting' and COLUMN_NAME = 'allow_file_extension' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_sys_setting` ADD `allow_file_extension` VARCHAR(255)  DEFAULT \'peg,jpg,gif,bmp,png,txt,ppt,pptx,xls,xlsx,doc,docx,pdf,wav,mhtml,ofd,zip,7z,war,rar,jpeg\'
COMMENT ''允许上传的附件后缀'' ',
    'select ''INFO: allow_file_extension 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_sys_setting' and COLUMN_NAME = 'allow_file_size' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_sys_setting` ADD `allow_file_size` int(11)  NULL  DEFAULT 200  COMMENT ''允许上传的附件大小（M）'' ',
    'select ''INFO: allow_file_size 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_schedule' and COLUMN_NAME = 'repeat_status' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_schedule` ADD `repeat_status` int  COMMENT ''重复状态'' ',
    'select ''INFO: repeat_status 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
		where table_name = 'toa_schedule' and COLUMN_NAME = 'repeat_end_time' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_schedule` ADD `repeat_end_time` DATE  COMMENT ''重复结束时间'' ',
    'select ''INFO: repeat_end_time 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

CREATE TABLE IF NOT EXISTS `comm_table_snapshot` (
    `id` varchar(50) NOT NULL DEFAULT '',
    `table_name` varchar(50) NOT NULL DEFAULT '' COMMENT '表名称',
    `row_pk_value` varchar(50) DEFAULT NULL COMMENT '行主键值',
    `row_json_old` text COMMENT '行记录旧',
    `row_json_new` text COMMENT '行记录新',
    `create_date` datetime DEFAULT NULL COMMENT '创建时间',
    `create_user` varchar(36) DEFAULT NULL COMMENT '创建人',
    `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建人名称',
    `update_date` datetime DEFAULT NULL COMMENT '修改时间',
    `update_user` varchar(36) DEFAULT NULL COMMENT '修改人',
    `update_user_name` varchar(30) DEFAULT NULL COMMENT '修改人名称',
    `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识',
    `sso_org_code` varchar(36) DEFAULT NULL COMMENT '机构编码',
    `sso_org_name` varchar(36) DEFAULT NULL COMMENT '机构名称',
    PRIMARY KEY (`id`),
    KEY `table_name` (`table_name`,`row_pk_value`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8;


set @exist := (select count(1) from information_schema.columns
		where table_name = 'hrms_newsalary_option_emp' and COLUMN_NAME = 'effective_date' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `hrms_newsalary_option_emp` ADD `effective_date` VARCHAR(30)  NULL  DEFAULT NULL  COMMENT ''生效时间''  AFTER `remark`',
    'select ''INFO: effective_date 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


set @exist := (select count(1) from information_schema.columns 
		where table_name = 'ws_fault_equipment' and COLUMN_NAME = 'equipment_remark' and table_schema = 'ts_wo');
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_wo`.`ws_fault_equipment` ADD `equipment_remark` VARCHAR(255)  COMMENT ''设备描述'' ',
    'select ''INFO: equipment_remark 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'ws_customet_service' and COLUMN_NAME = 'play_voice' and table_schema = 'ts_wo');
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_wo`.`ws_customet_service` ADD `play_voice` int  COMMENT ''语音播报'' ',
    'select ''INFO: play_voice 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'wf_definition_info' and COLUMN_NAME = 'share_org_user_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`wf_definition_info` ADD `share_org_user_code` VARCHAR(255)  COMMENT ''共享机构'' ',
    'select ''INFO: share_org_user_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


set @exist := (select count(1) from information_schema.columns 
		where table_name = 'wf_definition_info' and COLUMN_NAME = 'share_org_user_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`wf_definition_info` ADD `share_org_user_name` VARCHAR(255)  COMMENT ''共享机构'' ',
    'select ''INFO: share_org_user_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

CREATE TABLE IF NOT EXISTS `ts_base_oa`.`wf_intervene_logs`  (
  `id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `opt_content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '操作内容',
  `opt_remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作备注',
  `IS_DELETED` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除标识',
  `CREATE_USER` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_DATE` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_USER` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `UPDATE_DATE` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `CREATE_USER_NAME` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `UPDATE_USER_NAME` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人姓名',
  `sso_org_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_affairs_change_manage' and COLUMN_NAME = 'dept_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_affairs_change_manage` ADD `dept_code` VARCHAR(50)  COMMENT ''创建人所在部门科室'' ',
    'select ''INFO: dept_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
		where table_name = 'toa_critical_value' and COLUMN_NAME = 'sample_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_critical_value` ADD `sample_name` VARCHAR(200)  COMMENT ''检验样本'' ',
    'select ''INFO: sample_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 	
		where table_name = 'toa_critical_value' and COLUMN_NAME = 'bed_doctor_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_critical_value` ADD `bed_doctor_code` VARCHAR(50)  COMMENT ''主管医生工号'' ',
    'select ''INFO: bed_doctor_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_critical_value' and COLUMN_NAME = 'bed_doctor_account' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_critical_value` ADD `bed_doctor_account` VARCHAR(50)  COMMENT ''主管医生账号'' ',
    'select ''INFO: bed_doctor_account 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_critical_value' and COLUMN_NAME = 'bed_doctor_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_critical_value` ADD `bed_doctor_name` VARCHAR(50)  COMMENT ''主管医生姓名'' ',
    'select ''INFO: bed_doctor_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_critical_value' and COLUMN_NAME = 'dept_id' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_critical_value` ADD `dept_id` VARCHAR(50)  COMMENT ''患者科室id'' ',
    'select ''INFO: dept_id 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_critical_value' and COLUMN_NAME = 'criticai_values_json' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_critical_value` ADD `criticai_values_json` VARCHAR(4000)  COMMENT ''危急值内容详情'' ',
    'select ''INFO: criticai_values_json 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_critical_value' and COLUMN_NAME = 'reg_no' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_critical_value` ADD `reg_no` VARCHAR(50)  COMMENT ''病历号'' ',
    'select ''INFO: reg_no 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_critical_value' and COLUMN_NAME = 'receive_user' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_critical_value` ADD `receive_user` VARCHAR(50)  COMMENT ''开单医生'' ',
    'select ''INFO: receive_user 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_critical_value' and COLUMN_NAME = 'sign_date' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_critical_value` ADD `sign_date` VARCHAR(50)  COMMENT ''危急值报告时间'' ',
    'select ''INFO: receive_user 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_affairs_change_expenditure' and COLUMN_NAME = 'model' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_affairs_change_expenditure` ADD `model` VARCHAR(50)  COMMENT ''规格型号'' ',
    'select ''INFO: model 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_affairs_change_expenditure' and COLUMN_NAME = 'supplier_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_affairs_change_expenditure` ADD `supplier_name` VARCHAR(200)  COMMENT ''品牌'' ',
    'select ''INFO: supplier_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_affairs_bill_maintenance' and COLUMN_NAME = 'requisition_dept_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_affairs_bill_maintenance` ADD `requisition_dept_code` VARCHAR(50)  COMMENT ''领用部门key'' ',
    'select ''INFO: requisition_dept_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

CREATE TABLE IF NOT EXISTS `wf_step_logs`  (
  `id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
  `wf_definition_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程定义id',
  `opt_content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '操作内容',
  `opt_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人ip',
  `CREATE_USER` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_DATE` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_USER` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `UPDATE_DATE` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `CREATE_USER_NAME` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `UPDATE_USER_NAME` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人姓名',
  `IS_DELETED` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

CREATE TABLE IF NOT EXISTS `hrms_suggestion_pwd`  (
  `id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `pwd_one` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `pwd_two` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'DP_TABLE' and COLUMN_NAME = 'table_width' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`DP_TABLE` ADD `table_width` int(11)  COMMENT ''表格宽度'' ',
    'select ''INFO: table_width 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


