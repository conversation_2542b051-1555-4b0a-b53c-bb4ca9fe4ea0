<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.dao.CommInterfaceLogsMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.homs.base.model.CommInterfaceLogs">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="register_id" jdbcType="VARCHAR" property="registerId" />
    <result column="interface_name" jdbcType="VARCHAR" property="interfaceName" />
    <result column="interwork_platform" jdbcType="VARCHAR" property="interworkPlatform" />
    <result column="request_url" jdbcType="VARCHAR" property="requestUrl" />
    <result column="take_time" jdbcType="VARCHAR" property="takeTime" />
    <result column="response_status" jdbcType="VARCHAR" property="responseStatus" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="request_params" jdbcType="LONGVARCHAR" property="requestParams" />
    <result column="response_params" jdbcType="LONGVARCHAR" property="responseParams" />
  </resultMap>
</mapper>