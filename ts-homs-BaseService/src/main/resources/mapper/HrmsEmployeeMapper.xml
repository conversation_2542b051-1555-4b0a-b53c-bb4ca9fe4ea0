<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapper.HrmsEmployeeMapper">
	<resultMap id="BaseResultMap" type="cn.trasen.homs.base.model.HrmsEmployee">
		<id column="employee_id" jdbcType="VARCHAR" property="employeeId" />
		<result column="old_employee_no" jdbcType="VARCHAR" property="oldEmployeeNo" />
		<result column="his_employee_no" jdbcType="VARCHAR" property="hisEmployeeNo" />
		<result column="employee_no" jdbcType="VARCHAR" property="employeeNo" />
		<result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
		<result column="org_id" jdbcType="VARCHAR" property="orgId" />
		<result column="used_name" jdbcType="VARCHAR" property="usedName" />
		<result column="gender" jdbcType="VARCHAR" property="gender" />
		<result column="birthday" jdbcType="DATE" property="birthday" />
		<result column="identity_number" jdbcType="VARCHAR" property="identityNumber" />
		<result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
		<result column="landline_number" jdbcType="VARCHAR" property="landlineNumber" />
		<result column="employee_category" jdbcType="VARCHAR" property="employeeCategory" />
		<result column="employee_status" jdbcType="VARCHAR" property="employeeStatus" />
		<result column="establishment_type" jdbcType="VARCHAR" property="establishmentType" />
		<result column="birthplace" jdbcType="VARCHAR" property="birthplace" />
		<result column="nationality" jdbcType="VARCHAR" property="nationality" />
		<result column="political_status" jdbcType="VARCHAR" property="politicalStatus" />
		<result column="avatar" jdbcType="VARCHAR" property="avatar" />
		<result column="address" jdbcType="VARCHAR" property="address" />
		<result column="residence_address" jdbcType="VARCHAR" property="residenceAddress" />
		<result column="postcode" jdbcType="VARCHAR" property="postcode" />
		<result column="email" jdbcType="VARCHAR" property="email" />
		<result column="marriage_status" jdbcType="VARCHAR" property="marriageStatus" />
		<result column="health_status" jdbcType="VARCHAR" property="healthStatus" />
		<result column="blood_group" jdbcType="VARCHAR" property="bloodGroup" />
		<result column="name_stroke" jdbcType="VARCHAR" property="nameStroke" />
		<result column="name_spell" jdbcType="VARCHAR" property="nameSpell" />
		<result column="personal_profile" jdbcType="VARCHAR" property="personalProfile" />
		<result column="position_id" jdbcType="VARCHAR" property="positionId" />
		<result column="post_id" jdbcType="VARCHAR" property="postId" />
		<result column="salary_level_id" jdbcType="VARCHAR" property="salaryLevelId" />
		<result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
		<result column="is_enable" jdbcType="VARCHAR" property="isEnable" />
		<result column="is_retire" jdbcType="VARCHAR" property="isRetire" />
		<result column="entry_date" jdbcType="DATE" property="entryDate" />
		<result column="retire_date" jdbcType="VARCHAR" property="retireDate" />
		<result column="quit_date" jdbcType="VARCHAR" property="quitDate" />
		<result column="reemployment_date" jdbcType="VARCHAR" property="reemploymentDate" />
		<result column="party_date" jdbcType="DATE" property="partyDate" />
		<result column="work_start_date" jdbcType="VARCHAR" property="workStartDate" />
		<result column="unit_start_date" jdbcType="VARCHAR" property="unitStartDate" />
		<result column="personal_identity" jdbcType="VARCHAR" property="personalIdentity" />
		<result column="work_nature" jdbcType="VARCHAR" property="workNature" />
		<result column="good_at" jdbcType="VARCHAR" property="goodAt" />
		<result column="check_work_depart" jdbcType="VARCHAR" property="checkWorkDepart" />
		<result column="review_depart" jdbcType="VARCHAR" property="reviewDepart" />
		<result column="upgrade_flag" jdbcType="VARCHAR" property="upgradeFlag" />
		<result column="improve_flag" jdbcType="VARCHAR" property="improveFlag" />
		<result column="is_duplicate_entry" jdbcType="VARCHAR" property="isDuplicateEntry" />
		<result column="emergency_contact" jdbcType="VARCHAR" property="emergencyContact" />
		<result column="emergency_tel" jdbcType="VARCHAR" property="emergencyTel" />
		<result column="probation_salary" jdbcType="VARCHAR" property="probationSalary" />
		<result column="regular_salary" jdbcType="VARCHAR" property="regularSalary" />
		<result column="buy_social_date" jdbcType="VARCHAR" property="buySocialDate" />
		<result column="buy_provident_date" jdbcType="VARCHAR" property="buyProvidentDate" />
		<result column="salary_remark" jdbcType="VARCHAR" property="salaryRemark" />
		<result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId" />
		<result column="first_education_type" jdbcType="VARCHAR" property="firstEducationType" />
		<result column="job_deion_type" jdbcType="VARCHAR" property="jobDeionType" />
		<result column="job_deion_type_time" jdbcType="DATE" property="jobDeionTypeTime" />
		<result column="concurrent_position" jdbcType="VARCHAR" property="concurrentPosition" />
		<result column="concurrent_position_time" jdbcType="VARCHAR" property="concurrentPositionTime" />
		<result column="is_leader" jdbcType="VARCHAR" property="isLeader" />
		<result column="post_type" jdbcType="VARCHAR" property="postType" />
		<result column="doctor_qualification_certificate" jdbcType="VARCHAR" property="doctorQualificationCertificate" />
		<result column="midwife" jdbcType="VARCHAR" property="midwife" />
		<result column="start_employ_date" jdbcType="DATE" property="startEmployDate" />
		<result column="end_employ_date" jdbcType="VARCHAR" property="endEmployDate" />
		<result column="is_veteran" jdbcType="VARCHAR" property="isVeteran" />
		<result column="unit_name" jdbcType="VARCHAR" property="unitName" />
		<result column="business_id" jdbcType="VARCHAR" property="businessId" />
		<result column="born_address" jdbcType="VARCHAR" property="bornAddress" />
		<result column="born_address_name" jdbcType="VARCHAR" property="bornAddressName" />
		<result column="authorized_org" jdbcType="VARCHAR" property="authorizedOrg" />
		<result column="employ_duty" jdbcType="VARCHAR" property="employDuty" />
		<result column="employ_duty_date" jdbcType="DATE" property="employDutyDate" />
		<result column="employ_duty_equally_date" jdbcType="DATE" property="employDutyEquallyDate" />
		<result column="employ_duty_duration" jdbcType="INTEGER" property="employDutyDuration" />
		<result column="compliance_training" jdbcType="VARCHAR" property="complianceTraining" />
		<result column="operation_date" jdbcType="VARCHAR" property="operationDate" />
		<result column="operation_org" jdbcType="VARCHAR" property="operationOrg" />
		<result column="operation_scope" jdbcType="VARCHAR" property="operationScope" />
		<result column="operation_number" jdbcType="VARCHAR" property="operationNumber" />
		<result column="operation_type" jdbcType="VARCHAR" property="operationType" />
		<result column="archive_address" jdbcType="VARCHAR" property="archiveAddress" />
		<result column="emp_password" jdbcType="VARCHAR" property="empPassword" />
		<result column="emp_nick_name" jdbcType="VARCHAR" property="empNickName" />
		<result column="emp_head_img" jdbcType="VARCHAR" property="empHeadImg" />
		<result column="emp_signimg" jdbcType="VARCHAR" property="empSignimg" />
		<result column="signature_img_name" jdbcType="VARCHAR" property="signatureImgName" />
		<result column="signature_imgsave_name" jdbcType="VARCHAR" property="signatureImgsaveName" />
		<result column="fire_reason" jdbcType="VARCHAR" property="fireReason" />
		<result column="emp_age" jdbcType="VARCHAR" property="empAge" />
		<result column="user_accounts" jdbcType="VARCHAR" property="userAccounts" />
		<result column="user_simple_name" jdbcType="VARCHAR" property="userSimpleName" />
		<result column="key_validate" jdbcType="VARCHAR" property="keyValidate" />
		<result column="key_serial" jdbcType="VARCHAR" property="keySerial" />
		<result column="is_ad_check" jdbcType="VARCHAR" property="isAdCheck" />
		<result column="user_is_sleep" jdbcType="VARCHAR" property="userIsSleep" />
		<result column="is_sms_reminder" jdbcType="VARCHAR" property="isSmsReminder" />
		<result column="is_voice_reminder" jdbcType="VARCHAR" property="isVoiceReminder" />
		<result column="is_wx_reminder" jdbcType="VARCHAR" property="isWxReminder" />
		<result column="is_display_phone_no" jdbcType="VARCHAR" property="isDisplayPhoneNo" />
		<result column="is_use_signature" jdbcType="VARCHAR" property="isUseSignature" />
		<result column="user_isactive" jdbcType="VARCHAR" property="userIsactive" />
		<result column="user_isformaluser" jdbcType="VARCHAR" property="userIsformaluser" />
		<result column="user_issuper" jdbcType="VARCHAR" property="userIssuper" />
		<result column="user_super_begin" jdbcType="VARCHAR" property="userSuperBegin" />
		<result column="user_super_end" jdbcType="VARCHAR" property="userSuperEnd" />
		<result column="browserange" jdbcType="VARCHAR" property="browserange" />
		<result column="browserange_name" jdbcType="VARCHAR" property="browserangeName" />
		<result column="hosp_code" jdbcType="VARCHAR" property="hospCode" />
		<result column="create_dept" jdbcType="VARCHAR" property="createDept" />
		<result column="create_dept_name" jdbcType="VARCHAR" property="createDeptName" />
		<result column="user_is_deleted" jdbcType="VARCHAR" property="userIsDeleted" />
		<result column="emp_payroll" jdbcType="VARCHAR" property="empPayroll" />
		<result column="open_id" jdbcType="VARCHAR" property="openId" />
		<result column="is_birthday_protect" jdbcType="VARCHAR" property="isBirthdayProtect" />
		<result column="emp_title_id" jdbcType="VARCHAR" property="empTitleId" />
		<result column="emp_title_name" jdbcType="VARCHAR" property="empTitleName" />
		<result column="year_work" jdbcType="VARCHAR" property="yearWork" />
		<result column="year_number" jdbcType="VARCHAR" property="yearNumber" />
		<result column="year_days" jdbcType="VARCHAR" property="yearDays" />
		<result column="create_date" jdbcType="DATE" property="createDate" />
		<result column="create_user" jdbcType="VARCHAR" property="createUser" />
		<result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
		<result column="update_date" jdbcType="VARCHAR" property="updateDate" />
		<result column="update_user" jdbcType="VARCHAR" property="updateUser" />
		<result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
		<result column="bdwlxgl" jdbcType="INTEGER" property="bdwlxgl" />
		<result column="remark" jdbcType="LONGVARCHAR" property="remark" />
		<result column="idCardFile" jdbcType="VARCHAR" property="id_card_file" />
		<result column="xuexin_net_file" jdbcType="VARCHAR" property="xuexinNetFile" />
		<result column="zyfj" jdbcType="VARCHAR" property="zyfj" />
	</resultMap>

	<select id="findByEmployeeIds" resultType="cn.trasen.homs.base.bean.HrmsEmployeeResp">
		SELECT 
			e.*,
			i.*,
			o.NAME AS orgName, 
			o.CODE AS orgCode, 
			a4.post_name as postName,
			a5.position_name as positionName 
		FROM
		cust_emp_base e 
		left join cust_emp_info i on e.employee_id = i.info_id
		LEFT JOIN comm_organization o ON e.org_id = o.organization_id AND o.is_deleted = 'N' 
		LEFT JOIN comm_post AS a4 on i.post_id = a4.post_id left join
		comm_position as a5 on e.position_id = a5.position_id 
		WHERE e.is_deleted = 'N' 
		AND e.employee_id in
		<foreach collection="employeeIds" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		order by e.create_date desc,e.employee_id desc
	</select>

	<select id="findByEmployeeNos" resultType="cn.trasen.homs.base.bean.HrmsEmployeeResp">
		SELECT 
			e.*,
			i.*,
			o.NAME AS orgName, 
			o.CODE AS orgCode, 
			a4.post_name as postName, 
			a5.position_name as positionName
		FROM cust_emp_base e
		left join cust_emp_info i on e.employee_id = i.info_id
		LEFT JOIN comm_organization o ON e.org_id = o.organization_id AND o.is_deleted ='N'
		LEFT JOIN comm_post AS a4 on i.post_id=a4.post_id
		left join comm_position as a5 on e.position_id=a5.position_id
		WHERE e.is_deleted ='N'
		<if test="employeeNos != null and employeeNos.size() > 0">
			AND e.employee_no in
			<foreach collection="employeeNos" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		order by e.create_date desc
	</select>

	<select id="selectListByDeptCode" resultType="cn.trasen.homs.base.bean.HrmsEmployeeResp">
		SELECT 
			e.*,
			i.*,
			o.NAME AS orgName, 
			o.CODE AS orgCode, 
			a4.post_name as postName,
			a5.position_name as positionName 
		FROM 
		cust_emp_base e 
		left join cust_emp_info i on e.employee_id = i.info_id 
		LEFT JOIN comm_organization o ON e.org_id = o.organization_id AND o.is_deleted ='N'
		LEFT JOIN comm_post AS a4 on i.post_id=a4.post_id
		left join comm_position as a5 on e.position_id=a5.position_id
		WHERE e.is_deleted ='N' and e.employee_status in ('1','5','6','9','12','99')
		<if test="list !=null and list.size()>0">
			AND e.org_id in
			<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		order by e.emp_sort,e.employee_no
	</select>

	<select id="getEmployeeByCodes" resultType="cn.trasen.homs.base.bean.HrmsEmployeeResp">
		SELECT 
			e.*,
			i.*,
			o.NAME AS orgName 
		FROM cust_emp_base e
		left join cust_emp_info i on e.employee_id = i.info_id 
		LEFT JOIN comm_organization o ON e.org_id = o.organization_id  AND o.is_deleted = 'N' 
		WHERE e.is_deleted = 'N' and e.employee_status in ('1','5','6','9','12','99') 
		AND e.employee_no in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		order by e.emp_sort,e.employee_no
	</select>

	<select id="findByEmployeeId" resultType="cn.trasen.homs.base.bean.HrmsEmployeeResp">
		SELECT
			e.*,
			i.*,
			o.NAME AS orgName,
			o.CODE AS orgCode,
			a4.post_name as postName,
			a5.position_name as positionName,
			item.item_name as nationalityName
		FROM  
		cust_emp_base e 
		left join cust_emp_info i on e.employee_id = i.info_id
		LEFT JOIN comm_organization o ON e.org_id = o.organization_id AND o.is_deleted = 'N'
		LEFT JOIN comm_post AS a4 on i.post_id=a4.post_id
		left join comm_position as a5 on e.position_id=a5.position_id
		left join (	select s.item_code,s.item_name 
				from comm_dict_item s 
				where	s.DIC_TYPE_ID='nationality_name' ) item on i.nationality = item.ITEM_CODE
		WHERE  e.employee_id = #{employeeId}
	</select>


	<select id="findByEmployeePhoneNumber" resultType="cn.trasen.homs.base.bean.HrmsEmployeeResp">
		SELECT
			e.*,
			i.*,
			o.NAME AS orgName,
			o.CODE AS orgCode,
			a4.post_name as postName,
			a5.position_name as positionName
		FROM
		cust_emp_base e 
		left join cust_emp_info i on e.employee_id = i.info_id
		LEFT JOIN comm_organization o ON e.org_id = o.organization_id AND o.is_deleted = 'N'
		LEFT JOIN comm_post AS a4 on i.post_id=a4.post_id
		left join comm_position as a5 on e.position_id=a5.position_id
		WHERE
		e.is_deleted = 'N'
		and e.sso_org_code = #{ssoOrgCode}
		AND e.phone_number = #{phoneNumber}
	</select>


	<select id="findByEmployeeNo" resultType="cn.trasen.homs.base.bean.HrmsEmployeeResp">
		SELECT
			e.*,
			i.*,
			o.NAME AS orgName,
			o.CODE AS orgCode,
			a4.post_name as postName,
			a5.position_name as positionName
		FROM
		cust_emp_base e 
		left join cust_emp_info i on e.employee_id = i.info_id
		LEFT JOIN comm_organization o ON e.org_id = o.organization_id AND o.is_deleted = 'N'
		LEFT JOIN comm_post AS a4 on i.post_id=a4.post_id
		left join comm_position as a5 on e.position_id=a5.position_id
		WHERE
		e.is_deleted = 'N'
		AND e.employee_no = #{employeeNo}
	</select>


	<select id="getPageList" resultType="cn.trasen.homs.base.bean.HrmsEmployeeResp">
		SELECT 
			e.employee_id,e.employee_no,e.emp_payroll,e.his_employee_no,e.employee_name,e.org_id,e.open_id,e.employee_status,
			e.is_enable,e.gender,e.identity_number,e.phone_number,e.avatar,e.signature_img_name,e.emp_signimg,e.entry_date,
			date_format(e.birthday, '%Y-%m-%d') birthday,
			e.emp_age,e.name_spell,e.email,e.carNo,e.emp_nick_name,e.emp_business_phone,e.emp_sort,e.year_work,
			e.year_days,e.year_number,e.position_id,e.org_attributes,e.post_type,e.personal_identity,
			e.job_attributes,e.positive_time,e.is_display_phone_no,e.is_sms_reminder,e.is_use_signature,
			e.is_voice_reminder,e.is_wx_reminder,e.password_expire_date,e.organization_parttime_id,
			e.organization_parttime_name,e.is_enable_process_agent,e.agent_start_time,e.agent_end_time,e.agent_ids,
			e.agent_names,e.upload_file_size,e.hosp_code,e.remark,e.employee_face,e.create_date,e.create_user,
			e.create_user_name,e.create_dept,e.create_dept_name,e.update_user,e.update_user_name,e.update_date,e.is_deleted,
			e.sso_org_code,e.sso_org_name,
			i.*,
			o.NAME AS orgName, 
			o.CODE AS orgCode, 
			a4.post_name as postName,
			a5.position_name as positionName 
		FROM
		cust_emp_base e 
		left join cust_emp_info i on e.employee_id = i.info_id
		LEFT JOIN comm_organization o ON e.org_id = o.organization_id AND o.is_deleted = 'N' 
		LEFT JOIN comm_post AS a4 on i.post_id=a4.post_id 
		left join comm_position as a5 on e.position_id=a5.position_id
		WHERE e.is_deleted ='N' and
		e.sso_org_code = #{ssoOrgCode}
		<if test="employeeNo != null and employeeNo != ''">
			AND ((employee_no LIKE CONCAT('%',#{employeeNo},'%') )
			or (employee_name LIKE
			CONCAT('%',#{employeeNo},'%'))
			or (name_spell LIKE CONCAT('%',#{employeeNo},'%') ))
		</if>

		<if test="employeeName != null and employeeName != ''">
			AND (
			(employee_no LIKE CONCAT('%',#{employeeName},'%'))
			or (employee_name LIKE CONCAT('%',#{employeeName},'%'))
			or (name_spell LIKE CONCAT('%',#{employeeName},'%'))
			or (o.NAME LIKE CONCAT('%',#{employeeName},'%'))
			or (phone_number LIKE CONCAT('%',#{employeeName},'%'))
			or (emp_business_phone LIKE CONCAT('%',#{employeeName},'%'))
			)
		</if>

		<if test="personalIdentity != null and personalIdentity != ''">
			AND personal_identity = #{personalIdentity}
		</if>

		<if test="gender != null and gender != ''">
			AND gender = #{gender}
		</if>

		<if test="establishmentType != null and establishmentType != ''">
			AND e.establishment_type = #{establishmentType}
		</if>

		<if test="phoneNumber != null and phoneNumber != ''">
			AND ( phone_number LIKE CONCAT('%',#{phoneNumber},'%')
			or emp_business_phone LIKE CONCAT('%',#{phoneNumber},'%')
			)
		</if>

		<if test="eqOrgId != null and eqOrgId != ''">
			AND e.org_id = #{eqOrgId}
		</if>

		<if test="orgName != null and orgName != ''">
			AND o.name = #{orgName}
		</if>

		<if test="searchKey != null and searchKey != ''">
			AND (
			o.name LIKE CONCAT('%',#{searchKey},'%')
			or employee_name LIKE CONCAT('%',#{searchKey},'%')
			)
		</if>

		<if test="likeOrgName != null and likeOrgName != ''">
			AND o.name LIKE CONCAT('%',#{likeOrgName},'%')
		</if>


		<if test="email != null and email != ''">
			AND e.email = #{email}
		</if>

		<if test="businessPhone != null and businessPhone != ''">
			AND ((emp_business_phone LIKE CONCAT('%',#{businessPhone},'%'))
			or (emp_telecom_business_phone LIKE CONCAT('%',#{businessPhone},'%'))
			or (emp_unicom_business_phone LIKE CONCAT('%',#{businessPhone},'%'))
			)
		</if>

		<if test="landlineNumber != null and landlineNumber != ''">
			AND e.landline_number = #{landlineNumber}
		</if>

		<if test="orgIdList != null and orgIdList.size() > 0">
			AND org_id in
			<foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>

		<if test="noEmployeeStatusList != null and noEmployeeStatusList.size() > 0">
			AND employee_status not in
			<foreach collection="noEmployeeStatusList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>

		<if test="employeeStatusList != null and employeeStatusList.size() > 0">
			AND employee_status in
			<foreach collection="employeeStatusList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>

	</select>


	<select id="getEmployeeListByEqualPay" resultType="cn.trasen.homs.base.bean.HrmsEmployeeResp"
		parameterType="cn.trasen.homs.base.model.HrmsEmployee">
		select 
			s.*,
			i.*,
			a3.jobtitleCategoryName,
			o.name as orgName,
			a4.education_type_name as educationTypeName,
			item.personalIdentityName 
		from 
		cust_emp_base s 
		left join cust_emp_info i on s.employee_id = i.info_id
		left join comm_organization o on s.org_id =o.organization_id 
		left join (	select s.item_code,s.item_name as personalIdentityName 
				from comm_dict_item s 
				where	s.DIC_TYPE_ID='personal_identity' ) item on	s.personal_identity = item.ITEM_CODE 
		left join ( SELECT	info.employee_id,MIN(info.highest_level) highest_level,	b.jobtitle_basic_name as jobtitleCategoryName 
			FROM hrms_jobtitle_info 
			info inner join	hrms_jobtitle_basic b on info.jobtitle_category =b.jobtitle_basic_id
			WHERE info.is_deleted = 'N' GROUP BY info.employee_id,b.jobtitle_basic_name )a3 on a3.employee_id = s.employee_id 
		left join ( SELECT	ed.employee_id,MIN(ed.highest_level) highest_level,	dict.ITEM_NAME as education_type_name  
			FROM hrms_education_info ed 
			left join comm_dict_item dict on ed.education_type = dict.item_code and dict.dic_type_id ='education_type' 
			WHERE ed.is_deleted = 'N' GROUP BY	ed.employee_id,dict.ITEM_NAME )a4	on a4.employee_id = s.employee_id 
		where s.is_deleted = 'N' and
		s.sso_org_code = #{ssoOrgCode} and
		s.employee_name !='admin' and
		s.employee_name !='ts' and s.employee_status in ( 1,6,12)
		<if test="employeeName!=null and employeeName!='' ">
			and (
			s.identity_number like concat('',#{employeeName},'%')
			or s.phone_number like
			concat('',#{employeeName},'%')
			or s.emp_payroll like
			concat('',#{employeeName},'%')
			or s.employee_name like
			concat('',#{employeeName},'%')
			or s.employee_no like concat('',#{employeeName},'%')
			or s.name_spell like
			concat('',#{employeeName},'%')
			or s.name_stroke like concat('',#{employeeName},'%')
			)
		</if>

	</select>


	<select id="list" resultType="cn.trasen.homs.base.bo.EmployeeListOutBO">
		SELECT 
			e.*, 
			i.*,
			o.NAME AS orgName, 
			o.CODE AS orgCode, 
			a4.post_name as postName, 
			a5.position_name as positionName 
		FROM 
		cust_emp_base e 
		left join cust_emp_info i on e.employee_id = i.info_id
		LEFT JOIN comm_organization o ON e.org_id = o.organization_id AND o.is_deleted = 'N' 
		LEFT JOIN comm_post AS a4 on i.post_id=a4.post_id
		left join comm_position as a5 on e.position_id=a5.position_id 
		WHERE
		e.is_deleted = 'N' 
		and e.sso_org_code = #{ssoOrgCode}
		<if test="req.likeAllName != null and req.likeAllName != ''">
			AND ((employee_no LIKE CONCAT('%',#{req.likeAllName},'%') )
			or (employee_name LIKE
			CONCAT('%',#{req.likeAllName},'%') )
			or (name_spell LIKE
			CONCAT('%',#{req.likeAllName},'%') ))
		</if>


		<if test="req.orgIdList != null and req.orgIdList.size() > 0">
			AND
			org_id in
			<foreach collection="req.orgIdList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>

		<if test="req.noEmployeeStatusList != null and req.noEmployeeStatusList.size() > 0">
			AND employee_status not in
			<foreach collection="req.noEmployeeStatusList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>

		<if test="req.employeeStatusList != null and req.employeeStatusList.size() > 0">
			AND employee_status in
			<foreach collection="req.employeeStatusList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		order by e.create_date
		desc,e.employee_id desc
	</select>

	<!-- 修改员工工号start -->
	<update id="updateHrmsEmpNoByid" parameterType="cn.trasen.homs.base.bean.ResetJobNumberReq">
		update cust_emp_base
		set employee_no =#{employeeNo}
		<if test="empPayroll != null and empPayroll != ''">
			,emp_payroll = #{empPayroll}
		</if>
		where employee_id=#{employeeId}

	</update>

	<update id="updateToaEmpNoById" parameterType="cn.trasen.homs.base.bean.ResetJobNumberReq">
		update toa_employee
		set emp_code =#{employeeNo},
		<if test="empPayroll != null and empPayroll != ''">
			emp_payroll = #{empPayroll},
		</if>
		user_accounts =#{employeeNo}
		where id=#{employeeId}

	</update>

	<update id="updateThpsEmpNoByid" parameterType="cn.trasen.homs.base.bean.ResetJobNumberReq">
		update ts_thps.thps_user
		set usercode =#{employeeNo},
		oldusercode=#{employeeNo}
		where id=#{employeeId}
	</update>

	<!-- 浏阳中查询 个人信息采集表 -->
	<select id="getLyzXuexi" parameterType="java.lang.String" resultType="cn.trasen.homs.base.bo.EmployeeDetails">
		SELECT
		t2.`ITEM_NAME` AS v1,
		t1.`start_time`
		AS v2,
		t1.`end_time` AS v3,
		t1.`school_name` AS v4,
		t1.`professional` AS v5,
		t1.`education_degree` AS v6,
		t1.highest_level
		AS v7
		FROM hrms_education_info t1
		LEFT JOIN comm_dict_item t2 ON
		t1.`education_type`=t2.item_code AND
		t2.`DIC_TYPE_ID`='education_type' AND t2.`IS_DELETED`='N'
		WHERE t1.is_deleted ='N' and t1.employee_id =#{employeeId}
		ORDER
		BY t1.highest_level

	</select>
	<select id="getLyzYuanwai" parameterType="java.lang.String" resultType="cn.trasen.homs.base.bo.EmployeeDetails">
		SELECT
		start_time AS v1,
		end_time AS v2,
		work_unit AS v3,
		dept_name AS v4,
		post AS v5
		FROM hrms_work_experience
		WHERE is_deleted ='N' and employee_id
		=#{employeeId}
	</select>
	<select id="getLyzYuannei" parameterType="java.lang.String" resultType="cn.trasen.homs.base.bo.EmployeeDetails">
		SELECT

		change_start_date AS v2,
		old_post AS
		v3,
		old_dept AS v4,
		old_position AS v5
		FROM hrms_work_experience_hospital
		where is_deleted ='N' and employee_id
		=#{employeeId}
	</select>
	<select id="getLyzZhicheng" parameterType="java.lang.String" resultType="cn.trasen.homs.base.bo.EmployeeDetails">
		SELECT
		t2.jobtitle_basic_name AS v1,
		t1.assessment_date AS v2,
		t3.`jobtitle_basic_name` AS v3,
		t4.`jobtitle_basic_name` AS v4,
		t1.highest_level AS v7
		FROM
		hrms_jobtitle_info t1
		LEFT JOIN `comm_jobtitle_basic` t2 ON
		t1.`jobtitle_category`
		=t2.`jobtitle_basic_id`
		LEFT JOIN
		comm_jobtitle_basic t3 ON t1.`jobtitle_level` =
		t3.`jobtitle_basic_id`
		LEFT JOIN comm_jobtitle_basic t4
		ON
		t1.`jobtitle_name` =
		t4.`jobtitle_basic_id`
		WHERE t1.is_deleted ='N' and t1.employee_id =#{employeeId}

	</select>
	<select id="getLyzJinxiu" parameterType="java.lang.String" resultType="cn.trasen.homs.base.bo.EmployeeDetails">
		SELECT
		start_time AS v1,
		end_time AS v2,
		out_address AS v3
		FROM hrms_out_record
		WHERE
		out_type ='进修' AND is_deleted ='N'
		and employee_id =#{employeeId}

	</select>
	<select id="getLyzGuipei" parameterType="java.lang.String" resultType="cn.trasen.homs.base.bo.EmployeeDetails">
		SELECT
		start_time AS v1,
		end_time AS v2,
		out_address AS v3
		FROM hrms_out_record
		WHERE
		out_type ='规培' AND is_deleted ='N'
		and employee_id =#{employeeId}
	</select>

	<select id="getEmpAll" resultType="java.util.HashMap">
		SELECT 
			employee_id AS empId,
			employee_no AS empCode,
			employee_name AS empName,
			t2.name AS orgName,
			t2.organization_id AS orgCode,
			t1.identity_number AS idCard,
			t1.phone_number AS phone,
			t1.gender AS sex,
			t1.employee_status AS empStatus
		FROM cust_emp_base t1
		LEFT JOIN comm_organization t2 ON t1.org_id=t2.organization_id
		WHERE t1.is_deleted = 'N'
	</select>


	<select id="getKhjg" resultType="java.util.Map" parameterType="java.lang.String">
		SELECT assess_result AS val,assess_year AS 'key'
		FROM hrms_evaluation_result
		where employee_id= #{employeeId} and is_deleted='N'
		GROUP BY assess_result,assess_year
		ORDER
		BY (assess_year) DESC LIMIT 6
	</select>


	<select id="getKylw" resultType="java.util.Map" parameterType="java.lang.String">

		SELECT * FROM hrms_papers_books WHERE
		is_deleted='N' and employee_id=
		#{employeeId}
	</select>

	<select id="getJcjl" resultType="java.util.Map" parameterType="java.lang.String">
		SELECT * FROM hrms_reward_penalty WHERE
		is_deleted='N' and employee_id=
		#{employeeId}
	</select>

	<select id="getJxjl" resultType="java.util.Map" parameterType="java.lang.String">
		SELECT * FROM hrms_out_record WHERE is_deleted='N'
		and employee_id=
		#{employeeId}
	</select>

	<select id="getZyysgfhpx" resultType="java.util.Map" parameterType="java.lang.String">
		SELECT * FROM hrms_standardized_training
		WHERE is_deleted='N' and
		employee_id=
		#{employeeId}
	</select>

	<select id="excelSql">
		${excelSql}
	</select>
	<select id="getGpjl" resultType="java.util.Map" parameterType="java.lang.String">
		SELECT * FROM hrms_out_record_gp WHERE
		is_deleted='N' and employee_id=
		#{employeeId}
	</select>
	<update id="updateDisable" parameterType="java.lang.String">
		UPDATE cust_emp_base SET is_enable = #{disableStatus}
		WHERE
		employee_id=#{employeeId}
	</update>
	<update id="updateHrmsEmpBecome" parameterType="java.lang.String">
		UPDATE hrms_employee_become SET employee_no=#{nowEmployeeNo}
		WHERE
		employee_no=#{employeeNo} and is_deleted = 'N'
	</update>
	
	<select id="getEmployeeBaseList" parameterType="cn.trasen.homs.base.bean.EmployeeListReq" resultType="cn.trasen.homs.base.model.HrmsEmployee">
	  		select * from cust_emp_base
	  		where is_deleted = 'N' and sso_org_code = #{ssoOrgCode}
	  		and employee_status in ('1','5','6','9','12','99')
	  		<if test="eqOrgId != null and eqOrgId != ''">
	  			and org_id = #{eqOrgId}
	  		</if>
	  		<if test="employeeNo != null and employeeNo != ''">
	  			and employee_no = #{employeeNo}
	  		</if>
	</select>

	<select id="getEmployeeListReqList" parameterType="cn.trasen.homs.base.bean.RequestContent" resultType="cn.trasen.homs.base.bean.EmployeeListReq" >
		SELECT
			employee_id,
			employee_no,
			employee_name,
			org_id,
			t2.`name` orgName,
			t1.is_deleted,
			gender,
			identity_number,
			birthday,
			phone_number,
			employee_status,
			name_spell,
			entry_date,
			t1.is_enable,
			emp_business_phone,
			t1.sso_org_code
			<if test="orgCode != null and orgCode == 'cssdeshfly'.toString()">
				,t3.cblx
			</if>
		FROM
			cust_emp_base t1
		left join cust_emp_info t3 on t1.employee_id = t3.info_id
		LEFT JOIN comm_organization t2 on t1.org_id = t2.organization_id 
		where 1=1
		<if test="employeeStatus != null and employeeStatus != ''">
			and employee_status = #{employeeStatus}
		</if>
		<if test="isEnable != null and isEnable != ''">
			and is_enable = #{isEnable}
		</if>
		<if test="isDeleted != null and isDeleted != ''">
			and t1.is_deleted = #{isDeleted}
		</if>
	</select>
</mapper>
