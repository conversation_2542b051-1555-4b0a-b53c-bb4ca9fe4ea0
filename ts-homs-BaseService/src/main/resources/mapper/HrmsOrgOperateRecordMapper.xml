<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapper.HrmsOrgOperateRecordMapper">
	<resultMap id="BaseResultMap"
		type="cn.trasen.homs.base.model.HrmsOrgOperateRecord">
		<id column="operate_record_id" jdbcType="VARCHAR"
			property="operateRecordId" />
		<result column="old_org_id" jdbcType="VARCHAR" property="oldOrgId" />
		<result column="old_org_name" jdbcType="VARCHAR" property="oldOrgName" />
		<result column="new_org_id" jdbcType="VARCHAR" property="newOrgId" />
		<result column="new_org_name" jdbcType="VARCHAR" property="newOrgName" />
		<result column="operate_type" jdbcType="VARCHAR" property="operateType" />
		<result column="batch_number" jdbcType="VARCHAR" property="batchNumber" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="enterprise_id" jdbcType="VARCHAR"
			property="enterpriseId" />
		<result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
		<result column="create_user" jdbcType="VARCHAR" property="createUser" />
		<result column="create_user_name" jdbcType="VARCHAR"
			property="createUserName" />
		<result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
		<result column="update_user" jdbcType="VARCHAR" property="updateUser" />
		<result column="update_user_name" jdbcType="VARCHAR"
			property="updateUserName" />
		<result column="org_id" jdbcType="VARCHAR" property="orgId" />
		<result column="org_name" jdbcType="VARCHAR" property="orgName" />
		<result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
	</resultMap>

	<insert id="batchInsert">
		<![CDATA[
			INSERT INTO comm_org_operate_record
			(
				operate_record_id, 
				old_org_id, 
				old_org_name,
				new_org_id,
				new_org_name,
				operate_type,
				batch_number,
				remark, 
				create_date, 
				create_user, 
				create_user_name,
				org_id,
				org_name,
				is_deleted 
			) 
			VALUES 
		]]>
		<foreach collection="list" item="item" index="index" separator=",">
			<![CDATA[
			(
				#{item.operateRecordId}, 
				#{item.oldOrgId}, 
				#{item.oldOrgName},
				#{item.newOrgId},
				#{item.newOrgName},
				#{item.operateType},
				#{item.batchNumber},
				#{item.remark}, 
				#{item.createDate}, 
				#{item.createUser},
				#{item.createUserName}, 
				#{item.orgId}, 
				#{item.orgName},  
				#{item.isDeleted}
			)
			]]>
		</foreach>
	</insert>

</mapper>