<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapper.HrmsPositionMapper">
	<resultMap id="BaseResultMap" type="cn.trasen.homs.base.model.HrmsPosition">
		<id column="position_id" jdbcType="VARCHAR" property="positionId" />
		<result column="position_name" jdbcType="VARCHAR"
			property="positionName" />
		<result column="serial_number" jdbcType="INTEGER"
			property="serialNumber" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="enterprise_id" jdbcType="VARCHAR"
			property="enterpriseId" />
		<result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
		<result column="create_user" jdbcType="VARCHAR" property="createUser" />
		<result column="create_user_name" jdbcType="VARCHAR"
			property="createUserName" />
		<result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
		<result column="update_user" jdbcType="VARCHAR" property="updateUser" />
		<result column="update_user_name" jdbcType="VARCHAR"
			property="updateUserName" />
		<result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
	</resultMap>
</mapper>