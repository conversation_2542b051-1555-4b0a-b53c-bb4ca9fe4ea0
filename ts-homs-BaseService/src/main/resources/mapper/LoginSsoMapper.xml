<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapper.LoginSsoMapper">
	<resultMap id="BaseResultMap" type="cn.trasen.homs.base.model.LoginSso">
		<!--
      WARNING - @mbg.generated
    -->
		<id column="ID" jdbcType="VARCHAR" property="id" />
		<result column="TITLE" jdbcType="VARCHAR" property="title" />
		<result column="ICON" jdbcType="VARCHAR" property="icon" />
		<result column="INTRANET_ADDRESS" jdbcType="VARCHAR"
			property="intranetAddress" />
		<result column="OUTER_NET_ADDRESS" jdbcType="VARCHAR"
			property="outerNetAddress" />
		<result column="PARAMETER_NAME1" jdbcType="VARCHAR"
			property="parameterName1" />
		<result column="PARAMETER_NAME2" jdbcType="VARCHAR"
			property="parameterName2" />
		<result column="PARAMETER_VALUE1" jdbcType="VARCHAR"
			property="parameterValue1" />
		<result column="PARAMETER_VALUE2" jdbcType="VARCHAR"
			property="parameterValue2" />
		<result column="HOSPITAL_NAME" jdbcType="VARCHAR"
			property="hospitalName" />
		<result column="REMARK" jdbcType="VARCHAR" property="remark" />
		<result column="STATUS" jdbcType="VARCHAR" property="status" />
		<result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
		<result column="CREATE_USER_NAME" jdbcType="VARCHAR"
			property="createUserName" />
		<result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
		<result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
		<result column="UPDATE_USER_NAME" jdbcType="VARCHAR"
			property="updateUserName" />
		<result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
		<result column="IS_DELETED" jdbcType="VARCHAR" property="isDeleted" />
		<result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
		<result column="HOSP_CODE" jdbcType="VARCHAR" property="hospCode" />
		<result column="CREATE_DEPT" jdbcType="VARCHAR" property="createDept" />
		<result column="CREATE_DEPT_NAME" jdbcType="VARCHAR"
			property="createDeptName" />
		<result column="SORT" jdbcType="INTEGER" property="sort" />
	</resultMap>

</mapper>