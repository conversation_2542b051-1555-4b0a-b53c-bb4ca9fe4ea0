<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapper.OrgGroupMapper">
	<resultMap id="BaseResultMap" type="cn.trasen.homs.base.model.OrgGroup">
		<!-- WARNING - @mbg.generated -->
		<id column="GROUP_ID" jdbcType="VARCHAR" property="groupId" />
		<result column="GROUP_NAME" jdbcType="VARCHAR" property="groupName" />
		<result column="GROUP_PINYIN" jdbcType="VARCHAR" property="groupPinyin" />
		<result column="GROUP_DESCRIPTION" jdbcType="VARCHAR" property="groupDescription" />
		<result column="DOMAIN_ID" jdbcType="VARCHAR" property="domainId" />
		<result column="GROUP_TYPE" jdbcType="DECIMAL" property="groupType" />
		<result column="GROUP_ORDER" jdbcType="DECIMAL" property="groupOrder" />
		<result column="GROUP_CLASS_ID" jdbcType="VARCHAR" property="groupClassId" />
		<result column="GROUP_CLASS_NAME" jdbcType="VARCHAR" property="groupClassName" />
		<result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
		<result column="CREATE_USER_NAME" jdbcType="VARCHAR" property="createUserName" />
		<result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
		<result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
		<result column="UPDATE_USER_NAME" jdbcType="VARCHAR" property="updateUserName" />
		<result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
		<result column="IS_DELETED" jdbcType="DECIMAL" property="isDeleted" />
		<result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
		<result column="HOSP_CODE" jdbcType="VARCHAR" property="hospCode" />
		<result column="CREATE_DEPT" jdbcType="VARCHAR" property="createDept" />
		<result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
		<result column="GROUP_USER_NAMES" jdbcType="CLOB" property="groupUserNames" />
		<result column="GROUP_USER_STRING" jdbcType="CLOB" property="groupUserString" />
		<result column="RANGE_NAME" jdbcType="CLOB" property="rangeName" />
		<result column="RANGE_EMP" jdbcType="CLOB" property="rangeEmp" />
		<result column="RANGE_ORG" jdbcType="CLOB" property="rangeOrg" />
		<result column="IS_TOP" jdbcType="VARCHAR" property="isTop" />
	</resultMap>

	<!-- 移动端-查询自定义群组信息列表 -->
	<select id="getOrgGroupList" resultType="cn.trasen.homs.base.model.OrgGroup"
		parameterType="cn.trasen.homs.base.model.OrgGroup">
		SELECT * FROM COMM_ORG_GROUP 
		WHERE IS_DELETED = 'N' and sso_org_code = #{ssoOrgCode}
		<if test="groupType != null and groupType == 1">
			AND CREATE_USER = #{createUser}
		</if>
		<if test="groupType != null and groupType == 0">
			AND (
			(RANGE_NAME is null OR	RANGE_NAME = '')
			OR	(find_in_set(#{createUser},RANGE_EMP))
			OR	(find_in_set(#{createDept},RANGE_ORG))
			)
		</if>
		<if test="groupName != null and groupName != ''">
			AND GROUP_NAME like '%'||#{groupName}||'%'
		</if>

		<if test="groupType != null">
			AND GROUP_TYPE=#{groupType}
		</if>
		ORDER BY IS_TOP	ASC
		<choose>
			<when test="_databaseId=='kingbase'">
				,cast(GROUP_ORDER as SIGNED) asc
			</when>
			<otherwise>
				,CONVERT(GROUP_ORDER,SIGNED) asc
			</otherwise>
		</choose>
		,CREATE_DATE desc
	</select>

	<select id="getOrgGroupData" resultType="cn.trasen.homs.base.model.OrgGroup"
		parameterType="cn.trasen.homs.base.model.OrgGroup">
		SELECT * FROM COMM_ORG_GROUP 
		WHERE IS_DELETED = 'N' AND	GROUP_ID NOT IN (#{groupId})
		AND GROUP_TYPE = #{groupType}
	</select>

	<select id="getOrgGroupTree" resultMap="BaseResultMap" parameterType="Map">
		SELECT * FROM COMM_ORG_GROUP 
		WHERE GROUP_TYPE =	#{groupType} AND IS_DELETED = 'N' AND is_enable = '1' and sso_org_code=#{ssoOrgCode}
		<if test="groupType != null and groupType == 1">
			AND CREATE_USER = #{currentUserCode}
		</if>
		AND ( (RANGE_NAME is null OR RANGE_NAME = '') OR (find_in_set(#{currentUserCode},RANGE_EMP))
		OR	(find_in_set(#{currentUserOrg},RANGE_ORG)) ) ORDER BY
		<choose>
			<when test="_databaseId=='kingbase'">
				cast(GROUP_ORDER as SIGNED) asc,CREATE_DATE desc
			</when>
			<when test="_databaseId=='dm'">
				convert(integer, GROUP_ORDER) ASC,CREATE_DATE DESC
			</when>
			<otherwise>
				CONVERT(GROUP_ORDER,SIGNED) asc,CREATE_DATE desc
			</otherwise>
		</choose>
	</select>

	<select id="selectOrgGroupList" resultType="Map" parameterType="String">
		SELECT GROUP_ID groupId,GROUP_NAME groupName 
		FROM COMM_ORG_GROUP 
		WHERE IS_DELETED = 'N' AND GROUP_TYPE = 0 and SSO_ORG_CODE = #{ssoOrgCode}
		<if test="groupName != null and groupName != ''">
			and GROUP_NAME LIKE CONCAT('%',#{groupName},'%')
		</if>
	</select>

	<select id="selectGroupIdByUserCode" resultType="String" parameterType="String">
		SELECT GROUP_ID FROM COMM_ORG_GROUP
		WHERE
		IS_DELETED = 'N' and SSO_ORG_CODE = #{ssoOrgCode} AND GROUP_TYPE =
		0 AND
		find_in_set(#{userCode},GROUP_USER_STRING)
	</select>


</mapper>