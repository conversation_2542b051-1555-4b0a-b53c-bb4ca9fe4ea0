<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapper.OrganizationMapper">

	<update id="deleteAll">
		update comm_organization set is_deleted='Y'
	</update>

	<select id="getAllPageList" parameterType="cn.trasen.homs.base.bean.OrganizationListReq" resultType="cn.trasen.homs.base.model.Organization"> 
		select * from (
		SELECT ( SELECT 
		<choose>
	    	<when test="_databaseId=='dm'">
	    		wm_concat( `employee_ids`) 
			</when>
			<otherwise>
				GROUP_CONCAT( `employee_ids`) 
			</otherwise>
		</choose>
		FROM
		comm_organization_leader t2 WHERE t1.organization_id = t2.org_id and
		employee_ids !='' and employee_ids is not null) AS leaderCodes,
		organization_id, CODE, NAME, tree_ids, parent_id, is_enable, org_level,
		seq_no, org_flag, manager_code, manager_name, assigne_manager_code,
		assigne_manager_name, responsibility, personnel_allocation, report_sort,
		remark, enterprise_id, create_date, create_user, update_date,
		update_user, is_deleted, doctor_sta, nurse_sta, doctor_three,
		nurse_three, tel, sort,org_type FROM comm_organization t1 WHERE ( is_deleted =
		'N' ) 
		<if test="parentId != null and parentId != ''">
			AND ( organization_id = #{parentId} OR parent_id = #{parentId} )
		</if>
		<if test="name != null and name != ''">
			AND name like concat('%',#{name},'%')
		</if>
		<if test="ssoOrgCode != null and ssoOrgCode != ''">
			AND t1.sso_org_code = #{ssoOrgCode}
		</if> 
		) a where 1=1 
		<if test="empCode != null and empCode != ''">
			and (find_in_set(#{empCode},a.leaderCodes))
		</if> 
		ORDER BY org_level ASC, seq_no ASC, create_date DESC 
   </select>
   
   <select id="selectLeaderListByDeptCode" parameterType="String" resultType="String">
  			select employee_ids from comm_organization_leader t1
			LEFT JOIN comm_organization t2 on t1.org_id = t2.organization_id
			where t1.is_deleted = 'N' 
			and t2.`organization_id` = #{orgId}
			and employee_ids is not null and employee_ids != ''
			and role_id in
	  		<foreach collection="roleIds" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
  </select>
  
  <select id="selectLeaderList" resultType="Map">
		select employee_id,employee_no,employee_name,gender,t2.`name` orgName from cust_emp_base t1
		left join comm_organization t2 on  t1.org_id = t2.organization_id
		where t1.is_deleted = 'N' and employee_status in ('1','5','6','9','12','88','99') and employee_no in
  		<foreach collection="employeeIdArray" item="item" separator="," open="(" close=")">
			#{item}
		</foreach>
  </select>
</mapper>