<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.dao.QuickMenuMapper">
	<resultMap id="BaseResultMap" type="cn.trasen.homs.base.model.QuickMenu">
		<!--
      WARNING - @mbg.generated
    -->
		<id column="id" jdbcType="VARCHAR" property="id" />
		<result column="menu_url" jdbcType="VARCHAR" property="menuUrl" />
		<result column="menu_name" jdbcType="VARCHAR" property="menuName" />
		<result column="create_name" jdbcType="VARCHAR" property="createName" />
		<result column="create_user" jdbcType="VARCHAR" property="createUser" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
		<result column="sord" jdbcType="INTEGER" property="sord" />
		<result column="menu_icon" jdbcType="VARCHAR" property="menuIcon" />
	</resultMap>
</mapper>