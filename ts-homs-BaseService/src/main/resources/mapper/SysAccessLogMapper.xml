<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapper.SysAccessLogMapper">
	<select id="getLogins" resultType="java.lang.Integer" parameterType="java.lang.String"> 
		select count(1) from (
		SELECT id FROM TOA_SYS_ACCESS_LOG
		 WHERE CREATE_TIME BETWEEN CONCAT(#{queryDate},' 00:00:00') and CONCAT(#{queryDate},' 23:59:59')
        <if test="loginType != null and loginType.length > 0">
            AND SOURCE IN(
            <foreach collection="loginType" item="type" index="index" separator=",">
                #{type,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="orgCode != null and orgCode !=''">
            AND sso_org_code = #{orgCode}
        </if>
        GROUP BY id, USER_NAME) as TEMP
	</select>
	<select id="getAccesses" resultType="java.lang.Integer" parameterType="java.lang.String"> 
		SELECT COUNT(1) FROM TOA_SYS_ACCESS_LOG 
        WHERE CREATE_TIME BETWEEN CONCAT(#{queryDate},' 00:00:00') and CONCAT(#{queryDate},' 23:59:59')
        <if test="orgCode != null and orgCode !=''">
            AND sso_org_code = #{orgCode}
        </if>
	</select>
	<!--<select id="getLoginList" resultType="cn.trasen.homs.baseCommLoginLogs" parameterType="java.lang.String"> SELECT *,max(create_time) 
		as create_time,max(create_time) as login_time,source as loginType FROM TOA_SYS_ACCESS_LOG WHERE CREATE_TIME >= #{loginDate} 
		<if test="loginType != null and loginType.length > 0"> AND SOURCE IN( <foreach collection="loginType" item="type" index="index" 
		separator=","> #{type,jdbcType=VARCHAR} </foreach> ) </if> GROUP BY USER_NAME ORDER BY CREATE_TIME DESC limit 1000 </select> -->
	<select id="getLoginList" resultType="cn.trasen.homs.base.model.CommLoginLogs" parameterType="java.lang.String"> 
		SELECT *,create_time as login_time,source as loginType FROM (
	        SELECT * FROM TOA_SYS_ACCESS_LOG 
	        WHERE CREATE_TIME BETWEEN CONCAT(#{queryDate},' 00:00:00') and CONCAT(#{queryDate},' 23:59:59')
	        <if test="orgCode != null and orgCode !=''">
	            AND sso_org_code = #{orgCode}
	        </if>
	        <if test="loginType != null and loginType.length > 0">
	            AND SOURCE IN(
	            <foreach collection="loginType" item="type" index="index" separator=",">
	                #{type,jdbcType=VARCHAR}
	            </foreach>
	            )
	        </if>
        ORDER BY CREATE_TIME DESC
        ) as TEMP group by id,sso_org_code,user_code, USER_NAME ,create_time,source,login_ip ORDER BY CREATE_TIME DESC limit 1000
    </select>
	<select id="getAccessList" resultType="cn.trasen.homs.base.model.CommLoginLogs" parameterType="java.lang.String"> 
		SELECT *,create_time as login_time,source as loginType 
        FROM TOA_SYS_ACCESS_LOG 
        WHERE CREATE_TIME BETWEEN CONCAT(#{queryDate},' 00:00:00') and CONCAT(#{queryDate},' 23:59:59')
        <if test="orgCode != null and orgCode !=''">
            AND sso_org_code = #{orgCode}
        </if>
        ORDER BY CREATE_TIME DESC limit 1000
	</select>
	
	<select id="selectOrgInfo" resultType="Map">
		select 
			t1.employee_no,
			t2.`name` 
		from cust_emp_base t1
		LEFT JOIN comm_organization t2 on t1.org_id = t2.organization_id 
		where employee_no in 
		 <foreach collection="userCodes" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
         </foreach>
	</select>
	
	<select id="getLoginsCount" resultType="Map" parameterType="java.lang.String">
		SELECT 
		    DATE_FORMAT(create_time, '%Y-%m') AS loginMonth,
		    COUNT(1) AS loginNumbers
		from (		
			SELECT create_time FROM toa_sys_access_log t1
			JOIN (
					SELECT user_code as emp_code, max(create_time) AS latest
					FROM toa_sys_access_log
					where YEAR(create_time) = #{queryYear}
					<if test="ssoOrgCode != null and ssoOrgCode != ''">
						and sso_org_code = #{ssoOrgCode}
					</if>
					GROUP BY DATE_FORMAT(create_time,  '%Y-%m-%d'),user_code
				) t2 ON t1.user_code = t2.emp_code AND t1.create_time = t2.latest
				order by create_time desc
		) t
		GROUP BY 
		    loginMonth
		ORDER BY 
		    loginMonth
	</select>
	
	<select id="getLoginsNumbers" resultType="Map" parameterType="java.lang.String">
			SELECT 
			    DATE_FORMAT(create_time, '%Y-%m') AS loginMonth,
			    COUNT(1) AS loginNumbers
			FROM 
			    toa_sys_access_log
			where  
			YEAR(create_time) = #{queryYear}
			<if test="ssoOrgCode != null and ssoOrgCode != ''">
				and sso_org_code = #{ssoOrgCode}
			</if>
			<if test="source != null and source == 'PC'.toString()">
				and source = #{source}
			</if>
			<if test="source != null and source == 'WX'.toString()">
				and source in ('WX','DD')
			</if>
			GROUP BY 
			    loginMonth
			ORDER BY 
			    loginMonth
	</select>
	
	<select id="selectAccessMonth" resultType="Map" parameterType="java.lang.String">
		select * from toa_sys_access_month
		where login_month = #{loginMonth}
	</select>
	
	<insert id="insertAccessMonth">
		 insert into toa_sys_access_month values(#{id},#{loginMonth},#{loginNumbers},null,null,null)
	</insert>
	
	<update id="updateAccessMonth">
		 update toa_sys_access_month 
		 <trim prefix="set" suffixOverrides=",">
		 	<if test="loginNumbers !=null and loginNumbers !=''">login_numbers = #{loginNumbers},</if>
		 	<if test="loginPCNumbers !=null and loginPCNumbers !=''">logins_pc_numbers = #{loginPCNumbers},</if>
		 	<if test="loginWXNumbers !=null and loginWXNumbers !=''">logins_wx_numbers = #{loginWXNumbers},</if>
		 </trim>
		 where login_month = #{loginMonth}
	</update>
	
	<select id="getLoginsMonth" resultType="Map" parameterType="java.lang.String">
		select login_month as loginMonth,
		<if test="source == null or source == ''">
			CONVERT(IFNULL(login_numbers, 0), SIGNED) as loginNumbers 
		</if>
		<if test="source != null and source == 'PC'.toString()">
			CONVERT(IFNULL(logins_pc_numbers, 0), SIGNED)  as loginNumbers 
		</if>
		<if test="source != null and source == 'WX'.toString()">
			CONVERT(IFNULL(logins_wx_numbers, 0), SIGNED) as loginNumbers 
		</if>
		from toa_sys_access_month
		where LEFT(login_month, 4) = #{queryYear}
	</select>
	
	<select id="selectTotalEmployee" parameterType="String" resultType="Long">
		select count(1) from cust_emp_base
		where is_deleted = 'N' and employee_status in ('1','5','6','9','11','12','88','99','13')
 		and employee_name not in ('admin', 'ts') and sso_org_code = #{ssoOrgCode}
	</select>
	
	<select id="sysUseAnalysisList" parameterType="Map" resultType="Map">
		SELECT
			t3.organization_id,
			t3.`name`,
			count( t1.employee_id ) as totalNumbers,
			count(t2.login_time) as useNumbers,
			sum(case when t2.login_time is null then 1 else 0 end ) as unUseNumbers,
			CEIL(sum(case when t2.login_time is not null then 1 else 0 end )/count( t1.employee_id ) * 100) as unUseRate
		FROM
			cust_emp_base t1
			LEFT JOIN (    
		        SELECT DISTINCT user_code    
		        FROM toa_sys_access_log    
		        WHERE create_time BETWEEN #{startDate} AND #{endDate}    
		    ) AS access_logs ON t1.employee_no = access_logs.user_code
			LEFT JOIN comm_login_logs t2 ON t1.employee_no = t2.user_code AND t2.user_code = access_logs.user_code
			LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id 
		WHERE
			t1.is_deleted = 'N' 
			AND t1.employee_status IN ( '1', '5', '6', '9', '11', '12', '88', '99', '13' ) 
			AND t3.`name` IS NOT NULL 
			AND t3.sso_org_code = #{ssoOrgCode}
			and t1.employee_name not in ('admin', 'ts')
		GROUP BY
			t3.organization_id 
		ORDER BY
			${sidx}
			${sord}
	</select>
	
	<select id="sysUseAnalysisDetailList" parameterType="Map" resultType="Map">
			SELECT emp.employee_no,emp.employee_name,org.`name`,t1.create_time FROM toa_sys_access_log t1
			JOIN (
					SELECT user_code as emp_code, max(create_time) AS latest
					FROM toa_sys_access_log
					where create_time BETWEEN #{startDate} AND #{endDate}
					GROUP BY DATE_FORMAT(create_time,  '%Y-%m-%d'),user_code
				) t2 ON t1.user_code = t2.emp_code AND t1.create_time = t2.latest	
			RIGHT join cust_emp_base emp on t1.user_code = emp.employee_no
			LEFT JOIN comm_organization org on emp.org_id = org.organization_id
			where 
			emp.is_deleted = 'N'
			AND emp.employee_status IN ( '1', '5', '6', '9', '11', '12', '88', '99', '13' ) 
			AND emp.sso_org_code = #{ssoOrgCode} 
			and emp.employee_name not in ('admin', 'ts')
			<if test="organizationId != null and organizationId != ''">
				and org.organization_id = #{organizationId}
			</if>
			<if test="useStatus != null and useStatus == '1'.toString()">
				and t1.create_time is not null
			</if>
			<if test="useStatus != null and useStatus == '2'.toString()">
				and t1.create_time is null
			</if>
			GROUP BY emp.employee_no,emp.employee_name,org.`name`
	</select>
</mapper>