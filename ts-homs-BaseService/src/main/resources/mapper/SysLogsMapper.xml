<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapper.SysLogsMapper">
	<resultMap id="BaseResultMap" type="cn.trasen.homs.base.model.SysLogs">
		<!--
      WARNING - @mbg.generated
    -->
		<id column="id" jdbcType="VARCHAR" property="id" />
		<result column="module" jdbcType="VARCHAR" property="module" />
		<result column="func_name" jdbcType="VARCHAR" property="funcName" />
		<result column="params" jdbcType="VARCHAR" property="params" />
		<result column="operation_desc" jdbcType="VARCHAR"
			property="operationDesc" />
		<result column="operation_user" jdbcType="VARCHAR"
			property="operationUser" />
		<result column="operation_user_name" jdbcType="VARCHAR"
			property="operationUserName" />
		<result column="operation_time" jdbcType="TIMESTAMP"
			property="operationTime" />
		<result column="operation_ip" jdbcType="VARCHAR" property="operationIp" />
		<result column="operation_type" jdbcType="VARCHAR"
			property="operationType" />
		<result column="recource" jdbcType="VARCHAR" property="recource" />
	</resultMap>

	<select id="getDataList" resultMap="BaseResultMap"
		parameterType="cn.trasen.homs.base.model.SysLogs"> select * from
		comm_sys_logs <where>
			<if test="operationUser != null and operationUser != ''">
				AND operation_user LIKE CONCAT(CONCAT('%', #{operationUser}),
		'%')
			</if>
			<if test="operationUserName != null and operationUserName != ''">
				AND operation_user_name LIKE CONCAT(CONCAT('%',
		#{operationUserName}), '%')
			</if>
			<if test="operationDesc != null and operationDesc != ''">
				AND operation_desc LIKE CONCAT(CONCAT('%', #{operationDesc}),
		'%')
			</if>
			<if test="beginDate != null and beginDate != ''">
				AND operation_time BETWEEN #{beginDate} AND #{endDate}
			</if>
		</where>
	</select>
</mapper>