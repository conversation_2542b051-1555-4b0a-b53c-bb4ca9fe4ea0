<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapper.VersionRecordMapper">
	<resultMap id="BaseResultMap" type="cn.trasen.homs.base.model.VersionRecord">
		<!--
      WARNING - @mbg.generated
    -->
		<result column="id" jdbcType="VARCHAR" property="id" />
		<result column="version_id" jdbcType="VARCHAR" property="versionId" />
		<result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
		<result column="create_user" jdbcType="VARCHAR" property="createUser" />
		<result column="create_user_name" jdbcType="VARCHAR"
			property="createUserName" />
		<result column="update_user" jdbcType="VARCHAR" property="updateUser" />
		<result column="update_user_name" jdbcType="VARCHAR"
			property="updateUserName" />
		<result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
		<result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
	</resultMap>

	<select id="selectVersionRecord"
		resultType="cn.trasen.homs.base.model.Version" parameterType="String">
		SELECT v.*,vr.id as rid from comm_version v
		LEFT JOIN comm_version_record vr on v.id = vr.version_id and
		vr.create_user = #{userCode}
		where v.IS_DELETED = 'N'
		order by CREATE_DATE desc
		limit 1
	</select>
</mapper>