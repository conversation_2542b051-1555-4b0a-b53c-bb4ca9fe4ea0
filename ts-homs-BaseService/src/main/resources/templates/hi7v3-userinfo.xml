<PRPM_IN303010UV01 xmlns="urn:hl7-org:v3" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ITSVersion="XML_1.0" xsi:schemaLocation="urn:hl7-org:v3 ../multicacheschemas/PRPM_IN301010UV01.xsd">
	<id root="2.16.156.10011.2.5.1.1" extension="GUID"/>
	<creationTime value="创建时间"/>
	<interactionId root="2.16.840.1.113883.1.6" extension="P5311"/>
	<processingCode code="P"/>
	<processingModeCode code="I"/>
	<acceptAckCode code="AL"/>
	<receiver typeCode="RCV">
		<telecom/>
		<device classCode="DEV" determinerCode="INSTANCE">
			<id root="2.16.156.10011.0.1.1" extension="JCPT"/>
		</device>
	</receiver>
	<sender typeCode="SND">
		<telecom/>
		<device classCode="DEV" determinerCode="INSTANCE">
			<id root="2.16.156.10011.0.1.2" extension="OA"/>
		</device>
	</sender>
	<controlActProcess classCode="CACT" moodCode="EVN">
		<subject typeCode="SUBJ">
			<registrationRequest classCode="REG" moodCode="RQO">
				<statusCode code="active"/>
				<subject1 typeCode="SBJ">
					<healthCareProvider classCode="PROV">
						<id>
							<item root="2.16.156.10011.1.4" extension="人员id" value=""  />
							
							<item root="personcode" extension="人员工号"/>
						</id>
						
						<code code="1" codeSystem="2.16.156.10011.2.3.3.10" codeSystemName="专业技术职务代码（ GB/T 8561-1988）">
							<displayName value="专业技术职务 [coldate]:正高"/>
						</code>
						<addr xsi:type="DSET_AD"/>
						
                         <levelCode  code=""/>

                        <emplType  value="01" />

                        <expertFlag  value="" />
                     
                        <emplTel  value="电话" />
                     
                        <brithdayPalce value="" />
                     
                        <remarks  value="" />                   
						
						<telecom xsi:type="DSET_TEL"/>
					
						<statusCode code="1"/>
						
						<effectiveTime>
							<low value="20100101"/> 
							<high value="20501231"/>  
						</effectiveTime>
						<healthCarePrincipalPerson classCode="PSN" determinerCode="INSTANCE">
						
							<id>
								<item root="2.16.156.10011.1.3" extension="身份证号 [coldate]:120109197706015518"/>
							</id>
							<idCategory code="01" codeSystem="2.16.156.10011.*******" codeSystemName="身份证件类别代码表">
								<displayName value="居民身份证"/>
							</idCategory>
							
							<name xsi:type="LIST_EN">
								<item>
									
									<part value="刘永好" pycpde="LYH"/>
								</item>
							</name>
							
							<administrativeGenderCode code="1" codeSystem="2.16.156.10011.*******" codeSystemName="生理性别代码表（GB/T 2261.1）">
								<displayName value="男性"/>
							</administrativeGenderCode>
							
							<age value="33"/>

							<birthTime value="19570323"/>
						
							<asAffiliate classCode="AFFL">
							
								<code>
									<translation code="医院编码 [coldate]:医院编码" context="机构 [coldate]:取值"/>
									<translation code="院区编码 [coldate]:院区编码" context="院区 [coldate]:取值"/>
								</code>
							
								<effectiveTime/>
								<affiliatedPrincipalOrganization classCode="ORG" determinerCode="INSTANCE">
									<id>
										<item root="2.16.156.10011.1.26" extension="科室id[coldate]:xxx12345-X"/>
									</id>
									<name xsi:type="LIST_EN">
										<item>
											<part value="科室名称 [coldate]:呼吸内科"/>
										</item>
									</name>
								</affiliatedPrincipalOrganization>
							</asAffiliate>
							<birthplace classCode="">
								<addr/>
							</birthplace>
						</healthCarePrincipalPerson>
					</healthCareProvider>
					
					<assignedEntity classCode="ASSIGNED">
						<subjectOf2 typeCode="SBJ">
							<roleActivation>
								<reasonCode>
									<displayName>

										<translation flavorId="PoliticalOutlook" code="" displayName=""/>
									
										<translation flavorId="Education" code="" displayName=""/>
										
										<translation flavorId="FileNumber" value=""/>
										
										<translation flavorId="EmployeeCategory" code="" displayName=""/>
										
										<translation flavorId="StartWorkingTime" value=""/>
										
										<translation flavorId="TilteCode" code="" displayName=""/>
										
										<translation flavorId="TilteLevel" value=""/>
										
										<translation flavorId="PracticingCertificateNo" value=""/>
										
										<translation flavorId="PracticingCertificateTime" value=""/>
										
										<translation flavorId="Work" code="1" displayName="护士"/>
										
										<translation flavorId="WorkType" code="1" displayName=""/>
										
										<translation flavorId="WorkLevel" code="" displayName=""/>
										
										<translation flavorId="AppointmentPostTime" value=""/>
										
										<translation flavorId="EmploymentForm" code="" displayName=""/>
										
										<translation flavorId="School" value=""/>
										
										<translation flavorId="AcademicDegree" value=""/>
										
										<translation flavorId="SpecializesIn" value=""/>
										
										<translation flavorId="GraduationTime" value=""/>
										
										<translation flavorId="GetNVQTime" value=""/>
										
										<translation flavorId="QualificationCertificateNo" value=""/>
								        
                                        <translation  flavorId="is_manager" value=""/>
										
										<translation flavorId="NVQLevel" value=""/>
										
										<translation flavorId="AppointmentNVQ" code="" displayName=""/>
										
										<translation flavorId="AppointmentNVQLevel" value=""/>
										
										<translation flavorId="DelFlag" value=""/>
										
                                        <translation  flavorId="HISDEPTID" value=""/>
										
                                        <translation  flavorId="HISDEPTCODE" value=""/>
										
                                        <translation  flavorId="HRPDEPTCODE" value=""/>
										
                                        <translation  flavorId="HRPDEPTNAME" value=""/>
									</displayName>
									<originalText>
										
										<translation flavorId="" value=""/>
									</originalText>
								</reasonCode>
							</roleActivation>
						</subjectOf2>
					</assignedEntity>
					
				</subject1>
				
				<author typeCode="AUT">
					<assignedEntity classCode="ASSIGNED">
						
						<id>
							<item root="2.16.156.10011.1.4" extension="注册人员ID[coldate]:120109197706015518"/>
						</id>
						<assignedPerson classCode="PSN" determinerCode="INSTANCE">
							<name xsi:type="LIST_EN">
								<item>
									<part value="注册人员[coldate]:李人事"/>
								</item>
							</name>
						</assignedPerson>
						<representedOrganization classCode="ORG" determinerCode="INSTANCE">
							
							<id>
								<item root="2.16.156.10011.1.26" extension="注册人员科室ID[coldate]:xxx12345-X"/>
							</id>
							<name xsi:type="LIST_EN">
								<item>
									<part value="注册人员科室[coldate]:人事科"/>
								</item>
							</name>
							<contactParty classCode="CON">
								<contactPerson classCode="PSN" determinerCode="INSTANCE">
									<name xsi:type="LIST_EN">
										<item>
											<part value=""/>
										</item>
									</name>
								</contactPerson>
							</contactParty>
						</representedOrganization>
					</assignedEntity>
				</author>
			</registrationRequest>
		</subject>
	</controlActProcess>
</PRPM_IN303010UV01>
