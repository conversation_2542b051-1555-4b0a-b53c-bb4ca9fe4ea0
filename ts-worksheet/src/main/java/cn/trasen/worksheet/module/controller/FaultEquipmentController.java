package cn.trasen.worksheet.module.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.annotation.NoRepeatSubmit;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.dto.inputVo.FaultEquipmentInputVo;
import cn.trasen.worksheet.module.dto.outVo.FaultEquipmentOutVo;
import cn.trasen.worksheet.module.service.WsFaultEquipmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * <AUTHOR>
 * @date: 2021/7/2 16:36
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Api(tags = "设备信息管理")
@RestController
public class FaultEquipmentController {

    @Autowired
    private WsFaultEquipmentService faultEquipmentService;

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="保存或修改设备信息管理")
    @ApiOperation(value = "保存或修改设备信息管理", notes = "保存或修改设备信息管理")
    @PostMapping("/faultEquipment/save")
    public PlatformResult save(@RequestBody @Validated FaultEquipmentInputVo faultEquipmentInputVo) {
        return PlatformResult.success(faultEquipmentService.saveEquipment(faultEquipmentInputVo));
    }

    @ControllerLog(description="查询详情")
    @ApiOperation(value = "查询详情", notes = "查询详情")
    @PostMapping("/faultEquipment/selectOne")
    public PlatformResult selectOne(String pkFaultEquipmentId){
        return PlatformResult.success(faultEquipmentService.selectOne(pkFaultEquipmentId));
    }

    @ControllerLog(description="设备信息分页列表")
    @ApiOperation(value = "设备信息分页列表", notes = "设备信息分页列表")
    @PostMapping("/faultEquipment/selectPageList")
    public DataSet<FaultEquipmentOutVo> selectPageList(Page page, FaultEquipmentInputVo faultEquipmentInputVo){
        List<FaultEquipmentOutVo> faultEquipmentOutVos = faultEquipmentService.selectPageList(page, faultEquipmentInputVo);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),faultEquipmentOutVos);
    }


    @ControllerLog(description="查看二维码信息")
    @ApiOperation(value = "查看二维码信息", notes = "查看二维码信息")
    @GetMapping("/faultEquipment/checkTheQRCodeInfo/{pkFaultEquipmentId}")
    public PlatformResult checkTheQRCodeInfo(@PathVariable @ApiParam(value = "设备id") String pkFaultEquipmentId){
        return PlatformResult.success(faultEquipmentService.checkTheQRCodeInfo(pkFaultEquipmentId));
    }


    @ControllerLog(description="设备信息列表（不分页）")
    @ApiOperation(value = "设备信息列表（不分页）", notes = "设备信息列表（不分页）")
    @PostMapping("/faultEquipment/selectAllList")
    public PlatformResult selectAllList(FaultEquipmentInputVo faultEquipmentInputVo){
        return PlatformResult.success(faultEquipmentService.selectAllList(faultEquipmentInputVo));    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="逻辑删除设备信息")
    @ApiOperation(value = "逻辑删除设备信息", notes = "逻辑删除设备信息")
    @PostMapping("/faultEquipment/deleteEquipment/{pkFaultEquipmentId}")
    public PlatformResult deleteEquipment(@PathVariable @ApiParam(value = "设备id") String pkFaultEquipmentId){
        return PlatformResult.success(faultEquipmentService.deleteEquipmenById(pkFaultEquipmentId));
    }
}
