package cn.trasen.worksheet.module.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.annotation.NoRepeatSubmit;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeBaseInputVo;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeBaseMobileInputVo;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeBaseOperationInputVo;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeBaseQueryInputVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeBasePageOutVo;
import cn.trasen.worksheet.module.entity.WsKnowledgeBase;
import cn.trasen.worksheet.module.service.KnowledgeBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**知识库管理
 * <AUTHOR>
 * @date: 2021/8/2 11:47
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@RestController
@Api(tags = "知识库管理")
public class KnowledgeBaseController {

    @Autowired
    private KnowledgeBaseService knowledgeBaseService;

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="知识库保存或修改")
    @ApiOperation(value = "知识库保存或修改", notes = "知识库保存或修改")
    @PostMapping("/knowledgeBase/save")
    public PlatformResult save(@RequestBody @Validated KnowledgeBaseInputVo knowledgeBaseInputVo){
        return PlatformResult.success(knowledgeBaseService.saveOrUpdateKnowledgeBase(knowledgeBaseInputVo));
    }

    @ControllerLog(description="知识库分页列表")
    @ApiOperation(value = "知识库分页列表", notes = "知识库分页列表")
    @PostMapping("/knowledgeBase/knowledgeBasePageList")
    public DataSet<KnowledgeBasePageOutVo> knowledgeBasePageList(Page page, @Validated KnowledgeBaseQueryInputVo knowledgeBaseQueryInputVo){
        List<KnowledgeBasePageOutVo> knowledgeBasePageOutVoList = knowledgeBaseService.selectPageList(page, knowledgeBaseQueryInputVo);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),knowledgeBasePageOutVoList);
    }

    @ControllerLog(description="创建工单右侧知识库列表")
    @ApiOperation(value = "创建工单右侧知识库列表", notes = "创建工单右侧知识库列表")
    @PostMapping("/knowledgeBase/selectPageCreateWorkList")
    public DataSet<KnowledgeBasePageOutVo> selectPageCreateWorkList(Page page, @Validated KnowledgeBaseQueryInputVo knowledgeBaseQueryInputVo){
        List<KnowledgeBasePageOutVo> knowledgeBasePageOutVoList = knowledgeBaseService.selectPageCreateWorkList(page, knowledgeBaseQueryInputVo);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),knowledgeBasePageOutVoList);
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="知识点点赞")
    @ApiOperation(value = "知识点点赞", notes = "知识点点赞")
    @GetMapping("/knowledgeBase/giveALike/{pkKnowledgeBaseId}")
    public PlatformResult giveALike(@PathVariable String pkKnowledgeBaseId){
        return PlatformResult.success(knowledgeBaseService.giveALike(pkKnowledgeBaseId));
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="知识点取消赞")
    @ApiOperation(value = "知识点取消赞", notes = "知识点取消赞")
    @GetMapping("/knowledgeBase/cancelThePraise/{pkKnowledgeBaseId}")
    public PlatformResult cancelThePraise(@PathVariable String pkKnowledgeBaseId){
        return PlatformResult.success(knowledgeBaseService.cancelThePraise(pkKnowledgeBaseId));
    }

    @ControllerLog(description="知识点详情")
    @ApiOperation(value = "知识点详情", notes = "知识点详情")
    @GetMapping("/knowledgeBase/getKnowledgeBaseInfo/{pkKnowledgeBaseId}")
    public PlatformResult getKnowledgeBaseInfo(@PathVariable String pkKnowledgeBaseId){
        return PlatformResult.success(knowledgeBaseService.selectOneInfo(pkKnowledgeBaseId));
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="移出")
    @ApiOperation(value = "移出", notes = "移出")
    @PostMapping("/knowledgeBase/remove")
    public PlatformResult remove(@RequestBody @Validated KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo){
        return PlatformResult.success(knowledgeBaseService.remove(knowledgeBaseOperationInputVo,new WsKnowledgeBase()));
    }

//    @ApiOperation(value = "审批不通过", notes = "审批不通过")
//    @PostMapping("/knowledgeBase/notApproved")
//    public PlatformResult notApproved(@RequestBody @Validated KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo){
//        return PlatformResult.success(knowledgeBaseService.notApproved(knowledgeBaseOperationInputVo,new WsKnowledgeBase()));
//    }
//
//    @ApiOperation(value = "审批通过", notes = "审批通过")
//    @PostMapping("/knowledgeBase/approve")
//    public PlatformResult approve(@RequestBody @Validated KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo){
//        return PlatformResult.success(knowledgeBaseService.approve(knowledgeBaseOperationInputVo,new WsKnowledgeBase()));
//    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="撤回")
    @ApiOperation(value = "撤回", notes = "撤回")
    @PostMapping("/knowledgeBase/withdraw")
    public PlatformResult withdraw(@RequestBody @Validated KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo){
        return PlatformResult.success(knowledgeBaseService.withdraw(knowledgeBaseOperationInputVo,new WsKnowledgeBase()));
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="移入")
    @ApiOperation(value = "移入", notes = "移入")
    @PostMapping("/knowledgeBase/move")
    public PlatformResult move(@RequestBody @Validated KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo){
        return PlatformResult.success(knowledgeBaseService.move(knowledgeBaseOperationInputVo,new WsKnowledgeBase()));
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="提交")
    @ApiOperation(value = "提交", notes = "提交")
    @PostMapping("/knowledgeBase/submit")
    public PlatformResult submit(@RequestBody @Validated KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo){
        return PlatformResult.success(knowledgeBaseService.submit(knowledgeBaseOperationInputVo,new WsKnowledgeBase()));
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="逻辑删除")
    @ApiOperation(value = "逻辑删除", notes = "逻辑删除")
    @PostMapping("/knowledgeBase/delete")
    public PlatformResult delete(@RequestBody @Validated KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo){
        return PlatformResult.success(knowledgeBaseService.delete(knowledgeBaseOperationInputVo,new WsKnowledgeBase()));
    }

    @ControllerLog(description="知识库类型的知识点信息树结构列表")
    @ApiOperation(value = "知识库类型的知识点信息树结构列表", notes = "知识库类型的知识点信息树结构列表")
    @GetMapping("/knowledgeBase/selectKnowledgeTreeAllList")
    public PlatformResult selectKnowledgeTreeAllList(@ApiParam(value = "分类名称") String categoryName) {
        return PlatformResult.success(knowledgeBaseService.selectKnowledgeTreeAllList(categoryName));
    }

    @ControllerLog(description="查询知识库菜单，各页签数据")
    @ApiOperation(value = "查询知识库菜单，各页签数据", notes = "查询知识库菜单，各页签数据")
    @GetMapping("/knowledgeBase/selectCountsGroupStatus")
    public PlatformResult selectCountsGroupStatus(KnowledgeBaseQueryInputVo knowledgeBaseQueryInputVo){
        return PlatformResult.success(knowledgeBaseService.selectCountsGroupStatus(knowledgeBaseQueryInputVo));
    }

    @ControllerLog(description="知识点查阅-近期热门、科室常见")
    @ApiOperation(value = "知识点查阅-近期热门、科室常见", notes = "知识点查阅-近期热门、科室常见")
    @GetMapping("/knowledgeBase/knowledgeLikeRank")
    public PlatformResult knowledgeLikeRank(KnowledgeBaseMobileInputVo knowledgeBaseMobileInputVo){
        return PlatformResult.success(knowledgeBaseService.knowledgeLikeRank(knowledgeBaseMobileInputVo));
    }


}
