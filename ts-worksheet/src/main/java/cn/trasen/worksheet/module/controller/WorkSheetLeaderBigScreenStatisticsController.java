package cn.trasen.worksheet.module.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.worksheet.common.util.DateUtils;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetScreenListOutVo;
import cn.trasen.worksheet.module.service.WsEvaluationService;
import cn.trasen.worksheet.module.service.WsSheetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 领导免登录大屏
 * <AUTHOR>
 * @date: 2021/11/11 11:45
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Api(tags = "领导免登录大屏")
@RestController
public class WorkSheetLeaderBigScreenStatisticsController {

    @Autowired
    private WsSheetService wsSheetService;

    @Autowired
    private WsEvaluationService wsEvaluationService;

    @ControllerLog(description="科室办理业务Top榜单")
    @ApiOperation(value = "科室办理业务Top榜单", notes = "科室办理业务Top榜单")
    @GetMapping("/leaderBigScreenStatistics/getDeptCountTopDatas")
    public DataSet<Map<String, Object>> getDeptCountTopDatas(Page page,
                                                             @ApiParam(value = "有值为科室级、null为全院级")String fkDeptId) {
        List<Map<String, Object>> deptCountTopDatas = wsSheetService.getDeptCountTopDatas(page, DateUtils.getMonthStart(), DateUtils.getCurrentTime(), fkDeptId);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                deptCountTopDatas
        );
    }

    @ControllerLog(description="大屏工单关键数据指标")
    @ApiOperation(value = "大屏工单关键数据指标", notes = "大屏工单关键数据指标")
    @GetMapping("/leaderBigScreenStatistics/getKeyDataIndicatorsOfWorkOrder")
    public PlatformResult getKeyDataIndicatorsOfWorkOrderG(@ApiParam(value = "有值为科室级、null为全院级") String fkDeptId) {
        UserInfoHolder.getToken();
        return PlatformResult.success(wsSheetService.getKeyDataIndicatorsOfWorkOrder(fkDeptId));
    }

    @ControllerLog(description="科室工单质量")
    @ApiOperation(value = "科室工单质量", notes = "科室工单质量")
    @GetMapping("/leaderBigScreenStatistics/getDepartmentWorkOrderQuality")
    public PlatformResult getDepartmentWorkOrderQuality(@ApiParam(value = "有值为科室级、null为全院级") String fkDeptId,
                                                        @ApiParam(value = "返回数据条数")int limit) {
        return PlatformResult.success(wsSheetService.getDepartmentWorkOrderQuality(fkDeptId, DateUtils.getMonthStart(), DateUtils.getCurrentTime(),limit));
    }

    @ControllerLog(description="大屏处理中工单")
    @ApiOperation(value = "大屏处理中工单", notes = "大屏处理中工单")
    @GetMapping("/leaderBigScreenStatistics/wsSheetScreenPageList")
    public DataSet<WsWsSheetScreenListOutVo> wsSheetScreenPageList(Page page, @ApiParam(value = "有值为科室级、null为全院级") String fkDeptId) {
        List<WsWsSheetScreenListOutVo> sheetScreenListOutVos = wsSheetService.wsSheetScreenPageList(page, fkDeptId);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                sheetScreenListOutVos
        );
    }

    @ControllerLog(description="科室处理情况")
    @ApiOperation(value = "科室处理情况", notes = "科室处理情况")
    @GetMapping("/leaderBigScreenStatistics/getDayGroupByDept")
    public DataSet<Map<String, Object>> getDayGroupByDept(Page page) {
        List<Map<String, Object>> dayGroupByDept = wsSheetService.getDayGroupByDept(page);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                dayGroupByDept
        );
    }

    @ControllerLog(description="本月工单质量")
    @ApiOperation(value = "本月工单质量", notes = "本月工单质量")
    @GetMapping("/leaderBigScreenStatistics/getEvaluationGroupByDeptAverageScore")
    public DataSet<Map<String, Object>> getEvaluationGroupByDeptAverageScore(Page page) {
        List<Map<String, Object>> evaluationGroupByDeptAverageScore = wsEvaluationService.getEvaluationGroupByDeptAverageScore(page);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                evaluationGroupByDeptAverageScore
        );
    }

    @ControllerLog(description="大屏今日分配工单")
    @ApiOperation(value = "大屏今日分配工单", notes = "大屏今日分配工单")
    @GetMapping("/leaderBigScreenStatistics/wsSheetDistributionScreenPageList")
    public DataSet<Map<String, Object>> wsSheetDistributionScreenPageList(Page page,@ApiParam(value = "有值为科室级、null为全院级") String fkDeptId) {
        List<Map<String, Object>> distributionScreenPageList = wsSheetService.wsSheetDistributionScreenPageList(page, fkDeptId,DateUtils.getDayStart(),DateUtils.getCurrentTime());
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                distributionScreenPageList
        );
    }
}
