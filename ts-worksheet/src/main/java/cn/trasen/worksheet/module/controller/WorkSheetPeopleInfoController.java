package cn.trasen.worksheet.module.controller;

import java.util.List;
import java.util.Optional;

import org.apache.http.HttpRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Lists;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetPeoppleInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsSheetPeopleInfoOutVo;
import cn.trasen.worksheet.module.service.WsSheetPeopleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date: 2021/6/24 13:52
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Api(tags = "工单业务人员管理")
@RestController
public class WorkSheetPeopleInfoController {

    @Autowired
    private WsSheetPeopleService wsSheetPeopleService;

    @ControllerLog(description="处理人员信息")
    @ApiOperation(value = "处理人员信息", notes = "处理人员信息")
    @PostMapping("/workSheetPeopple/getPeopleInfoList")
    public DataSet<WsSheetPeopleInfoOutVo> getPeopleListInfo(HttpRequest request, Page page, WsWorkSheetPeoppleInputVo wsWorkSheetPeoppleInputVo){
        List<WsSheetPeopleInfoOutVo> oaPeopleAndExternalPersonnelList = wsSheetPeopleService.getPeopleListInfo(page,wsWorkSheetPeoppleInputVo);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),oaPeopleAndExternalPersonnelList);
   
    }


    @ControllerLog(description="处理人员信息")
    @ApiOperation(value = "处理人员信息", notes = "处理人员信息")
    @PostMapping("/workSheetPeopple/getNoPagePeopleInfoList")
    public DataSet<WsSheetPeopleInfoOutVo> getNoPagePeopleInfoList(HttpRequest request,Page page, WsWorkSheetPeoppleInputVo wsWorkSheetPeoppleInputVo){
        if(page.getPageNo()> IndexEnum.ONE.getValue()){
            return new DataSet<>(page.getPageNo(), IndexEnum.ZERO.getValue(), IndexEnum.ONE.getValue(), IndexEnum.ZERO.getValue(),Lists.newArrayList());
        }
        List<WsSheetPeopleInfoOutVo> peopleListInfo =
                Optional.ofNullable(wsSheetPeopleService.getPeopleListInfo(wsWorkSheetPeoppleInputVo))
                        .map(temp -> temp)
                        .orElseGet(() -> Lists.newArrayList());
        return new DataSet<>(IndexEnum.ONE.getValue(), peopleListInfo.size(), IndexEnum.ONE.getValue(), peopleListInfo.size(),peopleListInfo);

    }

    @ControllerLog(description="获取当前登录人信息")
    @ApiOperation(value = "获取当前登录人信息", notes = "获取当前登录人信息")
    @GetMapping("/workSheetPeopple/loginPersonInfo")
    public PlatformResult loginPersonInfo(){
        return PlatformResult.success(wsSheetPeopleService.loginPersonInfo());
    }

}
