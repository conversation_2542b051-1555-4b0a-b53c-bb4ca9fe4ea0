package cn.trasen.worksheet.module.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.worksheet.common.util.DateUtils;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetHomeListInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetMobileStatisticalInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetStatisticalInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkSheetHomeListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkSheetOderAgingOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkSheetSendAgingOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetScreenListOutVo;
import cn.trasen.worksheet.module.service.KnowledgeBaseService;
import cn.trasen.worksheet.module.service.WsEvaluationService;
import cn.trasen.worksheet.module.service.WsSheetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 大屏、统计数据
 */
@Api(tags = "大屏、统计数据管理")
@RestController
public class WorkSheetStatisticsDataController {

    @Autowired
    private WsSheetService wsSheetService;
    @Autowired
    private KnowledgeBaseService knowledgeBaseService;
    @Autowired
    private WsEvaluationService wsEvaluationService;

    @ControllerLog(description="科室办理业务Top榜单")
    @ApiOperation(value = "科室办理业务Top榜单", notes = "科室办理业务Top榜单")
    @GetMapping("/statisticsData/getDeptCountTopDatas")
    public DataSet<Map<String, Object>> getDeptCountTopDatas(Page page,
                                                             @ApiParam(value = "有值为科室级、null为全院级")String fkDeptId) {
        List<Map<String, Object>> deptCountTopDatas = wsSheetService.getDeptCountTopDatas(page, DateUtils.getMonthStart(), DateUtils.getCurrentTime(), fkDeptId);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                deptCountTopDatas
        );

    }



    @ControllerLog(description="一级故障类型各类型工单数量")
    @ApiOperation(value = "一级故障类型各类型工单数量", notes = "一级故障类型各类型工单数量")
    @GetMapping("/statisticsData/getLevelOneFaultTypeDatas")
    public PlatformResult getLevelOneFaultTypeDatas(@ApiParam(value = "开始时间") String beginTime,
                                                    @ApiParam(value = "结束时间") String endTime) {
        return PlatformResult.success(wsSheetService.getLevelOneFaultTypeDatas(beginTime, endTime, UserInfoHolder.getCurrentUserInfo().getDeptId()));
    }

    @ControllerLog(description="知识库点赞Top榜单")
    @ApiOperation(value = "知识库点赞Top榜单", notes = "知识库点赞Top榜单")
    @GetMapping("/statisticsData/getKnowledgeLikeCountTopDatas")
    public DataSet<Map<String, Object>> getKnowledgeLikeCountTopDatas(Page page,
                                                                      @ApiParam(value = "1为科室级、null为全院级") String type) {
        List<Map<String, Object>> knowledgeLikeCountTopDatas = wsSheetService.getKnowledgeLikeCountTopDatas(page, null, null, UserInfoHolder.getCurrentUserInfo().getDeptId());
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                knowledgeLikeCountTopDatas
        );

    }



    @ControllerLog(description="大屏工单关键数据指标")
    @ApiOperation(value = "大屏工单关键数据指标", notes = "大屏工单关键数据指标")
    @GetMapping("/statisticsData/leader/getKeyDataIndicatorsOfWorkOrder")
    public PlatformResult getKeyDataIndicatorsOfWorkOrderG(@ApiParam(value = "有值为科室级、null为全院级") String fkDeptId) {
        return PlatformResult.success(wsSheetService.getKeyDataIndicatorsOfWorkOrder(fkDeptId));
    }

    @ControllerLog(description="大屏工单关键数据指标")
    @ApiOperation(value = "大屏工单关键数据指标", notes = "大屏工单关键数据指标")
    @GetMapping("/statisticsData/getKeyDataIndicatorsOfWorkOrder")
    public PlatformResult getKeyDataIndicatorsOfWorkOrder(@ApiParam(value = "有值为科室级、null为全院级") String fkDeptId) {
        return PlatformResult.success(wsSheetService.getKeyDataIndicatorsOfWorkOrder(fkDeptId));
    }


    @ControllerLog(description="月度划分，提单、电话提单、办结趋势数据")
    @ApiOperation(value = "月度划分，提单、电话提单、办结趋势数据", notes = "月度划分，提单、电话提单、办结趋势数据")
    @GetMapping("/statisticsData/getHotTrend")
    public PlatformResult getHotTrend(@ApiParam(value = "1为科室级、null为全院级") String type) {
        return PlatformResult.success(wsSheetService.getHotTrend(type));
    }

    @ControllerLog(description="处理中工单")
    @ApiOperation(value = "处理中工单", notes = "处理中工单")
    @GetMapping("/statisticsData/getProcessTheWorkOrder")
    public PlatformResult getProcessTheWorkOrder(@ApiParam(value = "1为科室级、null为全院级") String type) {
        return PlatformResult.success(wsSheetService.getProcessTheWorkOrder(type));
    }

    @ControllerLog(description="科室工单数量")
    @ApiOperation(value = "科室工单数量", notes = "科室工单数量")
    @GetMapping("/statisticsData/getDeptWorkSheetCount")
    public PlatformResult getDeptWorkSheetCount(@ApiParam(value = "1为科室级、null为全院级") String type) {
        return PlatformResult.success(wsSheetService.getDeptWorkSheetCount(type));
    }

    @ControllerLog(description="科室工单质量")
    @ApiOperation(value = "科室工单质量", notes = "科室工单质量")
    @GetMapping("/statisticsData/getDepartmentWorkOrderQuality")
    public PlatformResult getDepartmentWorkOrderQuality(@ApiParam(value = "有值为科室级、null为全院级") String fkDeptId,
                                                        @ApiParam(value = "返回数据条数")int limit) {
        return PlatformResult.success(wsSheetService.getDepartmentWorkOrderQuality(fkDeptId, DateUtils.getMonthStart(), DateUtils.getCurrentTime(),limit));
    }

    @ControllerLog(description="服务台人员页面-统计指标")
    @ApiOperation(value = "服务台人员页面-统计指标", notes = "服务台人员页面-统计指标")
    @GetMapping("/statisticsData/getServiceDeskStaffStatisticalIndicators")
    public PlatformResult getServiceDeskStaffStatisticalIndicators(@ApiParam(value = "科室id") String fkDeptId) {
        return PlatformResult.success(wsSheetService.getServiceDeskStaffStatisticalIndicators(fkDeptId));
    }

    @ControllerLog(description="异常工单各种状态统计")
    @ApiOperation(value = "异常工单各种状态统计", notes = "异常工单各种状态统计")
    @GetMapping("/statisticsData/getAbnormalWorkSheetStatisCounts")
    public PlatformResult getAbnormalWorkSheetStatisCounts(@Validated WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        return PlatformResult.success(wsSheetService.getAbnormalWorkSheetStatisCounts(wsWorkSheetHomeListInputVo));
    }

    @ControllerLog(description="超期工单数据")
    @ApiOperation(value = "超期工单数据", notes = "超期工单数据")
    @GetMapping("/statisticsData/getExceedTimeWorkSheets")
    public DataSet<WsWorkSheetHomeListOutVo> getExceedTimeWorkSheets(Page page, @Validated WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        List<WsWorkSheetHomeListOutVo> exceedTimeWorkSheets = wsSheetService.getExceedTimeWorkSheets(page, wsWorkSheetHomeListInputVo);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                exceedTimeWorkSheets
        );
    }

    @ControllerLog(description="催办工单")
    @ApiOperation(value = "催办工单", notes = "催办工单")
    @GetMapping("/statisticsData/getHastenWorkSheets")
    public DataSet<WsWorkSheetHomeListOutVo> getHastenWorkSheets(Page page, @Validated WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        List<WsWorkSheetHomeListOutVo> hastenWorkSheets = wsSheetService.getHastenWorkSheets(page, wsWorkSheetHomeListInputVo);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                hastenWorkSheets
        );
    }

    @ControllerLog(description="今日终止/暂停工单")
    @ApiOperation(value = "今日终止/暂停工单", notes = "今日终止/暂停工单")
    @GetMapping("/statisticsData/getSuspendTerminateSheets")
    public DataSet<WsWorkSheetHomeListOutVo> getSuspendTerminateSheets(Page page, @Validated WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        List<WsWorkSheetHomeListOutVo> suspendTerminateSheets = wsSheetService.getSuspendTerminateSheets(page, wsWorkSheetHomeListInputVo);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                suspendTerminateSheets
        );
    }

    @ControllerLog(description="今日暂停工单")
    @ApiOperation(value = "今日暂停工单", notes = "今日暂停工单")
    @GetMapping("/statisticsData/getTodaySuspendedSheets")
    public DataSet<WsWorkSheetHomeListOutVo> getTodaySuspendedSheets(Page page, @Validated WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        List<WsWorkSheetHomeListOutVo> suspendTerminateSheets = wsSheetService.getTodaySuspendedSheets(page, wsWorkSheetHomeListInputVo);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                suspendTerminateSheets
        );
    }

    @ControllerLog(description="今日终止工单")
    @ApiOperation(value = "今日终止工单", notes = "今日终止工单")
    @GetMapping("/statisticsData/getTodayTerminationSheets")
    public DataSet<WsWorkSheetHomeListOutVo> getTodayTerminationSheets(Page page, @Validated WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        List<WsWorkSheetHomeListOutVo> suspendTerminateSheets = wsSheetService.getTodayTerminationSheets(page, wsWorkSheetHomeListInputVo);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                suspendTerminateSheets
        );
    }

    @ControllerLog(description="差评工单数据")
    @ApiOperation(value = "差评工单数据", notes = "差评工单数据")
    @GetMapping("/statisticsData/getBadReviewSheets")
    public DataSet<WsWorkSheetHomeListOutVo> getBadReviewSheets(Page page, @Validated WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        List<WsWorkSheetHomeListOutVo> badReviewSheets = wsSheetService.getBadReviewSheets(page, wsWorkSheetHomeListInputVo);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                badReviewSheets
        );

    }

    @ControllerLog(description="打回工单数据")
    @ApiOperation(value = "打回工单数据", notes = "打回工单数据")
    @GetMapping("/statisticsData/getBackSheets")
    public DataSet<WsWorkSheetHomeListOutVo> getBackSheets(Page page, @Validated WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        List<WsWorkSheetHomeListOutVo> backSheets = wsSheetService.getBackSheets(page, wsWorkSheetHomeListInputVo);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                backSheets
        );
    }

    @ControllerLog(description="协助工单数据")
    @ApiOperation(value = "协助工单数据", notes = "协助工单数据")
    @GetMapping("/statisticsData/getAssistWorkOrder")
    public DataSet<WsWorkSheetHomeListOutVo> getAssistWorkOrder(Page page, @Validated WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        List<WsWorkSheetHomeListOutVo> assistWorkOrder = wsSheetService.getAssistWorkOrder(page, wsWorkSheetHomeListInputVo);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                assistWorkOrder
        );
    }

    @ControllerLog(description="申请人首页-工单列表分页")
    @ApiOperation(value = "申请人首页-工单列表分页", notes = "申请人首页-工单列表分页")
    @PostMapping("/statisticsData/workSheetList")
    public DataSet<WsWsSheetListOutVo> workSheetList(Page page) {
        List<WsWsSheetListOutVo> workSheetPageList = wsSheetService.getApplicantWorkSheetPageList(page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), workSheetPageList);
    }

    @ControllerLog(description="处理人、报修人各状态工单数量")
    @ApiOperation(value = "处理人、报修人各状态工单数量", notes = "处理人、报修人各状态工单数量")
    @GetMapping("/statisticsData/getCountGroupByWorkStatus")
    public PlatformResult getCountGroupByWorkStatus(@Validated WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        return PlatformResult.success(wsSheetService.getCountGroupByWorkStatus(wsWorkSheetHomeListInputVo));
    }

    @ControllerLog(description="处理人员信息")
    @ApiOperation(value = "申请人首页-我的知识点", notes = "申请人首页-我的知识点")
    @GetMapping("/statisticsData/getMyKnowledgeBaseCount/{fkUserId}")
    public PlatformResult getMyKnowledgeBaseCount(@PathVariable @ApiParam(value = "用户id") String fkUserId) {
        return PlatformResult.success(knowledgeBaseService.getMyKnowledgeBaseCount(fkUserId));
    }

    @ControllerLog(description="工单综合评分")
    @ApiOperation(value = "工单综合评分", notes = "工单综合评分")
    @GetMapping("/statisticsData/getComprehensiveScoreOfWorkOrder")
    public PlatformResult getComprehensiveScoreOfWorkOrder(@ApiParam(value = "用户id") String fkUserId) {
        return PlatformResult.success(wsEvaluationService.getComprehensiveScoreOfWorkOrder(null, fkUserId, null, null));
    }

    @ControllerLog(description="提单及办结趋势")
    @ApiOperation(value = "提单及办结趋势", notes = "提单及办结趋势")
    @PostMapping("/statisticsData/getWorkOrderProcessing")
    public PlatformResult getWorkOrderProcessing(@RequestBody @Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsSheetService.getWorkOrderProcessing(wsWorkSheetStatisticalInputVo));
    }
    
    @ControllerLog(description="总未完成工单")
    @ApiOperation(value = "总未完成工单(不随时间变化:统计待派单、待接单、处理中、已暂停)", notes = "总未完成工单(不随时间变化:统计待派单、待接单、处理中、已暂停")
    @PostMapping("/statisticsData/getWorkOrderUnfinished")
    public PlatformResult getWorkOrderUnfinished(@RequestBody @Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsSheetService.getWorkOrderUnfinished(wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="工单各状态占比")
    @ApiOperation(value = "工单各状态占比", notes = "工单各状态占比")
    @PostMapping("/statisticsData/getWorkGroupByWorkStatusDatas")
    public PlatformResult getWorkGroupByWorkStatusDatas(@RequestBody @Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsSheetService.getWorkGroupByType(wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="工单各报修方式占比")
    @ApiOperation(value = "工单各报修方式占比", notes = "工单各报修方式占比")
    @PostMapping("/statisticsData/getWorkGroupByRepairTypeDatas")
    public PlatformResult getWorkGroupByRepairTypeDatas(@RequestBody @Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsSheetService.getWorkGroupByType(wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="工单各故障紧急程度占比")
    @ApiOperation(value = "工单各故障紧急程度占比", notes = "工单各故障紧急程度占比")
    @PostMapping("/statisticsData/getWorkGroupByFaultEmergencyDatas")
    public PlatformResult getWorkGroupByFaultEmergencyDatas(@RequestBody @Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsSheetService.getWorkGroupByType(wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="工单各故障影响范围数占比")
    @ApiOperation(value = "工单各故障影响范围数占比", notes = "工单各故障影响范围数占比")
    @PostMapping("/statisticsData/getWorkGroupByFaultAffectScopeDatas")
    public PlatformResult getWorkGroupByFaultAffectScopeDatas(@RequestBody @Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsSheetService.getWorkGroupByType(wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="科室-人员接单及处理详情")
    @ApiOperation(value = "科室-人员接单及处理详情", notes = "科室-人员接单及处理详情")
    @GetMapping("/statisticsData/getDeptReceiveWorkSheetDatas")
    public DataSet<Map<String, Object>> getDeptReceiveWorkSheetDatas(Page page, @Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        List<Map<String, Object>> deptReceiveWorkSheetDatas = wsSheetService.getDeptReceiveWorkSheetDatas(page, wsWorkSheetStatisticalInputVo);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                deptReceiveWorkSheetDatas
        );
    }
    
  //增加到人员20240321
    @ControllerLog(description="科室-人员接单及处理详情")
    @ApiOperation(value = "科室-人员接单及处理详情", notes = "科室-人员接单及处理详情")
    @GetMapping("/statisticsData/getDeptUserReceiveWorkSheetDatas")
    public DataSet<Map<String, Object>> getDeptUserReceiveWorkSheetDatas(Page page, @Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        List<Map<String, Object>> deptReceiveWorkSheetDatas = wsSheetService.getDeptUserReceiveWorkSheetDatas(page, wsWorkSheetStatisticalInputVo);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                deptReceiveWorkSheetDatas
        );
    }

    @ControllerLog(description="科室提单情况")
    @ApiOperation(value = "科室提单情况", notes = "科室提单情况")
    @GetMapping("/statisticsData/getDeptCountDatas")
    public PlatformResult getDeptBillOfLading(Page page, @Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsSheetService.getDeptBillOfLading(page, wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="一级故障分类(带时间区间查询)")
    @ApiOperation(value = "一级故障分类(带时间区间查询)", notes = "一级故障分类(带时间区间查询)")
    @GetMapping("/statisticsData/getLevelOneFaultTypeDatasToDate")
    public PlatformResult getLevelOneFaultTypeDatasToDate(@Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsSheetService.getLevelOneFaultTypeDatasToDate(wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="工单各设备故障分类数据统计")
    @ApiOperation(value = "工单各设备故障分类数据统计", notes = "工单各设备故障分类数据统计")
    @GetMapping("/statisticsData/getFaultEquipment")
    public PlatformResult getFaultEquipment(@Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsSheetService.getFaultEquipment(wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="完成质量分析-总评分、处理速度、服务态度、技术水平")
    @ApiOperation(value = "完成质量分析-总评分、处理速度、服务态度、技术水平", notes = "完成质量分析-总评分、处理速度、服务态度、技术水平")
    @GetMapping("/statisticsData/geTScoreAnalysis")
    public PlatformResult geTScoreAnalysis(@Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsEvaluationService.geTScoreAnalysis(wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="工单各评价类型（总评分、处理速度、服务态度、技术水平）的评价等级")
    @ApiOperation(value = "工单各评价类型（总评分、处理速度、服务态度、技术水平）的评价等级", notes = "工单各评价类型（总评分、处理速度、服务态度、技术水平）的评价等级")
    @GetMapping("/statisticsData/getEvaluationLevel")
    public PlatformResult getEvaluationLevel(@Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsEvaluationService.getEvaluationLevel(wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="工单各评价类型（总评分、处理速度、服务态度、技术水平）平均分")
    @ApiOperation(value = "工单各评价类型（总评分、处理速度、服务态度、技术水平）平均分", notes = "工单各评价类型（总评分、处理速度、服务态度、技术水平）平均分")
    @GetMapping("/statisticsData/getEvaluationAverageScore")
    public PlatformResult getEvaluationAverageScore(@Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsEvaluationService.getEvaluationAverageScore(wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="各科室工单评价各类型平均分及总评分平均分")
    @ApiOperation(value = "各科室工单评价各类型平均分及总评分平均分", notes = "各科室工单评价各类型平均分及总评分平均分")
    @GetMapping("/statisticsData/getDeptEvaluationAverageScore")
    public PlatformResult getDeptEvaluationAverageScore(@Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsEvaluationService.getDeptEvaluationAverageScore(wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="科室服务平均用时排名")
    @ApiOperation(value = "科室服务平均用时排名", notes = "科室服务平均用时排名")
    @GetMapping("/statisticsData/getDeptQualityOfService")
    public PlatformResult getDeptQualityOfService(@Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsSheetService.getDeptQualityOfService(wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="人员服务质量")
    @ApiOperation(value = "人员服务质量", notes = "人员服务质量")
    @GetMapping("/statisticsData/getQualityOfPersonnelService")
    public DataSet<Map<String, Object>> getQualityOfPersonnelService(Page page, @Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        List<Map<String, Object>> qualityOfPersonnelService = wsSheetService.getQualityOfPersonnelService(page, wsWorkSheetStatisticalInputVo);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                qualityOfPersonnelService
        );
    }

    @ControllerLog(description="知识点提交趋势")
    @ApiOperation(value = "知识点提交趋势", notes = "知识点提交趋势")
    @GetMapping("/statisticsData/getKnowledgeBaseCountByDate")
    public PlatformResult getKnowledgeBaseCountByDate(@Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(knowledgeBaseService.getKnowledgeBaseCountByDate(wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="一级知识点占比")
    @ApiOperation(value = "一级知识点占比", notes = "一级知识点占比")
    @GetMapping("/statisticsData/getLevelOneKnowledgeBaseTypeDatas")
    public PlatformResult getLevelOneKnowledgeBaseTypeDatas(@Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(knowledgeBaseService.getLevelOneKnowledgeBaseTypeDatas(wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="知识点提交top榜单")
    @ApiOperation(value = "知识点提交top榜单", notes = "知识点提交top榜单")
    @GetMapping("/statisticsData/getKnowledgeBaseSubmitTopDatas")
    public DataSet<Map<String, Object>> getKnowledgeBaseSubmitTopDatas(Page page, @Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                knowledgeBaseService.getKnowledgeBaseSubmitTopDatas(page, wsWorkSheetStatisticalInputVo)
        );
    }

    @ControllerLog(description="知识库点赞Top榜单(带时间区间)")
    @ApiOperation(value = "知识库点赞Top榜单(带时间区间)", notes = "知识库点赞Top榜单(带时间区间)")
    @GetMapping("/statisticsData/getKnowledgeLikeCountTop")
    public DataSet<Map<String, Object>> getKnowledgeLikeCountTop(Page page, @Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        List<Map<String, Object>> knowledgeLikeCountTop = knowledgeBaseService.getKnowledgeLikeCountTop(page, wsWorkSheetStatisticalInputVo);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                knowledgeLikeCountTop
        );
    }

    @ControllerLog(description="移动端-工单统计-工单情况")
    @ApiOperation(value = "移动端-工单统计-工单情况", notes = "移动端-工单统计-工单情况")
    @GetMapping("/statisticsData/getWorkOrderSituation")
    public PlatformResult getWorkOrderSituation(@Validated WsWorkSheetMobileStatisticalInputVo wsWorkSheetMobileStatisticalInputVo) {
        return PlatformResult.success(wsSheetService.getWorkOrderSituation(wsWorkSheetMobileStatisticalInputVo));
    }

    @ControllerLog(description="移动端-工单统计-工单状态、紧急程度、影响范围")
    @ApiOperation(value = "移动端-工单统计-工单状态、紧急程度、影响范围", notes = "移动端-工单统计-工单状态、紧急程度、影响范围")
    @GetMapping("/statisticsData/getWorkOrderType")
    public PlatformResult getWorkOrderType(@Validated WsWorkSheetMobileStatisticalInputVo wsWorkSheetMobileStatisticalInputVo) {
        return PlatformResult.success(wsSheetService.getWorkOrderType(wsWorkSheetMobileStatisticalInputVo));
    }

    @ControllerLog(description="大屏处理中工单")
    @ApiOperation(value = "大屏处理中工单", notes = "大屏处理中工单")
    @GetMapping("/statisticsData/wsSheetScreenPageList")
    public DataSet<WsWsSheetScreenListOutVo> wsSheetScreenPageList(Page page,@ApiParam(value = "有值为科室级、null为全院级") String fkDeptId) {
        List<WsWsSheetScreenListOutVo> sheetScreenListOutVos = wsSheetService.wsSheetScreenPageList(page, fkDeptId);
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                sheetScreenListOutVos
        );
    }

    @ControllerLog(description="大屏今日分配工单")
    @ApiOperation(value = "大屏今日分配工单", notes = "大屏今日分配工单")
    @GetMapping("/statisticsData/wsSheetDistributionScreenPageList")
    public DataSet<Map<String, Object>> wsSheetDistributionScreenPageList(Page page,@ApiParam(value = "有值为科室级、null为全院级") String fkDeptId) {
        List<Map<String, Object>> distributionScreenPageList = wsSheetService.wsSheetDistributionScreenPageList(page, fkDeptId,DateUtils.getDayStart(),DateUtils.getCurrentTime());
        return new DataSet<>(
                page.getPageNo(),
                page.getPageSize(),
                page.getTotalPages(),
                page.getTotalCount(),
                distributionScreenPageList
        );
    }

    @ControllerLog(description="时效性分析-派单时效")
    @ApiOperation(value = "时效性分析-派单时效", notes = "时效性分析-派单时效")
    @GetMapping("/statisticsData/selectSheetSendAgingOutVo")
    public PlatformResult<List<WsWorkSheetSendAgingOutVo>> selectSheetSendAgingOutVo(@Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsSheetService.selectSheetSendAgingOutVo(wsWorkSheetStatisticalInputVo));
    }

    @ControllerLog(description="时效性分析-接单时效")
    @ApiOperation(value = "时效性分析-接单时效", notes = "时效性分析-接单时效")
    @GetMapping("/statisticsData/selectSheetOderAgingOutVo")
    public PlatformResult<List<WsWorkSheetOderAgingOutVo>> selectSheetOderAgingOutVo(@Validated WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        return PlatformResult.success(wsSheetService.selectSheetOderAgingOutVo(wsWorkSheetStatisticalInputVo));
    }

}
