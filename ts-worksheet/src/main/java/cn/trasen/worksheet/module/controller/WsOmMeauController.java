package cn.trasen.worksheet.module.controller;


import java.util.List;

import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.annotation.NoRepeatSubmit;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.common.constant.ConstantYml;
import cn.trasen.worksheet.module.dto.inputVo.WsOmMeauInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkFlowListOutVo;
import cn.trasen.worksheet.module.service.WsOmMeauService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

@Api(tags = "OM语音菜单管理")
@RestController
@Slf4j
public class WsOmMeauController {

    @Autowired
    private WsOmMeauService wsOmMeauService;


    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="保存修改语音菜单")
    @ApiOperation(value = "保存修改语音菜单", notes = "保存修改语音菜单")
    @PostMapping("/omMeau/saveOrUpdate")
    public PlatformResult saveOrUpdate(@Validated @RequestBody WsOmMeauInputVo wsOmMeauInputVo){
        return PlatformResult.success(wsOmMeauService.saveOrUpdate(wsOmMeauInputVo));
    }

    @ControllerLog(description="语音菜单分页列表")
    @ApiOperation(value = "语音菜单分页列表", notes = "语音菜单分页列表")
    @GetMapping("/omMeau/selectOmMeauList")
    public PlatformResult selectOmMeauList(Page page){
        return PlatformResult.success(wsOmMeauService.selectOmMeauList(page));
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="逻辑删除语音菜单")
    @ApiOperation(value = "逻辑删除语音菜单", notes = "逻辑删除语音菜单")
    @PostMapping("/omMeau/deleteOmMeau/{ids}")
    public PlatformResult deleteOmMeau(@PathVariable @ApiParam(value = "语音菜单id，多个以英文逗号拼接") String ids){
        return PlatformResult.success(wsOmMeauService.deleteOmMeau(ids));
    }

    @ControllerLog(description="语音菜单列表（即处理科室列表）")
    @ApiOperation(value = "语音菜单列表（即处理科室列表）", notes = "语音菜单列表（即处理科室列表）")
    @GetMapping("/workSheet/meauList")
    public PlatformResult meauList() {
        return PlatformResult.success(wsOmMeauService.selectOmMeauAllList());

    }

    @ControllerLog(description="语音菜单列表（即处理科室列表）")
    @ApiOperation(value = "语音菜单列表（即处理科室列表）", notes = "语音菜单列表（即处理科室列表）")
    @GetMapping("/workSheet/itemMeauList")
    public PlatformResult<List<WsWorkFlowListOutVo>> itemMeauList() {
        return PlatformResult.success(wsOmMeauService.selectOmItemMeauAllList());

    }

    @ControllerLog(description="人员数据权限过滤语音菜单（即科室处理科室列表）")
    @ApiOperation(value = "人员数据权限过滤语音菜单（即科室处理科室列表）", notes = "人员数据权限过滤语音菜单（即科室处理科室列表）")
    @GetMapping("/omMeau/meauPermissionsList")
    public PlatformResult meauPermissionsList() {
        return PlatformResult.success(wsOmMeauService.meauPermissionsList());
    }



    @ApiOperation(value = "拖动修改排序", notes = "拖动修改排序")
    @PostMapping(value = "/omMeau/updateSort")
    public PlatformResult updateSort(@RequestBody List<WsOmMeauInputVo> omMeauInputVos) {
        return PlatformResult.success(wsOmMeauService.updateSort(omMeauInputVos));
    }

    @ControllerLog(description="OM配置语音菜单")
    @ApiOperation(value = "OM配置语音菜单", notes = "OM配置语音菜单")
    @GetMapping("/omMeau/configuringTheVoiceMenu")
    public PlatformResult configuringTheVoiceMenu() {

        try {
            HttpUtil.post(ConstantYml.getInstance().getOmIpHost(), "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n" +
                    "<Control attribute=\"Assign\">\n" +
                    "    <menu id=\"1\">\n" +
                    "        <voicefile>user_menu</voicefile>\n" +
                    "        <repeat>3</repeat>\n" +
                    "        <infolength>1</infolength>\n" +
                    "    </menu>\n" +
                    "</Control>");
        }catch (Exception e){
            log.error("OM配置语音菜单失败：" + e.getMessage(), e);
            e.printStackTrace();
            return PlatformResult.failure("OM配置语音菜单失败");
        }
        return PlatformResult.success("OM配置语音菜单成功");
    }


    public static void main(String[] args) {
        String b = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<Control attribute=\"TTSConvert\">" +
                "<content>再次测试合成语音</content>" +
                "<lang>ch_female</lang>" +
                "<speed>5</speed>" +
                "<volume>5</volume>" +
                "<wav>0</wav>" +
                "<pcm>1</pcm>" +
                "<dat>0</dat>" +
                "</Control>";

        String body1 = HttpRequest.post("tts.newrocktech.com:9000")
                .header("Content-Type","text/xml")
                .header("Cache-Control","no-cache")
                .body("XML_POST",b)
                .execute().body();
        Document document = null;
        try {
            document = DocumentHelper.parseText(body1);
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        System.out.println(document.getRootElement().element("pcm_url").getStringValue());
        System.out.println(body1);

        String d ="<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n" +
                "<Control attribute=\"Assign\">\n" +
                " <menu id=\"1\">\n" +
                "<voicefile>"+document.getRootElement().element("pcm_url").getStringValue()+"</voicefile>\n" +
                " <repeat>1</repeat>\n" +
                " <infolength>3</infolength>\n" +
                " <exit>#</exit>\n" +
                " </menu>\n" +
                "</Control>\n";
        String c="<?xml version=\"1.0\" encoding=\"utf-8\" ?>" +
                "<Control attribute=\"Assign\">" +
                " <menu id=\"1\">" +
                "<voicefile>"+document.getRootElement().element("pcm_url").getStringValue()+"</voicefile>" +
                " <repeat>3</repeat>" +
                " <infolength>1</infolength>" +
                " </menu>" +
                "</Control>";
        String post = HttpUtil.post("192.168.81.249", c);
        System.out.println(post);
    }
}
