package cn.trasen.worksheet.module.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.dto.inputVo.WsWsCostPageListInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsCostSaveInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsCostOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsCostPageListOutVo;
import cn.trasen.worksheet.module.service.WsWsCostService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * <AUTHOR>
 * @date: 2021/7/22 10:51
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Api(tags = "工单费用设置")
@RestController
public class WsWsCosttController {

    @Autowired
    private WsWsCostService wsCostService;

    @ControllerLog(description = "保存、修改工单费用信息")
    @ApiOperation(value = "保存、修改工单费用信息", notes = "保存、修改工单费用信息")
    @PostMapping("/wsCost")
    public PlatformResult<Integer> wsCost(@RequestBody WsWsCostSaveInputVo costSaveInputVo) {
        return PlatformResult.success(wsCostService.wsCost(costSaveInputVo));
    }

    @ControllerLog(description = "工单费用信息分页列表")
    @ApiOperation(value = "工单费用信息分页列表", notes = "工单费用信息分页列表")
    @GetMapping("/wsCostList")
    public DataSet<WsWsCostPageListOutVo> getPageList(Page page, WsWsCostPageListInputVo costPageListInputVo) {
        List<WsWsCostPageListOutVo> costPageListOutVoLists = wsCostService.getPageList(page, costPageListInputVo);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), costPageListOutVoLists);
    }

    @ControllerLog(description = "工单费用详情")
    @ApiOperation(value = "工单费用详情", notes = "工单费用详情")
    @GetMapping("/wsCost/{pkWsCostId}")
    public PlatformResult<WsWsCostOutVo> wsCost(@PathVariable("pkWsCostId") String pkWsCostId) {
        return PlatformResult.success(wsCostService.getOne(pkWsCostId));
    }

    @ControllerLog(description = "逻辑删除工单费用信息")
    @ApiOperation(value = "逻辑删除工单费用信息", notes = "逻辑删除工单费用信息")
    @PostMapping("/wsCostDel")
    public PlatformResult<Integer> wsCost(@RequestBody List<String> pkWsCostIds) {
        return PlatformResult.success(wsCostService.deleteWsCost(pkWsCostIds));
    }

    @ControllerLog(description = "费用分页列表导出")
    @ApiOperation(value = "费用分页列表导出", notes = "费用分页列表导出")
    @GetMapping("/exportExcel")
    public void exportExcel(Page page,
                            WsWsCostPageListInputVo costPageListInputVo,
                            @ApiParam(value = "all为汇总，one为我的费用") String type,
                            HttpServletResponse response,
                            HttpServletRequest request) {
        wsCostService.exportExcel(page, costPageListInputVo,type, response, request);
    }


    @ControllerLog(description = "费用明细导出")
    @ApiOperation(value = "费用明细导出", notes = "费用明细导出")
    @GetMapping("/exportExcel/{workNumber}")
    public void exportExcelByWorkNumber(@PathVariable("workNumber") String workNumber, HttpServletResponse response, HttpServletRequest request) {
        wsCostService.exportExcelByWorkNumber(workNumber, response, request);
    }

    @ControllerLog(description = "费用总金额")
    @ApiOperation(value = "费用总金额", notes = "费用总金额")
    @GetMapping("/wsCostSum")
    public PlatformResult<Float> wsCostSum(WsWsCostPageListInputVo costPageListInputVo) {
        return PlatformResult.success(wsCostService.wsCostSum( costPageListInputVo));
    }

}
