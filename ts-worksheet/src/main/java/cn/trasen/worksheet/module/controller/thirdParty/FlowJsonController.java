package cn.trasen.worksheet.module.controller.thirdParty;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.service.WsSheetService;
import io.swagger.annotations.Api;

/**
 * 对接OA流程接口
 * <AUTHOR>
 * @date: 2021/12/1 11:47
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@RestController
@Api(tags = "OA流程")
public class FlowJsonController {

    @Autowired
    private WsSheetService wsSheetService;

    /**
     * 流程模块保存工单入口
     * @param request
     * @return
     */
    @RequestMapping("/OA/workSheet")
    public PlatformResult flowToWorkSheet(HttpServletRequest request){
        return wsSheetService.flowToWorkSheet(request);
    }
}
