package cn.trasen.worksheet.module.dto.inputVo;

import cn.trasen.worksheet.module.entity.WsBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.util.function.Supplier;

@Setter
@Getter
public class CustometServiceStatusInputVo {

    @NotNull(message = "坐席人员id不能为空")
    @ApiModelProperty(value = "坐席人员id")
    private String fkUserId;

    @NotNull(message = "坐席人员号码不能为空")
    @ApiModelProperty(value = "坐席人员号码")
    private String phone;

}