package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date: 2021/7/1 15:42
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Getter
@Setter
public class FaultCommonInputVo {

    /**
     * 常见故障ID
     */
    @ApiModelProperty(value = "常见故障ID")
    private String pkFaultCommonId;

    /**
     * 故障描述
     */
    @NotNull(message = "故障描述不能为空")
    @ApiModelProperty(value = "故障描述")
    private String falutDeion;

}
