package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date: 2021/7/2 11:20
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Getter
@Setter
public class FaultEquipmentInputVo {

    @ApiModelProperty(value = "设备ID")
    private String pkFaultEquipmentId;

    @NotNull(message = "设备编码不能为空")
    @ApiModelProperty(value = "设备编码")
    private String equipmentNumber;

    @NotNull(message = "设备名称不能为空")
    @ApiModelProperty(value = "设备名称")
    private String equipmentName;
    
    @NotNull(message = "设备描述不能为空")
    @ApiModelProperty(value = "设备描述")
    private String equipmentRemark;

    @NotNull(message = "所属科室ID不能为空")
    @ApiModelProperty(value = "所属科室ID")
    private String fkDeptId;

    @NotNull(message = "所属科室不能为空")
    @ApiModelProperty(value = "所属科室")
    private String fkDeptName;
    
}
