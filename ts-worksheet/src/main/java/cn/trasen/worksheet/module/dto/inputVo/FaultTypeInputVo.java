package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date: 2021/7/3 11:05
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Getter
@Setter
public class FaultTypeInputVo {

    @ApiModelProperty(value = "故障类型ID")
    private String pkFaultTypeId;

    @NotNull(message = "全路径不能为空")
    @ApiModelProperty(value = "全路径")
    private String fullPath;

    @ApiModelProperty(value = "上级ID（即父ID）")
    private String parentId;

    @NotNull(message = "分类名称不能为空")
    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @ApiModelProperty(value = "故障处理人id（多个以英文逗号拼接）")
    private String peoples;

    @NotNull(message = "故障类型状态不能为空")
    @ApiModelProperty(value = "故障类型状态")
    private String faultStatus;

    @NotNull(message = "故障类型状态所属科室不能为空")
    @ApiModelProperty(value = "故障类型状态所属科室")
    private String fkDeptId;

}
