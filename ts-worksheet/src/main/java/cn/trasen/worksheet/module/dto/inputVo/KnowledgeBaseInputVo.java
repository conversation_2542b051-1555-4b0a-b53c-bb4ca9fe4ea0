package cn.trasen.worksheet.module.dto.inputVo;

import cn.trasen.worksheet.module.entity.WsBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.function.Supplier;

@Setter
@Getter
public class KnowledgeBaseInputVo  implements Supplier {
    /**
     * 知识库id
     */
    @ApiModelProperty(value = "知识库id")
    private String pkKnowledgeBaseId;

    /**
     * 知识点类型id
     */
    @NotNull(message = "知识点类型id不能为空")
    @ApiModelProperty(value = "知识点类型id")
    private String fkKnowledgeTypeId;

    /**
     * 知识点标题
     */
    @NotNull(message = "知识点标题不能为空")
    @ApiModelProperty(value = "知识点标题")
    private String knowledgeTitle;

    /**
     * 推荐工时
     */
    @ApiModelProperty(value = "推荐工时")
    private int recommendedWorkHours;

//    @ApiModelProperty(value = "提交状态（0草稿、1提交）")
//    private int knowledgeStatus;

    /**
     * 知识点内容
     */
    @NotNull(message = "知识点内容不能为空")
    @ApiModelProperty(value = "知识点内容")
    private String knowledgeContent;

    @NotNull(message = "状态不能为空")
    @ApiModelProperty(value = "状态（0为草稿、-1审批不通过、2审批核通过、1提交）")
    private int knowledgeStatus;

//    @ApiModelProperty(hidden = true)
//    private int status;

    @ApiModelProperty(value = "审批不通过原因")
    private String backReason;

    @Override
    public KnowledgeBaseInputVo get() {
        return this;
    }
}