package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Data
public class KnowledgeBaseMobileInputVo {

    /**
     * 用于sql查询
     */
    @ApiModelProperty(hidden = true)
    private String beginTime;

    /**
     * 用于sql查询
     */
    @ApiModelProperty(hidden = true)
    private String endTime;

    @ApiModelProperty(value = "返回数据条数")
    private Integer limit;

    @ApiModelProperty(value = "科室id(传科室id为科室常见、未传未近期热门)")
    private String fkUserDeptId;

}