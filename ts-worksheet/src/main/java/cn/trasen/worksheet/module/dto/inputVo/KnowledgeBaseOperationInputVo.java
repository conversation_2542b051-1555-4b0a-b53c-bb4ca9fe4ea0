package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.util.function.Supplier;

@Setter
@Getter
public class KnowledgeBaseOperationInputVo {

    @NotNull(message = "知识点id不能为空")
    @ApiModelProperty(value = "知识库id")
    private String pkKnowledgeBaseId;

    @NotNull(message = "状态不能为空")
    @ApiModelProperty(value = "状态（1移出、2审批不通过、3审批核通过、4撤回、5、移入、6提交、7删除）")
    private int knowledgeStatus;

    @ApiModelProperty(hidden = true)
    private int status;

    @ApiModelProperty(value = "备注（移除原因、审批不通过原因、撤回原因）")
    private String remark;

}