package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.function.Supplier;

@Setter
@Getter
public class KnowledgeBaseQueryInputVo{


    @ApiModelProperty(hidden = true)
    private List<String> list;

    @ApiModelProperty(value = "知识点类型id")
    private String fkKnowledgeTypeId;

    @ApiModelProperty(value = "知识点标题")
    private String knowledgeTitle;

    @NotNull(message = "知识点状态不能为空")
    @ApiModelProperty(value = "0草稿、1审核中、2审核通过、3移除、-1审核未通过,4知识库")
    private int knowledgeStatus;

    @ApiModelProperty(value = "知识点内容")
    private String knowledgeContent;

    /**
     *  用户id
     */
    @ApiModelProperty(hidden = true)
    private String fkUserId;

    /**
     * 是否为管理员，null为不是管理员，存在值为是管理员
     */
    @ApiModelProperty(hidden = true)
    private String admin;

    /**
     * 查询当前用户存在存在赞所需参数
     */
    @ApiModelProperty(hidden = true)
    private String userId;

}