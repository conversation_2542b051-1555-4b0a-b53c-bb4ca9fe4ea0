package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date: 2021/7/3 11:05
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Getter
@Setter
public class KnowledgeTypeStatusInputVo {

    @NotNull(message = "知识类型id不能为空")
    @ApiModelProperty(value = "知识类型ID")
    private String pkKnowledgeTypeId;


    @NotNull(message = "知识类型状态不能为空")
    @ApiModelProperty(value = "知识点类型状态（0停用1启用）")
    private int knowledgeStatus;
}
