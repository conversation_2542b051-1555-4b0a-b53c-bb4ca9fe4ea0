package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date: 2021/7/26 10:04
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */

@Getter
@Setter
public class WsCustomeServiceInputVo {

    @ApiModelProperty(value = "坐席ID")
    private String pkCustometServiceId;

    @NotNull(message = "来电是否弹屏不能为空")
    @ApiModelProperty(value = "来电是否弹屏（0否1是）")
    private int playScreen;
    
    @ApiModelProperty(value = "来电是否弹屏（0否1是）")
    private int playVoice;

    @ApiModelProperty(value = "坐席联系方式")
    private String phone;

    @ApiModelProperty(value = "坐席状态（0休息中1工作中）")
    private int custometServiceStatus;

    @NotNull(message = "坐席人员ID不能为空")
    @ApiModelProperty(value = "坐席人员ID")
    private String fkUserId;

    @NotNull(message = "坐席科室id不能为空")
    @ApiModelProperty(value = "坐席科室id")
    private String fkUserDeptId;
}
