package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 文件信息
 * <AUTHOR>
 * @date: 2021/7/12 9:19
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Getter
@Setter
public class WsFileInputVo {

    @ApiModelProperty(value = "文件名称")
    private String fkFileName;

    @ApiModelProperty(value = "文件id")
    private String fkFileId;

    @ApiModelProperty(value = "文件后缀")
    private String fileSuffix;

    @ApiModelProperty(value = "文件访问URL")
    private String fileUrl;

}
