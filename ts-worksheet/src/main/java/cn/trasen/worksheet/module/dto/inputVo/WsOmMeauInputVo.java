package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WsOmMeauInputVo {

    @ApiModelProperty(value = "语音菜单配置id")
    private String pkOmMeauId;

    @ApiModelProperty(value = "科室id")
    private String deptId;

    @ApiModelProperty(value = "科室名称")
    private String deptName;

    @ApiModelProperty(value = "语音菜单按键内容（仅可0-9之间的数字）")
    private String inputContent;

    @ApiModelProperty(value = "排序字段")
    private Integer px;

    @ApiModelProperty(value = "附件是否必填（0否1是）")
    private Integer fileRequired;
}
