package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 统计页面入参DTO
 * <AUTHOR>
 * @date: 2021/7/14 16:18
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Getter
@Setter
public class WsStatisticsFormInputVo {

    @ApiModelProperty(value = "开始时间")
    private String beginTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "是否排序")
    private String order;

    @ApiModelProperty(value = "条数")
    private int limit;

}
