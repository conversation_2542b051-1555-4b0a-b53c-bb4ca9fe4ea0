package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Setter
@Getter
public class WsWorkSheetHomeListInputVo {

    @NotNull(message = "类型不能为空")
    @ApiModelProperty(value = "类型，0为全部，1为个人，2为科室，3为第三方公司，（仅首页-申请人页面、办理人页面传type为5、4。4为处理人，5为报修人，6查询报修人、处理人信息）")
    private int type;

    @ApiModelProperty(value = "用户id（类型为1时用户id不能为空）")
    private String fkUserId;

    @ApiModelProperty(value = "科室id（类型为2时科室id不能为空）")
    private String fkUserDeptId;

    private String fkDeptId;
    /**
     * 仅用于暂停、终止 sql查询 （workStatus为7，complete为0 查询暂停；workStatus为8，complete为1 查询终止）
     */
    @ApiModelProperty(hidden = true)
    private String workStatus;

    @ApiModelProperty(hidden = true)
    private String beginTime;

    @ApiModelProperty(hidden = true)
    private String endTime;

    /**
     * 仅用于暂停、终止 sql查询
     */
    @ApiModelProperty(hidden = true)
    private int complete;


    public WsWorkSheetHomeListInputVo(String fkUserId) {
        this.fkUserId = fkUserId;
    }

    public WsWorkSheetHomeListInputVo() {
    }

    public WsWorkSheetHomeListInputVo(int type, String fkUserDeptId, String beginTime, String endTime, String fkDeptId) {
        this.type = type;
        this.fkUserDeptId = fkUserDeptId;
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.fkDeptId = fkDeptId;
    }
}
