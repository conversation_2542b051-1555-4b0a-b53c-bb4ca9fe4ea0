package cn.trasen.worksheet.module.dto.inputVo;

import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.util.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Setter
@Getter
public class WsWorkSheetStatisticalInputVo {

    public WsWorkSheetStatisticalInputVo() {
    }

    public WsWorkSheetStatisticalInputVo(String beginTime, String endTime, String statusType,int type) {
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.statusType = statusType;
        this.type = type;
    }

    public WsWorkSheetStatisticalInputVo(String statusType, String fkUserId) {
        this.statusType = statusType;
        this.fkUserId = fkUserId;
    }

    @NotNull(message = "时间类型不能为空")
    @Valid
    @ApiModelProperty(value = "时间类型,0为日，1为月,2为年")
    private int dayOrMonthType;

    @NotNull(message = "查询类型不能为空")
    @ApiModelProperty(value = "查询类型,0为本天、1为本周、2为本月、3为本年、4为全部、5为时间区间查询")
    private int type;

    @ApiModelProperty(value = "查询开始时间（yyyy-MM-dd HH:mm:ss）")
    private String beginTime;

    @ApiModelProperty(value = "查询结束时间（yyyy-MM-dd HH:mm:ss）")
    private String endTime;

    @ApiModelProperty(value = "工单状态为work_status,报修方式为repair_type,故障紧急程度为fault_emergency,故障影响范围为fault_affect_scope")
    private String statusType;

    @ApiModelProperty(value = "评价类型,0为总评分,1为处理速度，2为服务态度，3为技术水平")
    private int evaluationType;

    @ApiModelProperty(value = "用户科室id（仅知识点页签不使用）")
    private String fkUserDeptId;

    @ApiModelProperty(value = "用户id（仅完成质量分析-人员服务质量情况使用查询参数）")
    private String fkUserId;

    @ApiModelProperty(value = "用户名称（仅完成质量分析-人员服务质量情况使用查询参数）")
    private String fkUserName;

    @ApiModelProperty(value = "排名")
    private int limit;

}
