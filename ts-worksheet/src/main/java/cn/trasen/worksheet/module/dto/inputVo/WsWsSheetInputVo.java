package cn.trasen.worksheet.module.dto.inputVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2021/6/17 15:49
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */

@Setter
@Getter
public class WsWsSheetInputVo implements Serializable {

    public WsWsSheetInputVo() {
        super();
    }

    @ApiModelProperty(value = "工单编号")
    private String workNumber;

    @ApiModelProperty(value = "院区id")
    private String fkHospitalDistrictId;

    @ApiModelProperty(value = "通话记录id")
    private String pkCustometLogId;

    @ApiModelProperty(value = "通话记录业务状态 5回拨")
    private int callWorkStatus;

    @ApiModelProperty(value = "业务所属科室名称")
    private String businessDeptName;

    @NotEmpty(message = "业务所属科室id不能为空")
    @ApiModelProperty(value = "业务所属科室id")
    private String businessDeptId;

    @NotEmpty(message = "报修科室ID不能为空")
    @ApiModelProperty(value = "报修科室ID")
    private String repairManDeptId;

    @ApiModelProperty(value = "报修地址")
    private String repairDeptAddress;

    @NotEmpty(message = "报修人ID不能为空")
    @ApiModelProperty(value = "报修人ID")
    private String repairManId;

    @NotEmpty(message = "报修人联系电话不能为空")
    @ApiModelProperty(value = "报修人联系电话")
    private String repairPhone;

    @ApiModelProperty(value = "故障类型ID")
    private String fkFaultTypeId;

    @ApiModelProperty(value = "故障设备名称")
    private String faultEquipmentName;
    
    @ApiModelProperty(value = "故障设备描述")
    private String faultEquipmentRemark;

    @ApiModelProperty(value = "故障设备ID")
    private String fkFaultEquipmentId;

    @NotEmpty(message = "故障描述不能为空")
    @ApiModelProperty(value = "故障描述")
    private String faultDeion;

    @NotNull(message = "报修方式不能为空")
    @ApiModelProperty(value = "报修方式。电话报修：1,微信报修：2,电脑报修：3,上门报修：4，补录：5")
    private int repairType;

    @NotNull(message = "故障紧急程度不能为空")
    @ApiModelProperty(value = "故障紧急程度。非常紧急：1,比较急：2：常规处理：3")
    private int faultEmergency;

    @NotNull(message = "故障影响范围不能为空")
    @ApiModelProperty(value = "故障影响范围。个人事件：1,科室事件：2,多科室事件：3,全院事件：4")
    private int faultAffectScope;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "故障要求完成时间")
    private Date requiredCompletionTime;

    @ApiModelProperty(value = "处理人ID")
    private String fkUserId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "处理人科室ID")
    private String fkUserDeptId;

    @ApiModelProperty(value = "节点id")
    private String pkWsTaskId;

    @ApiModelProperty(value = "工单类型（1服务台提单，2工单列表提单，3电话提单）")
    private int workSheetType;

    private String createBy;

    private String createByName;

    private List<WsFileInputVo> wsFileInputVo;
    
    private List<WsFileInputVo> audioList;


    /**
     * 流程信息
     */

    @ApiModelProperty(value = "业务id")
    private String lBusinessId;

    @ApiModelProperty(value = "流程实例id")
    private String workflowInstId;

    @ApiModelProperty(value = "流程编号")
    private String workflowNo;

}
