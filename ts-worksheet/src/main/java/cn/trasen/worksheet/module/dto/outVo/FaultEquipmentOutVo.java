package cn.trasen.worksheet.module.dto.outVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.InputStream;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2021/7/2 11:20
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Getter
@Setter
public class FaultEquipmentOutVo {

    @ApiModelProperty(value = "设备ID")
    private String pkFaultEquipmentId;

    @ApiModelProperty(value = "设备编码")
    private String equipmentNumber;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;
    
    @ApiModelProperty(value = "设备描述")
    private String equipmentRemark;

    @ApiModelProperty(value = "所属科室ID")
    private String fkDeptId;

    @ApiModelProperty(value = "所属科室")
    private String fkDeptName;

    @ApiModelProperty(value = "登记日期")
    private Date createTime;

    @ApiModelProperty(value = "报修次数")
    private int counts;

    @ApiModelProperty(value = "二维码")
    private String qrCode;
}
