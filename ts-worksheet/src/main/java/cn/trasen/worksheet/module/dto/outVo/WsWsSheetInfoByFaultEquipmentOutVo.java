package cn.trasen.worksheet.module.dto.outVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2021/6/17 15:49
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */

@Setter
@Getter
public class WsWsSheetInfoByFaultEquipmentOutVo implements Serializable {

    @ApiModelProperty(value = "工单编号")
    public String workNumber;

    @ApiModelProperty(value = "工单状态")
    public String workStatus;

    @ApiModelProperty(value = "工单状态字典中文")
    public String workStatusValue;

    @ApiModelProperty(value = "报修人")
    public String repairManName;

    @ApiModelProperty(value = "报修人所属机构")
    public String repairManDeptName;

    @ApiModelProperty(value = "故障类型")
    public String fkFaultType;

    @ApiModelProperty(value = "故障描述")
    public String faultDeion;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "要求完成时间")
    public Date requiredCompletionTime;

    @ApiModelProperty(value = "处理人")
    public String fkUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "完成时间")
    public Date actualCompletionTime;

    @ApiModelProperty(value = "工时")
    public Float workHours;

}
