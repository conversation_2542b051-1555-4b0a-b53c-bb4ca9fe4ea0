package cn.trasen.worksheet.module.dto.outVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2021/6/17 15:49
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */

@Setter
@Getter
public class WsWsSheetInfoOutVo implements Serializable {

	@ApiModelProperty(value = "工单id")
    public String pkWsSheetId;
	
    @ApiModelProperty(value = "工单编号")
    public String workNumber;

    @ApiModelProperty(value = "节点ID")
    public String pkWsTaskId;

    @ApiModelProperty(value = "工单状态")
    public String workStatus;

    @ApiModelProperty(value = "工单状态中文值")
    public String workStatusVaule;

    @ApiModelProperty(value = "报修人id")
    public String repairManId;

    @ApiModelProperty(value = "报修人")
    public String repairManName;

    @ApiModelProperty(value = "报修人所属机构ID")
    public String repairManDeptId;

    @ApiModelProperty(value = "报修人所属机构")
    public String repairManDeptName;

    @ApiModelProperty(value = "报修地址")
    public String repairDeptAddress;

    @ApiModelProperty(value = "报修电话")
    public String repairPhone;

    @ApiModelProperty(value = "故障类型id")
    public String fkFaultTypeId;

    @ApiModelProperty(value = "故障类型")
    public String fkFaultType;

    @ApiModelProperty(value = "故障设备名称")
    public String faultEquipmentName;
    
    @ApiModelProperty(value = "故障设备描述")
    public String equipmentRemark;


    @ApiModelProperty(value = "故障设备id")
    public String fkFaultEquipmentId;

    @ApiModelProperty(value = "故障描述")
    public String faultDeion;

    @ApiModelProperty(value = "故障紧急程度")
    public int faultEmergency;

    @ApiModelProperty(value = "故障范围")
    public int faultAffectScope;

    @ApiModelProperty(value = "报修方式")
    public int repairType;

    @ApiModelProperty(value = "故障紧急程度Value")
    public String faultEmergencyValue;

    @ApiModelProperty(value = "故障范围Value")
    public String faultAffectScopeValue;

    @ApiModelProperty(value = "报修方式Value")
    public String repairTypeValue;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "要求完成时间")
    public Date requiredCompletionTime;

    @ApiModelProperty(value = "提单人")
    public String createByName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "提单时间")
    public Date createTime;

    @ApiModelProperty(value = "处理人")
    public String fkUserName;

    @ApiModelProperty(value = "处理人id")
    public String fkUserId;

    @ApiModelProperty(value = "备注")
    public String remark;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "完成时间")
    public Date actualCompletionTime;

    @ApiModelProperty(value = "工时")
    public Float workHours;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "派单时间(废弃)")
    public Date sendOrderTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "派单时间")
    public Date dispatchTime;

    @ApiModelProperty(value = "派单人")
    public String dispatcher;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "接单时间")
    public Date revTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "确认时间")
    public Date confirmTime;

    @ApiModelProperty(value = "协助人（多个以英文逗号拼接）")
    public String assist;

    @ApiModelProperty(value = "协助人id（多个以英文逗号拼接）")
    public String assistId;

    @ApiModelProperty(value = "催办次数")
    public int hatencount;

    @ApiModelProperty(value = "超期天数")
    public int cqDays;

    @ApiModelProperty(value = "科室id")
    private String businessDeptId;

    @ApiModelProperty(value = "科室名称")
    private String businessDeptName;

    @ApiModelProperty(value = "院区id")
    private String fkHospitalDistrictId;

    @ApiModelProperty(value = "院区名称")
    private String hospitalDistrictName;

}
