package cn.trasen.worksheet.module.dto.thirdParty;

import cn.trasen.worksheet.module.dto.inputVo.WsFileInputVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date: 2021/12/1 14:16
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Data
public class FlowInputVo {

    @NotEmpty(message = "业务所属科室id不能为空")
    @ApiModelProperty(value = "业务所属科室id")
    private String businessDeptId;

    @NotEmpty(message = "故障类型ID不能为空")
    @ApiModelProperty(value = "故障类型ID")
    private String fkFaultTypeId;

    @NotEmpty(message = "故障描述不能为空")
    @ApiModelProperty(value = "故障描述")
    private String faultDeion;

    @ApiModelProperty(value = "附件id，多个以英文逗号拼接")
    private String fileIds;

    @ApiModelProperty(value = "院区id")
    private String fkHospitalDistrictId;

    @ApiModelProperty(value = "报修方式")
    private int repairType;

    @ApiModelProperty(value = "报修科室ID")
    private String repairManDeptId;

    @ApiModelProperty(value = "报修人ID")
    private String repairManId;

    @ApiModelProperty(value = "附件信息")
    private List<WsFileInputVo> wsFileInputVo;

}
