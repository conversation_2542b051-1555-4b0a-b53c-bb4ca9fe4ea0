package cn.trasen.worksheet.module.entity;

import java.util.Date;

import javax.persistence.Column;

import cn.trasen.homs.core.utils.UserInfoHolder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date: 2021/6/18 8:57
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Setter
@Getter
public class WsBase {

    /**
     * 创建人ID
     */
    @Column(name = "CREATE_BY")
    private String createBy;

    /**
     * 创建人名称
     */
    @Column(name = "CREATE_BY_NAME")
    private String createByName;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /**
     * 修改人ID
     */
    @Column(name = "UPDATE_BY")
    private String updateBy;

    /**
     * 修改人名称
     */
    @Column(name = "UPDATE_BY_NAME")
    private String updateByName;

    /**
     * 修改时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 逻辑删除标记（0未删除，1已删除）
     */
    @Column(name = "DELETE_STATUS")
    private int deleteStatus;


    public WsBase() {
        this.createBy = UserInfoHolder.getCurrentUserId();
        this.createByName = UserInfoHolder.getCurrentUserName();
        this.createTime = new Date();
        this.updateBy = UserInfoHolder.getCurrentUserId();
        this.updateByName = UserInfoHolder.getCurrentUserName();
        this.updateTime = new Date();
        this.deleteStatus = 0;
    }
}
