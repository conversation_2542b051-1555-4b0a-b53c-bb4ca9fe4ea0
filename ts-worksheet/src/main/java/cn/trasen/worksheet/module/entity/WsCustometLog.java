package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.function.Supplier;
import javax.persistence.*;
import lombok.*;

@Table(name = "ws_customet_log")
@Setter
@Getter
public class WsCustometLog extends WsBase implements Supplier {

    public WsCustometLog() {
        super();
    }

    public WsCustometLog(String pkCustometLogId, int callType, int callWorkStatus,Date updateTime ,String updateBy) {
        this.pkCustometLogId = pkCustometLogId;
        this.callType = callType;
        this.callWorkStatus = callWorkStatus;
        this.setUpdateTime(updateTime);
        this.setUpdateBy(updateBy);
    }



    public WsCustometLog(int callType, String fkUserId) {
        this.callType = callType;
        this.fkUserId = fkUserId;
    }

    public WsCustometLog(String pkCustometLogId, int callType, int callWorkStatus, String fkUserId) {
        this.pkCustometLogId = pkCustometLogId;
        this.callType = callType;
        this.callWorkStatus = callWorkStatus;
        this.fkUserId = fkUserId;
    }

    @ApiModelProperty(value = "坐席记录ID")
    private String pkCustometLogId;

    @ApiModelProperty(value = "工单编号")
    private String workNumber;

    @ApiModelProperty(value = "通话类型(未接0，已接1，等候-1)")
    private int callType = 0;

    @ApiModelProperty(value = "通话记录业务状态（0未接听、1未建单、2无效来电、3已建单、4电话已解决、5回拨）")
    private int callWorkStatus = 0;

    @ApiModelProperty(value = "坐席设备号")
    private String equipmentId;

    @ApiModelProperty(value = "坐席人员ID")
    private String fkUserId;

    @ApiModelProperty(value = "来访电话号码")
    private String visitPhone;

    @ApiModelProperty(value = "呼出电话号码")
    private String callPhone;

    @ApiModelProperty(value = "来访人员id")
    private String visitUserId;

    @ApiModelProperty(value = "来访人员姓名")
    private String visitUserName;

    @ApiModelProperty(value = "来访人员部门id")
    private String visitUserDeptId;

    @ApiModelProperty(value = "来访人员部门名称")
    private String visitUserDeptName;

    @ApiModelProperty(value = "是否已读")
    private int isRead = 0;

    @ApiModelProperty(value = "科室id")
    private String businessDeptId;


    @Override
    public WsCustometLog get() {
        return this;
    }
}