package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.function.Supplier;
import javax.persistence.*;
import lombok.*;

@Table(name = "ws_fault_equipment")
@Setter
@Getter
public class WsFaultEquipment extends WsBase implements Supplier {
    /**
     * 设备ID
     */
    @Column(name = "pk_fault_equipment_id")
    @ApiModelProperty(value = "设备ID")
    private String pkFaultEquipmentId;

    /**
     * 设备编号
     */
    @Column(name = "equipment_number")
    @ApiModelProperty(value = "设备编号")
    private String equipmentNumber;

    /**
     * 设备名称
     */
    @Column(name = "equipment_name")
    @ApiModelProperty(value = "设备名称")
    private String equipmentName;
    
    @Column(name = "equipment_remark")
    @ApiModelProperty(value = "设备描述")
    private String equipmentRemark;


    /**
     * 所属科室ID
     */
    @Column(name = "fk_dept_id")
    @ApiModelProperty(value = "所属科室ID")
    private String fkDeptId;

    /**
     * 登记日期
     */
    @ApiModelProperty(value = "登记日期")
    private Date registrationDate;

    /**
     * 设备图形码
     */
    @Column(name = "equipment_code")
    @ApiModelProperty(value = "设备图形码")
    private String equipmentCode;

    /**
     * 所属科室
     */
    @Column(name = "fk_dept_name")
    @ApiModelProperty(value = "所属科室")
    private String fkDeptName;

    @Override
    public WsFaultEquipment get() {
        return this;
    }
}