package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;
import javax.persistence.*;
import lombok.*;

@Table(name = "ws_fault_man")
@Setter
@Getter
public class WsFaultMan {
    /**
     * 故障类型ID
     */
    @Column(name = "fk_fault_type_id")
    @ApiModelProperty(value = "故障类型ID")
    private String fkFaultTypeId;

    /**
     * 处理人员ID
     */
    @Column(name = "fk_user_id")
    @ApiModelProperty(value = "处理人员ID")
    private String fkUserId;

    /**
     * 处理人员名称
     */
    @Column(name = "fk_user_name")
    @ApiModelProperty(value = "处理人员名称")
    private String fkUserName;

    /**
     * 处理人科室id
     */
    @Column(name = "fk_user_dept_id")
    @ApiModelProperty(value = "处理人科室id")
    private String fkUserDeptId;

    /**
     * 处理人科室名称
     */
    @Column(name = "fk_user_dept_name")
    @ApiModelProperty(value = "处理人科室名称")
    private String fkUserDeptName;
}