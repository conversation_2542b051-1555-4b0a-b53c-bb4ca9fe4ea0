package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;

import java.util.function.Supplier;

@Table(name = "ws_fault_type")
@Setter
@Getter
public class WsFaultType extends WsBase implements Supplier {
    /**
     * 故障类型ID
     */
    @Column(name = "PK_FAULT_TYPE_ID")
    @ApiModelProperty(value = "故障类型ID")
    private String pkFaultTypeId;


    /**
     * 全路径
     */
    @Column(name = "FULL_PATH")
    @ApiModelProperty(value = "全路径")
    private String fullPath;

    /**
     * 上级ID（即父ID）
     */
    @Column(name = "PARENT_ID")
    @ApiModelProperty(value = "上级ID（即父ID）")
    private String parentId;

    /**
     * 分类名称
     */
    @Column(name = "CATEGORY_NAME")
    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    /**
     * 故障类型状态
     */
    @Column(name = "FAULT_STATUS")
    @ApiModelProperty(value = "故障类型状态")
    private String faultStatus;

    @ApiModelProperty(value = "故障类型处理人员id（英文逗号拼接多个）")
    private String fkUserId;

    @ApiModelProperty(value = "故障类型状态所属科室")
    private String fkDeptId;

    @Override
    public WsFaultType get() {
        return this;
    }
}