package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.function.Supplier;
import javax.persistence.*;
import lombok.*;

@Table(name = "ws_sys_config")
@Setter
@Getter
public class WsSysConfig implements Supplier {
    /**
     * 主键
     */
    @Column(name = "pk_sys_config_id")
    @ApiModelProperty(value = "主键")
    private String pkSysConfigId;

    /**
     * 是否自动验收（0否1是）
     */
    @Column(name = "automated_acceptance")
    @ApiModelProperty(value = "是否自动验收（0否1是）")
    private String automatedAcceptance;

    /**
     * 验收天数
     */
    @Column(name = "acceptance_days")
    @ApiModelProperty(value = "验收天数")
    private String acceptanceDays;

    /**
     * 验收默认分数
     */
    @Column(name = "acceptance_default_score")
    @ApiModelProperty(value = "验收默认分数")
    private String acceptanceDefaultScore;

    /**
     * 是否自动评价（0否1是）
     */
    @Column(name = "automated_evaluate")
    @ApiModelProperty(value = "是否自动评价（0否1是）")
    private String automatedEvaluate;

    /**
     * 评价天数
     */
    @Column(name = "evaluate_days")
    @ApiModelProperty(value = "评价天数")
    private String evaluateDays;

    /**
     * 评价默认分数
     */
    @Column(name = "evaluate_default_score")
    @ApiModelProperty(value = "评价默认分数")
    private String evaluateDefaultScore;

    /**
     * 是否开启电话分机（0否1是）
     */
    @Column(name = "tel_ext")
    @ApiModelProperty(value = "是否开启电话分机（0否1是）")
    private String telExt;

    /**
     * 是否开启多院区（0否1是）
     */
    @Column(name = "multi_hospital_district")
    @ApiModelProperty(value = "是否开启多院区（0否1是）")
    private String multiHospitalDistrict;

    @ApiModelProperty(value = "报修地址是否必填（0否1是）")
    private Integer repairAddressRequired;

    /**
     * 创建人ID
     */
    @Column(name = "create_by")
    @ApiModelProperty(value = "创建人ID")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人ID
     */
    @Column(name = "update_by")
    @ApiModelProperty(value = "修改人ID")
    private String updateBy;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 逻辑删除标记（0未删除，1已删除）
     */
    @Column(name = "delete_status")
    @ApiModelProperty(value = "逻辑删除标记（0未删除，1已删除）")
    private Integer deleteStatus;

    @ApiModelProperty(value = "要求接单时间")
    private Float requestReceivingTime;


    @Override
    public WsSysConfig get() {
        return this;
    }
}