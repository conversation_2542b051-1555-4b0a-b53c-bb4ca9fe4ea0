package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "ws_ws_assist")
@Setter
@Getter
public class WsWsAssist {
    /**
     * 工单协助ID
     */
    @Column(name = "PK_WS_ASSIST_ID")
    @ApiModelProperty(value = "工单协助ID")
    private String pkWsAssistId;

    /**
     * 工单编号
     */
    @Column(name = "WORK_NUMBER")
    @ApiModelProperty(value = "工单编号")
    private String workNumber;

    /**
     * 创建人ID
     */
    @Column(name = "CREATE_BY")
    @ApiModelProperty(value = "创建人ID")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人ID
     */
    @Column(name = "UPDATE_BY")
    @ApiModelProperty(value = "修改人ID")
    private String updateBy;

    /**
     * 修改时间
     */
    @Column(name = "UPDATE_TIME")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 逻辑删除标记
     */
    @Column(name = "DELETE_STATUS")
    @ApiModelProperty(value = "逻辑删除标记")
    private Short deleteStatus;

    /**
     * 协助人员ID
     */
    @Column(name = "FK_ASSIST_USER_ID")
    @ApiModelProperty(value = "协助人员ID")
    private String fkAssistUserId;

    /**
     * 协助人员名称
     */
    @Column(name = "FK_ASSIST_USER_NAME")
    @ApiModelProperty(value = "协助人员名称")
    private String fkAssistUserName;

    /**
     * 协助人所属机构
     */
    @Column(name = "FK_ASSIST_USER__DEPT_ID")
    @ApiModelProperty(value = "协助人所属机构")
    private String fkAssistUserDeptId;

    /**
     * 协助人员所属机构名称
     */
    @Column(name = "FK_ASSIST_USER__DEPT_NAME")
    @ApiModelProperty(value = "协助人员所属机构名称")
    private String fkAssistUserDeptName;

    /**
     * 是否协助
     */
    @Column(name = "ASSIST")
    @ApiModelProperty(value = "是否协助")
    private Short assist;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    @ApiModelProperty(value = "备注")
    private String remark;
}