package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "ws_ws_cost")
@Setter
@Getter
public class WsWsCost {
    /**
     * 费用id
     */
    @Column(name = "pk_ws_cost_id")
    @ApiModelProperty(value = "费用id")
    private String pkWsCostId;

    /**
     * 工单编号
     */
    @Column(name = "work_number")
    @ApiModelProperty(value = "工单编号")
    private String workNumber;

    /**
     * 费用
     */
    @ApiModelProperty(value = "费用")
    private Float money;

    /**
     * 费用描述
     */
    @Column(name = "cost_deion")
    @ApiModelProperty(value = "费用描述")
    private String costDeion;

    /**
     * 费用发生时间
     */
    @Column(name = "cost_time")
    @ApiModelProperty(value = "费用发生时间")
    private Date costTime;

    /**
     * 填报人
     */
    @Column(name = "fill_user")
    @ApiModelProperty(value = "填报人")
    private String fillUser;

    /**
     * 填报人id
     */
    @Column(name = "fill_user_id")
    @ApiModelProperty(value = "填报人id")
    private String fillUserId;

    /**
     * 填报科室id
     */
    @Column(name = "fill_dept_id")
    @ApiModelProperty(value = "填报科室id")
    private String fillDeptId;

    /**
     * 填报科室
     */
    @Column(name = "fill_dept_name")
    @ApiModelProperty(value = "填报科室")
    private String fillDeptName;


    /**
     * 费用状态（0未报销，1审核中、已报销2）
     */
    @Column(name = "cost_status")
    @ApiModelProperty(value = "费用状态（0未报销，1审核中、已报销2）")
    private Integer costStatus;

    /**
     * 附件业务id
     */
    @Column(name = "files")
    @ApiModelProperty(value = "附件业务id")
    private String files;

    /**
     * 附件数量
     */
    @Column(name = "file_count")
    @ApiModelProperty(value = "附件数量")
    private String fileCount;

    /**
     * 创建人ID
     */
    @Column(name = "create_by")
    @ApiModelProperty(value = "创建人ID")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人ID
     */
    @Column(name = "update_by")
    @ApiModelProperty(value = "修改人ID")
    private String updateBy;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 逻辑删除标记（0未删除，1已删除）
     */
    @Column(name = "delete_status")
    @ApiModelProperty(value = "逻辑删除标记（0未删除，1已删除）")
    private Integer deleteStatus;
}