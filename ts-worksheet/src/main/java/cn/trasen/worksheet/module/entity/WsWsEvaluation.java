package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;

import javax.persistence.*;
import lombok.*;

@Table(name = "ws_ws_evaluation")
@Setter
@Getter
public class WsWsEvaluation extends WsBase{
    /**
     * 工单评价ID
     */
    @Column(name = "pk_ws_evaluation_id")
    @ApiModelProperty(value = "工单评价ID")
    private String pkWsEvaluationId;

    /**
     * 工单编号
     */
    @Column(name = "work_number")
    @ApiModelProperty(value = "工单编号")
    private String workNumber;

    /**
     * 处理速度
     */
    @Column(name = "process_speed")
    @ApiModelProperty(value = "处理速度")
    private int processSpeed;

    /**
     * 服务态度
     */
    @Column(name = "service_attituude")
    @ApiModelProperty(value = "服务态度")
    private int serviceAttituude;

    /**
     * 技术水平
     */
    @Column(name = "technical_level")
    @ApiModelProperty(value = "技术水平")
    private int technicalLevel;
}