package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.function.Supplier;
import javax.persistence.*;
import lombok.*;

@Table(name = "ws_ws_message")
@Setter
@Getter
public class WsWsMessage extends WsBase implements Supplier {

    @ApiModelProperty(value = "工单消息ID")
    private String pkWsMessageId;

    @ApiModelProperty(value = "工单编号")
    private String workNumber;

    @ApiModelProperty(value = "工单状态")
    private String workStatus;

    @ApiModelProperty(value = "消息类型")
    private int messageType;

    @ApiModelProperty(value = "消息标题")
    private String messageTitle;

    @ApiModelProperty(value = "消息内容")
    private String messageContent;

    @ApiModelProperty(value = "接收人ID")
    private String fkUserIdRev;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "接收人姓名")
    private String fkUserNameRev;

    @ApiModelProperty(value = "接收人姓名")
    private String url;

    @ApiModelProperty(value = "是否已读（0否1是）")
    private int isRead;

    public WsWsMessage() {
        super();
    }

    public WsWsMessage(String pkWsMessageId, String workNumber, String messageTitle, String messageContent,String fkUserIdRev,String url) {
        this.pkWsMessageId = pkWsMessageId;
        this.workNumber = workNumber;
        this.messageTitle = messageTitle;
        this.messageContent = messageContent;
        this.fkUserIdRev = fkUserIdRev;
        this.isRead = 0;
        this.url = url;
    }

    @Override
    public WsWsMessage get() {
        return this;
    }
}