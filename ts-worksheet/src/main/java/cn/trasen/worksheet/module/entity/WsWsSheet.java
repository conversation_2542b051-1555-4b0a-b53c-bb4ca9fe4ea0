package cn.trasen.worksheet.module.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "ws_ws_sheet")
@Setter
@Getter
public class WsWsSheet extends WsBase{
    /**
     * 工单ID
     */
	@Id
    @Column(name = "pk_ws_sheet_id")
    @ApiModelProperty(value = "工单ID")
    private String pkWsSheetId;

    @ApiModelProperty(value = "院区id")
    private String fkHospitalDistrictId;


    @ApiModelProperty(value = "终止前状态")
    private String endWorkStatus;

    /**
     * 工单编号
     */
    @Column(name = "work_number")
    @ApiModelProperty(value = "工单编号")
    private String workNumber;

    @ApiModelProperty(value = "创建人科室id")
    private String createByDeptId;

    /**
     * 工单状态 (1：待派单，2：待接单，3：处理中，4：待验收，5：待评价，6：已完成，7：已暂停，8：已终止)
     */
    @Column(name = "work_status")
    @ApiModelProperty(value = "工单状态 (1：待派单，2：待接单，3：处理中，4：待验收，5：待评价，6：已完成，7：已暂停，8：已终止)")
    private String workStatus;

    /**
     * 报修科室ID
     */
    @Column(name = "repair_man_dept_id")
    @ApiModelProperty(value = "报修科室ID")
    private String repairManDeptId;

    /**
     * 报修科室名称
     */
    @Column(name = "repair_man_dept_name")
    @ApiModelProperty(value = "报修科室名称")
    private String repairManDeptName;

    /**
     * 报修地址
     */
    @Column(name = "repair_dept_address")
    @ApiModelProperty(value = "报修地址")
    private String repairDeptAddress;

    /**
     * 报修人ID
     */
    @Column(name = "repair_man_id")
    @ApiModelProperty(value = "报修人ID")
    private String repairManId;

    /**
     * 报修人名称
     */
    @Column(name = "repair_man_name")
    @ApiModelProperty(value = "报修人名称")
    private String repairManName;

    /**
     * 报修人联系电话
     */
    @Column(name = "repair_phone")
    @ApiModelProperty(value = "报修人联系电话")
    private String repairPhone;

    /**
     * 故障类型ID
     */
    @Column(name = "fk_fault_type_id")
    @ApiModelProperty(value = "故障类型ID")
    private String fkFaultTypeId;

    /**
     * 故障设备名称
     */
    @Column(name = "fault_equipment_name")
    @ApiModelProperty(value = "故障设备名称")
    private String faultEquipmentName;
    

    /**
     * 故障设备ID
     */
    @Column(name = "fk_fault_equipment_id")
    @ApiModelProperty(value = "故障设备ID")
    private String fkFaultEquipmentId;

    /**
     * 故障描述
     */
    @ApiModelProperty(value = "故障描述")
    private String faultDeion;

    /**
     * 报修方式
     */
    @Column(name = "repair_type")
    @ApiModelProperty(value = "报修方式")
    private int repairType;

    /**
     * 故障紧急程度
     */
    @Column(name = "fault_emergency")
    @ApiModelProperty(value = "故障紧急程度")
    private int faultEmergency;

    /**
     * 故障影响范围
     */
    @Column(name = "fault_affect_scope")
    @ApiModelProperty(value = "故障影响范围")
    private int faultAffectScope;

    /**
     * 处理人ID
     */
    @Column(name = "fk_user_id")
    @ApiModelProperty(value = "处理人ID")
    private String fkUserId;

    /**
     * 处理人
     */
    @Column(name = "fk_user_name")
    @ApiModelProperty(value = "处理人")
    private String fkUserName;

    /**
     * 故障要求完成时间
     */
    @Column(name = "required_completion_time")
    @ApiModelProperty(value = "故障要求完成时间")
    private Date requiredCompletionTime;

    /**
     * 实际完成时间
     */
    @Column(name = "actual_completion_time")
    @ApiModelProperty(value = "实际完成时间")
    private Date actualCompletionTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 工时
     */
    @Column(name = "work_hours")
    @ApiModelProperty(value = "工时")
    private Float workHours;

    /**
     * 处理人科室id
     */
    @Column(name = "fk_user_dept_id")
    @ApiModelProperty(value = "处理人科室id")
    private String fkUserDeptId;
    /**
     * 处理人科室名称
     */
    @Column(name = "fk_user_dept_name")
    @ApiModelProperty(value = "处理人科室名称")
    private String fkUserDeptName;

    @ApiModelProperty(value = "处理人联系方式")
    private String fkUserPhone;

    /**
     * 催办次数
     */
    @ApiModelProperty(hidden = true)
    private int hatenCount;


    @ApiModelProperty(value = "工单类型（1服务台提单，2工单列表提单，3电话提单4、移动端、5流程转工单）")
    private int workSheetType;

    @ApiModelProperty(value = "业务所属科室id")
    private String businessDeptId;


    /**
     * 流程信息
     */

    @ApiModelProperty(value = "业务id")
    private String lBusinessId;

    @ApiModelProperty(value = "流程实例id")
    private String workflowInstId;

    @ApiModelProperty(value = "流程编号")
    private String workflowNo;
    
    @Transient
    @ApiModelProperty(value = "故障设备描述")
    private String equipmentRemark;

}