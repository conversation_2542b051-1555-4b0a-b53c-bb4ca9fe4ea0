package cn.trasen.worksheet.module.mapper;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.outVo.ExternalPersonnelPageListOutVo;
import cn.trasen.worksheet.module.entity.WsExternalPersonnel;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface WsExternalPersonnelMapper extends Mapper<WsExternalPersonnel> {


    int insertExternalPersonnel(WsExternalPersonnel wsExternalPersonnel);

    /**
     * 根据id主键查询外部人员
     * @param pkExternalPersonnelId id主键
     * @return
     */
    WsExternalPersonnel selectOneById(String pkExternalPersonnelId);



    /**
     * 根据科室id键查询外部人员
     * @param deptId 科室id
     * @return
     */
    List<WsExternalPersonnel> selectAllListByDeptId(String deptId);

    List<WsExternalPersonnel> selectOneByIds(@Param("list") List<String> ids);

    WsExternalPersonnel selectOneByPhone(String phone);

    int updateExternalPersonnel(WsExternalPersonnel wsExternalPersonnel);

    /**
     * 分页列表
     * @param page
     * @param fuzzy 所属机构名称或姓名模糊搜索
     * @return
     */
    List<ExternalPersonnelPageListOutVo> selectPageList(Page page,
                                                        @Param("fuzzy") String fuzzy,
                                                        @Param("deptId") String deptId);


    /**
     * 列表
     * @param fuzzy 所属机构名称或姓名模糊搜索
     * @return
     */
    List<ExternalPersonnelPageListOutVo> selectAllList( @Param("fuzzy") String fuzzy,
                                                        @Param("deptId") String deptId);

    /**
     * 去除查询所有机构名称
     * @param institutionalAffiliations
     * @return
     */
    List<Map<String,Object>> selectAllInstitutionalAffiliations(String institutionalAffiliations);

    /**
     * 去除查询职位名称
     * @param position
     * @return
     */
    List<Map<String,Object>> selectAllPosition(String position);

}