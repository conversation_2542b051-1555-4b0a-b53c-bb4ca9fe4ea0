package cn.trasen.worksheet.module.mapper;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.inputVo.FaultEquipmentInputVo;
import cn.trasen.worksheet.module.dto.outVo.FaultEquipmentOutVo;
import cn.trasen.worksheet.module.entity.WsFaultEquipment;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface WsFaultEquipmentMapper extends Mapper<WsFaultEquipment> {

    int insertEquipment(WsFaultEquipment faultEquipment);

    int updateEquipment(WsFaultEquipment faultEquipment);

    WsFaultEquipment selectEquipmenById(String pkFaultEquipmentId);

    WsFaultEquipment selectEquipmenByEquipmentNumber(String equipmentNumber);


    int deleteEquipmenById(String pkFaultEquipmentId);

    List<FaultEquipmentOutVo> selectPageList(Page page, FaultEquipmentInputVo faultEquipmentInputVo);

    List<FaultEquipmentOutVo> selectAllList(FaultEquipmentInputVo faultEquipmentInputVo);
}