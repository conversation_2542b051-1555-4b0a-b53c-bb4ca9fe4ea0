package cn.trasen.worksheet.module.mapper;

import cn.trasen.worksheet.module.dto.outVo.WsFileOutVo;
import cn.trasen.worksheet.module.entity.WsFileFile;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface WsFileFileMapper extends Mapper<WsFileFile> {

    int insertFile(WsFileFile wsFileFile);

    int insertBatchFile(@Param("list")List<WsFileFile> list);

    int updateBatchFile(@Param("list")List<WsFileFile> list);

    int deleteFile(String id);

    /**
     * 删除历史报修附件信息，不包含录音
     * @param workNumber
     * @return
     */
    int deleteFileByWorkNumber(String workNumber);

    List<WsFileOutVo> selectAllList(String workNumber);

    List<WsFileOutVo> selectAllByTaskIdList(@Param("list") List<String> taskIds);

    List<WsFileFile> selectAllByCustometLogIdList(@Param("list") List<String> custometLogId);

}