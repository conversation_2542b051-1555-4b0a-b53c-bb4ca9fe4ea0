package cn.trasen.worksheet.module.mapper;

import cn.trasen.worksheet.module.entity.WsKnowledgeLike;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

public interface WsKnowledgeLikeMapper extends Mapper<WsKnowledgeLike> {

    WsKnowledgeLike selectOneByIdAndFkUserId(@Param("fkUserId") String fkUserId,
                                             @Param("fkKnowledgeBaseId") String fk_knowledge_base_id);

    int updateKnowledgeLike(WsKnowledgeLike wsKnowledgeLike);
}