package cn.trasen.worksheet.module.mapper;

import cn.trasen.worksheet.module.entity.WsOmFile;
import tk.mybatis.mapper.common.Mapper;

import java.util.Date;
import java.util.List;

public interface WsOmFileMapper extends Mapper<WsOmFile> {

    int insertOmFile(WsOmFile wsOmFile);

    WsOmFile selectByUrl(String url);

    int updateWhetherToUpload(WsOmFile wsOmFile);

    int updateBatchWhetherToUpload(List<WsOmFile> wsOmFile);

    List<WsOmFile> selectListOmFileByTime(Date date);

    WsOmFile selectOneByfkCustometLogId(String workNumber);
}