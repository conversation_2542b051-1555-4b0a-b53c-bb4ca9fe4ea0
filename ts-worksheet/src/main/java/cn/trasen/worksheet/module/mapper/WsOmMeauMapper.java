package cn.trasen.worksheet.module.mapper;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.outVo.WsOmMeauListOutVo;
import cn.trasen.worksheet.module.entity.WsOmMeau;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;


/**
 * 语音菜单配置
 */
public interface WsOmMeauMapper extends Mapper<WsOmMeau> {


    int insertOmMeau(WsOmMeau wsOmMeau);

    int updateOmMeau(WsOmMeau wsOmMeau);

    int deleteOmMeau(List<String> list);

    List<WsOmMeauListOutVo>  selectOmMeauList(Page page);

    List<WsOmMeauListOutVo>  selectOmMeauAllList();

    WsOmMeau seleteOneOmMeau(WsOmMeau wsOmMeau);

    /**
     * 根据workNumber查询单条数据
     *
     * @param workNumber 工单好
     * @return
     */
    WsOmMeau seleteOneOmMeauByWorkNumber(String workNumber);
}