package cn.trasen.worksheet.module.mapper;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.inputVo.WsWsCostPageListInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsCostPageListOutVo;
import cn.trasen.worksheet.module.entity.WsWsCost;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface WsWsCostMapper extends Mapper<WsWsCost> {

    /**
     * 费用分页列表
     * @param costPageListInputVo 查询参数
     * @return
     */
    List<WsWsCostPageListOutVo> getPageList(Page page, WsWsCostPageListInputVo costPageListInputVo);

    /**
     * 费用总金额
     * @param costPageListInputVo
     */
    Float wsCostSum(WsWsCostPageListInputVo costPageListInputVo);

}