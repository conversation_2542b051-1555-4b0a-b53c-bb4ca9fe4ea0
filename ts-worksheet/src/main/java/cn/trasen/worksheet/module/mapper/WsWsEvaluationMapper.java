package cn.trasen.worksheet.module.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetStatisticalInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsEvaluationTopOutVo;
import cn.trasen.worksheet.module.entity.WsWsEvaluation;
import tk.mybatis.mapper.common.Mapper;

public interface WsWsEvaluationMapper extends Mapper<WsWsEvaluation> {

    void insertBatch(List<WsWsEvaluation> wsEvaluationList);


    /**
     * 工单综合评分
     *
     * @param fkDeptId  科室id（不传为查询所有工单）
     * @param fkUserId  用户id（不传为查询所有工单）
     * @param beginTime
     * @param endTime
     * @return
     */
    Map<String, Object> getComprehensiveScoreOfWorkOrder(
            @Param("fkDeptId") String fkDeptId,
            @Param("fkUserId") String fkUserId,
            @Param("beginTime") String beginTime,
            @Param("endTime") String endTime);

    /**
     * 工单各评价类型（总评分、处理速度、服务态度、技术水平）的评价等级
     *
     * @return
     */
    Map<String, Object> getEvaluationLevel(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 工单各评价类型（总评分、处理速度、服务态度、技术水平）平均分
     *
     * @return
     */
    List<Map<String, Object>> getEvaluationAverageScore(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 各科室工单评价各类型平均分及总评分平均分
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    List<Map<String, Object>> getDeptEvaluationAverageScore(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 查询用户处理工单评价分
     *
     * @param list  科室id
     * @param beginTime
     * @param endTime
     * @param limit     返回条数
     * @return
     */
    List<WsEvaluationTopOutVo> getUserEvaluationAverageScore(@Param("list") List<String> list,
                                                       @Param("beginTime") String beginTime,
                                                       @Param("endTime") String endTime,
                                                       @Param("limit") Integer limit);


    /**
     * 查询各科室某段时间内，科室评分
     *
     * @param page
     * @param beginTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getEvaluationGroupByDeptAverageScore(Page page,
                                                             @Param("beginTime") String beginTime,
                                                             @Param("endTime") String endTime);


}