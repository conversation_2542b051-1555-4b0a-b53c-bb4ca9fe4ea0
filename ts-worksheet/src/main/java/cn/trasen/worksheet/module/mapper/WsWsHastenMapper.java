package cn.trasen.worksheet.module.mapper;

import cn.trasen.worksheet.module.dto.outVo.WsWsHatenListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsHatenOutVo;
import cn.trasen.worksheet.module.entity.WsWsHasten;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface WsWsHastenMapper extends Mapper<WsWsHasten> {

    int insertHasten(WsWsHasten wsHasten);

    /**
     * 查询催办次数，最新催办时间
     *
     * @param workNumber 工单编号
     * @return
     */
    WsWsHatenOutVo getHastenInfo(String workNumber);

    /**
     * 查询催办信息
     *
     * @param workNumber 工单编号
     * @return
     */
    List<WsWsHatenListOutVo> getHastenInfoList(String workNumber);
}