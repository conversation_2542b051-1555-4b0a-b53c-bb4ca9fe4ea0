package cn.trasen.worksheet.module.mapper;

import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.outVo.WsWsMessageListOutVo;
import cn.trasen.worksheet.module.entity.WsWsMessage;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface WsWsMessageMapper extends Mapper<WsWsMessage> {

    int insertMessage(WsWsMessage WsWsMessage);

    int updateMessage(@Param("pkWsMessageId") String pkWsMessageId);

    int updateMessageAllByFkUserId(@Param("fkUserId") String fkUserId);

    List<WsWsMessageListOutVo> selectMessagePageList(@Param("page") Page page,
                                                     @Param("fkUserId") String fkUserId,
                                                     @Param("isRead") int isRead);

    List<WsWsMessageListOutVo> selectMessageAllList(@Param("fkUserId") String fkUserId,
                                                    @Param("isRead") int isRead);

    WsWsMessage selectOneById(String pkWsMessageId);


    /**
     * 查询待派单，待接单消息推送时间
     * @param workNumber 工单业务好
     * @param workStatus 工单状态
     * @return
     */
    List<Map<String, Object>> selectOneDpdDjdByWorkNumber(@Param("list") List<String> workNumber,
                                                   @Param("workStatus") String workStatus);

	List<EmployeeResp> getSchedule(@Param("deptId")String deptId,@Param("startDate")String startDate,@Param("endDate")String endDate);

}