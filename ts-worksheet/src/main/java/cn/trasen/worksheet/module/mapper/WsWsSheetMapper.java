package cn.trasen.worksheet.module.mapper;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetHomeListInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetMobileStatisticalInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetStatisticalInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsSheetListSelectInputVo;
import cn.trasen.worksheet.module.dto.outVo.*;
import cn.trasen.worksheet.module.entity.WsWsSheet;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface WsWsSheetMapper extends Mapper<WsWsSheet> {

	int updateByWorkNumber(WsWsSheet wsSheet);
	int updateFkFaultTypeId(WsWsSheet wsSheet);
	
    int insertWsSheet(WsWsSheet wsSheet);

    int updateWsSheetByWorkNumber(WsWsSheet wsSheet);

    void updateBatch(List<WsWsSheet> wsSheet);

    WsWsSheet selectOneWsSheet(String workNumber);

    WsWsSheet selectOneWsSheetByBusinessId(String businessId);



    List<WsWsSheet> selectListWsSheetByFkUserId(String fkUserId);

    /**
     * 根据故障设备id查询工单信息
     *
     * @param faultEquipmentId
     * @return
     */
    List<WsWsSheetInfoByFaultEquipmentOutVo> selectListWsSheetByfaultEquipmentId(String faultEquipmentId);

    WsWsSheetOutVo selectOneWsWsSheetOutVo(String workNumber);

    WsWsSheet selectOneWsSheetByTaskId(String taskId);

    List<WsWsSheet> selectWsSheetListByWorkStatus(String workStatus);

    List<WsWsSheetListOutVo> getWorkSheetPageList(Page page, WsWsSheetListSelectInputVo wsSheetListSelectInputVo);

    List<WsWsSheetListOutVo> getCreateWorkSheetPageList(Page page,
                                                        @Param("faultDeion") String faultDeion,
                                                        @Param("repairManDeptId") String repairManDeptId);

    List<WsWsSheetListOutVo> getApplicantWorkSheetPageList(Page page,
                                                           @Param("fkUserId") String fkUserId);


    WsWsSheetInfoOutVo selectOneWsSheetInfo(@Param("workNumber") String workNumber,
                                            @Param("repairManId") String repairManId,
                                            @Param("fkUserId") String fkUserId);

    List<Map<String, Object>> selectCountGroupByWorkNumber(Map<String, Object> map);

    List<WsWsSheetListOutVo> getTookPartPageList(Page page, WsWsSheetListSelectInputVo wsSheetListSelectInputVo);

    /**
     * 科室办理业务Top榜单
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getDeptCountTopDatas(Page page,
                                                   @Param("beginTime") String beginTime,
                                                   @Param("endTime") String endTime,
                                                   @Param("deptId") String deptId);

    /**
     * 一级故障类型各类型工单数量
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getLevelOneFaultTypeDatas(@Param("beginTime") String beginTime,
                                                        @Param("endTime") String endTime);

    List<Map<String, Object>> getKnowledgeBaseSubmitTopDatas(Page page,
                                                             @Param("beginTime") String beginTime,
                                                             @Param("endTime") String endTime);

    /**
     * 知识库点赞Top榜单
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getKnowledgeLikeCountTopDatas(Page page,
                                                            @Param("beginTime") String beginTime,
                                                            @Param("endTime") String endTime,
                                                            @Param("deptId") String deptId);

    /**
     * 大屏工单关键数据指标
     *
     * @param type         1为科室级、null为全院级
     * @param fkUserDeptId 科室id
     * @param faultType    故障类型是否全部设置处理人 null是 1否
     * @param count        科室所有人员数量
     * @return
     */
    List<Map<String, Object>> getKeyDataIndicatorsOfWorkOrder(@Param("type") String type,
                                                              @Param("fkUserDeptId") String fkUserDeptId,
                                                              @Param("faultType") String faultType,
                                                              @Param("count") Integer count);

    /**
     * 月度划分，提单、电话提单、办结趋势数据
     *
     * @param type         1为科室级、null为全院级
     * @param fkUserDeptId 科室id
     * @return
     */
    List<Map<String, Object>> getHotTrend(@Param("type") String type,
                                          @Param("fkUserDeptId") String fkUserDeptId);

    /**
     * 处理中工单
     *
     * @param type 1为科室级、null为全院级
     * @return
     */
    List<Map<String, Object>> getProcessTheWorkOrder(@Param("type") String type,
                                                     @Param("fkUserDeptId") String fkUserDeptId);

    /**
     * 科室工单统计
     *
     * @param type 1为科室级、null为全院级
     * @return
     */
    List<Map<String, Object>> getDeptWorkSheetCount(@Param("type") String type,
                                                    @Param("fkUserDeptId") String fkUserDeptId);

    /**
     * 科室工单质量
     *
     * @param fkUserDeptId 有值为科室级、null为全院级
     * @param fkUserDeptId 科室id
     * @return
     */
    WsEvaluationOutVo getDepartmentWorkOrderQuality(
            @Param("fkUserDeptId") String fkUserDeptId,
            @Param("beginTime") String beginTime,
            @Param("endTime") String endTime);


    /**
     * 服务台人员页面-统计指标
     *
     * @param fkDeptId 科室id
     * @return
     */
    Map<String, Object> getServiceDeskStaffStatisticalIndicators(@Param("list") List<String> fkDeptId);

    /**
     * 异常工单各种状态统计
     *
     * @return
     */
    Map<String, Object> getAbnormalWorkSheetStatisCounts(WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);

    /**
     * 超期工单数据
     *
     * @return
     */
    List<WsWorkSheetHomeListOutVo> getExceedTimeWorkSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);

    /**
     * 催办工单
     *
     * @return
     */
    List<WsWorkSheetHomeListOutVo> getHastenWorkSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);

    /**
     * 今日终止/暂停工单
     *
     * @return
     */
    List<WsWorkSheetHomeListOutVo> getSuspendTerminateSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);

    /**
     * 差评工单数据
     *
     * @return
     */
    List<WsWorkSheetHomeListOutVo> getBadReviewSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);

    /**
     * 打回工单数据
     *
     * @return
     */
    List<WsWorkSheetHomeListOutVo> getBackSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);

    /**
     * 协助工单数据
     *
     * @param page
     * @param wsWorkSheetHomeListInputVo
     * @return
     */
    List<WsWorkSheetHomeListOutVo> getAssistWorkOrder(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);

    /**
     * 处理人、报修人各状态工单数量
     *
     * @return
     */
    List<Map<String, Object>> getCountGroupByWorkStatus(WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);


    /**
     * 获取某个时间段，各科室的建单数据及总数
     * @param page
     * @param beginTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getDayGroupByDept(Page page,
                                                @Param("beginTime") String beginTime,
                                                @Param("endTime") String endTime);


    /**
     * 获取所有工单数据时间区间
     *
     * @return
     */
    Map<String, Object> getAllWorkOrderTemporalInterval();


    /**
     * 工单处理情况
     *
     * @return
     */
    List<Map<String, Object>> getWorkOrderProcessing(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);
    
    /**
     * 未完成工单
     *
     * @return
     */
    Integer getWorkOrderUnfinished(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 工单各状态数据统计（工单状态为work_status,报修方式为repair_type,故障紧急程度为fault_emergency,故障影响范围为fault_affect_scope）
     *
     * @return
     */
    List<Map<String, Object>> getWorkByTypeDatas(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 科室接单统计
     *
     * @return
     */
    List<Map<String, Object>> getDeptReceiveWorkSheetDatas(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 科室接单统计到个人
     *
     * @return
     */
    List<Map<String, Object>> getDeptUserReceiveWorkSheetDatas(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);
    
    /**
     * 工单各设备故障分类数据统计
     *
     * @return
     */
    List<Map> getFaultEquipment(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 科室服务平均用时排名
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    List<Map<String, Object>> getDeptQualityOfService(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 人员服务质量
     *
     * @param page
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    List<Map<String, Object>> getQualityOfPersonnelService(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);


    /**
     * 人员服务质量 - 未完成数及占比
     *
     * @param fkUserIds
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    List<Map<String, Object>> getQualityOfPersonnelServiceWwc(List<String> fkUserIds, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);


    /**
     * 查询所有工单
     *
     * @return
     */
    List<WsWsSheet> selectAllList(@Param("deptId") String deptId,
                                  @Param("beginTime") String beginTime,
                                  @Param("endTime") String endTime);


    /**
     * 移动端，工单列表，各页签数量
     *
     * @return
     */
    Map<String, Object> mobileWorkSheetListBusCounts(Map<String, Object> map);

    /**
     * 移动端-工作台-我的工单状态业务数量
     *
     * @return
     */
    Map<String, Object> mobileWorkbenchWorkSheetBusCounts(@Param("fkUserId") String fkUserId);

    /**
     * 查询移动端个人详情，个人综合评分、接单、平均处理用时、完结率、返工率等
     *
     * @param fkUserId 用户id
     * @return
     */
    WsWsSheetMobileInfoOutVo selectMobileInfo(String fkUserId);


    /**
     * 查询工单验收通过率、平均用时、综合得分
     *
     * @param wsWorkSheetMobileStatisticalInputVo
     * @return
     */
    Map<String, Object> passRateAndAvgWorkHoursAndAvgScore(WsWorkSheetMobileStatisticalInputVo wsWorkSheetMobileStatisticalInputVo);

    /**
     * 大屏工单处理中数据
     *
     * @param page
     * @param fkDeptId 有值为科室级、null为全院级
     * @return
     */
    List<WsWsSheetScreenListOutVo> wsSheetScreenPageList(Page page, @Param("fkDeptId") String fkDeptId);

    /**
     * 未派单工单数量
     *
     * @param fkDeptId  有值为科室级、null为全院级
     * @param beginTime
     * @param endTime
     * @return
     */
    int noSendOrders(@Param("fkDeptId") String fkDeptId,
                     @Param("beginTime") String beginTime,
                     @Param("endTime") String endTime);

    /**
     * 大屏，工单分配
     *
     * @param page
     * @param fkDeptId 有值为科室级、null为全院级
     * @return
     */
    List<Map<String, Object>> wsSheetDistributionScreenPageList(Page page,
                                                                @Param("fkDeptId") String fkDeptId,
                                                                @Param("beginTime") String beginTime,
                                                                @Param("endTime") String endTime);

    Map<String, Object> workSheetremind(String fkUserId);

    List<WsSheetPeopleInfoOutVo> peopleProcessCount(@Param("list") List<String> fkUserId);


    /**
     * 查询科室未派单、未结单最新工单信息
     *
     * @param workStatus 工单状态
     * @param deptId     处理科室id
     * @param list       节点状态
     * @return
     */
    List<Map<String, Object>> unprocessedMessageAlertsDpdDjd(@Param("workStatus") String workStatus,
                                                             @Param("deptId") String deptId,
                                                             @Param("list") List<String> list);

    /**
     * 查询处理人，某工单状态数量
     * @param fkUserId
     * @param workStatus
     * @return
     */
    int fkUserWorkStatusCount(@Param("fkUserId")String fkUserId,
                              @Param("fkDeptId") String fkDeptId,
                              @Param("workStatus") String workStatus);


    /**
     * 时效性分析-派单时效
     * @return
     */
    List<WsWorkSheetSendAgingOutVo> selectSheetSendAgingOutVo(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 时效性分析-接单时效
     * @return
     */
    List<WsWorkSheetOderAgingOutVo> selectSheetOderAgingOutVo(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);
}