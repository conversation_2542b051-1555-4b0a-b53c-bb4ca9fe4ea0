package cn.trasen.worksheet.module.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.inputVo.FaultTypeStatusInputVo;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeTypeInputVo;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeTypeStatusInputVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeTypeListOutVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeTypeTreeOutVo;
import cn.trasen.worksheet.module.entity.WsKnowledgeType;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2021/7/3 11:39
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
public interface KnowledgeTypeService {

    String saveKnowledgeType(KnowledgeTypeInputVo knowledgeTypeInputVo);

    /**
     * 知识类型启用、停用
     *
     * @param knowledgeTypeStatusInputVo
     * @return
     */
    int status(KnowledgeTypeStatusInputVo knowledgeTypeStatusInputVo);

    /**
     * 知识类型启用
     *
     * @param pkKnowledgeTypeId 知识类型id
     * @return
     */
    int knowledgeTypeEnable(String pkKnowledgeTypeId);

    KnowledgeTypeListOutVo selectOne(String id);

    WsKnowledgeType selectOneById(String id);

    int deleteKnowledgeType(String id);

    /**
     * 分页列表
     *
     * @param page
     * @param categoryName       分类名称
     * @param pkKnowledgeTypeIds 知识类型id
     * @return
     */
    List<KnowledgeTypeListOutVo> selectKnowledgeTypePageList(Page page, String categoryName,String pkKnowledgeTypeIds);

    /**
     * 知识类型所有数据 树结构展示
     *
     * @param categoryName 分类名称
     * @return
     */
    List<KnowledgeTypeTreeOutVo> selectKnowledgeTypeTreeAllList(String categoryName);

    /**
     * 知识类型所有数据
     *
     * @param categoryName 分类名称
     * @return
     */
    List<KnowledgeTypeTreeOutVo> selectKnowledgeTypeAllList(String categoryName);

    /**
     * 一级知识点类型
     * @return
     */
    List<KnowledgeTypeTreeOutVo> getLevelOneLKnowledgeTypeAllList();


}
