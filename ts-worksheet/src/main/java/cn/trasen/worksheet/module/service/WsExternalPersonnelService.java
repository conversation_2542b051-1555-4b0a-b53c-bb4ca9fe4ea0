package cn.trasen.worksheet.module.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.dto.inputVo.WsExternalPersonnelInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsExternalPersonnelStatusInputVo;
import cn.trasen.worksheet.module.dto.outVo.ExternalPersonnelPageListOutVo;
import cn.trasen.worksheet.module.dto.outVo.FaultTypeListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsSheetPeopleInfoOutVo;
import cn.trasen.worksheet.module.entity.WsExternalPersonnel;
import cn.trasen.worksheet.module.entity.WsFaultType;

public interface WsExternalPersonnelService {

    int saveOrUpdate(WsExternalPersonnelInputVo externalPersonnelInputVo);

    /**
     *
     * @param page
     * @param fuzzy 所属机构名称或姓名模糊搜索
     * @return
     */
    List<ExternalPersonnelPageListOutVo> selectPageList(Page page, String fuzzy,String deptId);

    /**
     *
     * @param fuzzy 所属机构名称或姓名模糊搜索
     * @return
     */
    List<ExternalPersonnelPageListOutVo> selectAllList(String fuzzy,String deptId);

    PlatformResult QueryingPersonnelStatus(String pkExternalPersonnelId);

    int enableOrDisable(WsExternalPersonnelStatusInputVo wsExternalPersonnelStatusInputVo);

    int save(WsExternalPersonnelInputVo externalPersonnelInputVo);

    int update(WsExternalPersonnelInputVo externalPersonnelInputVo);

    WsExternalPersonnel selectOneById(String pkExternalPersonnelId);

    List<WsExternalPersonnel> selectOneByIds(List<String> ids);

    /**
     * 根据科室id键查询外部人员
     * @param deptId 科室id
     * @return
     */
    List<WsExternalPersonnel> selectAllListByDeptId(String deptId);

    WsExternalPersonnel selectOneByPhone(String phone);

    int updateExternalPersonnel(WsExternalPersonnel WsExternalPersonnel);

    int insertExternalPersonnel(WsExternalPersonnel WsExternalPersonnel);

    /**
     * 填充故障类型人员信息
     * @param faultTypeListOutVos
     */
    List<FaultTypeListOutVo> fillPeopleInfo(List<FaultTypeListOutVo> faultTypeListOutVos);

    /**
     * 填充故障类型人员信息
     * @param faultType
     */
    List<WsSheetPeopleInfoOutVo> fillPeopleInfo(WsFaultType faultType);

    /**
     * 查询所有机构名称
     * @param institutionalAffiliations
     * @return
     */
    List<Map<String,Object>> selectAllInstitutionalAffiliations(String institutionalAffiliations);

    /**
     * 去除查询职位名称
     * @param position
     * @return
     */
    List<Map<String,Object>> selectAllPosition(String position);

//    <T >void  test(T t);

}
