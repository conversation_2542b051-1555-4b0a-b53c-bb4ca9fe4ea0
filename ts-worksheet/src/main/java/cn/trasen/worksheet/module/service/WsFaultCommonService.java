package cn.trasen.worksheet.module.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.dto.inputVo.FaultCommonInputVo;
import cn.trasen.worksheet.module.dto.outVo.FaultCommonOutVo;

/**
 * <AUTHOR>
 * @date: 2021/7/1 15:38
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
public interface WsFaultCommonService {

    PlatformResult save(FaultCommonInputVo faultCommonInputVo);

    int deleteAllByIds(String ids);

    PlatformResult deleteById(String id);

    List<FaultCommonOutVo> selectFaultCommonAllList(String falutDeion);

    List<FaultCommonOutVo> selectFaultCommonPageList(Page page, String falutDeion);

}
