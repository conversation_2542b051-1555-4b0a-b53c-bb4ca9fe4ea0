package cn.trasen.worksheet.module.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.inputVo.FaultTypeInputVo;
import cn.trasen.worksheet.module.dto.inputVo.FaultTypeStatusInputVo;
import cn.trasen.worksheet.module.dto.outVo.FaultTypeListOutVo;
import cn.trasen.worksheet.module.dto.outVo.FaultTypeTreeOutVo;
import cn.trasen.worksheet.module.entity.WsFaultType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2021/7/3 11:39
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
public interface WsFaultTypeService {

    int insertFaultType(WsFaultType wsFaultType);

    int updateFaultType(WsFaultType wsFaultType);

    String saveFaultType(FaultTypeInputVo faultTypeInputVo);

    int status(FaultTypeStatusInputVo faultTypeStatusInputVo);

    int faultTypeEnable(String pkFaultTypeId);

    FaultTypeListOutVo selectOne(String id);

    WsFaultType selectOneById(String pkFaultTypeId);

    WsFaultType selectOneByCategoryNameAndParentId(String categoryName,String parentId,String fkDeptId);

    int deleteFaultType(String id);

    /**
     * 分页列表
     *
     * @param page
     * @param fkDeptId   科室id
     * @param categoryName   分类名称
     * @param pkFaultTypeIds 故障类型id
     * @return
     */
    List<FaultTypeListOutVo> selectFaultTypePageList(Page page,String fkDeptId, String categoryName, String pkFaultTypeIds);

    /**
     * 故障类型，树状结构
     * @param status 0包含停用数据 1仅启用数据
     * @param fkDeptId  科室id
     * @param categoryName  故障类型名称
     * @return
     */
    List<FaultTypeTreeOutVo> selectFaultTypeTreeAllList(String status,String fkDeptId,String categoryName);


    /**
     * 故障类型
     * @param status 0包含停用数据 1仅启用数据
     * @param fkDeptId  科室id
     * @param categoryName  故障类型名称
     * @return
     */
    List<FaultTypeTreeOutVo> selectFaultTypeAllList(String status,String fkDeptId,String categoryName);

    /**
     * 查询未设置故障类型处理人的故障类型数量
     * @return
     */
    int faultTypePeopleIsNullCounts();


    /**
     * 查询所有一级故障类型分类
     * @return
     */
    List<WsFaultType> selectFaultTypeAllList();

}
