package cn.trasen.worksheet.module.service;

import java.util.List;

import cn.trasen.worksheet.module.dto.inputVo.WsWsHatenInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsHatenListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsHatenOutVo;

/**
 * <AUTHOR>
 * @date: 2021/7/1 11:53
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
public interface WsHastenService {

    int save(WsWsHatenInputVo wsHatenInputVo);

    /**
     * 查询催办次数，最新催办时间
     *
     * @param workNumber 工单编号
     * @return
     */
    WsWsHatenOutVo getHastenInfo(String workNumber);

    /**
     * 查询催办信息
     *
     * @param workNumber 工单编号
     * @return
     */
    List<WsWsHatenListOutVo> getHastenInfoList(String workNumber);
}
