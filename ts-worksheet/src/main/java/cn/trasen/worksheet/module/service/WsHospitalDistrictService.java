package cn.trasen.worksheet.module.service;


import cn.trasen.worksheet.module.dto.inputVo.WsHospitalDistrictSaveInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsSysConfigSaveInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsHospitalDistrictListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsOmMeauListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsSysConfigInfoOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkFlowListOutVo;
import cn.trasen.worksheet.module.entity.WsHospitalDistrict;

import java.util.List;

/**
 * 工单院区配置
 */
public interface WsHospitalDistrictService {


    /**
     * 新增、修改工单院区配置
     *
     * @param hospitalDistrictSaveInputVo
     * @return
     */
    Integer hospitalDistrict(WsHospitalDistrictSaveInputVo hospitalDistrictSaveInputVo);

    /**
     * 查询工单院区配置
     *
     * @return
     */
    List<WsHospitalDistrictListOutVo> getHospitalDistrictList();

    /**
     * 所有院区信息
     * @param hospitalDistrictStatus 0禁用，1启用
     * @return
     */
    List<WsWorkFlowListOutVo> getHospitalDistrictEnableList(String hospitalDistrictStatus);

    /**
     * 启用禁用(0禁用，1启用)
     *
     * @param pkHospitalDistrictId   院区id
     * @param hospitalDistrictStatus 0禁用，1启用
     * @return
     */
    Integer enableOrDisable(String pkHospitalDistrictId, String hospitalDistrictStatus);

    /**
     * 所有院区信息
     * @param hospitalDistrictStatus 0禁用，1启用
     * @return
     */
    List<WsHospitalDistrict> getAllList(String hospitalDistrictStatus);

    /**
     * 单个院区信息
     * @param pkHospitalDistrictId 院区id
     * @return
     */
    WsHospitalDistrict getOne(String pkHospitalDistrictId);
}
