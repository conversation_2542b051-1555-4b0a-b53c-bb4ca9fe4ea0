package cn.trasen.worksheet.module.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.inputVo.WsOmMeauInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsOmMeauListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkFlowListOutVo;
import cn.trasen.worksheet.module.entity.WsOmMeau;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * 语音菜单配置
 */
public interface WsOmMeauService {

    /**
     * 保存修改
     *
     * @param wsOmMeauInputVo
     * @return
     */
    int saveOrUpdate(WsOmMeauInputVo wsOmMeauInputVo);

    /**
     * 根据id查询单条数据
     *
     * @param wsOmMeau
     * @return
     */
    WsOmMeau seleteOneOmMeau(WsOmMeau wsOmMeau);

    /**
     * 分页列表
     *
     * @param page
     * @return
     */
    List<WsOmMeauListOutVo> selectOmMeauList(Page page);

    List<WsWorkFlowListOutVo> selectOmItemMeauAllList();

    List<WsOmMeauListOutVo> selectOmMeauAllList();

    List<WsOmMeauListOutVo> meauPermissionsList();

    /**
     * 拖动排序修改
     * @param omMeauInputVos
     * @return
     */
    int updateSort(List<WsOmMeauInputVo> omMeauInputVos);

    int insertOmMeau(WsOmMeau wsOmMeau);

    int updateOmMeau(WsOmMeau wsOmMeau);

    int deleteOmMeau(String ids);

    /**
     * 个人是否属于处理科室
     * @return
     */
    boolean personalBelongToBusinessDept();


    /**
     * 根据workNumber查询单条数据
     *
     * @param workNumber 工单好
     * @return
     */
    WsOmMeau seleteOneOmMeauByWorkNumber(String workNumber);

}
