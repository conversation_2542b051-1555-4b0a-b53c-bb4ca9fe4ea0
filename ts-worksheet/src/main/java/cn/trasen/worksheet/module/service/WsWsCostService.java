package cn.trasen.worksheet.module.service;


import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.inputVo.WsSysConfigSaveInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsCostPageListInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsCostSaveInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsSysConfigInfoOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsCostOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsCostPageListOutVo;
import cn.trasen.worksheet.module.entity.WsWsCost;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 工单费用
 */
public interface WsWsCostService {

    /**
     * 新增、修改工单费用
     * @param costSaveInputVo
     * @return
     */
    Integer wsCost(WsWsCostSaveInputVo costSaveInputVo);


    /**
     * 费用分页列表
     * @param costPageListInputVo 查询参数
     * @return
     */
    List<WsWsCostPageListOutVo> getPageList(Page page, WsWsCostPageListInputVo costPageListInputVo);


    /**
     * 费用总金额
     * @param costPageListInputVo
     */
    Float wsCostSum(WsWsCostPageListInputVo costPageListInputVo);


    /**
     * 逻辑删除工单费用
     * @param pkWsCostIds 费用id
     * @return
     */
    Integer deleteWsCost(List<String> pkWsCostIds);

    /**
     * 工单费用详情
     * @param pkWsCostId 主键
     * @return
     */
    WsWsCostOutVo getOne(String pkWsCostId);

    /**
     * 工单费用详情列表数据
     * @param workNumber 工单号
     * @return
     */
    List<WsWsCostPageListOutVo> getListByWorkNumber(String workNumber);

    /**
     * 费用分页列表导出
     * @param costPageListInputVo 查询参数
     * @return
     */
    void exportExcel(Page page, WsWsCostPageListInputVo costPageListInputVo,String type, HttpServletResponse response, HttpServletRequest request);

    /**
     * 费用导出
     * @param workNumber 工单号
     * @return
     */
    void exportExcelByWorkNumber(String workNumber, HttpServletResponse response, HttpServletRequest request);




}
