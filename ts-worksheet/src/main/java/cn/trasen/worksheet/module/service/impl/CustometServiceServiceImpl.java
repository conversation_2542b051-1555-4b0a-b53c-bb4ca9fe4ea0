package cn.trasen.worksheet.module.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.assertj.core.util.Lists;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;

import cn.hutool.http.HttpUtil;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.base.MessageWebPushReq;
import cn.trasen.homs.bean.base.OrganizationListReq;
import cn.trasen.homs.bean.base.OrganizationListSimpleRes;
import cn.trasen.homs.bean.hrms.HrmsSchedulingManage;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.homs.feign.base.MessageFeignService;
import cn.trasen.worksheet.MessageWebSocket;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.constant.ConstantYml;
import cn.trasen.worksheet.common.enums.CuttingOperatorEnum;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.enums.OmLineEnum;
import cn.trasen.worksheet.common.util.DateUtils;
import cn.trasen.worksheet.common.util.FeignInfoUitls;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.common.util.MyBeanUtils;
import cn.trasen.worksheet.common.util.RequestUtils;
import cn.trasen.worksheet.module.dto.inputVo.CustometServiceStatusInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsCustomeServiceInputVo;
import cn.trasen.worksheet.module.dto.outVo.WebSocketMsgOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsCustometLogOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsCustometServiceOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsOmMeauListOutVo;
import cn.trasen.worksheet.module.entity.WsCustometLog;
import cn.trasen.worksheet.module.entity.WsCustometService;
import cn.trasen.worksheet.module.entity.WsOmFile;
import cn.trasen.worksheet.module.mapper.WsCustometServiceMapper;
import cn.trasen.worksheet.module.service.CustometServiceService;
import cn.trasen.worksheet.module.service.WsCustometLogService;
import cn.trasen.worksheet.module.service.WsOmFileService;
import cn.trasen.worksheet.module.service.WsOmMeauService;
import cn.trasen.worksheet.module.service.WsSheetService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date: 2021/7/20 10:04
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Slf4j
@Service
public class CustometServiceServiceImpl implements CustometServiceService {

    @Autowired
    private WsCustometServiceMapper wsCustometServiceMapper;
    @Autowired
    private WsOmFileService wsOmFileService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private WsCustometLogService wsCustometLogService;
    @Autowired
    private WsOmMeauService wsOmMeauService;
    @Autowired
    private MessageFeignService messageFeignService;
    @Autowired
    private WsSheetService wsSheetService;
    @Autowired
    private HrmsOrganizationFeignService hrmsOrganizationFeignService;
    @Value("${om.host}")
    private String host;
    @Value("${om.fileUrl}")
    private String omFileUrl;


    /**
     * OM调用应服务器入口
     *
     * @param request
     */
    @Override
    public void theEntrance(HttpServletRequest request) {
        // 读取HttpServletRequest中信息
        String requestInfo = RequestUtils.readRequestInfo(request);
        log.info("OMXML信息：" + requestInfo);
        // 处理HttpServletRequest信息（XML数据结构）
        Document document = null;
        try {
            document = DocumentHelper.parseText(requestInfo);
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        // 获得xml的根节点
        Element rootElement = document.getRootElement();
        // mac地址
        String mac = rootElement.element(CommonlyConstants.Om.Mac.MAC).getText();
        Attribute attribute = rootElement.attribute(CommonlyConstants.Om.XmlNode.ATTRIBUTE);
        // 获取事件名称
        String index = "";
        if (null != attribute) {
            index = attribute.getValue();
        }
        // 来电编号
        AtomicReference<String> visitorId = new AtomicReference<>("");
        AtomicReference<String> callId = new AtomicReference<>("");
        // 语音菜单操作
        AtomicReference<String> infoText = new AtomicReference<>("");
        // 主叫方
        AtomicReference<String> form = new AtomicReference<>("");
        // 主叫方
        // 被叫方
        AtomicReference<String> extId = new AtomicReference<>("");
        // 被叫方
        AtomicReference<String> outerTo = new AtomicReference<>("");
        // 存在录音的节点
        AtomicReference<String> recordingText = new AtomicReference<>("");

        // 获得根节点下面所有的子节点
        rootElement.elements().forEach(elementTemp -> {
            if (CommonlyConstants.Om.XmlNode.VISITOR.equals(elementTemp.getName())) {
                visitorId.set(
                        Optional.ofNullable(elementTemp.attribute(CommonlyConstants.Om.XmlNode.ID))
                                .map(temp -> temp.getValue())
                                .orElse(""));
                form.set(
                        Optional.ofNullable(elementTemp.attribute(CommonlyConstants.Om.XmlNode.FORM))
                                .map(temp -> temp.getValue())
                                .orElse(""));
                callId.set(
                        Optional.ofNullable(elementTemp.attribute(CommonlyConstants.Om.XmlNode.CALLID))
                                .map(temp -> temp.getValue())
                                .orElse(""));
                infoText.set(elementTemp.getStringValue());

            }
            if (CommonlyConstants.Om.XmlNode.EXT.equals(elementTemp.getName())) {
                extId.set(
                        Optional.ofNullable(elementTemp.attribute(CommonlyConstants.Om.XmlNode.ID))
                                .map(temp -> temp.getValue())
                                .orElse(""));
            }
            if (CommonlyConstants.Om.XmlNode.OUTER.equals(elementTemp.getName())) {
                outerTo.set(
                        Optional.ofNullable(elementTemp.attribute(CommonlyConstants.Om.XmlNode.OUTER_TO))
                                .map(temp -> temp.getValue())
                                .orElse(""));
                form.set(
                        Optional.ofNullable(elementTemp.attribute(CommonlyConstants.Om.XmlNode.FORM))
                                .map(temp -> temp.getValue())
                                .orElse(""));
                callId.set(
                        Optional.ofNullable(elementTemp.attribute(CommonlyConstants.Om.XmlNode.CALLID))
                                .map(temp -> temp.getValue())
                                .orElse(""));
            }
            if (CommonlyConstants.Om.XmlNode.RECORDING.equals(elementTemp.getName()) && "".equals(recordingText.get())) {
                recordingText.set(elementTemp.getText());
            }

        });
        // 来电转接语音菜单
        if (CommonlyConstants.Om.Event.INCOMING.equals(index)) {
            log.info("OM来电转接语音菜单");
            String result = HttpUtil.post(ConstantYml.getInstance().getOmIpHost()
                            + ":"
                            + host,
                    CommonlyConstants.Om.StitchingXML.CALL_TO_MEAU_PREFIX + visitorId.get() + CommonlyConstants.Om.StitchingXML.CALL_TO_MEAU_SUFFIX);
            log.info("OM来电转接语音菜单返回XML信息：" + result);
            // 语音菜单转接个人
        } else if (CommonlyConstants.Om.Event.DTMF.equals(index)) {
            // 分机号、移动电话号码
            List<WsCustometService> custometServiceServiceList = Lists.newArrayList();
            // 语音菜单转接
            // 配置语音菜单信息
            List<WsOmMeauListOutVo> meau = wsOmMeauService.selectOmMeauAllList()
                    .stream()
                    .filter(temp -> temp.getInputContent().trim().equals(infoText.get().trim()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(meau)) {
                // 来电按键错误语音，请求参数
                String temp = "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n" +
                        "<Transfer attribute=\"Connect\">\n" +
                        " <visitor id=\"" + visitorId + "\" />\n" +
                        " <menu id=\"1\"/>\n" +
                        " <voicefile>" + CommonlyConstants.Om.HardwareInfo.MEAU_ERROR + "+"
                        + CommonlyConstants.Om.HardwareInfo.MEAU + "</voicefile>\n" +
                        "</Transfer>";
                log.info("OM来电按键错误语音，请求参数XML信息：" + temp);
                log.info("OM来电转接语音菜单");
                String result = HttpUtil.post(ConstantYml.getInstance().getOmIpHost()
                        + ":"
                        + host, temp);
                log.info("OM来电转接语音菜单返回XML信息：" + result);
            } else {
                custometServiceServiceList = wsCustometServiceMapper.selectListByDeptId(
                        meau.stream()
                                .map(WsOmMeauListOutVo::getDeptId)
                                .collect(Collectors.toList())
                );
                // 无配置坐席，语音提示
                if (CollectionUtils.isEmpty(custometServiceServiceList)) {
                    String temp = "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n" +
                            "<Transfer attribute=\"Connect\">\n" +
                            " <visitor id=\"" + visitorId + "\" />\n" +
                            " <menu id=\"1\"/>\n" +
                            " <voicefile>" + CommonlyConstants.Om.HardwareInfo.MEAU_NOPERSONNEL + "+"
                            + CommonlyConstants.Om.HardwareInfo.MEAU + "</voicefile>\n" +
                            "</Transfer>";
                    log.info("OM来电转接语音菜单，无配置坐席语音提示，请求参数XML信息：" + temp);
                    log.info("OM来电转接语音菜单，无配置坐席语音提示");
                    String result = HttpUtil.post(ConstantYml.getInstance().getOmIpHost()
                            + ":"
                            + host, temp);
                    log.info("OM来电转接语音菜单，无配置坐席语音提示返回XML信息：" + result);
                }
            }

            if (!CollectionUtils.isEmpty(custometServiceServiceList)) {
                // 根据语音菜单转接
                String fkUserId = voiceMenuToIndividual(visitorId.get(), form.get(), callId.get(), custometServiceServiceList);

                // 当前通话未进入等候列表才予以来电弹屏
                if (StringUtil.isEmpty(fkUserId)) {
                    // 来电弹屏
                    screenPopUp(visitorId.get(), form.get(), fkUserId);
                }
            }

            // 响铃
        } else if (CommonlyConstants.Om.Event.RING.equals(index) || CommonlyConstants.Om.Event.TRANSIENT.equals(index)) {
            if (StringUtil.isEmpty(extId.get()) && !StringUtil.isEmpty(outerTo.get())) {
                extId.set(outerTo.get());
            }
            if (!StringUtil.isEmpty(visitorId.get()) && !StringUtil.isEmpty(extId.get()) && !StringUtil.isEmpty(form.get())) {
                // 存在mac地址及启用无坐席分机方案
                if (!StringUtil.isEmpty(mac)) {
                    // 主叫号码存入redis
                    redisTemplate.opsForValue().set(form.get(), IdUtils.getId() + visitorId.get());

                    // 处理科室id
                    String meauDeptId = wsOmMeauService.selectOmMeauAllList()
                            .stream()
                            .filter(temp -> mac.equals(temp.getMac()))
                            .map(WsOmMeauListOutVo::getDeptId)
                            .collect(Collectors.joining());
                    if (StringUtil.isEmpty(meauDeptId)) {
                        return;
                    }
                    List<String> fkUserIds = Lists.newArrayList();
                    // 值班人员信息
                    List<HrmsSchedulingManage> informationOnDuty = wsSheetService.informationOnDuty(meauDeptId, DateUtils.dateToStringFormat("yyyy-MM-dd", new Date()));

                    // 确认推送弹屏人员信息
                    if (!CollectionUtils.isEmpty(informationOnDuty)) {
                        fkUserIds = informationOnDuty.stream()
                                .map(HrmsSchedulingManage::getEmployeeId)
                                .collect(Collectors.toList());
                        List<WsCustometService> wsCustometServices = selectListByFkUserId(
                                fkUserIds,
                                null,
                                IndexEnum.ONE.getValue()
                        );
                        if (!CollectionUtils.isEmpty(wsCustometServices)) {
                            fkUserIds = wsCustometServices.stream()
                                    .map(WsCustometService::getFkUserId)
                                    .collect(Collectors.toList());
                        }
                    }

                    // 保存通话记录
                    WsCustometLog wsCustometLog = new WsCustometLog();
                    wsCustometLog.setPkCustometLogId(redisTemplate.opsForValue().get(form.get()) + "");
//                    wsCustometLog.setFkUserId(wsCustometService.getFkUserId());
//                    wsCustometLog.setEquipmentId(wsCustometService.getEquipment());
//                    wsCustometLog.setCallPhone(wsCustometService.getPhone());
                    wsCustometLog.setVisitPhone(form.get());
                    wsCustometLog.setBusinessDeptId(meauDeptId);
                    // 主叫方个人信息
                    EmployeeResp userNameByPhone = FeignInfoUitls.getUserNameByPhone(form.get());
                    if (null == userNameByPhone) {
                        OrganizationListReq req = new OrganizationListReq();
                        req.setTel(form.get());
                        req.setIsEnable("1");
                        try {
                            log.info("------------------------------调用基础服务查询机构信息开始");
                            PlatformResult<List<OrganizationListSimpleRes>> simpleList = hrmsOrganizationFeignService.getSimpleList(req);
                            if (simpleList.isSuccess() && !CollectionUtils.isEmpty(simpleList.getObject())) {
                                wsCustometLog.setVisitUserDeptName(simpleList.getObject().stream().map(OrganizationListSimpleRes::getName).collect(Collectors.joining()));
                                wsCustometLog.setVisitUserDeptId(simpleList.getObject().stream().map(OrganizationListSimpleRes::getOrganizationId).collect(Collectors.joining()));
                            } else {
                                log.error("------------------------------调用基础服务查询机构信息失败：" + simpleList.getMessage());
                                wsCustometLog.setVisitUserName("未知来电");
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.error("------------------------------调用基础服务查询机构信息失败：" + e.getMessage());
                            wsCustometLog.setVisitUserName("未知来电");
                        }
                        wsCustometLog.setVisitUserName("未知来电");
                    } else {
                        wsCustometLog.setVisitUserDeptId(userNameByPhone.getOrgId());
                        wsCustometLog.setVisitUserDeptName(userNameByPhone.getOrgName());
                        wsCustometLog.setVisitUserId(userNameByPhone.getEmployeeId());
                        wsCustometLog.setVisitUserName(userNameByPhone.getEmployeeName());
                    }
                    wsCustometLogService.insertWsCustometLog(wsCustometLog);
                    if (!CollectionUtils.isEmpty(fkUserIds)) {
                        // 来电弹屏
                        screenPopUp(
                                visitorId.get(),
                                form.get(),
                                fkUserIds
                        );
                    }

                } else {
                    WsCustometService wsCustometService = wsCustometServiceMapper.selectOneCustometServiceByPhone(
                            "0".equals(extId.get().substring(0, 1)) ? extId.get().substring(1, extId.get().length()) : extId.get()
                    );
                    // 来电弹屏
                    screenPopUp(visitorId.get(), form.get(), wsCustometService.getFkUserId());
                }
            }
            // 接听
        } else if (CommonlyConstants.Om.Event.ANSWERED.equals(index)) {
            if (!StringUtil.isEmpty(visitorId.get()) && !StringUtil.isEmpty(extId.get())) {
                if (outerTo.get().startsWith(IndexEnum.ZERO.getValue() + "")) {
                    outerTo.set(outerTo.get().substring(IndexEnum.ONE.getValue()));

                }
                // 接听后自动打开创建工单页面
                EmployeeResp userNameByPhone = FeignInfoUitls.getUserNameByPhone(form.get());
                
                openCreateWorkSheet(redisTemplate.opsForValue().get(form.get()) + "",
                        StringUtil.isEmpty(extId.get()) ? outerTo.get() : extId.get(),form.get(),userNameByPhone.getEmployeeName(),userNameByPhone.getOrgName());
            }
            // 拉取OM录音文件上传至服务器
        } else if (requestInfo.contains("<Cdr")) {
            String recording = null == rootElement.element("Recording") ? null : rootElement.element("Recording").getStringValue();
            if (StringUtil.isEmpty(recording)) {
                return;
            }
            hangUpAndUploadRecording(rootElement.element("CPN").getStringValue(), rootElement.element("Recording").getStringValue());
            // 挂断
        } else if (CommonlyConstants.Om.Event.BYE.equals(index) || "IDLE".equals(index)) {
            if (outerTo.get().startsWith(IndexEnum.ZERO.getValue() + "")) {
                outerTo.set(outerTo.get().substring(IndexEnum.ONE.getValue()));

            }
            // 推送信息至客户端
            log.info("--------------------------------------------关闭弹屏");

            // 存在mac地址及启用无坐席分机方案
            if (!StringUtil.isEmpty(mac)) {
                // 处理科室id
                String meauDeptId = wsOmMeauService.selectOmMeauAllList()
                        .stream()
                        .filter(temp -> mac.equals(temp.getMac()))
                        .map(WsOmMeauListOutVo::getDeptId)
                        .collect(Collectors.joining());
                if (StringUtil.isEmpty(meauDeptId)) {
                    return;
                }

                // 值班人员信息
                List<HrmsSchedulingManage> informationOnDuty = wsSheetService.informationOnDuty(meauDeptId, DateUtils.dateToStringFormat("yyyy-MM-dd", new Date()));
                if (CollectionUtils.isEmpty(informationOnDuty)) {
                    return;
                }


                // 关闭来电弹窗
                closeScreenPopUp(StringUtil.isEmpty(extId.get()) ? outerTo.get() : extId.get(),
                        form.get(),
                        informationOnDuty.stream()
                                .map(HrmsSchedulingManage::getEmployeeId)
                                .collect(Collectors.toList())
                );
            }
        }
    }


    /**
     * 坐席保存、修改
     *
     * @param wsCustomeServiceInputVo
     * @return
     */
    @Transactional
    @Override
    public String saveCustometService(WsCustomeServiceInputVo wsCustomeServiceInputVo) {
        WsCustometService wsCustometService = new WsCustometService();
        if (StringUtil.isEmpty(wsCustomeServiceInputVo.getPkCustometServiceId())) {
            MyBeanUtils.copyBeanNotNull2Bean(wsCustomeServiceInputVo, wsCustometService);
            wsCustometService.setPkCustometServiceId(IdUtils.getId());
            if (IndexEnum.ZERO.getValue() == wsCustometServiceMapper.saveCustometService(wsCustometService)) {
                throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
            }
        } else {
            // 修改坐席为工作中时
//            if (IndexEnum.ONE.getValue() == wsCustomeServiceInputVo.getCustometServiceStatus()) {
//                // 修改该坐席其他人为休息中
//                wsCustometServiceMapper.updateOther(new WsCustometService(wsCustomeServiceInputVo.getPhone()));
//            }
            wsCustometService = selectOneById(wsCustomeServiceInputVo.getPkCustometServiceId());
            MyBeanUtils.copyBeanNotNull2Bean(wsCustomeServiceInputVo, wsCustometService);
            if (IndexEnum.ZERO.getValue() == updateCustometService(wsCustometService)) {
                throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
            }
        }
        return CommonlyConstants.OperationReturnValue.SUCCESS;
    }

    @Override
    public WsCustometService selectOneById(String pkCustometServiceId) {
        return Optional.ofNullable(wsCustometServiceMapper.selectOneById(pkCustometServiceId))
                .map(WsCustometService::get)
                .orElseGet(null);
    }

    @Transactional
    @Override
    public int updateCustometService(WsCustometService wsCustometService) {
        if (CommonlyConstants.YesOrNo.NO == wsCustometServiceMapper.updateCustometService(wsCustometService)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }

    /**
     * 根据用户id查询坐席信息
     *
     * @param fkUserId
     * @return
     */
    @Override
    public WsCustometServiceOutVo getOneCustometService(String fkUserId) {
        WsCustometService wsCustometService = wsCustometServiceMapper.selectOneByFkUserId(fkUserId);
        WsCustometServiceOutVo wsCustometServiceOutVo = new WsCustometServiceOutVo();
        MyBeanUtils.copyBeanNotNull2Bean(null == wsCustometService ? new WsCustometService() : wsCustometService, wsCustometServiceOutVo);
        return wsCustometServiceOutVo;
    }

    /**
     * 根据用户id查询坐席
     *
     * @param fkUserId
     * @param custometServiceStatus 来电是否弹屏（0否1是）
     * @param playScreen            坐席状态（0休息1工作中）
     * @return
     */
    @Override
    public List<WsCustometService> selectListByFkUserId(List<String> fkUserId, Integer custometServiceStatus, Integer playScreen) {
        return wsCustometServiceMapper.selectListByFkUserId(fkUserId, custometServiceStatus, playScreen);
    }

    @Override
    public WsCustometService selectOneCustometServiceByPhone(String phone) {
        return wsCustometServiceMapper.selectOneCustometServiceByPhone(phone);
    }

    @Override
    public String getOneCustometServiceStatus(CustometServiceStatusInputVo custometServiceStatusInputVo) {
        if (null != wsCustometServiceMapper.getOneCustometServiceStatus(custometServiceStatusInputVo)) {
            throw new BusinessException("坐席已被其他人使用，继续操作将把其他人挤下线");
        }
        return null;
    }


    /**
     * 语音菜单转接个人
     *
     * @param visitorId                  来电编号
     * @param form                       主叫方
     * @param custometServiceServiceList 所有坐席号码
     * @return 被叫方电话
     */
    public String voiceMenuToIndividual(String visitorId, String form, String callId, List<WsCustometService> custometServiceServiceList) {
        // 分机号
        List<WsCustometService> specialPlaneList = custometServiceServiceList
                .stream()
                .filter(temp -> temp.getPhone().length() < 11)
                .collect(Collectors.toList());
        // 空闲分机号
        List<WsCustometService> readySpecialPlaneList = Lists.newArrayList();

        specialPlaneList.forEach(temp -> {
            String c = "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n" +
                    "<Control attribute=\"Query\">\n" +
                    " <ext id=\"" + temp.getPhone() + "\"/>\n" +
                    "</Control>";
            String result = HttpUtil.post(ConstantYml.getInstance().getOmIpHost() + ":" + host, c);

            try {
                // 分机状态
                AtomicReference<String> stateText = new AtomicReference<>("");
                Document resultDocument = DocumentHelper.parseText(result);
                // Status-ext-state节点text值
                stateText.set(resultDocument
                        .getRootElement()
                        .element(CommonlyConstants.Om.XmlNode.EXT)
                        .element(CommonlyConstants.Om.XmlNode.STATE)
                        .getText());
                if (CommonlyConstants.Om.XmlNode.STATE_TEXT.equals(stateText.get())) {
                    readySpecialPlaneList.add(temp);
                }
            } catch (DocumentException e) {
                e.printStackTrace();
            }
        });
        WsCustometService wsCustometService;
        String param = "";
        // 是否存在空闲分机，默认存在
        boolean isFree = true;
        // 无空闲分机
        if (CollectionUtils.isEmpty(readySpecialPlaneList)) {
            wsCustometService = custometServiceServiceList.get(new Random().nextInt(custometServiceServiceList.size()));
            // 随机呼叫被叫方为分机，进入呼叫队列
            if (CommonlyConstants.MobileNumber.DIGITS != wsCustometService.getPhone().length()) {
                param = "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n" +
                        "<Transfer attribute=\"Queue\">\n" +
                        " <visitor id=\"" + visitorId + "\"/>\n" +
                        " <ext id=\"" + wsCustometService.getPhone() + "\"/>\n" +
                        "</Transfer>";
                isFree = false;
            }
        } else {
            // 去除状态不是空闲的分机
            custometServiceServiceList.removeAll(specialPlaneList);
            custometServiceServiceList.addAll(readySpecialPlaneList);
            wsCustometService = custometServiceServiceList.get(new Random().nextInt(custometServiceServiceList.size()));
        }
        if (StringUtil.isEmpty(param)) {
            if (CommonlyConstants.MobileNumber.DIGITS != wsCustometService.getPhone().length()) {
                param = "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n" +
                        "<Transfer attribute=\"Connect\">\n" +
                        " <visitor id=\"" + visitorId + "\"/>\n" +
                        " <ext id=\"" + wsCustometService.getPhone() + "\"/>\n" +
                        "</Transfer>";
            } else {
                param = "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n" +
                        "<Transfer attribute=\"Connect\">\n" +
                        " <visitor id=\"" + visitorId + "\" />\n" +
                        " <outer to=\"" + wsCustometService.getPhone() + "\" />\n" +
                        "</Transfer>";
            }
        }
        // 主叫号码存入redis
        redisTemplate.opsForValue().set(form, IdUtils.getId() + visitorId);

        // 保存通话记录
        WsCustometLog wsCustometLog = new WsCustometLog();
        wsCustometLog.setPkCustometLogId(redisTemplate.opsForValue().get(form) + "");
        wsCustometLog.setFkUserId(wsCustometService.getFkUserId());
        wsCustometLog.setEquipmentId(wsCustometService.getEquipment());
        wsCustometLog.setCallPhone(wsCustometService.getPhone());
        wsCustometLog.setVisitPhone(form);
        // 主叫方个人信息
        EmployeeResp userNameByPhone = FeignInfoUitls.getUserNameByPhone(form);
        if (null == userNameByPhone) {
            wsCustometLog.setVisitUserName("未知来电");
        } else {
            wsCustometLog.setVisitUserDeptId(userNameByPhone.getOrgId());
            wsCustometLog.setVisitUserDeptName(userNameByPhone.getOrgName());
            wsCustometLog.setVisitUserId(userNameByPhone.getEmployeeId());
            wsCustometLog.setVisitUserName(userNameByPhone.getEmployeeName());
        }
        WebSocketMsgOutVo webSocketMsgOutVo = new WebSocketMsgOutVo();

        if (!isFree) {
            // 初始化等候通话记录
            wsCustometLog.setCallType(IndexEnum.MINUS_ONE.getValue());
        }
        wsCustometLogService.insertWsCustometLog(wsCustometLog);
        System.out.println(param);
        String post = HttpUtil.post(
                CommonlyConstants.Om.HardwareInfo.ACCESS_PROTOCOL +
                        ConstantYml.getInstance().getOmIpHost() + ":" +
                        host
                , param);
        System.out.println(post);
        if (!isFree) {
            //组装等待列表消息
            webSocketMsgOutVo.setType(IndexEnum.ZERO.getValue() + "");
            // 个人坐席通话记录
            List<WsCustometLogOutVo> wsCustometLogOutVos = wsCustometLogService.selectAllList(
                    new WsCustometLog(IndexEnum.MINUS_ONE.getValue(), wsCustometService.getFkUserId()));
            webSocketMsgOutVo.setWsCustometLogOutVoList(wsCustometLogOutVos);
            // 消息推送
            MessageWebSocket.sendtoUser(JSON.toJSONString(webSocketMsgOutVo), wsCustometService.getFkUserId());
            pushMessage(JSON.toJSONString(webSocketMsgOutVo), wsCustometService.getFkUserId());
        } else {
            screenPopUp(visitorId, form, wsCustometService.getFkUserId());
        }
        return wsCustometService.getFkUserId();
    }


//    public static void main(String[] args) {
//        String a = "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n" +
//                "<Control attribute=\"Query\">\n" +
//                " <trunk id=\"203\"/>\n" +
//                "</Control>";
//        String b = "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n" +
//                "<Control attribute=\"Assign\">\n" +
//                " <menu id=\"1\">\n" +
//                " <voicefile>user_meau</voicefile>\n" +
//                " <repeat>3</repeat>\n" +
//                " <infolength>1</infolength>\n" +
//                " </menu>\n" +
//                "</Control>";
//        String post = HttpUtil.post("http://192.168.81.249:80", b);
//        System.out.println(post);
//
//
//    }

    /**
     * 来电弹屏
     *
     * @param visitorId 来电编号
     * @param form      主叫方(报修人电话)
     * @param fkUserId  弹屏用户
     */
    public void screenPopUp(String visitorId, String form, Object fkUserId) {
        if (StringUtil.isEmpty(visitorId) || StringUtil.isEmpty(form)) {
            return;
        }
        if (fkUserId instanceof List) {
            List<String> fkUserIds = (List) fkUserId;
            fkUserIds.forEach(fkUserIdTemp -> {
                assembleScreenPopUp(visitorId, form, fkUserIdTemp);
            });
        } else if (fkUserId instanceof String) {
            assembleScreenPopUp(visitorId, form, fkUserId + "");
        } else {
            log.error("推送来电弹屏失败：fkUserId类型有误:" + fkUserId);
        }
    }

    /**
     * 组装来电弹屏信息
     *
     * @param visitorId
     * @param form
     * @param fkUserId
     */
    public void assembleScreenPopUp(String visitorId, String form, String fkUserId) {
        // 等候列表转来电弹屏时，修改通话记录状态
        WsCustometLog wsCustometLog = wsCustometLogService.selectOneById(redisTemplate.opsForValue().get(form) + "");
        if (IndexEnum.MINUS_ONE.getValue() == wsCustometLog.getCallType()) {
            wsCustometLog.setCallType(IndexEnum.ZERO.getValue());
            wsCustometLog.setCallWorkStatus(IndexEnum.ZERO.getValue());
            wsCustometLog.setUpdateTime(new Date());
            wsCustometLog.setUpdateBy(UserInfoHolder.getCurrentUserId());
            wsCustometLogService.updateWsCustometLog(wsCustometLog);
        }
        //
        WebSocketMsgOutVo webSocketMsgOutVo = new WebSocketMsgOutVo();
        webSocketMsgOutVo.setType(IndexEnum.ONE.getValue() + "");
        webSocketMsgOutVo.setRepairPhone(form);
        webSocketMsgOutVo.setPkCustometLogId(redisTemplate.opsForValue().get(form) + "");

        // 等候列表
        List<WsCustometLogOutVo> wsCustometLogOutVos = wsCustometLogService.selectAllList(
                new WsCustometLog(IndexEnum.MINUS_ONE.getValue(), fkUserId));
        webSocketMsgOutVo.setWsCustometLogOutVoList(wsCustometLogOutVos);
        // 主叫方个人信息
        EmployeeResp userNameByPhone = FeignInfoUitls.getUserNameByPhone(form);
        if (null == userNameByPhone) {
            OrganizationListReq req = new OrganizationListReq();
            req.setTel(form);
            req.setIsEnable("1");
            try {
                log.info("------------------------------调用基础服务查询机构信息开始");
                PlatformResult<List<OrganizationListSimpleRes>> simpleList = hrmsOrganizationFeignService.getSimpleList(req);
                if (simpleList.isSuccess() && !CollectionUtils.isEmpty(simpleList.getObject())) {
                    webSocketMsgOutVo.setRepairManDeptName(simpleList.getObject().stream().map(OrganizationListSimpleRes::getName).collect(Collectors.joining()));
                    webSocketMsgOutVo.setRepairManDeptId(simpleList.getObject().stream().map(OrganizationListSimpleRes::getOrganizationId).collect(Collectors.joining()));
                } else {
                    log.error("------------------------------调用基础服务查询机构信息失败：" + simpleList.getMessage());
                    webSocketMsgOutVo.setRepairManName("未知来电");
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("------------------------------调用基础服务查询机构信息失败：" + e.getMessage());
                webSocketMsgOutVo.setRepairManName("未知来电");
            }

        } else {
            webSocketMsgOutVo.setRepairManDeptId(userNameByPhone.getOrgId());
            webSocketMsgOutVo.setRepairManDeptName(userNameByPhone.getOrgName());
            webSocketMsgOutVo.setRepairManId(userNameByPhone.getEmployeeId());
            webSocketMsgOutVo.setRepairManName(userNameByPhone.getEmployeeName());
            webSocketMsgOutVo.setRepairAvatar(userNameByPhone.getAvatar());
            webSocketMsgOutVo.setCount(wsCustometLogService.numberOfDailyRepairReports(form));
        }

        // 推送信息至客户端
        log.info("--------------------------------------------来电弹屏");

        MessageWebSocket.sendtoUser(JSON.toJSONString(webSocketMsgOutVo), fkUserId);
        pushMessage(JSON.toJSONString(webSocketMsgOutVo), fkUserId);
    }

    /**
     * 关闭来电弹屏
     *
     * @param phone 被叫方
     */
    @Transactional
    public void closeScreenPopUp(String phone, String from, List<String> fkUserIds) {
        if (CollectionUtils.isEmpty(fkUserIds)) {
            // 查询被叫人
            WsCustometService wsCustometService = wsCustometServiceMapper.selectOneCustometServiceByPhone(phone);
            WebSocketMsgOutVo webSocketMsgOutVo = new WebSocketMsgOutVo();
            webSocketMsgOutVo.setType(IndexEnum.THREE.getValue() + "");
            WsCustometLog wsCustometLog = null;
            if (!StringUtil.isEmpty(from)) {
                wsCustometLog = wsCustometLogService.selectOneById(redisTemplate.opsForValue().get(from) + "");
            }

            if (null != wsCustometLog) {
                // 挂断后处理等待队列中通话记录
                if (IndexEnum.MINUS_ONE.getValue() == wsCustometLog.getCallType()) {
                    wsCustometLog.setCallType(IndexEnum.ZERO.getValue());
                    wsCustometLog.setCallWorkStatus(IndexEnum.ZERO.getValue());
                    wsCustometLog.setUpdateTime(new Date());
                    wsCustometLog.setUpdateBy(UserInfoHolder.getCurrentUserId());
                    wsCustometLogService.updateWsCustometLog(wsCustometLog);

                    // 组装等候列表消息
                    webSocketMsgOutVo.setType(IndexEnum.ZERO.getValue() + "");
                    // 个人坐席通话记录
                    List<WsCustometLogOutVo> wsCustometLogOutVos = wsCustometLogService.selectAllList(
                            new WsCustometLog(IndexEnum.MINUS_ONE.getValue(), wsCustometService.getFkUserId()));
                    webSocketMsgOutVo.setWsCustometLogOutVoList(wsCustometLogOutVos);
                }
            }
            // 推送信息至前端
            MessageWebSocket.sendtoUser(JSON.toJSONString(webSocketMsgOutVo), wsCustometService.getFkUserId());
            pushMessage(JSON.toJSONString(webSocketMsgOutVo), wsCustometService.getFkUserId());
        } else {
            fkUserIds.forEach(fkUserIdTemp -> {
                WebSocketMsgOutVo webSocketMsgOutVo = new WebSocketMsgOutVo();
                webSocketMsgOutVo.setType(IndexEnum.THREE.getValue() + "");
                // 推送信息至前端
                MessageWebSocket.sendtoUser(JSON.toJSONString(webSocketMsgOutVo), fkUserIdTemp);
                pushMessage(JSON.toJSONString(webSocketMsgOutVo), fkUserIdTemp);
            });
        }
    }

    public void pushMessage(String content, String fkUserId) {
    	log.error("------------------------来电弹屏消息推送内容：" + content + "--------fkUserId" + fkUserId);
        MessageWebPushReq message = new MessageWebPushReq();
        MessageWebPushReq.WebMessageTemplate messageTemplate = new MessageWebPushReq.WebMessageTemplate();
        message.setReceiver(fkUserId);
        messageTemplate.setContent(content);
        messageTemplate.setTitle("工单来电");
        message.setWebMessageTemplate(messageTemplate);
        PlatformResult platformResult = messageFeignService.webPush(message);
        if (!platformResult.isSuccess()) {
            log.error("------------------------来电弹屏消息推送失败：" + platformResult.getMessage());
        }
    }

    /**
     * 打开创建工单页面
     *
     * @param phone           被叫方
     * @param pkCustometLogId 坐席通话记录id
     */
    public void openCreateWorkSheet(String pkCustometLogId, String phone,String call,String callName,String callDept) {
        // 获取被叫方信息
        WsCustometService wsCustometService = wsCustometServiceMapper.selectOneCustometServiceByPhone(phone);
        WebSocketMsgOutVo webSocketMsgOutVo = new WebSocketMsgOutVo();
        webSocketMsgOutVo.setType(IndexEnum.TWO.getValue() + "");
        webSocketMsgOutVo.setPkCustometLogId(pkCustometLogId);
        webSocketMsgOutVo.setRepairPhone(call);
        webSocketMsgOutVo.setRepairManName(callName);
        webSocketMsgOutVo.setRepairManDeptName(callDept);
        
        
        // 个人坐席通话记录
        List<WsCustometLogOutVo> wsCustometLogOutVos = wsCustometLogService.selectAllList(
                new WsCustometLog(
                        pkCustometLogId + "",
                        IndexEnum.ONE.getValue(),
                        IndexEnum.ONE.getValue(),
                        null == wsCustometService ? null : wsCustometService.getFkUserId()

                )
        );
        webSocketMsgOutVo.setWsCustometLogOutVoList(wsCustometLogOutVos);

        // 修改通话记录
        WsCustometLog wsCustometLog = wsCustometLogService.selectOneById(pkCustometLogId);
        if (null != wsCustometLog) {
            wsCustometLog.setCallType(IndexEnum.ONE.getValue());
            wsCustometLog.setCallWorkStatus(IndexEnum.ONE.getValue());
            wsCustometLog.setIsRead(IndexEnum.ONE.getValue());
            wsCustometLog.setUpdateTime(new Date());
            wsCustometLog.setUpdateBy(UserInfoHolder.getCurrentUserId());
            wsCustometLogService.updateWsCustometLog(wsCustometLog);
        }

        // 推送信息至客户端
        log.info("--------------------------------------------打开弹屏");
        // 存在用户id时推送
        if (!StringUtil.isEmpty(wsCustometService.getFkUserId())) {
            // 推送信息至客户端
            //MessageWebSocket.sendtoUser(JSON.toJSONString(webSocketMsgOutVo), wsCustometService.getFkUserId());
            pushMessage(JSON.toJSONString(webSocketMsgOutVo), wsCustometService.getFkUserId());
        }

    }

    /**
     * 挂断电话后，获取OM远程服务器录音文件，上传服务器，并插入工单文件表记录
     */
    @Transactional
    public void hangUpAndUploadRecording(String form, String recordingText) {
        // 过滤多个
        Optional.ofNullable(recordingText.split(CuttingOperatorEnum.SLASH.getValue())[IndexEnum.ONE.getValue()])
                .map(temp -> {
                    // 录音文件名称
                    String[] split = temp.split(CuttingOperatorEnum.UNDERLINE.getValue());
                    for (String splitTemp : split) {
                        if (null != OmLineEnum.getByValue(splitTemp)) {
                            return null;
                        }
                    }
                    WsOmFile wsOmFile = new WsOmFile();
                    wsOmFile.setPkOmfileId(IdUtils.getId());
                    wsOmFile.setFkCustometLogId(redisTemplate.opsForValue().get(form) + "");
                    wsOmFile.setUrl(CommonlyConstants.Om.HardwareInfo.ACCESS_PROTOCOL
                            + ConstantYml.getInstance().getOmIpHost()
                            + omFileUrl
                            + recordingText);
                    wsOmFileService.insertOmFile(wsOmFile);
//                    redisTemplate.delete(form);
                    return null;
                });
    }


}
