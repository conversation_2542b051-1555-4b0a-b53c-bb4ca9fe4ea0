package cn.trasen.worksheet.module.service.impl;

import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.enums.CuttingOperatorEnum;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.util.DateUtils;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.common.util.MyBeanUtils;
import cn.trasen.worksheet.common.util.TreeUtils;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeBaseInputVo;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeBaseMobileInputVo;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeBaseOperationInputVo;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeBaseQueryInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetStatisticalInputVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeBaseInfoOutVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeBaseMobileInfoOutVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeBasePageOutVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeTypeTreeOutVo;
import cn.trasen.worksheet.module.entity.WsKnowledgeBase;
import cn.trasen.worksheet.module.entity.WsKnowledgeLike;
import cn.trasen.worksheet.module.mapper.WsKnowledgeBaseMapper;
import cn.trasen.worksheet.module.service.KnowledgeBaseService;
import cn.trasen.worksheet.module.service.KnowledgeTypeService;
import cn.trasen.worksheet.module.service.WsKnowledgeLikeService;
import cn.trasen.worksheet.module.service.WsSheetService;

/**
 * 知识库管理
 *
 * <AUTHOR>
 * @date: 2021/8/2 14:27
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */

@Service
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

    @Autowired
    private WsKnowledgeBaseMapper wsKnowledgeBaseMapper;
    @Autowired
    private KnowledgeTypeService knowledgeTypeService;
    @Autowired
    private WsKnowledgeLikeService wsKnowledgeLikeService;
    @Autowired
    private WsSheetService wsSheetService;


    @Transactional
    @Override
    public int saveOrUpdateKnowledgeBase(KnowledgeBaseInputVo knowledgeBaseInputVo) {
        if (IndexEnum.ZERO.getValue() == knowledgeBaseInputVo.getKnowledgeStatus() ||
                IndexEnum.ONE.getValue() == knowledgeBaseInputVo.getKnowledgeStatus()) {
            // 限制分类下相同知识点保存
            WsKnowledgeBase knowledgeBasePOJO = wsKnowledgeBaseMapper.selectOneKnowledgeBase
                    (new WsKnowledgeBase(knowledgeBaseInputVo.getFkKnowledgeTypeId(), knowledgeBaseInputVo.getKnowledgeTitle()));
            if (null != knowledgeBasePOJO && !knowledgeBasePOJO.getPkKnowledgeBaseId().equals(knowledgeBaseInputVo.getPkKnowledgeBaseId() + "")) {
                throw new BusinessException("知识点已存在，贡献人为" + knowledgeBasePOJO.getFkUserName() + "，请先确认是否重复提交");
            }
        }
        if (StringUtils.isEmpty(knowledgeBaseInputVo.getPkKnowledgeBaseId())) {
            saveKnowledgeBase(knowledgeBaseInputVo);
        } else {
            updateKnowledgeBase(knowledgeBaseInputVo);
        }
        return IndexEnum.ONE.getValue();
    }

    /**
     * 知识库分页列表
     *
     * @param page
     * @param knowledgeBaseQueryInputVo
     * @return
     */
    @Override
    public List<KnowledgeBasePageOutVo> selectPageList(Page page, KnowledgeBaseQueryInputVo knowledgeBaseQueryInputVo) {
        ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
        knowledgeBaseQueryInputVo.setFkUserId(UserInfoHolder.getCurrentUserId());
        knowledgeBaseQueryInputVo.setUserId(UserInfoHolder.getCurrentUserId());
        // 知识库页签展示所有
        if (IndexEnum.FOUR.getValue() == knowledgeBaseQueryInputVo.getKnowledgeStatus()) {
            knowledgeBaseQueryInputVo.setFkUserId(null);
        }
        // 用户为管理员，查看所有
        if (!StringUtils.isEmpty(currentUserInfo.getSysRoleCode()) &&
                currentUserInfo.getSysRoleCode().contains(CommonlyConstants.Work_Sheet_Role.KNOWLEDGE_BASE_ROLE)
                && IndexEnum.ZERO.getValue() != knowledgeBaseQueryInputVo.getKnowledgeStatus()) {
            knowledgeBaseQueryInputVo.setFkUserId(null);
        }
        if(!StringUtils.isEmpty(knowledgeBaseQueryInputVo.getFkKnowledgeTypeId())){
            knowledgeBaseQueryInputVo.setList(Arrays.asList(knowledgeBaseQueryInputVo.getFkKnowledgeTypeId().split(CuttingOperatorEnum.COMMA.getValue())));
        }
        List<KnowledgeBasePageOutVo> knowledgeBasePageOutVoList = wsKnowledgeBaseMapper.selectPageList(page, knowledgeBaseQueryInputVo);
        // 人员类型 0为普通用户 1为管理员
        int peopleType = 0;
        if (!StringUtils.isEmpty(currentUserInfo.getSysRoleCode()) &&
                currentUserInfo.getSysRoleCode().contains(CommonlyConstants.Work_Sheet_Role.KNOWLEDGE_BASE_ROLE)) {
            peopleType = IndexEnum.ONE.getValue();
        }
        int finalTempPeopleType = peopleType;
        knowledgeBasePageOutVoList.forEach(temp -> {
            // 验证当前人是否为贡献人
            if (UserInfoHolder.getCurrentUserId().equals(temp.getFkUserId())) {
                temp.setThePeople(true);
            } else {
                temp.setThePeople(false);
            }
            temp.setPeopleType(finalTempPeopleType);
        });
        return knowledgeBasePageOutVoList;
    }

    /**
     * 创建工单右侧知识库
     *
     * @param page
     * @param knowledgeBaseQueryInputVo
     * @return
     */
    @Override
    public List<KnowledgeBasePageOutVo> selectPageCreateWorkList(Page page, KnowledgeBaseQueryInputVo knowledgeBaseQueryInputVo) {
        knowledgeBaseQueryInputVo.setUserId(UserInfoHolder.getCurrentUserId());
        return wsKnowledgeBaseMapper.selectPageList(page, knowledgeBaseQueryInputVo);
    }

    /**
     * 修改知识点
     *
     * @param knowledgeBaseInputVo
     * @return
     */
    @Transactional
    @Override
    public int updateKnowledgeBase(KnowledgeBaseInputVo knowledgeBaseInputVo) {
        WsKnowledgeBase knowledgeBase = selectOneById(knowledgeBaseInputVo.getPkKnowledgeBaseId());
        MyBeanUtils.copyBeanNotNull2Bean(knowledgeBaseInputVo, knowledgeBase);
        // 提交时填充贡献时间
        if (IndexEnum.SIX.getValue() == knowledgeBaseInputVo.getKnowledgeStatus()) {
            knowledgeBase.setContributionTime(new Date());
        }
        ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
        // 直接提交
        if (IndexEnum.ONE.getValue() == knowledgeBaseInputVo.getKnowledgeStatus()) {
            // 用户为管理员，状态为审核通过
            if (!StringUtils.isEmpty(currentUserInfo.getSysRoleCode()) &&
                    currentUserInfo.getSysRoleCode().contains(CommonlyConstants.Work_Sheet_Role.KNOWLEDGE_BASE_ROLE)) {
                // 审核状态
                knowledgeBase.setKnowledgeStatus(IndexEnum.TWO.getValue());
                knowledgeBase.setReviewTime(new Date());
            }
        }
        // 初始化审核时间
        if (IndexEnum.MINUS_ONE.getValue() == knowledgeBaseInputVo.getKnowledgeStatus() ||
                IndexEnum.TWO.getValue() == knowledgeBaseInputVo.getKnowledgeStatus()) {
            knowledgeBase.setReviewTime(new Date());
        }
        if (IndexEnum.ZERO.getValue() == wsKnowledgeBaseMapper.updateknowledgeBase(knowledgeBase)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }

    @Override
    public WsKnowledgeBase selectOneById(String pkKnowledgeBaseId) {
        return Optional.ofNullable(wsKnowledgeBaseMapper.selectOneById(pkKnowledgeBaseId, UserInfoHolder.getCurrentUserId()))
                .map(WsKnowledgeBase::get)
                .orElseThrow(() -> new BusinessException("未查询到知识点信息"));
    }

    /**
     * 知识库详情
     *
     * @param pkKnowledgeBaseId
     * @return
     */
    @Override
    public KnowledgeBaseInfoOutVo selectOneInfo(String pkKnowledgeBaseId) {
        WsKnowledgeBase wsKnowledgeBase = selectOneById(pkKnowledgeBaseId);
        KnowledgeBaseInfoOutVo knowledgeBaseInfoOutVo = new KnowledgeBaseInfoOutVo();
        MyBeanUtils.copyBeanNotNull2Bean(wsKnowledgeBase, knowledgeBaseInfoOutVo);
        knowledgeBaseInfoOutVo.setFkKnowledgeTypeName(knowledgeTypeService.selectOne(knowledgeBaseInfoOutVo.getFkKnowledgeTypeId()).getFullPath());
        ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
        // 人员类型 0为普通用户 1为管理员
        int peopleType = 0;
        if (!StringUtils.isEmpty(currentUserInfo.getSysRoleCode()) &&
                currentUserInfo.getSysRoleCode().contains(CommonlyConstants.Work_Sheet_Role.KNOWLEDGE_BASE_ROLE)) {
            peopleType = IndexEnum.ONE.getValue();
        }
        knowledgeBaseInfoOutVo.setPeopleType(peopleType);
        return knowledgeBaseInfoOutVo;
    }

    /**
     * 知识点点赞
     *
     * @param pkKnowledgeBaseId
     * @return
     */
    @Override
    public int giveALike(String pkKnowledgeBaseId) {
        return wsKnowledgeLikeService.insertKnowledgeLike(new WsKnowledgeLike(IdUtils.getId(), pkKnowledgeBaseId, UserInfoHolder.getCurrentUserId()));
    }

    /**
     * 取消赞
     *
     * @param pkKnowledgeBaseId
     * @return
     */
    @Override
    public int cancelThePraise(String pkKnowledgeBaseId) {
        WsKnowledgeLike wsKnowledgeLike = wsKnowledgeLikeService.selectOneByIdAndFkUserId(UserInfoHolder.getCurrentUserId(), pkKnowledgeBaseId);
        wsKnowledgeLike.setDeleteStatus(IndexEnum.ONE.getValue());
        return wsKnowledgeLikeService.updateKnowledgeLike(wsKnowledgeLike);
    }

    /**
     * 移出
     *
     * @param knowledgeBaseOperationInputVo
     * @return
     */
    @Override
    public int remove(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase) {
        wsKnowledgeBase.setRemoveReason(knowledgeBaseOperationInputVo.getRemark());
        wsKnowledgeBase.setRemoveTime(new Date());
        wsKnowledgeBase.setKnowledgeStatus(IndexEnum.THREE.getValue());
        return operationKnowledgeBase(knowledgeBaseOperationInputVo, wsKnowledgeBase);
    }

    /**
     * 审批不通过
     *
     * @param knowledgeBaseOperationInputVo
     * @return
     */
    @Override
    public int notApproved(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase) {
        Optional.ofNullable(knowledgeBaseOperationInputVo.getRemark())
                .orElseThrow(() -> new BusinessException("备注不能为空"));
        wsKnowledgeBase.setBackReason(knowledgeBaseOperationInputVo.getRemark());
        wsKnowledgeBase.setReviewTime(new Date());
        wsKnowledgeBase.setKnowledgeStatus(IndexEnum.MINUS_ONE.getValue());
        return operationKnowledgeBase(knowledgeBaseOperationInputVo, wsKnowledgeBase);
    }

    /**
     * 审批通过
     *
     * @param knowledgeBaseOperationInputVo
     * @return
     */
    @Override
    public int approve(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase) {
        wsKnowledgeBase.setReviewTime(new Date());
        wsKnowledgeBase.setKnowledgeStatus(IndexEnum.TWO.getValue());
        return operationKnowledgeBase(knowledgeBaseOperationInputVo, wsKnowledgeBase);
    }

    /**
     * 撤回
     *
     * @param knowledgeBaseOperationInputVo
     * @param wsKnowledgeBase
     * @return
     */
    @Override
    public int withdraw(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase) {
        Optional.ofNullable(knowledgeBaseOperationInputVo.getRemark())
                .orElseThrow(() -> new BusinessException("撤回原因不能为空"));
        wsKnowledgeBase.setKnowledgeStatus(IndexEnum.ZERO.getValue());
        wsKnowledgeBase.setRemark(knowledgeBaseOperationInputVo.getRemark());
        return operationKnowledgeBase(knowledgeBaseOperationInputVo, wsKnowledgeBase);
    }

    /**
     * 移入
     *
     * @param knowledgeBaseOperationInputVo
     * @param wsKnowledgeBase
     * @return
     */
    @Override
    public int move(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase) {
        wsKnowledgeBase.setKnowledgeStatus(IndexEnum.TWO.getValue());
        return operationKnowledgeBase(knowledgeBaseOperationInputVo, wsKnowledgeBase);
    }

    /**
     * 提交
     *
     * @param knowledgeBaseOperationInputVo
     * @param wsKnowledgeBase
     * @return
     */
    @Override
    public int submit(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase) {
        ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
        // 用户为管理员，状态为审核通过
        if (!StringUtils.isEmpty(currentUserInfo.getSysRoleCode()) &&
                currentUserInfo.getSysRoleCode().contains(CommonlyConstants.Work_Sheet_Role.KNOWLEDGE_BASE_ROLE)) {
            // 审核状态
            wsKnowledgeBase.setKnowledgeStatus(IndexEnum.TWO.getValue());
            wsKnowledgeBase.setReviewTime(new Date());
        } else {
            wsKnowledgeBase.setKnowledgeStatus(IndexEnum.ONE.getValue());
            // 填充贡献人信息
            wsKnowledgeBase.setFkUserId(UserInfoHolder.getCurrentUserId());
            wsKnowledgeBase.setFkUserName(UserInfoHolder.getCurrentUserName());
            wsKnowledgeBase.setFkUserDeptId(currentUserInfo.getDeptId());
            wsKnowledgeBase.setFkUserDeptName(currentUserInfo.getDeptname());
            wsKnowledgeBase.setContributionTime(new Date());
        }
        return operationKnowledgeBase(knowledgeBaseOperationInputVo, wsKnowledgeBase);
    }

    /**
     * 删除
     *
     * @param knowledgeBaseOperationInputVo
     * @param wsKnowledgeBase
     * @return
     */
    @Override
    public int delete(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase) {
        wsKnowledgeBase.setDeleteStatus(IndexEnum.ONE.getValue());
        return operationKnowledgeBase(knowledgeBaseOperationInputVo, wsKnowledgeBase);
    }

    /**
     * 知识库操作
     *
     * @param knowledgeBaseOperationInputVo pkKnowledgeBaseId  知识库id
     * @param knowledgeBaseOperationInputVo knowledgeStatus    1移出、2审批不通过、3审批核通过、4撤回、5、移入、6提交、7删除
     * @param knowledgeBaseOperationInputVo remark             备注（移除原因、审批不通过原因）
     * @return
     */
    @Override
    public int operationKnowledgeBase(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase) {

        WsKnowledgeBase knowledgeBase = selectOneById(knowledgeBaseOperationInputVo.getPkKnowledgeBaseId());
        MyBeanUtils.copyBeanNotNull2Bean(wsKnowledgeBase, knowledgeBase);
        if (IndexEnum.ZERO.getValue() == wsKnowledgeBaseMapper.updateknowledgeBase(knowledgeBase)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }

    /**
     * 申请人首页-我的知识点
     *
     * @param fkUserId
     * @return
     */
    @Override
    public Map<String, Object> getMyKnowledgeBaseCount(String fkUserId) {
        return wsKnowledgeBaseMapper.getMyKnowledgeBaseCount(fkUserId);
    }

    /**
     * 知识点提交趋势
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    @Override
    public Map<String, Object> getKnowledgeBaseCountByDate(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        wsSheetService.intiTime(wsWorkSheetStatisticalInputVo);
        List<Map<String, Object>> knowledgeBaseCountByDate = wsKnowledgeBaseMapper.getKnowledgeBaseCountByDate(wsWorkSheetStatisticalInputVo);
        // 无数据补零
        DateUtils.timeIntervalZeroize(
                wsWorkSheetStatisticalInputVo.getDayOrMonthType(),
                wsWorkSheetStatisticalInputVo.getBeginTime(),
                wsWorkSheetStatisticalInputVo.getEndTime(),
                knowledgeBaseCountByDate,
                "counts"
        );
        Map<String, Object> map = Maps.newHashMap();
        map.put("dayOrMonthType", wsWorkSheetStatisticalInputVo.getDayOrMonthType());
        map.put("list",
                // 时间升序
                knowledgeBaseCountByDate.stream()
                        .sorted(Comparator.comparing(o -> DateUtils.stringtoDate(o.get("date") + "", wsWorkSheetStatisticalInputVo.getDayOrMonthType())))
                        .collect(Collectors.toList()));

        return map;
    }

    /**
     * 一级知识点占比
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    @Override
    public List<Map<String, Object>> getLevelOneKnowledgeBaseTypeDatas(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        wsSheetService.intiTime(wsWorkSheetStatisticalInputVo);
        List<Map<String, Object>> result = Lists.newArrayList();

        // 一级知识点类型
        List<Map<String, Object>> levelOneKnowledgeBaseTypeDatas = wsKnowledgeBaseMapper.getLevelOneKnowledgeBaseTypeDatas(wsWorkSheetStatisticalInputVo);

        // 所有知识点
        List<KnowledgeTypeTreeOutVo> knowledgeTypeTreeOutVos = wsKnowledgeBaseMapper.selectKnowledgeAllList(
                IndexEnum.ONE.getValue(),
                null,
                wsWorkSheetStatisticalInputVo.getBeginTime(),
                wsWorkSheetStatisticalInputVo.getEndTime()
        );
        // 时间区间内所有知识点
        List<KnowledgeTypeTreeOutVo> dateknowledgeTypeTreeOutVos = wsKnowledgeBaseMapper.selectKnowledgeAllList(
                IndexEnum.ONE.getValue(),
                null,
                wsWorkSheetStatisticalInputVo.getBeginTime(),
                wsWorkSheetStatisticalInputVo.getEndTime()
        );
        levelOneKnowledgeBaseTypeDatas
                .forEach(levelOneKnowledgeBaseType -> {
                    List<KnowledgeTypeTreeOutVo> subNodes = Lists.newArrayList();
                    // 查询改节点下所有节点id
                    TreeUtils.queryQllSubNodes(
                            knowledgeTypeTreeOutVos,
                            levelOneKnowledgeBaseType.get("pkKnowledgeTypeId") + "",
                            subNodes,
                            "getPid",
                            "getId"
                    );
                    // 组装返回数据
                    Map<String, Object> resultTemp = Maps.newHashMap();
                    resultTemp.put("categoryName", levelOneKnowledgeBaseType.get("categoryName") + "");
                    resultTemp.put(
                            "counts",
                            subNodes.stream()
                                    .filter(subNode -> dateknowledgeTypeTreeOutVos.contains(subNode))
                                    .collect(Collectors.toList())
                                    .size()
                    );
                    result.add(resultTemp);
                });
        return result;
    }

    /**
     * 知识点提交top榜单
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    @Override
    public List<Map<String, Object>> getKnowledgeBaseSubmitTopDatas(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        wsSheetService.intiTime(wsWorkSheetStatisticalInputVo);
        return wsKnowledgeBaseMapper.getKnowledgeBaseSubmitTopDatas(page, wsWorkSheetStatisticalInputVo);
    }

    @Override
    public List<Map<String, Object>> getKnowledgeLikeCountTop(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        wsSheetService.intiTime(wsWorkSheetStatisticalInputVo);
        return wsSheetService.getKnowledgeLikeCountTopDatas(page, wsWorkSheetStatisticalInputVo.getBeginTime(), wsWorkSheetStatisticalInputVo.getEndTime(), null);
    }


    /**
     * 保存知识点
     *
     * @param knowledgeBaseInputVo
     * @return
     */
    @Transactional
    @Override
    public int saveKnowledgeBase(KnowledgeBaseInputVo knowledgeBaseInputVo) {
        WsKnowledgeBase knowledgeBase = new WsKnowledgeBase();
        MyBeanUtils.copyBeanNotNull2Bean(knowledgeBaseInputVo, knowledgeBase);
        knowledgeBase.setPkKnowledgeBaseId(IdUtils.getId());
        knowledgeBase.setContributionTime(new Date());
        // 填充贡献人信息
        knowledgeBase.setFkUserId(UserInfoHolder.getCurrentUserId());
        knowledgeBase.setFkUserName(UserInfoHolder.getCurrentUserName());
        ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
        knowledgeBase.setFkUserDeptId(currentUserInfo.getDeptId());
        knowledgeBase.setFkUserDeptName(currentUserInfo.getDeptname());
        // 直接提交
        if (IndexEnum.ONE.getValue() == knowledgeBaseInputVo.getKnowledgeStatus()) {
            // 用户为管理员，状态为审核通过
            if (!StringUtils.isEmpty(currentUserInfo.getSysRoleCode()) &&
                    currentUserInfo.getSysRoleCode().contains(CommonlyConstants.Work_Sheet_Role.KNOWLEDGE_BASE_ROLE)) {
                // 审核状态
                knowledgeBase.setKnowledgeStatus(IndexEnum.TWO.getValue());
                knowledgeBase.setReviewTime(new Date());
            }
        }
        // 初始化审核时间
        if (IndexEnum.MINUS_ONE.getValue() == knowledgeBaseInputVo.getKnowledgeStatus() ||
                IndexEnum.TWO.getValue() == knowledgeBaseInputVo.getKnowledgeStatus()) {
            knowledgeBase.setReviewTime(new Date());
        }
        // 有用次数
        knowledgeBase.setUsefulNumbers(IndexEnum.ZERO.getValue());
        if (IndexEnum.ZERO.getValue() == wsKnowledgeBaseMapper.insertknowledgeBase(knowledgeBase)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }

    /**
     * 知识类型所有数据 树结构展示
     *
     * @param categoryName 分类名称
     * @return
     */
    @Override
    public List<KnowledgeTypeTreeOutVo> selectKnowledgeTreeAllList(String categoryName) {
        return TreeUtils.listToTree(
                wsKnowledgeBaseMapper.selectKnowledgeAllList(IndexEnum.ONE.getValue(), categoryName, null, null),
                "Pid",
                "Id",
                "Count",
                "Children"
        );
    }

    /**
     * 查询知识库菜单，各页签数据
     *
     * @param knowledgeBaseQueryInputVo
     * @return
     */
    @Override
    public Map<String, Object> selectCountsGroupStatus(KnowledgeBaseQueryInputVo knowledgeBaseQueryInputVo) {
        ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
        knowledgeBaseQueryInputVo.setFkUserId(UserInfoHolder.getCurrentUserId());
        if (!StringUtils.isEmpty(knowledgeBaseQueryInputVo.getFkKnowledgeTypeId())) {
            knowledgeBaseQueryInputVo.setList(Arrays.asList(knowledgeBaseQueryInputVo.getFkKnowledgeTypeId().split(CuttingOperatorEnum.COMMA.getValue())));
        }
        // 用户为管理员，查看所有
        if (!StringUtils.isEmpty(currentUserInfo.getSysRoleCode()) &&
                currentUserInfo.getSysRoleCode().contains(CommonlyConstants.Work_Sheet_Role.KNOWLEDGE_BASE_ROLE)) {
            knowledgeBaseQueryInputVo.setAdmin("admin");
        }
        return wsKnowledgeBaseMapper.selectCountsGroupStatus(knowledgeBaseQueryInputVo);
    }

    /**
     * 知识点点赞排行（点赞数排序，相同时取贡献时间晚的）
     *
     * @param knowledgeBaseMobileInputVo
     * @return
     */
    @Override
    public List<KnowledgeBaseMobileInfoOutVo> knowledgeLikeRank(KnowledgeBaseMobileInputVo knowledgeBaseMobileInputVo) {
        // 科室id为空，则查询近30天
        if (StringUtils.isEmpty(knowledgeBaseMobileInputVo.getFkUserDeptId())) {
            // 初始化时间
            knowledgeBaseMobileInputVo.setBeginTime(
                    DateUtils.dateToStringFormat(
                            "yyyy-MM-dd HH:mm:ss",
                            DateUtils.dateMinusNDay(new Date(), IndexEnum.THIRTY.getValue())
                    )
            );
            knowledgeBaseMobileInputVo.setEndTime(DateUtils.getCurrentTime());
        }
        return wsKnowledgeBaseMapper.knowledgeLikeRank(knowledgeBaseMobileInputVo);
    }
}
