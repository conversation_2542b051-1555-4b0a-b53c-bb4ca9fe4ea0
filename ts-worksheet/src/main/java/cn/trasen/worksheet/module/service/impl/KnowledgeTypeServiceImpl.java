package cn.trasen.worksheet.module.service.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;

import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.enums.CuttingOperatorEnum;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.common.util.MyBeanUtils;
import cn.trasen.worksheet.common.util.TreeUtils;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeTypeInputVo;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeTypeStatusInputVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeTypeListOutVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeTypeTreeOutVo;
import cn.trasen.worksheet.module.entity.WsKnowledgeType;
import cn.trasen.worksheet.module.mapper.WsKnowledgeTypeMapper;
import cn.trasen.worksheet.module.service.KnowledgeTypeService;

/**
 * <AUTHOR>
 * @date: 2021/7/3 11:40
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Service
public class KnowledgeTypeServiceImpl implements KnowledgeTypeService {

    @Autowired
    private WsKnowledgeTypeMapper wsKnowledgeTypeMapper;

    /**
     * 保存、修改
     *
     * @param KnowledgeTypeInputVo
     * @return
     */
    @Transactional
    @Override
    public String saveKnowledgeType(KnowledgeTypeInputVo KnowledgeTypeInputVo) {
        if (!StringUtil.isEmpty(KnowledgeTypeInputVo.getParentId()) && StringUtil.isEmpty(KnowledgeTypeInputVo.getFullPath())) {
            throw new BusinessException("全路径数据不能为空");
        }
        WsKnowledgeType wsKnowledgeType = new WsKnowledgeType();
        int change;
        if (StringUtil.isEmpty(KnowledgeTypeInputVo.getPkKnowledgeTypeId())) {
            wsKnowledgeType.setPkKnowledgeTypeId(IdUtils.getId());
            MyBeanUtils.copyBeanNotNull2Bean(KnowledgeTypeInputVo, wsKnowledgeType);
            change = wsKnowledgeTypeMapper.insertKnowledgeType(wsKnowledgeType);
        } else {
            wsKnowledgeType = wsKnowledgeTypeMapper.selectOneById(KnowledgeTypeInputVo.getPkKnowledgeTypeId());
            MyBeanUtils.copyBeanNotNull2Bean(KnowledgeTypeInputVo, wsKnowledgeType);
            if (StringUtil.isEmpty(KnowledgeTypeInputVo.getParentId())) {
                wsKnowledgeType.setParentId(null);
            }
            change = wsKnowledgeTypeMapper.updateKnowledgeType(wsKnowledgeType);
        }
        Optional.ofNullable(change)
                .orElseThrow(() -> new BusinessException(CommonlyConstants.OperationReturnValue.FAIL));
        return CommonlyConstants.OperationReturnValue.SUCCESS;
    }

    /**
     * 知识类型启用、停用
     *
     * @param knowledgeTypeStatusInputVo
     * @return
     */
    @Override
    public int status(KnowledgeTypeStatusInputVo knowledgeTypeStatusInputVo) {
        WsKnowledgeType knowledgeType = selectOneById(knowledgeTypeStatusInputVo.getPkKnowledgeTypeId());
        // 所有知识类型信息
        List<KnowledgeTypeTreeOutVo> children = Lists.newArrayList();
        // 获取当前知识点类型及所有下级知识点类型id
        List<KnowledgeTypeTreeOutVo> knowledgeTypeTreeOutVos = wsKnowledgeTypeMapper.selectKnowledgeTypeAllListContainsDisable();
        TreeUtils.queryQllSubNodes(knowledgeTypeTreeOutVos, knowledgeType.getPkKnowledgeTypeId(), children, "getPid", "getId");
        List<String> ids = children.stream().map(KnowledgeTypeTreeOutVo::getId).collect(Collectors.toList());
        ids.add(knowledgeType.getPkKnowledgeTypeId());
        if (ids.size() != wsKnowledgeTypeMapper.updateBatchStatus(ids, knowledgeTypeStatusInputVo.getKnowledgeStatus())) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }


    /**
     * 知识类型启用
     *
     * @param pkKnowledgeTypeId 知识类型id
     * @return
     */
    @Override
    public int knowledgeTypeEnable(String pkKnowledgeTypeId) {
        WsKnowledgeType knowledgeType = selectOneById(pkKnowledgeTypeId);
        if (!StringUtil.isEmpty(knowledgeType.getParentId())) {
            WsKnowledgeType parntKnowledgeType = selectOneById(knowledgeType.getParentId());
            if (IndexEnum.ZERO.getValue() == parntKnowledgeType.getKnowledgeStatus()) {
                throw new BusinessException("该知识类型上级分类未启用，请先启用上级分类再操作");
            }
        }
        knowledgeType.setKnowledgeStatus(IndexEnum.ONE.getValue());
        if (IndexEnum.ZERO.getValue() == wsKnowledgeTypeMapper.updateKnowledgeType(knowledgeType)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }


    @Override
    public KnowledgeTypeListOutVo selectOne(String pkKnowledgeTypeId) {
        KnowledgeTypeListOutVo KnowledgeTypeListOutVo = new KnowledgeTypeListOutVo();
        WsKnowledgeType knowledgeType = selectOneById(pkKnowledgeTypeId);
        MyBeanUtils.copyBeanNotNull2Bean(knowledgeType, KnowledgeTypeListOutVo);
        return KnowledgeTypeListOutVo;
    }

    @Override
    public WsKnowledgeType selectOneById(String pkKnowledgeTypeId) {
        return Optional.ofNullable(wsKnowledgeTypeMapper.selectOneById(pkKnowledgeTypeId))
                .map(WsKnowledgeType::get)
                .orElseThrow(() -> new BusinessException("未查询到知识点类型信息"));
    }

    /**
     * 批量删除
     *
     * @param pkKnowledgeTypeIds 知识类型id，多个以英文逗号拼接
     * @return
     */
    @Transactional
    @Override
    public int deleteKnowledgeType(String pkKnowledgeTypeIds) {
        String[] pkKnowledgeTypeId = Optional.ofNullable(pkKnowledgeTypeIds)
                .map(map -> map.split(CuttingOperatorEnum.COMMA.getValue()))
                .orElseThrow(() -> new BusinessException("知识类型id不能为空"));
        // 批量删除所有id
        List<String> deleteIds = Lists.newArrayList();
        // 所有知识类型信息
        List<KnowledgeTypeTreeOutVo> knowledgeTypeTreeOutVos = selectKnowledgeTypeAllList(null);
        for (String temp : pkKnowledgeTypeId) {
            List<KnowledgeTypeTreeOutVo> children = Lists.newArrayList();
            // 获取当前知识点类型及所有下级知识点类型id
            TreeUtils.queryQllSubNodes(knowledgeTypeTreeOutVos, temp, children, "getPid", "getId");
            List<String> ids = children.stream().map(KnowledgeTypeTreeOutVo::getId).collect(Collectors.toList());
            // 填充当前知识点当前id及下级知识点类型id
            deleteIds.addAll(ids);
            deleteIds.add(temp);
        }
        // id去重
        List<String> collect = deleteIds.stream().distinct().collect(Collectors.toList());
        if (collect.size() != wsKnowledgeTypeMapper.deleteBatch(collect)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return collect.size();
    }

    /**
     * 分页列表
     *
     * @param page
     * @param categoryName       分类名称
     * @param pkKnowledgeTypeIds 知识类型id
     * @return
     */
    @Override
    public List<KnowledgeTypeListOutVo> selectKnowledgeTypePageList(Page page, String categoryName, String pkKnowledgeTypeIds) {
        List<String> list = null;
        if(!StringUtil.isEmpty(pkKnowledgeTypeIds)){
            list = Arrays.asList(pkKnowledgeTypeIds.split(CuttingOperatorEnum.COMMA.getValue()));
        }
        return wsKnowledgeTypeMapper.selectKnowledgeTypePageList(page, categoryName, list);
    }

    /**
     * 知识类型所有数据 树结构展示
     *
     * @param categoryName 分类名称
     * @return
     */
    @Override
    public List<KnowledgeTypeTreeOutVo> selectKnowledgeTypeTreeAllList(String categoryName) {
        return TreeUtils.listToTree(
                wsKnowledgeTypeMapper.selectKnowledgeTypeAllList(categoryName),
                "Pid",
                "Id",
                "Count",
                "Children"
        );
    }

    /**
     * 知识类型所有数据
     *
     * @param categoryName 分类名称
     * @return
     */
    @Override
    public List<KnowledgeTypeTreeOutVo> selectKnowledgeTypeAllList(String categoryName) {
        return wsKnowledgeTypeMapper.selectKnowledgeTypeAllList(categoryName);
    }

    /**
     * 一级知识点类型,包含所有子节点
     *
     * @return
     */
    @Override
    public List<KnowledgeTypeTreeOutVo> getLevelOneLKnowledgeTypeAllList() {
        // 获取所有知识点类型
        List<KnowledgeTypeTreeOutVo> knowledgeType = wsKnowledgeTypeMapper.selectKnowledgeTypeAllList(null);
        // 获取所有一级知识点类型
        List<KnowledgeTypeTreeOutVo> levelOneknowledgeType = knowledgeType
                .stream()
                .filter(filterTemp -> StringUtil.isEmpty(filterTemp.getPid()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(knowledgeType)) {
            return null;
        }
        // 填充一级知识点类型所有子节点id
        levelOneknowledgeType.forEach(levelOneknowledgeTypeTemp -> {
            List<KnowledgeTypeTreeOutVo> children = Lists.newArrayList();
            TreeUtils.queryQllSubNodes(knowledgeType, levelOneknowledgeTypeTemp.getId(), children, "getPid", "getId");
            Optional.ofNullable(children)
                    .map(temp -> {
                        levelOneknowledgeTypeTemp.setId(
                                temp.stream()
                                        .map(KnowledgeTypeTreeOutVo::getId)
                                        .collect(Collectors.joining(CuttingOperatorEnum.COMMA.getValue()))
                                        + CuttingOperatorEnum.COMMA.getValue()
                                        + levelOneknowledgeTypeTemp.getId()
                        );
                        return null;
                    });
        });
        return levelOneknowledgeType;
    }


}
