package cn.trasen.worksheet.module.service.impl;

import cn.trasen.homs.core.utils.DateUtil;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.module.mapper.SequenceMapper;
import cn.trasen.worksheet.module.service.SequenceService;
import lombok.extern.log4j.Log4j2;

import java.text.NumberFormat;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date: 2021/7/8 15:53
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Service
@Log4j2
public class SequenceServiceImpl implements SequenceService {

    @Autowired
    private SequenceMapper sequenceMapper;

    @Override
    public void createSequence() {
        sequenceMapper.createSequence();
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)  // 这个方法不会被事务管理
    public String getSequence() {
    	
      // 序列补零
      NumberFormat numberFormat = NumberFormat.getInstance();
      // 禁用数字格式化分组。 如：  000,001
      numberFormat.setGroupingUsed(false);
      // 保留最小位数
      numberFormat.setMinimumIntegerDigits(CommonlyConstants.SerialNumber.SERIAL_NUMBER);
      // 保留最大位数
      numberFormat.setMaximumIntegerDigits(CommonlyConstants.SerialNumber.SERIAL_NUMBER);
      
      String sequence = DateUtil.getDateTime("yyMMdd") + numberFormat.format(sequenceMapper.getSequence());
      
      Long isHave = sequenceMapper.selectSequence(sequence);
      
      if(isHave > 0) {
    	  sequence = DateUtil.getDateTime("yyMMdd") + numberFormat.format(sequenceMapper.getSequence());
      }
      
      return sequence;
    	
    }
    
    @Override
    public void deleteSequence() {
        sequenceMapper.deleteSequence();
    }
}
