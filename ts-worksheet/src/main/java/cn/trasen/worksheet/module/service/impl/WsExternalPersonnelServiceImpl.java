package cn.trasen.worksheet.module.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;

import cn.hutool.core.collection.CollectionUtil;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.base.HrmsEmployeeSaveReq;
import cn.trasen.homs.core.enums.EnableEnum;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.enums.CuttingOperatorEnum;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.enums.WorkSheetStatusEnum;
import cn.trasen.worksheet.common.util.FeignInfoUitls;
import cn.trasen.worksheet.common.util.MyBeanUtils;
import cn.trasen.worksheet.module.dto.inputVo.WsExternalPersonnelInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsExternalPersonnelStatusInputVo;
import cn.trasen.worksheet.module.dto.outVo.ExternalPersonnelPageListOutVo;
import cn.trasen.worksheet.module.dto.outVo.FaultTypeListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsSheetPeopleInfoOutVo;
import cn.trasen.worksheet.module.entity.WsExternalPersonnel;
import cn.trasen.worksheet.module.entity.WsFaultType;
import cn.trasen.worksheet.module.entity.WsWsSheet;
import cn.trasen.worksheet.module.mapper.WsExternalPersonnelMapper;
import cn.trasen.worksheet.module.service.WsExternalPersonnelService;
import cn.trasen.worksheet.module.service.WsSheetService;

/**
 * 外部人员管理
 */
@Service
public class WsExternalPersonnelServiceImpl implements WsExternalPersonnelService {

    @Autowired
    private WsExternalPersonnelMapper wsExternalPersonnelMapper;
    @Autowired
    private WsSheetService wsSheetService;
    @Autowired
    private HrmsEmployeeFeignService hrmsEmployeeFeignService;
    @Value("${externalPersonnel.defaultPassword}")
    private String defaultPassWord;
    @Value("${externalPersonnel.ORG_ID}")
    private String defaultOrgId;
    @Value("${externalPersonnel.ROLE_CODE}")
    private String defaultRoleCode;


    /**
     * 保存或修改人员信息
     *
     * @param externalPersonnelInputVo
     * @return
     */
    @Transactional
    @Override
    public int saveOrUpdate(WsExternalPersonnelInputVo externalPersonnelInputVo) {
        int index;
        if (StringUtil.isEmpty(externalPersonnelInputVo.getPkExternalPersonnelId())) {
            index = save(externalPersonnelInputVo);
        } else {
            index = update(externalPersonnelInputVo);
        }
        if (IndexEnum.ZERO.getValue() == index) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return index;
    }


//    // 初始化OA人员信息
//    List<EmployeeResp> userListByIds = FeignInfoUitls.getUserListByIds(Arrays.asList(fkUserIds));
//    // 外部人员信息
//    List<WsExternalPersonnel> wsExternalPersonnels = wsExternalPersonnelService.selectOneByIds(
//            userListByIds
//                    .stream()
//                    .map(EmployeeResp::getEmployeeId)
//                    .collect(Collectors.toList())
//    );
//        wsTaskLists.forEach(task ->{
//        userListByIds.forEach(user ->{
//            task.setFkUserName(user.getEmployeeName());
//            task.setFkUserDeptId(user.getOrgId());
//            task.setFkUserDeptName(user.getOrgName());
//        });
//    });
//        wsTaskLists.forEach(task ->{
//        wsExternalPersonnels.forEach(externalPersonnels ->{
//            task.setFkUserDeptId(externalPersonnels.getInstitutionalAffiliations());
//            task.setFkUserDeptName(externalPersonnels.getInstitutionalAffiliations());
//        });
//    });

    /**
     * 分页列表
     *
     * @param page
     * @param fuzzy 所属机构名称或姓名模糊搜索
     * @return
     */
    @Override
    public List<ExternalPersonnelPageListOutVo> selectPageList(Page page, String fuzzy,String deptId) {
        return wsExternalPersonnelMapper.selectPageList(page, fuzzy,deptId);
    }

    @Override
    public List<ExternalPersonnelPageListOutVo> selectAllList(String fuzzy, String deptId) {
        return wsExternalPersonnelMapper.selectAllList(fuzzy, deptId);
    }

    /**
     * 禁用前查询人员状态
     *
     * @param pkExternalPersonnelId 院外人员id
     */
    @Override
    public PlatformResult QueryingPersonnelStatus(String pkExternalPersonnelId) {
        WsExternalPersonnel wsExternalPersonnel = selectOneById(pkExternalPersonnelId);
        // 处理中工单
        List<WsWsSheet> processingWsSheetList = wsSheetService.selectListWsSheetByFkUserId(wsExternalPersonnel.getPkExternalPersonnelId())
                .stream()
                .filter(wsWsSheet ->
                        !WorkSheetStatusEnum.EVALUATE.getValue().equals(wsWsSheet.getWorkStatus()) &&
                                !WorkSheetStatusEnum.COMPLETED.getValue().equals(wsWsSheet.getWorkStatus()) &&
                                !WorkSheetStatusEnum.TERMINATED.getValue().equals(wsWsSheet.getWorkStatus()))
                .collect(Collectors.toList());

        if (!CollectionUtil.isEmpty(processingWsSheetList)) {
            throw new BusinessException("当前人员有正在处理中的工单，确认停用其账号吗？");
        }
        return PlatformResult.success("停用账号后，该人员无法登录系统，确认操作吗？");
    }

    /**
     * 启用、禁用账号
     *
     * @param wsExternalPersonnelStatusInputVo
     * @return
     */
    @Override
    @Transactional
    public int enableOrDisable(WsExternalPersonnelStatusInputVo wsExternalPersonnelStatusInputVo) {
        WsExternalPersonnel wsExternalPersonnel = selectOneById(wsExternalPersonnelStatusInputVo.getPkExternalPersonnelId());
        // SSO信息同步
        hrmsEmployeeFeignService.enable(
                wsExternalPersonnelStatusInputVo.getPkExternalPersonnelId(),
                IndexEnum.ZERO.getValue() == wsExternalPersonnelStatusInputVo.getStatus() ? EnableEnum.N : EnableEnum.Y
        );
        wsExternalPersonnel.setStatus(wsExternalPersonnelStatusInputVo.getStatus());
        return updateExternalPersonnel(wsExternalPersonnel);
    }

    /**
     * 保存外部人员信息
     *
     * @param externalPersonnelInputVo
     * @return
     */
    @Transactional
    @Override
    public int save(WsExternalPersonnelInputVo externalPersonnelInputVo) {
        WsExternalPersonnel externalPersonnel = selectOneByPhone(externalPersonnelInputVo.getPhone());
        if (null != externalPersonnel) {
            throw new BusinessException("手机号码已经被其他人绑定，请核对输入是否有误");
        }
        // SSO人员信息验证是否存在
        if (null != FeignInfoUitls.getUserByCode(externalPersonnelInputVo.getPhone())) {
            throw new BusinessException("手机号码已经被其他人绑定，请核对输入是否有误");
        }
        WsExternalPersonnel wsExternalPersonnel = new WsExternalPersonnel();
        // 直接保存外部人员信息、SSO人员信息
        MyBeanUtils.copyBeanNotNull2Bean(externalPersonnelInputVo, wsExternalPersonnel);
        // 填充默认信息
        wsExternalPersonnel.setUserPassword(defaultPassWord);
        wsExternalPersonnel.setDefaultOrgId(defaultOrgId);
        wsExternalPersonnel.setDefaultRoleCode(defaultRoleCode);
        // OA系统保存人员信息
        wsExternalPersonnel.setPkExternalPersonnelId(FeignInfoUitls.addEmployeeResp(wsExternalPersonnel));
        return insertExternalPersonnel(wsExternalPersonnel);
    }

    /**
     * 修改外部人员信息
     *
     * @param externalPersonnelInputVo
     * @return
     */
    @Transactional
    @Override
    public int update(WsExternalPersonnelInputVo externalPersonnelInputVo) {
        WsExternalPersonnel externalPersonnel = wsExternalPersonnelMapper.selectOneById(externalPersonnelInputVo.getPkExternalPersonnelId());
        //SSO同步信息
        if (externalPersonnelInputVo.getStatus() != externalPersonnel.getStatus() ||
                !externalPersonnelInputVo.getUserName().equals(externalPersonnel.getUserName())) {

            HrmsEmployeeSaveReq employeeSaveReq = new HrmsEmployeeSaveReq();
            employeeSaveReq.setEmployeeId(externalPersonnel.getPkExternalPersonnelId());
            employeeSaveReq.setIsEnable(externalPersonnelInputVo.getStatus() + "");
            employeeSaveReq.setEmployeeName(externalPersonnelInputVo.getUserName());
            FeignInfoUitls.updateEmployeeResp(employeeSaveReq);
        }
        MyBeanUtils.copyBeanNotNull2Bean(externalPersonnelInputVo, externalPersonnel);
        externalPersonnel.setUpdateBy(UserInfoHolder.getCurrentUserId());
        externalPersonnel.setUpdateTime(new Date());
        return updateExternalPersonnel(externalPersonnel);
    }

    /**
     * 根据id主键查询外部人员
     *
     * @param pkExternalPersonnelId id主键
     * @return
     */
    @Override
    public WsExternalPersonnel selectOneById(String pkExternalPersonnelId) {
        return Optional.ofNullable(wsExternalPersonnelMapper.selectOneById(pkExternalPersonnelId))
                .map(WsExternalPersonnel::get)
                .orElseThrow(() -> new BusinessException("未查询到外部人员信息"));
    }

    @Override
    public List<WsExternalPersonnel> selectOneByIds(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return wsExternalPersonnelMapper.selectOneByIds(ids);
    }

    @Override
    public List<WsExternalPersonnel> selectAllListByDeptId(String deptId) {
        return wsExternalPersonnelMapper.selectAllListByDeptId(deptId);
    }

    @Override
    public WsExternalPersonnel selectOneByPhone(String phone) {
        return wsExternalPersonnelMapper.selectOneById(phone);
    }

    @Transactional
    @Override
    public int updateExternalPersonnel(WsExternalPersonnel wsExternalPersonnel) {
        if (IndexEnum.ZERO.getValue() == wsExternalPersonnelMapper.updateExternalPersonnel(wsExternalPersonnel)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }

    @Transactional
    @Override
    public int insertExternalPersonnel(WsExternalPersonnel wsExternalPersonnel) {
        if (IndexEnum.ZERO.getValue() == wsExternalPersonnelMapper.insertExternalPersonnel(wsExternalPersonnel)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }

    /**
     * 填充故障类型人员信息
     *
     * @param faultTypeListOutVos
     */
    @Override
    public List<FaultTypeListOutVo> fillPeopleInfo(List<FaultTypeListOutVo> faultTypeListOutVos) {
        // 故障类型处理人id集合
        String fkUserIds = faultTypeListOutVos
                .stream()
                .map(FaultTypeListOutVo::getFkUserId)
                .collect(Collectors.joining(CuttingOperatorEnum.COMMA.getValue()));
        if (!StringUtil.isEmpty(fkUserIds)) {
            // 所有人员信息

            List<EmployeeResp> userListByIds = FeignInfoUitls.getUserListByIds(Arrays.asList(fkUserIds.split(CuttingOperatorEnum.COMMA.getValue())));
            // SSO外部人员信息
            List<EmployeeResp> externalPersonnelList = userListByIds
                    .stream()
                    .filter(temp -> defaultOrgId.equals(temp.getOrgId()))
                    .collect(Collectors.toList());
            // 仅剩OA人员信息
            userListByIds.removeAll(externalPersonnelList);
            // 工单外部人员信息
            List<WsExternalPersonnel> wsExternalPersonnels = selectOneByIds(
                    externalPersonnelList
                            .stream()
                            .map(EmployeeResp::getEmployeeId)
                            .collect(Collectors.toList())
            );
            faultTypeListOutVos.forEach(faultTypeListOutVo -> {
                if (StringUtil.isEmpty(faultTypeListOutVo.getFkUserId())) {
                    return;
                }
                List<WsSheetPeopleInfoOutVo> sheetPeopleInfoOutVoList = Lists.newArrayList();
                List<String> fkUserIdsTemp = Arrays.asList(faultTypeListOutVo.getFkUserId().split(CuttingOperatorEnum.COMMA.getValue()));
                // 补充OA人员信息
                fkUserIdsTemp
                        .forEach(temp -> {
                            userListByIds.forEach(user -> {
                                if (temp.equals(user.getEmployeeId())) {
                                    WsSheetPeopleInfoOutVo peopleInfoOutVo = new WsSheetPeopleInfoOutVo();
                                    peopleInfoOutVo.setUserId(user.getEmployeeId());
                                    peopleInfoOutVo.setName(user.getEmployeeName());
                                    peopleInfoOutVo.setDeptId(user.getOrgId());
                                    peopleInfoOutVo.setDeptName(user.getOrgName());
                                    sheetPeopleInfoOutVoList.add(peopleInfoOutVo);
                                }
                            });
                        });
                // 补充外部人员信息
                fkUserIdsTemp
                        .forEach(temp -> {
                            wsExternalPersonnels.forEach(user -> {
                                if (temp.equals(user.getPkExternalPersonnelId())) {
                                    WsSheetPeopleInfoOutVo peopleInfoOutVo = new WsSheetPeopleInfoOutVo();
                                    peopleInfoOutVo.setUserId(user.getPkExternalPersonnelId());
                                    peopleInfoOutVo.setName(user.getUserName());
                                    peopleInfoOutVo.setDeptId(user.getInstitutionalAffiliations());
                                    peopleInfoOutVo.setDeptName(user.getInstitutionalAffiliations());
                                    sheetPeopleInfoOutVoList.add(peopleInfoOutVo);
                                }
                            });
                        });
                faultTypeListOutVo.setPeopleInfoOutVos(sheetPeopleInfoOutVoList);
            });
        }
        return faultTypeListOutVos;
    }

    /**
     * 填充故障类型人员信息
     *
     * @param faultType
     */
    @Override
    public List<WsSheetPeopleInfoOutVo> fillPeopleInfo(WsFaultType faultType) {
        List<WsSheetPeopleInfoOutVo> sheetPeopleInfoOutVoList = Lists.newArrayList();
        if (!StringUtil.isEmpty(faultType.getFkUserId())) {
            // 所有人员信息
            List<EmployeeResp> userListByIds = FeignInfoUitls.getUserListByIds(
                    Arrays.asList(
                            faultType.getFkUserId().split(CuttingOperatorEnum.COMMA.getValue())
                    )
            );
            // SSO外部人员信息
            List<EmployeeResp> externalPersonnelList = userListByIds
                    .stream()
                    .filter(temp -> defaultOrgId.equals(temp.getOrgId()))
                    .collect(Collectors.toList());
            // 仅剩OA人员信息
            userListByIds.removeAll(externalPersonnelList);
            // 工单外部人员信息
            List<WsExternalPersonnel> wsExternalPersonnels = selectOneByIds(
                    externalPersonnelList
                            .stream()
                            .map(EmployeeResp::getEmployeeId)
                            .collect(Collectors.toList())
            );
            List<String> fkUserIdsTemp = Arrays.asList(faultType.getFkUserId().split(CuttingOperatorEnum.COMMA.getValue()));
            fkUserIdsTemp
                    .forEach(temp -> {
                        userListByIds.forEach(user -> {
                            // 补充信息
                            if (temp.equals(user.getEmployeeId())) {
                                WsSheetPeopleInfoOutVo peopleInfoOutVo = new WsSheetPeopleInfoOutVo();
                                peopleInfoOutVo.setUserId(user.getEmployeeId());
                                peopleInfoOutVo.setName(user.getEmployeeName());
                                peopleInfoOutVo.setDeptId(user.getOrgId());
                                peopleInfoOutVo.setDeptName(user.getOrgName());
                                peopleInfoOutVo.setSex(StringUtil.isEmpty(user.getGender()) ? null : user.getGender());
                                peopleInfoOutVo.setSexText(StringUtil.isEmpty(user.getGenderText()) ? null : user.getGenderText());
                                peopleInfoOutVo.setUrl(StringUtil.isEmpty(user.getAvatar()) ? null : user.getAvatar());
                                sheetPeopleInfoOutVoList.add(peopleInfoOutVo);
                            }
                        });
                    });
            fkUserIdsTemp
                    .forEach(temp -> {
                        wsExternalPersonnels.forEach(user -> {
                            if (temp.equals(user.getPkExternalPersonnelId())) {
                                WsSheetPeopleInfoOutVo peopleInfoOutVo = new WsSheetPeopleInfoOutVo();
                                peopleInfoOutVo.setUserId(user.getPkExternalPersonnelId());
                                peopleInfoOutVo.setName(user.getUserName());
                                peopleInfoOutVo.setDeptId(user.getInstitutionalAffiliations());
                                peopleInfoOutVo.setDeptName(user.getInstitutionalAffiliations());
                                sheetPeopleInfoOutVoList.add(peopleInfoOutVo);
                            }
                        });
                    });
        }
        return sheetPeopleInfoOutVoList;
    }

    /**
     * 查询所有机构名称
     *
     * @param institutionalAffiliations
     * @return
     */
    @Override
    public List<Map<String, Object>> selectAllInstitutionalAffiliations(String institutionalAffiliations) {
        return wsExternalPersonnelMapper.selectAllInstitutionalAffiliations(institutionalAffiliations);
    }

    /**
     * 去除查询职位名称
     *
     * @param position
     * @return
     */
    @Override
    public List<Map<String, Object>> selectAllPosition(String position) {
        return wsExternalPersonnelMapper.selectAllPosition(position);
    }

}
