package cn.trasen.worksheet.module.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.module.entity.WsFaultMan;
import cn.trasen.worksheet.module.mapper.WsFaultManMapper;
import cn.trasen.worksheet.module.service.WsFaultManService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class WsFaultManServiceImpl implements WsFaultManService {

    @Autowired
    private WsFaultManMapper wsFaultManMapper;

    @Transactional
    @Override
    public int insertBatch(List<WsFaultMan> faultManList) {
        if(CollectionUtil.isEmpty(faultManList)){
            throw new BusinessException("无数据，请勿使用批量操作");
        }
        if(faultManList.size() != wsFaultManMapper.insertBatch(faultManList)){
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return faultManList.size();
    }

    @Transactional
    @Override
    public int deleteById(String fkFaultTypeId) {
        return wsFaultManMapper.deleteFaultMan(fkFaultTypeId);
    }
}
