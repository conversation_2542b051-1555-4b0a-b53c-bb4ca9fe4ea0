package cn.trasen.worksheet.module.service.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;

import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.enums.CuttingOperatorEnum;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.util.FeignInfoUitls;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.common.util.MyBeanUtils;
import cn.trasen.worksheet.common.util.TreeUtils;
import cn.trasen.worksheet.module.dto.inputVo.FaultTypeInputVo;
import cn.trasen.worksheet.module.dto.inputVo.FaultTypeStatusInputVo;
import cn.trasen.worksheet.module.dto.outVo.FaultTypeListOutVo;
import cn.trasen.worksheet.module.dto.outVo.FaultTypeTreeOutVo;
import cn.trasen.worksheet.module.entity.WsExternalPersonnel;
import cn.trasen.worksheet.module.entity.WsFaultMan;
import cn.trasen.worksheet.module.entity.WsFaultType;
import cn.trasen.worksheet.module.mapper.WsFaultTypeMapper;
import cn.trasen.worksheet.module.service.WsExternalPersonnelService;
import cn.trasen.worksheet.module.service.WsFaultManService;
import cn.trasen.worksheet.module.service.WsFaultTypeService;

/**
 * <AUTHOR>
 * @date: 2021/7/3 11:40
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Service
public class WsFaultTypeServiceImpl implements WsFaultTypeService {

    @Autowired
    private WsFaultTypeMapper wsFaultTypeMapper;
    @Autowired
    private WsExternalPersonnelService wsExternalPersonnelService;
    @Autowired
    private WsFaultManService wsFaultManService;
    @Value("${externalPersonnel.ORG_ID}")
    private String defaultOrgId;


    @Override
    public int insertFaultType(WsFaultType wsFaultType) {
        if (IndexEnum.ZERO.getValue() == wsFaultTypeMapper.insertFaultType(wsFaultType)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }

    @Override
    public int updateFaultType(WsFaultType wsFaultType) {
        if (IndexEnum.ZERO.getValue() == wsFaultTypeMapper.updateFaultType(wsFaultType)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }

    /**
     * 保存、修改故障类型
     *
     * @param faultTypeInputVo
     * @return
     */
    @Transactional
    @Override
    public String saveFaultType(FaultTypeInputVo faultTypeInputVo) {
        WsFaultType wsFaultType = new WsFaultType();
        Integer change;
        String pkFaultTypeId;
        if (StringUtil.isEmpty(faultTypeInputVo.getPkFaultTypeId())) {
            // 验证唯一
            WsFaultType wsFaultTypeTemp = selectOneByCategoryNameAndParentId(faultTypeInputVo.getCategoryName(), faultTypeInputVo.getParentId(), UserInfoHolder.getCurrentUserInfo().getDeptId());
            if (null != wsFaultTypeTemp) {
                throw new BusinessException("已存在" + faultTypeInputVo.getCategoryName() + "，请勿重复添加");
            }

            pkFaultTypeId = IdUtils.getId();
            wsFaultType.setPkFaultTypeId(pkFaultTypeId);
            MyBeanUtils.copyBeanNotNull2Bean(faultTypeInputVo, wsFaultType);
            change = wsFaultTypeMapper.insertFaultType(wsFaultType);
        } else {
            pkFaultTypeId = faultTypeInputVo.getPkFaultTypeId();
            // 验证唯一
            WsFaultType wsFaultTypeTemp = selectOneByCategoryNameAndParentId(faultTypeInputVo.getCategoryName(), faultTypeInputVo.getParentId(), UserInfoHolder.getCurrentUserInfo().getDeptId());
            if (null != wsFaultTypeTemp && !pkFaultTypeId.equals(wsFaultTypeTemp.getPkFaultTypeId())) {
                throw new BusinessException("已存在" + faultTypeInputVo.getCategoryName() + "，请勿重复添加");
            }
            wsFaultType = wsFaultTypeMapper.selectOneById(faultTypeInputVo.getPkFaultTypeId());
            MyBeanUtils.copyBeanNotNull2Bean(faultTypeInputVo, wsFaultType);
            if (StringUtil.isEmpty(faultTypeInputVo.getParentId())) {
                wsFaultType.setParentId(null);
            }
            change = wsFaultTypeMapper.updateFaultType(wsFaultType);
            // 删除历史故障类型处理人
            wsFaultManService.deleteById(pkFaultTypeId);
        }
        // 保存故障类型处理人
        if (!StringUtil.isEmpty(faultTypeInputVo.getPeoples())) {
            List<WsFaultMan> faultManList = Lists.newArrayList();
            List<String> peopleIds = Arrays.asList(faultTypeInputVo.getPeoples().split(CuttingOperatorEnum.COMMA.getValue()));
            // OA人员信息
            List<EmployeeResp> userListByIds = FeignInfoUitls.getUserListByIds(peopleIds);
            // OA中的外部人员
            List<EmployeeResp> collect = userListByIds
                    .stream()
                    .filter(user -> user.getOrgId().equals(defaultOrgId))
                    .collect(Collectors.toList());
            // 过滤外部人员
            userListByIds.removeAll(collect);
            // 外部人员详细信息
            List<WsExternalPersonnel> wsExternalPersonnels = wsExternalPersonnelService.selectOneByIds(
                    collect.stream()
                            .map(EmployeeResp::getEmployeeId)
                            .collect(Collectors.toList())
            );

            userListByIds.forEach(temp -> {
                WsFaultMan faultMan = new WsFaultMan();
                faultMan.setFkFaultTypeId(pkFaultTypeId);
                faultMan.setFkUserId(temp.getEmployeeId());
                faultMan.setFkUserName(temp.getEmployeeName());
                faultMan.setFkUserDeptId(temp.getOrgId());
                faultMan.setFkUserDeptName(temp.getOrgName());
                faultManList.add(faultMan);
            });
            wsExternalPersonnels.forEach(temp -> {
                WsFaultMan faultMan = new WsFaultMan();
                faultMan.setFkFaultTypeId(pkFaultTypeId);
                faultMan.setFkUserId(temp.getPkExternalPersonnelId());
                faultMan.setFkUserName(temp.getUserName());
                faultMan.setFkUserDeptId(temp.getInstitutionalAffiliations());
                faultMan.setFkUserDeptName(temp.getInstitutionalAffiliations());
                faultManList.add(faultMan);
            });
            wsFaultManService.insertBatch(faultManList);
        }

        Optional.ofNullable(change)
                .orElseThrow(() -> new BusinessException(CommonlyConstants.OperationReturnValue.FAIL));
        return CommonlyConstants.OperationReturnValue.SUCCESS;
    }


    /**
     * 状态修改
     *
     * @param faultTypeStatusInputVo
     * @return
     */
    @Transactional
    @Override
    public int status(FaultTypeStatusInputVo faultTypeStatusInputVo) {
        WsFaultType faultType = selectOneById(faultTypeStatusInputVo.getPkFaultTypeId());
        // 所有故障类型信息
        List<FaultTypeTreeOutVo> children = Lists.newArrayList();
        // 获取当前故障点类型及所有下级故障点类型id
        List<FaultTypeTreeOutVo> faultTypeTreeOutVos = wsFaultTypeMapper.selectFaultTypeAllListContainsDisable();
        TreeUtils.queryQllSubNodes(faultTypeTreeOutVos, faultType.getPkFaultTypeId(), children, "getPid", "getId");

        List<String> ids = children.stream().map(FaultTypeTreeOutVo::getId).collect(Collectors.toList());
        ids.add(faultType.getPkFaultTypeId());
        if (ids.size() != wsFaultTypeMapper.updateBatchStatus(ids, faultTypeStatusInputVo.getFaultStatus())) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return ids.size();
    }

    /**
     * 故障类型启用
     *
     * @param pkFaultTypeId 故障类型id
     * @return
     */
    @Override
    public int faultTypeEnable(String pkFaultTypeId) {
        WsFaultType wsFaultType = selectOneById(pkFaultTypeId);
        if (!StringUtil.isEmpty(wsFaultType.getParentId())) {
            WsFaultType parentWsFaultType = selectOneById(wsFaultType.getParentId());
            if ((IndexEnum.ZERO.getValue() + "").equals(parentWsFaultType.getFaultStatus())) {
                throw new BusinessException("该故障类型上级分类未启用，请先启用上级分类再操作");
            }
        }
        wsFaultType.setFaultStatus(IndexEnum.ONE.getValue() + "");
        if (IndexEnum.ZERO.getValue() == wsFaultTypeMapper.updateFaultType(wsFaultType)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }

    @Override
    public FaultTypeListOutVo selectOne(String id) {
        FaultTypeListOutVo faultTypeListOutVo = new FaultTypeListOutVo();
        MyBeanUtils.copyBeanNotNull2Bean(
                Optional.ofNullable(wsFaultTypeMapper.selectOneById(id))
                        .orElseGet(() -> new WsFaultType()),
                faultTypeListOutVo
        );
        return faultTypeListOutVo;
    }

    @Override
    public WsFaultType selectOneById(String pkFaultTypeId) {
        return Optional.ofNullable(wsFaultTypeMapper.selectOneById(pkFaultTypeId))
                .map(WsFaultType::get)
                .orElseThrow(() -> new BusinessException("未查询到故障分类信息"));
    }

    @Override
    public WsFaultType selectOneByCategoryNameAndParentId(String categoryName, String parentId, String fkDeptId) {
        return wsFaultTypeMapper.selectOneByCategoryNameAndParentId(categoryName, parentId, fkDeptId);
    }

    /**
     * 批量逻辑删除故障类型，多个以英文逗号拼接
     *
     * @param pkFaultTypeIds 故障类型id
     * @return
     */
    @Transactional
    @Override
    public int deleteFaultType(String pkFaultTypeIds) {
        String[] pkFaultTypeId = Optional.ofNullable(pkFaultTypeIds)
                .map(map -> map.split(CuttingOperatorEnum.COMMA.getValue()))
                .orElseThrow(() -> new BusinessException("故障类型id不能为空"));
        // 批量删除所有id
        List<String> deleteIds = Lists.newArrayList();
        // 所有故障类型信息
        List<FaultTypeTreeOutVo> faultTypeTreeOutVoList = selectFaultTypeAllList("0", null, null);
        for (String temp : pkFaultTypeId) {
            List<FaultTypeTreeOutVo> children = Lists.newArrayList();
            // 获取当前故障点类型及所有下级故障点类型id
            TreeUtils.queryQllSubNodes(faultTypeTreeOutVoList, temp, children, "getPid", "getId");
            List<String> ids = children.stream().map(FaultTypeTreeOutVo::getId).collect(Collectors.toList());
            // 填充当前故障点当前id及下级故障点类型id
            deleteIds.addAll(ids);
            deleteIds.add(temp);
        }
        // id去重
        List<String> collect = deleteIds.stream().distinct().collect(Collectors.toList());
        if (collect.size() != wsFaultTypeMapper.deleteBatch(collect)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return collect.size();
    }

    /**
     * 分页列表
     *
     * @param page
     * @param fkDeptId       科室id
     * @param categoryName   分类名称
     * @param pkFaultTypeIds 故障类型id
     * @return
     */
    @Override
    public List<FaultTypeListOutVo> selectFaultTypePageList(Page page, String fkDeptId, String categoryName, String pkFaultTypeIds) {
        return Optional.ofNullable(fkDeptId)
                .map(temp -> {
                    return // 填充人员信息
                            wsExternalPersonnelService.fillPeopleInfo(
                                    // 查询故障类型信息，分页
                                    wsFaultTypeMapper.selectFaultTypePageList(
                                            page,
                                            fkDeptId,
                                            categoryName,
                                            // id 转List
                                            StringUtil.isEmpty(pkFaultTypeIds) ? null : Arrays.asList(pkFaultTypeIds.split(CuttingOperatorEnum.COMMA.getValue()))
                                    )
                            );
                })
                .orElseThrow(() -> new BusinessException("科室id不能为空"));
    }


    /**
     * 故障类型，树状结构
     *
     * @param status       0包含停用数据 1仅启用数据
     * @param fkDeptId     科室id
     * @param categoryName 故障类型名称
     * @return
     */
    @Override
    public List<FaultTypeTreeOutVo> selectFaultTypeTreeAllList(String status, String fkDeptId, String categoryName) {
        return TreeUtils.listToTree(
                wsFaultTypeMapper.selectFaultTypeAllList(status, fkDeptId, categoryName),
                "Pid",
                "Id",
                null,
                "Children"
        );
    }

    /**
     * 故障类型
     *
     * @param status       0包含停用数据 1仅启用数据
     * @param fkDeptId     科室id
     * @param categoryName 故障类型名称
     * @return
     */
    @Override
    public List<FaultTypeTreeOutVo> selectFaultTypeAllList(String status, String fkDeptId, String categoryName) {
        return wsFaultTypeMapper.selectFaultTypeAllList(status, fkDeptId, categoryName);
    }

    /**
     * 查询未设置故障类型处理人的故障类型数量
     *
     * @return
     */
    @Override
    public int faultTypePeopleIsNullCounts() {
        return wsFaultTypeMapper.faultTypePeopleIsNullCounts(UserInfoHolder.getCurrentUserInfo().getDeptId());
    }

    /**
     * 查询所有故障类型分类
     *
     * @return
     */
    @Override
    public List<WsFaultType> selectFaultTypeAllList() {
        return wsFaultTypeMapper.getFaultTypeAllList();
    }
}
