package cn.trasen.worksheet.module.service.impl;

import cn.trasen.worksheet.module.dto.outVo.WsFileOutVo;
import cn.trasen.worksheet.module.entity.WsFileFile;
import cn.trasen.worksheet.module.mapper.WsFileFileMapper;
import cn.trasen.worksheet.module.service.WsFileService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2021/7/3 17:48
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Service
public class WsFileServiceImpl implements WsFileService {

    @Autowired
    private WsFileFileMapper wsFileFileMapper;

    @Override
    public int insertFile(WsFileFile wsFileFile) {
        return wsFileFileMapper.insertFile(wsFileFile);
    }

    @Transactional
    @Override
    public int insertBatchFile(List<WsFileFile> list) {
        return wsFileFileMapper.insertBatchFile(list);
    }

    @Override
    public int updateBatchFile(List<WsFileFile> list) {
        return wsFileFileMapper.updateBatchFile(list);
    }

    @Override
    public int deleteFile(String id) {
        return wsFileFileMapper.deleteFile(id);
    }


    /**
     * 删除历史报修附件信息，不包含录音
     * @param workNumber
     * @return
     */
    @Override
    public int deleteFileByWorkNumber(String workNumber) {
        return wsFileFileMapper.deleteFileByWorkNumber(workNumber);
    }

    @Override
    public List<WsFileOutVo> selectAllList(String workNumber) {
        return wsFileFileMapper.selectAllList(workNumber);
    }

    /**
     * 根据节点id查询所有节点附件信息
     * @param taskIds
     * @return
     */
    @Override
    public List<WsFileOutVo> selectAllByTaskIdList(List<String> taskIds) {
        if(CollectionUtils.isEmpty(taskIds)){
            return null;
        }
        return wsFileFileMapper.selectAllByTaskIdList(taskIds);
    }

    /**
     * 根据通话记录id查询所有附件信息
     * @param custometLogId
     * @return
     */
    @Override
    public List<WsFileFile> selectAllByCustometLogIdList(List<String> custometLogId) {
        return wsFileFileMapper.selectAllByCustometLogIdList(custometLogId);
    }
}
