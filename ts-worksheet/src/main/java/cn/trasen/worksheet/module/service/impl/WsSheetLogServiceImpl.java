package cn.trasen.worksheet.module.service.impl;

import cn.trasen.worksheet.module.dto.inputVo.WsWsSheetInputVo;
import cn.trasen.worksheet.module.entity.WsWsLog;
import cn.trasen.worksheet.module.entity.WsWsSheet;
import cn.trasen.worksheet.module.mapper.WsWsLogMapper;
import cn.trasen.worksheet.module.service.WsSheetLogService;
import cn.trasen.worksheet.module.service.WsSheetService;
import cn.trasen.worksheet.common.util.IdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date: 2021/6/17 14:43
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Service
public class WsSheetLogServiceImpl implements WsSheetLogService {

    @Autowired
    private WsWsLogMapper wsLogMapper;


    @Override
    public int insert(String logTask, String taskName, String workNumber) {
        WsWsLog wsLog = new WsWsLog();
        wsLog.setPkWsLogId(IdUtils.getId());
        wsLog.setLogTask(logTask);
        wsLog.setLogTask(taskName);
//        wsLog.setWorkNubmer(workNumber);
        return wsLogMapper.insert(wsLog);
    }

    @Override
    public WsWsSheet checkworkSheetChange(WsWsSheetInputVo wsSheetInputVo, String taskLogName) {
        return null;
    }

    public static void main(String[] args) {
//        List<String> strings = Arrays.asList("admin", null);
//        System.out.println(strings.get(1));
//        WsWsTask task2 = new WsWsTask();
//        task2.setCreateBy("123");
//        WsWsTask task1 = Optional.ofNullable(task2.getCreateBy())
//                .map(temp -> {
//                    WsWsTask task = new WsWsTask();
//                    task.setWorkNumber("11111111111");
//                    return task;
//                })
//                .orElse(new WsWsTask("222222222"));
//
//        System.out.println(Optional.ofNullable(null).map(temp ->{
//            System.out.println("111111");
//            return "1";
//        }).orElseGet(WsSheetLogServiceImpl::test));
    }

    public static String test(){
        return "2222222222";
    }
//    /**
//     * 检查工单二次保存，是否发生数据改变
//     * @param wsSheetInputVo 传输DTO
//     */
//    @Override
//    public WsWsSheet checkworkSheetChange(WsWsSheetInputVo wsSheetInputVo){
//        WsWsSheet wsSheet = wsSheetService.selectOneWsSheet(wsSheetInputVo.getWorkNumber());
//        WsWsTask wsTask = new WsWsTask();
//        StringBuilder message = new StringBuilder("");
//        if(!wsSheetInputVo.getFkFaultTypeId().equals(wsSheet.getFkFaultTypeId())){
//            message.append("故障类型由‘" + wsSheet.getFkFaultTypeId() + "'修改为’" + wsSheetInputVo.getFkFaultTypeId());
//        }
//        if(!wsSheetInputVo.getFaultEquipmentName().equals(wsSheet.getFaultEquipmentName())){
//            message.append("故障设备由‘" + wsSheet.getFaultEquipmentName() + "'修改为’" + wsSheetInputVo.getFaultEquipmentName());
//        }
//        if(!wsSheetInputVo.getFaultDescription().equals(wsSheet.getFaultDescription())){
//            message.append("故障描述由‘" + wsSheet.getFaultDescription() + "'修改为’" + wsSheetInputVo.getFaultDescription());
//        }
//        if(!wsSheetInputVo.getFaultEmergency().equals(wsSheet.getFaultEmergency())){
//            message.append("故障紧急程度由‘" + wsSheet.getFaultEmergency() + "'修改为’" + wsSheetInputVo.getFaultEmergency());
//        }
//        if(!wsSheetInputVo.getFaultAffectScope().equals(wsSheet.getFaultAffectScope())){
//            message.append("故障紧急程度由‘" + wsSheet.getFaultAffectScope() + "'修改为’" + wsSheetInputVo.getFaultAffectScope());
//        }
//        if(!wsSheetInputVo.getRequiredCompletionTime().equals(wsSheet.getRequiredCompletionTime())){
//            message.append("故障完成时间由‘" + wsSheet.getRequiredCompletionTime() + "'修改为’" + wsSheetInputVo.getRequiredCompletionTime());
//        }
//        if(!(wsSheetInputVo.getRemark() + "").equals(wsSheet.getRemark())){
//            message.append("备注由‘" + wsSheet.getRemark() + "'修改为’" + wsSheetInputVo.getRemark());
//        }
//        if(!(wsSheetInputVo.getFkUserId() + "").equals(wsSheet.getFkUserId())){
//            if(StringUtils.isEmpty(wsSheet.getFkUserId())){
//                message.append("处理人为’" + wsSheetInputVo.getFkUserId());
//                wsTask.setWorkStatus(WorkSheetStatusEnum.WAITING.getValue());
//            }else{
//                message.append("处理人由‘" + wsSheet.getFkUserId() + "'修改为’" + wsSheetInputVo.getFkUserId());
//            }
//        }
//        if(!StringUtils.isEmpty(message + "")){
//            wsTask.setTaskName(WorkSheetStatusEnum.EDITOR.getValue());
//            wsTask.setWorkNumber(wsSheet.getWorkNumber());
//        }
//        return wsSheet;
//    }

}
