package cn.trasen.worksheet.module.service.impl;


import java.util.Date;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.hutool.core.bean.BeanUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.module.dto.inputVo.WsSysConfigSaveInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsHospitalDistrictListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsSysConfigInfoOutVo;
import cn.trasen.worksheet.module.entity.WsSysConfig;
import cn.trasen.worksheet.module.mapper.WsSysConfigMapper;
import cn.trasen.worksheet.module.service.WsHospitalDistrictService;
import cn.trasen.worksheet.module.service.WsSysConfigService;
import tk.mybatis.mapper.entity.Example;

/**
 * 工单系统配置
 */
@Service
public class WsSysConfigServiceImpl implements WsSysConfigService {


    @Autowired
    private WsSysConfigMapper mapper;
    @Autowired
    private WsHospitalDistrictService hospitalDistrictService;


    /**
     * 新增、修改工单系统配置
     *
     * @param sysConfigSaveInputVo
     * @return
     */
    @Override
    public Integer sysConfig(WsSysConfigSaveInputVo sysConfigSaveInputVo) {
        WsSysConfig sysConfig = BeanUtil.copyProperties(sysConfigSaveInputVo, WsSysConfig.class);
        return Optional.ofNullable(sysConfig.getPkSysConfigId())
                .map(temp -> modifySysConfig(sysConfig))
                .orElseGet(() -> saveSysConfig(sysConfig));
    }

    /**
     * 查询工单系统配置信息
     *
     * @return
     */
    @Override
    public WsSysConfigInfoOutVo getOne() {
        WsSysConfigInfoOutVo sysConfigInfoOutVo = mapper.selectAll()
                .stream()
                .findFirst()
                .map(sysConfig -> BeanUtil.copyProperties(sysConfig, WsSysConfigInfoOutVo.class))
                .orElseGet(() -> new WsSysConfigInfoOutVo());
        sysConfigInfoOutVo.setHospitalDistrictList(
                BeanUtil.copyToList(hospitalDistrictService.getAllList(IndexEnum.ONE.getValue() + ""), WsHospitalDistrictListOutVo.class)
        );
        return sysConfigInfoOutVo;
    }

    /**
     * 新增
     *
     * @param sysConfig
     * @return
     */
    public Integer saveSysConfig(WsSysConfig sysConfig) {
        if ((IndexEnum.ZERO.getValue() + "").equals(sysConfig.getAutomatedAcceptance())) {
            sysConfig.setAcceptanceDays(null);
            sysConfig.setAcceptanceDefaultScore(null);
        }
        if ((IndexEnum.ZERO.getValue() + "").equals(sysConfig.getAutomatedEvaluate())) {
            sysConfig.setEvaluateDays(null);
            sysConfig.setEvaluateDefaultScore(null);
        }
        sysConfig.setPkSysConfigId(IdUtils.getId());
        sysConfig.setDeleteStatus(IndexEnum.ZERO.getValue());
        sysConfig.setCreateBy(UserInfoHolder.getCurrentUserId());
        sysConfig.setCreateTime(new Date());
        return mapper.insert(sysConfig);
    }

    /**
     * 修改
     *
     * @param sysConfig
     * @return
     */
    public Integer modifySysConfig(WsSysConfig sysConfig) {
        sysConfig.setUpdateBy(UserInfoHolder.getCurrentUserId());
        sysConfig.setUpdateTime(new Date());
        Example example = new Example(WsSysConfig.class);
        example.createCriteria()
                .andEqualTo("pkSysConfigId", sysConfig.getPkSysConfigId());
        return mapper.updateByExampleSelective(sysConfig, example);
    }


}
