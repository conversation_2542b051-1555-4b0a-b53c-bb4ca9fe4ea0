package cn.trasen.worksheet.module.service.impl;

import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.worksheet.common.util.DateUtils;
import cn.trasen.worksheet.module.dto.outVo.WsFileOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkReportOutVo;
import cn.trasen.worksheet.module.entity.WsFileFile;
import cn.trasen.worksheet.module.entity.WsWrokReport;
import cn.trasen.worksheet.module.mapper.WsFileFileMapper;
import cn.trasen.worksheet.module.mapper.WsWorkReportMapper;
import cn.trasen.worksheet.module.service.WsCustometLogService;
import cn.trasen.worksheet.module.service.WsFileService;
import cn.trasen.worksheet.module.service.WsSheetService;
import cn.trasen.worksheet.module.service.WsWorkReportService;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 工单日报
 * <AUTHOR>
 * @date: 2021/7/3 17:48
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Service
public class WsWorkReportServiceImpl implements WsWorkReportService {

    @Autowired
    private WsWorkReportMapper mapper;

    /**
     * 保存
     * @param wsWorkReport
     * @return
     */
    @Override
    public int insert(WsWrokReport wsWorkReport) {
        return mapper.insert(wsWorkReport);
    }

    /**
     * 根据主键查询
     * @param pkWorkReportId 主键
     * @return
     */
    @Override
    public WsWrokReport selectOneById(String pkWorkReportId) {
        return Optional.ofNullable(mapper.selectOne(new WsWrokReport(pkWorkReportId)))
                .map(WsWrokReport::get)
                .orElseThrow(() -> new BusinessException("未找到工单日报数据"));
    }


    /**
     * 工单日报详情
     * @param pkWorkReportId 主键
     * @return
     */
    @Override
    public WsWorkReportOutVo workReportInfoById(String pkWorkReportId) {
        // 数据转换，填充科室名
        WsWrokReport wsWrokReport = selectOneById(pkWorkReportId);
        WsWorkReportOutVo workReportOutVo = JSON.parseObject(wsWrokReport.getContentInfo(), WsWorkReportOutVo.class);
        workReportOutVo.setDeptName(wsWrokReport.getRecDeptName());
        return workReportOutVo;
    }


}
