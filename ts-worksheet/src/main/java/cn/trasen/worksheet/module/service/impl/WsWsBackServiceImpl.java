package cn.trasen.worksheet.module.service.impl;

import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.module.entity.WsWsBack;
import cn.trasen.worksheet.module.mapper.WsWsBackMapper;
import cn.trasen.worksheet.module.service.WsWsBackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 工单打回（验收不不通过）记录
 */
@Service
public class WsWsBackServiceImpl implements WsWsBackService {

    @Autowired
    private WsWsBackMapper wsWsBackMapper;

    @Override
    public int insertBack(WsWsBack wsWsBack) {
        if(IndexEnum.ZERO.getValue() == wsWsBackMapper.insertBack(wsWsBack)){
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }
}
