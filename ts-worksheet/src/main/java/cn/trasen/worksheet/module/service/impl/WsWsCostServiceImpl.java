package cn.trasen.worksheet.module.service.impl;


import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.base.FileAttachmentByBusinessIdListRes;
import cn.trasen.homs.bean.base.FileAttachmentResp;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.FileAttachmentFeignService;
import cn.trasen.worksheet.common.enums.DictCodeEnum;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.util.DateUtils;
import cn.trasen.worksheet.common.util.DictUtils;
import cn.trasen.worksheet.common.util.ExportUtil;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.module.dto.inputVo.WsWsCostPageListInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsCostSaveInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsCostOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsCostPageListOutVo;
import cn.trasen.worksheet.module.entity.WsOmMeau;
import cn.trasen.worksheet.module.entity.WsWsCost;
import cn.trasen.worksheet.module.mapper.WsWsCostMapper;
import cn.trasen.worksheet.module.service.WsOmMeauService;
import cn.trasen.worksheet.module.service.WsWsCostService;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * 工单费用
 */
@Slf4j
@Service
public class WsWsCostServiceImpl implements WsWsCostService {


    @Autowired
    private WsWsCostMapper mapper;
    @Autowired
    private WsOmMeauService omMeauService;
    @Autowired
    private FileAttachmentFeignService fileAttachmentFeignService;


    /**
     * 新增、修改工单费用
     *
     * @param costSaveInputVo
     * @return
     */
    @Override
    public Integer wsCost(WsWsCostSaveInputVo costSaveInputVo) {
        WsWsCost wsCost = BeanUtil.copyProperties(costSaveInputVo, WsWsCost.class);
        return Optional.ofNullable(wsCost.getPkWsCostId())
                .map(temp -> modifyWsCost(wsCost))
                .orElseGet(() -> saveWsCost(wsCost));
    }

    /**
     * 费用分页列表
     *
     * @param costPageListInputVo 查询参数
     * @return
     */
    @Override
    public List<WsWsCostPageListOutVo> getPageList(Page page, WsWsCostPageListInputVo costPageListInputVo) {
        // 特殊处理时间
        costPageListInputVo.setCostEndTime(DateUtils.dateAddNDay(costPageListInputVo.getCostEndTime(), IndexEnum.ONE.getValue()));
        costPageListInputVo.setCreateEndTime(DateUtils.dateAddNDay(costPageListInputVo.getCreateEndTime(), IndexEnum.ONE.getValue()));
        List<WsWsCostPageListOutVo> costPageListOutVos = mapper.getPageList(page, costPageListInputVo);
        if (CollectionUtil.isEmpty(costPageListOutVos)) {
            return costPageListOutVos;
        }
        // 附件业务id
        List<String> fileIds = costPageListOutVos.stream()
                .filter(costPageListInputVoTemp -> !StringUtils.isEmpty(costPageListInputVoTemp.getFiles()))
                .map(WsWsCostPageListOutVo::getFiles)
                .collect(Collectors.toList());
        List<FileAttachmentByBusinessIdListRes> attachments = Lists.newArrayList();
        if (!StringUtils.isEmpty(fileIds)) {
            try {
                log.info("----------------------------------------调用基础服务-附件业务id查询附件信开始");
                PlatformResult<List<FileAttachmentByBusinessIdListRes>> attachment = fileAttachmentFeignService.listFileAttachmentByBusinessIdList(fileIds);
                if (!attachment.isSuccess()) {
                    log.error("----------------------------------------调用基础服务-附件业务id查询附件信失败：" + attachment.getMessage());
                } else {
                    attachments = attachment.getObject();
                    log.info("----------------------------------------调用基础服务-附件业务id查询附件信完成");
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("----------------------------------------调用基础服务-附件业务id查询附件信失败：" + e.getMessage());
            }
        }
        // 费用字典信息
        List<DictItemResp> costStatus = DictUtils.getList(DictCodeEnum.COST_STATUS.getValue());
        List<FileAttachmentByBusinessIdListRes> finalAttachments = attachments;
        return costPageListOutVos.stream()
                .map(cost -> {
                    // 填充费用状态字典name
                    DictItemResp dictItemResp = costStatus.stream()
                            .filter(costStatusTemp -> (cost.getCostStatus() + "").equals(costStatusTemp.getItemNameValue()))
                            .findFirst()
                            .map(temp -> temp)
                            .orElseGet(() -> new DictItemResp());
                    cost.setCostStatusName(dictItemResp.getItemName());
                    List<List<FileAttachmentResp>> collect = finalAttachments.stream()
                            .filter(attachment -> attachment.getBusinessId().equals(cost.getFiles()))
                            .map(attachment -> attachment.getFileAttachmentRespList())
                            .collect(Collectors.toList());
                    if(CollectionUtil.isEmpty(collect)){
                        cost.setFileCount("0");
                    }else{
                        cost.setFileCount(collect.get(0).size()+"");
                    }
                    return cost;
                })
                .collect(Collectors.toList());
    }

    /**
     * 费用总金额
     *
     * @param costPageListInputVo
     */
    @Override
    public Float wsCostSum(WsWsCostPageListInputVo costPageListInputVo) {
        // 特殊处理时间
        costPageListInputVo.setCostEndTime(DateUtils.dateAddNDay(costPageListInputVo.getCostEndTime(), IndexEnum.ONE.getValue()));
        costPageListInputVo.setCreateEndTime(DateUtils.dateAddNDay(costPageListInputVo.getCreateEndTime(), IndexEnum.ONE.getValue()));
        return mapper.wsCostSum(costPageListInputVo);
    }

    /**
     * 逻辑删除工单费用
     *
     * @param pkWsCostIds 费用id
     * @return
     */
    @Override
    public Integer deleteWsCost(List<String> pkWsCostIds) {
        WsWsCost wsCost = new WsWsCost();
        wsCost.setUpdateBy(UserInfoHolder.getCurrentUserId());
        wsCost.setUpdateTime(new Date());
        wsCost.setDeleteStatus(IndexEnum.ONE.getValue());

        Example example = new Example(WsWsCost.class);
        example.createCriteria()
                .andIn("pkWsCostId", pkWsCostIds);
        return mapper.updateByExampleSelective(wsCost, example);
    }

    /**
     * 工单费用详情
     *
     * @param pkWsCostId 主键
     * @return
     */
    @Override
    public WsWsCostOutVo getOne(String pkWsCostId) {
        Example example = new Example(WsWsCost.class);
        example.createCriteria()
                .andEqualTo("pkWsCostId", pkWsCostId);
        WsWsCost wsCost = mapper.selectOneByExample(example);
        WsWsCostOutVo wsCostOutVo = BeanUtil.copyProperties(wsCost, WsWsCostOutVo.class);
        // 填充字典name
        wsCostOutVo.setCostStatusName(
                DictUtils.getDictItemRespItemName(DictCodeEnum.COST_STATUS.getValue(), wsCostOutVo.getCostStatus() + "")
        );
        WsOmMeau omMeau = omMeauService.seleteOneOmMeauByWorkNumber(wsCostOutVo.getWorkNumber());
        wsCostOutVo.setBusinessDeptId(omMeau.getDeptId());
        wsCostOutVo.setBusinessDeptName(omMeau.getDeptName());
        return wsCostOutVo;
    }


    /**
     * 工单费用详情列表数据
     *
     * @param workNumber 工单号
     * @return
     */
    @Override
    public List<WsWsCostPageListOutVo> getListByWorkNumber(String workNumber) {
        Example example = new Example(WsWsCost.class);
        example.createCriteria()
                .andEqualTo("workNumber", workNumber)
                .andEqualTo("deleteStatus", IndexEnum.ZERO.getValue());
        return BeanUtil.copyToList(mapper.selectByExample(example), WsWsCostPageListOutVo.class);
    }

    /**
     * 费用分页列表
     *
     * @param costPageListInputVo 查询参数
     * @return
     */
    @Override
    public void exportExcel(Page page, WsWsCostPageListInputVo costPageListInputVo, String type, HttpServletResponse response, HttpServletRequest request) {
        Optional.ofNullable(type).orElseThrow(() -> new BusinessException("导出参数类型为必填"));
        // 需导出数据
        List<WsWsCostPageListOutVo> costPageListOutVos = getPageList(page, costPageListInputVo);
        // 附件信息
        List<FileAttachmentByBusinessIdListRes> attachments = Lists.newArrayList();
        // 附件业务id
        List<String> fileIds = costPageListOutVos.stream()
                .filter(costPageListInputVoTemp -> !StringUtils.isEmpty(costPageListInputVoTemp.getFiles()))
                .map(WsWsCostPageListOutVo::getFiles)
                .collect(Collectors.toList());
        if (!StringUtils.isEmpty(fileIds)) {
            try {
                log.info("----------------------------------------调用基础服务-附件业务id查询附件信开始");
                PlatformResult<List<FileAttachmentByBusinessIdListRes>> attachment = fileAttachmentFeignService.listFileAttachmentByBusinessIdList(fileIds);
                if (!attachment.isSuccess()) {
                    log.error("----------------------------------------调用基础服务-附件业务id查询附件信失败：" + attachment.getMessage());
                } else {
                    attachments = attachment.getObject();
                    log.info("----------------------------------------调用基础服务-附件业务id查询附件信完成");
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("----------------------------------------调用基础服务-附件业务id查询附件信失败：" + e.getMessage());
            }
        }
        List<FileAttachmentByBusinessIdListRes> finalAttachments = attachments;
        // 文件名、表头
        String excelName = "";
        // 列头
        List<String> headList = Lists.newArrayList();
        List<Map<String, Object>> dataList = Lists.newArrayList();
        if ("one".equals(type)) {
            excelName = "我的费用";
            headList.add("序号");
            headList.add("工单编号");
            headList.add("金额（元）");
            headList.add("费用描述");
            headList.add("发生时间");
            headList.add("填报时间");
            headList.add("附件数量");
//            headList.add("费用状态");
            for(int i=0 ;i<costPageListOutVos.size();i++){
                Map<String, Object> map = Maps.newHashMap();
                map.put("序号", i+1);
                map.put("工单编号", costPageListOutVos.get(i).getWorkNumber());
                map.put("金额（元）", costPageListOutVos.get(i).getMoney());
                map.put("费用描述", costPageListOutVos.get(i).getCostDeion());
                map.put("发生时间", DateUtils.dateToStringFormat("yyyy-MM-dd HH:mm", costPageListOutVos.get(i).getCostTime()));
                map.put("填报时间", DateUtils.dateToStringFormat("yyyy-MM-dd HH:mm", costPageListOutVos.get(i).getCreateTime()));
                if (StringUtils.isEmpty(costPageListOutVos.get(i).getFiles())) {
                    map.put("附件数量", "0");
                } else {
                    int finalI = i;
                    List<List<FileAttachmentResp>> collect = finalAttachments.stream()
                            .filter(attachment -> attachment.getBusinessId().equals(costPageListOutVos.get(finalI).getFiles()))
                            .map(attachment -> attachment.getFileAttachmentRespList())
                            .collect(Collectors.toList());
                    if(CollectionUtil.isEmpty(collect)){
                        map.put("附件数量", "0");
                    }else{
                        map.put("附件数量", collect.get(0).size()+"");
                    }
                }
                dataList.add(map);
            }
//            dataList = costPageListOutVos.stream().map(costOutVo -> {
//                index.getAndIncrement();
//                Map<String, Object> map = Maps.newHashMap();
//                map.put("序号", index);
//                map.put("工单编号", costOutVo.getWorkNumber());
//                map.put("金额（元）", costOutVo.getMoney());
//                map.put("费用描述", costOutVo.getCostDeion());
//                map.put("发生时间", DateUtils.dateToStringFormat("yyyy-MM-dd HH:mm", costOutVo.getCostTime()));
//                map.put("填报时间", DateUtils.dateToStringFormat("yyyy-MM-dd HH:mm", costOutVo.getCreateTime()));
//                if (StringUtils.isEmpty(costOutVo.getFiles())) {
//                    map.put("附件数量", "0");
//                } else {
//                    List<List<FileAttachmentResp>> collect = finalAttachments.stream()
//                            .filter(attachment -> attachment.getBusinessId().equals(costOutVo.getFiles()))
//                            .map(attachment -> attachment.getFileAttachmentRespList())
//                            .collect(Collectors.toList());
//                    if(CollectionUtil.isEmpty(collect)){
//                        map.put("附件数量", "0");
//                    }else{
//                        map.put("附件数量", collect.get(0).size()+"");
//                    }
//                }
//
//                // 需获取字典数据
////                map.put("费用状态", costOutVo.getCostStatusName());
//                return map;
//            }).collect(Collectors.toList());
        } else if ("all".equals(type)) {
            excelName = "费用汇总";
            headList.add("序号");
            headList.add("工单编号");
            headList.add("处理科室");
            headList.add("金额（元）");
            headList.add("费用描述");
            headList.add("发生时间");
            headList.add("填报科室");
            headList.add("填报人");
            headList.add("填报时间");
            headList.add("附件数量");
//            headList.add("费用状态");

            for(int i=0 ;i<costPageListOutVos.size();i++){
                Map<String, Object> map = Maps.newHashMap();
                map.put("序号", i+1);
                map.put("工单编号", costPageListOutVos.get(i).getWorkNumber());
                map.put("处理科室", costPageListOutVos.get(i).getBusinessDeptName());
                map.put("金额（元）", costPageListOutVos.get(i).getMoney());
                map.put("费用描述", costPageListOutVos.get(i).getCostDeion());
                map.put("发生时间", DateUtils.dateToStringFormat("yyyy-MM-dd HH:mm", costPageListOutVos.get(i).getCostTime()));
                map.put("填报科室", costPageListOutVos.get(i).getFillDeptName());
                map.put("填报人", costPageListOutVos.get(i).getFillUser());
                map.put("填报时间", DateUtils.dateToStringFormat("yyyy-MM-dd HH:mm", costPageListOutVos.get(i).getCreateTime()));
                if (StringUtils.isEmpty(costPageListOutVos.get(i).getFiles())) {
                    map.put("附件数量", "0");
                } else {
                    int finalI = i;
                    List<List<FileAttachmentResp>> collect = finalAttachments.stream()
                            .filter(attachment -> attachment.getBusinessId().equals(costPageListOutVos.get(finalI).getFiles()))
                            .map(attachment -> attachment.getFileAttachmentRespList())
                            .collect(Collectors.toList());
                    if(CollectionUtil.isEmpty(collect)){
                        map.put("附件数量", "0");
                    }else{
                        map.put("附件数量", collect.get(0).size()+"");
                    }
                }
                dataList.add(map);
            }
//            AtomicInteger index = new AtomicInteger(1);
//            dataList = costPageListOutVos.stream().map(costOutVo -> {
//                index.getAndIncrement();
//                Map<String, Object> map = Maps.newHashMap();
//                map.put("序号", index);
//                map.put("工单编号", costOutVo.getWorkNumber());
//                map.put("处理科室", costOutVo.getBusinessDeptName());
//                map.put("金额（元）", costOutVo.getMoney());
//                map.put("费用描述", costOutVo.getCostDeion());
//                map.put("发生时间", DateUtils.dateToStringFormat("yyyy-MM-dd HH:mm", costOutVo.getCostTime()));
//                map.put("填报科室", costOutVo.getFillDeptName());
//                map.put("填报人", costOutVo.getFillUser());
//                map.put("填报时间", DateUtils.dateToStringFormat("yyyy-MM-dd HH:mm", costOutVo.getCreateTime()));
//                if (StringUtils.isEmpty(costOutVo.getFiles())) {
//                    map.put("附件数量", "0");
//                } else {
//                    List<List<FileAttachmentResp>> collect = finalAttachments.stream()
//                            .filter(attachment -> attachment.getBusinessId().equals(costOutVo.getFiles()))
//                            .map(attachment -> attachment.getFileAttachmentRespList())
//                            .collect(Collectors.toList());
//                    if(CollectionUtil.isEmpty(collect)){
//                        map.put("附件数量", "0");
//                    }else{
//                        map.put("附件数量", collect.get(0).size()+"");
//                    }
//                }
//                // 需获取字典数据
////                map.put("费用状态", costOutVo.getCostStatusName());
//                return map;
//            }).collect(Collectors.toList());
        } else {
            throw new BusinessException("导出参数类型为必填");
        }
        try {
            log.info("----------------------------------------------生成Excel开始：" + new Date());
            ExportUtil.createExcel(excelName, headList, headList, dataList, response, request);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("----------------------------------------------生成Excel失败：" + new Date() + "。" + e.getMessage());
        }
    }

    /**
     * 费用导出
     *
     * @param workNumber 工单号
     * @return
     */
    @Override
    public void exportExcelByWorkNumber(String workNumber, HttpServletResponse response, HttpServletRequest request) {
        List<WsWsCostPageListOutVo> costListVo = getListByWorkNumber(workNumber)
                .stream()
                .sorted(Comparator.comparing(WsWsCostPageListOutVo::getCreateTime).reversed())
                .collect(Collectors.toList());
        // 附件信息
        List<FileAttachmentByBusinessIdListRes> attachments = Lists.newArrayList();
        // 附件业务id
        List<String> fileIds = costListVo.stream()
                .filter(costPageListInputVoTemp -> !StringUtils.isEmpty(costPageListInputVoTemp.getFiles()))
                .map(WsWsCostPageListOutVo::getFiles)
                .collect(Collectors.toList());
        if (!StringUtils.isEmpty(fileIds)) {
            try {
                log.info("----------------------------------------调用基础服务-附件业务id查询附件信开始");
                PlatformResult<List<FileAttachmentByBusinessIdListRes>> attachment = fileAttachmentFeignService.listFileAttachmentByBusinessIdList(fileIds);
                if (!attachment.isSuccess()) {
                    log.error("----------------------------------------调用基础服务-附件业务id查询附件信失败：" + attachment.getMessage());
                } else {
                    attachments = attachment.getObject();
                    log.info("----------------------------------------调用基础服务-附件业务id查询附件信完成");
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("----------------------------------------调用基础服务-附件业务id查询附件信失败：" + e.getMessage());
            }
        }
        List<FileAttachmentByBusinessIdListRes> finalAttachments = attachments;
        // 文件名、表头
        String excelName = "";
        // 列头
        List<String> headList = Lists.newArrayList();
        List<Map<String, Object>> dataList = Lists.newArrayList();
        excelName = "费用明细";
        headList.add("序号");
        headList.add("金额（元）");
        headList.add("费用描述");
        headList.add("发生时间");
        headList.add("填报科室");
        headList.add("填报人");
        headList.add("填报时间");
        headList.add("附件数量");
//        headList.add("费用状态");

        for(int i=0 ;i<costListVo.size();i++) {
            Map<String, Object> map = Maps.newHashMap();
            map.put("序号", i + 1);
            map.put("金额（元）", costListVo.get(i).getMoney());
            map.put("费用描述", costListVo.get(i).getCostDeion());
            map.put("发生时间", DateUtils.dateToStringFormat("yyyy-MM-dd HH:mm", costListVo.get(i).getCostTime()));
            map.put("填报科室", costListVo.get(i).getFillDeptName());
            map.put("填报人", costListVo.get(i).getFillUser());
            map.put("填报时间", DateUtils.dateToStringFormat("yyyy-MM-dd HH:mm", costListVo.get(i).getCreateTime()));

            if (StringUtils.isEmpty(costListVo.get(i).getFiles())) {
                map.put("附件数量", "0");
            } else {
                int finalI = i;
                List<List<FileAttachmentResp>> collect = finalAttachments.stream()
                        .filter(attachment -> attachment.getBusinessId().equals(costListVo.get(finalI).getFiles()))
                        .map(attachment -> attachment.getFileAttachmentRespList())
                        .collect(Collectors.toList());
                if (CollectionUtil.isEmpty(collect)) {
                    map.put("附件数量", "0");
                } else {
                    map.put("附件数量", collect.get(0).size() + "");
                }
                
            }
            dataList.add(map);
        }
//        AtomicInteger index = new AtomicInteger(1);
//        dataList = costListVo.stream().map(costOutVo -> {
//            index.getAndIncrement();
//            Map<String, Object> map = Maps.newHashMap();
//            map.put("序号", index);
//            map.put("金额（元）", costOutVo.getMoney());
//            map.put("费用描述", costOutVo.getCostDeion());
//            map.put("发生时间", DateUtils.dateToStringFormat("yyyy-MM-dd HH:mm", costOutVo.getCostTime()));
//            map.put("填报科室", costOutVo.getFillDeptName());
//            map.put("填报人", costOutVo.getFillUser());
//            map.put("填报时间", DateUtils.dateToStringFormat("yyyy-MM-dd HH:mm", costOutVo.getCreateTime()));
//
//            if (StringUtils.isEmpty(costOutVo.getFiles())) {
//                map.put("附件数量", "0");
//            } else {
//                List<List<FileAttachmentResp>> collect = finalAttachments.stream()
//                        .filter(attachment -> attachment.getBusinessId().equals(costOutVo.getFiles()))
//                        .map(attachment -> attachment.getFileAttachmentRespList())
//                        .collect(Collectors.toList());
//                if(CollectionUtil.isEmpty(collect)){
//                    map.put("附件数量", "0");
//                }else{
//                    map.put("附件数量", collect.get(0).size()+"");
//                }
//            }
//            // 需获取字典数据
////            map.put("费用状态", costOutVo.getCostStatusName());
//            return map;
//        }).collect(Collectors.toList());
        try {
            log.info("----------------------------------------------生成Excel开始：" + new Date());
            ExportUtil.createExcel(excelName, headList, headList, dataList, response, request);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("----------------------------------------------生成Excel失败：" + new Date() + "。" + e.getMessage());
        }
    }


    /**
     * 新增
     *
     * @param wsCost
     * @return
     */
    public Integer saveWsCost(WsWsCost wsCost) {
        wsCost.setPkWsCostId(IdUtils.getId());
        wsCost.setDeleteStatus(IndexEnum.ZERO.getValue());
        wsCost.setCreateBy(UserInfoHolder.getCurrentUserId());
        wsCost.setCreateTime(new Date());
        wsCost.setFillUser(UserInfoHolder.getCurrentUserName());
        wsCost.setFillUserId(UserInfoHolder.getCurrentUserId());
        wsCost.setFillDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
        wsCost.setFillDeptId(UserInfoHolder.getCurrentUserInfo().getDeptId());
        wsCost.setCostStatus(IndexEnum.ZERO.getValue());
        return mapper.insert(wsCost);
    }

    /**
     * 修改
     *
     * @param wsCost
     * @return
     */
    public Integer modifyWsCost(WsWsCost wsCost) {
        wsCost.setUpdateBy(UserInfoHolder.getCurrentUserId());
        wsCost.setUpdateTime(new Date());
        Example example = new Example(WsWsCost.class);
        example.createCriteria()
                .andEqualTo("pkWsCostId", wsCost.getPkWsCostId());
        return mapper.updateByExampleSelective(wsCost, example);
    }


}
