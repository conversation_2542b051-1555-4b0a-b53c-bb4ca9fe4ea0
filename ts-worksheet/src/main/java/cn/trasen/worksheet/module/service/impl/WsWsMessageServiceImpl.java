package cn.trasen.worksheet.module.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.util.MyBeanUtils;
import cn.trasen.worksheet.module.dto.outVo.WsWsMessageListOutVo;
import cn.trasen.worksheet.module.entity.WsWsMessage;
import cn.trasen.worksheet.module.mapper.WsWsMessageMapper;
import cn.trasen.worksheet.module.service.WsWsMessageService;

/**
 * <AUTHOR>
 * @date: 2021/8/27 17:25
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Service
public class WsWsMessageServiceImpl implements WsWsMessageService {
    @Autowired
    private WsWsMessageMapper wsWsMessageMapper;

    @Override
    public int insertMessage(WsWsMessage wsWsMessage) {
        return wsWsMessageMapper.insertMessage(wsWsMessage);
    }

    /**
     * 置为已读
     * @param pkWsMessageId 消息id
     * @return
     */
    @Transactional
    @Override
    public int updateMessage(String pkWsMessageId) {
        if (IndexEnum.ZERO.getValue() == wsWsMessageMapper.updateMessage(pkWsMessageId)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }

    @Transactional
    @Override
    public int updateMessageAllByFkUserId() {
        return wsWsMessageMapper.updateMessageAllByFkUserId(UserInfoHolder.getCurrentUserId());
    }

    /**
     * 分页列表
     * @param page
     * @param isRead
     * @return
     */
    @Override
    public List<WsWsMessageListOutVo> selectMessagePageList(Page page, int isRead) {
        return wsWsMessageMapper.selectMessagePageList(page,UserInfoHolder.getCurrentUserId(), isRead);
    }

    @Override
    public List<WsWsMessageListOutVo> selectMessageAllList(String userId, int isRead) {
        return wsWsMessageMapper.selectMessageAllList(userId,isRead);
    }


    @Override
    public WsWsMessage selectOneById(String pkWsMessageId) {
        return Optional.ofNullable(wsWsMessageMapper.selectOneById(pkWsMessageId))
                .map(WsWsMessage::get)
                .orElseThrow(() -> new BusinessException("未查询到消息"));
    }

    /**
     * 查看详情
     * @param pkWsMessageId 消息id
     * @param isRead 是否已读标记 0否1是
     * @return
     */
    @Transactional
    @Override
    public WsWsMessageListOutVo selectOneWsWsMessageListOutVoById(String pkWsMessageId,int isRead) {
        // 置为已读
        if(IndexEnum.ZERO.getValue() == isRead){
            updateMessage(pkWsMessageId);
        }
        WsWsMessageListOutVo messageListOutVo = new WsWsMessageListOutVo();
        MyBeanUtils.copyBeanNotNull2Bean(selectOneById(pkWsMessageId),messageListOutVo);
        return messageListOutVo;
    }

    /**
     * 查询待派单，待接单消息推送时间
     * @param workNumber 工单好
     * @param taskName 节点信息
     * @return
     */
    @Override
    public List<Map<String, Object>> selectOneDpdDjdByWorkNumber(List<String> workNumber, String taskName) {
        if (CollectionUtil.isEmpty(workNumber)){
            return null;
        }
        return wsWsMessageMapper.selectOneDpdDjdByWorkNumber(workNumber, taskName);
    }

	@Override
	public List<EmployeeResp> getSchedule(String deptId) {
		return wsWsMessageMapper.getSchedule(deptId,DateUtil.today() + " 00:00:00",DateUtil.today() + " 23:59:59");
	}
    
}
