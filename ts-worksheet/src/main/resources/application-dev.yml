appconfig:
  whiteUrlList:
    - "/static/"
    - "/swagger"
    - "/springfox-swagger-ui"
    - "/v2/api-docs"
    - "/favicon.ico"
    - "/product/catalog/update/buyCount/"
    - "/hardware/productInfo/update/"
    - "/doc.html"
    - "/webjars"
    - "/druid"
    - "/swagger-resources"
    - "/globalSetting/getSafeGlobalSetting"
    - "/globalSetting/getGlobalSetting"
    - "/messagewebsocket/"
    - "/sysLogs/save"
    - "/fileAttachment/readFile"
    - "/user/login"
    - "/dictItem/getDictItemByTypeCode"
    - "/fileAttachment/downloadFile/"
    - "/fileAttachment/openUpload"
    - "/fileAttachment/openUpload"
    - "/fileAttachment/getFileAttachmentByBusinessId"
    - "/commemployeesho/ref"
    - "/syncplatform/syncorg"
    - "/verify/captcha"
    - "/verify/verifyCode"
    - "/loginLogs/save"
    - "/leaderBigScreenStatistics/"
    - "/workSheet/print/"
    - "/OMInfo"
    
    
  whiteParamsList:
    - "imageBase64"

  defaultRoleCode: "00E8EFF2705D46A18D4251C5961744F6"
  ygtwqtsydwCode: "338227434429353984"
  attachment:
    savePath: /Data/ts-homs/data/attachment/
    saveOpenPath: /Data/ts-homs/data/attachment/
#    savePath: D:\Data\attachment\
#    saveOpenPath: D:\Data\attachment\
    allowFileExtension: "jpeg,jpg,gif,bmp,png,txt,ppt,pptx,xls,xlsx,doc,docx,pdf,wav,mhtml"
    allowFileExtensionOpen: "jpeg,jpg,gif,bmp,png,mhtml"
    realPath: "/ts-basics-bottom/"
    realResPath: "/attachment/"
    
logging:
 config: classpath:logback-custom.xml
pagehelper:
  helperDialect: mysql
  params: count=countSql
  reasonable: true
  supportMethodsArguments: true

server:
  port: 9835
  servlet:
    context-path: /ts-worksheet

#注册中心，通过切换type的值，来实现切换不同的注册中心
registry:
# type: nacos
 type: eureka
#Eureka注册中心配置 
eureka:
 client:
  service-url:
   defaultZone: http://**************:8761/eureka
  #register-with-eureka: false
  #fetch-registry: true
   enabled: true
 instance:
  ip-address: ${spring.cloud.client.ip-address}
  instance-id: ${spring.cloud.client.ip-address}:${server.port}
  prefer-ip-address: true
  
spring:
  application:
    name: ts-worksheet
  cloud:
    nacos:
      config:
        server-addr: **************:8848 #配置中心地址
        file-extension: yml  #指定yaml格式的配置
        auto-refresh: true # 是否启用动态刷新配置
        #        namespace: e722f520-5fd7-41b8-9f02-dd4df0548bdc # 命名空间
        encode: utf-8 # 编码
        enabled: true
      discovery:
        server-addr: **************:8848 #服务注册中心地址
        #        namespace: e722f520-5fd7-41b8-9f02-dd4df0548bdc # 命名空间
        # 是否开启Nacos注册
        enabled: false
        register-enabled: true
  main:
    allow-bean-definition-overriding: true
    banner-mode: off
  datasource:
#   type: com.zaxxer.hikari.HikariDataSource
   type: com.alibaba.druid.pool.DruidDataSource
   url: jdbc:mysql://**************:3306/ts_wo?zeroDateTimeBehavior=CONVERT_TO_NULL&useUnicode=true&characterEncoding=utf8&autoReconnect=true&allowMultiQueries=true&noAccessToProcedureBodies=true&serverTimezone=Asia/Shanghai
   username: root
   password: password
   driver-class-name: com.mysql.cj.jdbc.Driver
#   driver-class-name: com.kingbase8.Driver
#   url: ****************************************************************
#   username: system
#   password: Trasen_123
   druid:
    initialSize: 1
    min-idle: 1
    max-active: 40
    keep-alive: true
    max-wait: 60000
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 60000
    validation-query: select 1 
    validation-query-timeout: 1
    test-while-idle: true
    test-on-borrow: false
    test-on-return: false
    connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    use-global-data-source-stat: true
    remove-abandoned: false
    remove-abandoned-timeout: 180
    pool-prepared-statements: true
    max-open-prepared-statements: 50
    #aop-patterns: cn.trasen.*
    filters: stat,slf4j
    stat-view-servlet:
     enabled: true
     login-username: xtbg
     login-password: 123456@Xtbg
     allow: "*"
    web-stat-filter: #web监控
     enabled: true
     url-pattern: /*
     exclusions: '*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*'
    filter:
     stat: #sql监控
      slow-sql-millis: 1000
      log-slow-sql: true
      enabled: true
      db-type: mysql
     wall: #防火墙
      enabled: false
      db-type: mysql
     config:
        drop-table-allow: false
     
#    type: com.zaxxer.hikari.HikariDataSource
#    hikari:
#      jdbc-url: *****************************************************************************************************************************************************************************************************************************
#      driver-class-name: com.mysql.cj.jdbc.Driver
#      password: 123456
#      username: root
#      connection-timeout: 30000
#      auto-commit: true
#      max-lifetime: 1800000
#      pool-name: DatebookHikariCP
#      minimum-idle: 5
#      connection-test-query: SELECT 1
#      idle-timeout: 30000
#      maximum-pool-size: 103
  http:
    multipart:
      max-file-size: 300MB
      max-request-size: 300MB
  redis:
    database: 0
    host: **************
    jedis:
      pool:
        max-active: 8
        max-idle: 8
        max-wait: -1ms
        min-idle: 0
    password: 123456
    port: 6379
    timeout: 5000
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
#      location: D:\Data\tmp
      location: /Data/ts-homs/data/tmp
#  jackson:
#    serialization:
#      # 某些类对象无法序列化的时候，是否报错
#      fail_on_empty_beans: true
#    deserialization:
#       # json对象中有不存在的属性时候，是否报错
#      fail_on_unknown_properties: true 

springfox:
  base-package: cn.trasen
  description: TS WorkSheet Interface Manager
  enable: true
  service-url: http://localhost:9033/ts-hrms
  title: 数字化运营平台工单系统
  version: 1.0.1


mybatis:
  config-location: classpath:mybatis-config.xml
  mapper-locations: 
    - classpath*:mapper/*.xml
    - classpath*:/**/mapper/*.xml

feign: 
  client: 
    config: 
      default: 
        connectTimeout: 10000 # 连接超时时间，单位为毫秒
        readTimeout: 10000 # 读取超时时间，单位为毫秒


#security:
#  xss:
#    enabled: true
#    whiteUrlList:
#      - "/static/"
#  queryToken:
#    enabled: true


sso:
  defaultPrikey: MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAseG6INcVyA1vr4PpSr7tojShqTPdq86NnHTjU6QEqpLhKH8hQJdwDRW1tlG4svFPf7kOvy8c3+x4vZc0xywdEwIDAQABAkAT4QIIAYFxpe7BUqCTtdqgsfkPC7jOJns07Osqwb2zwh6pU9KUyk0O5yp6CNfHTGcH3THnYbI1nTyzKqqJnfQBAiEA2uUSg22bat6O5UIlEjVQrsB6hcecwRC63rwRSV7JUYECIQDQCOUjvq44CDWsesT8DVmpIGhHGCmVBHt7gyc0qRpQkwIgHnbdIb+Cbtg0qQGQqT0UUo3lP3MthM0wRMmF2mE/wYECIEg7Pmw50b3sw84eVnT5oa8CbJJ6xj1ScBmDgUJckKF3AiBQeY8dS/XSvcETlq/KVA4nF8ksV2Yx1sFdA4K+1F8+rg==
  defaultPubkey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALHhuiDXFcgNb6+D6Uq+7aI0oakz3avOjZx041OkBKqS4Sh/IUCXcA0VtbZRuLLxT3+5Dr8vHN/seL2XNMcsHRMCAwEAAQ==
#  domain: http://**************:8083
  domain: http://**************:8083
  loginUrl: ${sso.domain}/page/login
  verifyUrl: ${sso.domain}/user/verify
  
#修改密码同步到集成平台
changePwdByUserCodePlatformUrl: 

#同步his2.0人员数据开关
syncHis2Switch: 0
hospitalId: 368416827372064768
his2driver: com.microsoft.sqlserver.jdbc.SQLServerDriver
his2url: ******************************************************************************************
his2username: ts_user
his2pwd: hsry@123

#集成平台同步数据开关(0 关闭   1开启)
platformSyncSwitch: 0
#新增人员
platformAddEmp: http://192.168.208.18:10010/service/call/OuterMasterDataService/AddMember
#更新人员
platformUpdateEmp: http://192.168.208.18:10010/service/call/OuterMasterDataService/UpdateMember
#新增科室
platformAddOrg: http://192.168.208.18:10010/service/call/OuterMasterDataService/AddDepartment
#修改科室
platformUpdateOrg: http://192.168.208.18:10010/service/call/OuterMasterDataService/UpdateDepartment
# 医院名称
hospitalName: 堡垒机主线测试环境
# 医院编码
hospitalCode: BLJZXCSHJ
#平台秘钥 
platformSign: b23d251a918474d91ddfa5d462f21880547ec684
#平台同步appId
platformAppId: xtbg

#同步到his版本 2  或者 5
syncHis: 2

#同步his2.0接口地址 （人员新增）
syncHisAddEmpUrl: http://*********:8094/api/Bas/CallHisQueryApiServer

#his 接口参数
syncBhfyHis5Param: '{"ServiceName":"OuterBaseDataService","Parameter":{"hospCode":"BHFY","isGetMemberDept":"1","memberRole":"1","orgCode":"BHFY","pageIndex":1,"pageSize":9999,"status":"1"},"InterfaceName":"QueryMember"}'
#北海妇幼同步his接口地址
syncBhfyHis5Address: http://************:8888/ts-hsb/api/ifcall/671612582171258880/ksyg1m7oh0@d

smsProxySwitch: 0  #短信开关
smsconfig:
  accessReport: 1
  accessUploadMsg: 1
  account: N052120_N3696351
  balanceUrl: http://smsbj1.253.com/msg/balance/json
  password: IB8lt25fcD477a
  pullCount: 20
  pullUrl: http://smsbj1.253.com/msg/pull/mo
  reportCount: 20
  reportTimeMin: '*'
  reportTimeSec: 17
  reportUrl: http://smsbj1.253.com/msg/pull/report
  sendTimeMin: '*'
  sendTimeSec: 15
  sendUrl: http://smsbj1.253.com/msg/send/json
  sendWithVarsUrl: http://smsbj1.253.com/msg/variable/json
  uploadTimeMin: '*'
  uploadTimeSec: 16
  useWebService: 0
wechat:
  cp:
    aesKey: 
    agentId: 1000022
    corpId: ww2c74fea7da3630e9
    secret: MNC1SI20eYxropwbsXDqh3aUQGgZiasA31oWaTtmvd0
    token: 
    url: http://testxtbg.trasen.cn/ts-information
    wxUrl: http://testxtbg.trasen.cn/mobile-container/workbench
    #wxUrl: http://testxtbg.trasen.cn/trasen-wxoa/#/pages/index/index

wxSwitch: 1   #消息推送开关

wx:
  loginPage: http://testxtbg.trasen.cn/mobile-container/login
  loginUrl: http://testxtbg.trasen.cn/mobile-container/ts-mobile-oa/
  #loginPage: http://testxtbg.trasen.cn/trasen-wxoa/#/pages/login/login
  #loginUrl: http://testxtbg.trasen.cn/trasen-wxoa/
pc:
  indexPage: http://**************:9088/container/index
  loginPage: http://**************:9088/container/login
  emailPage: http://**************:9088/container/email/emailManagement
  informationPage: http://**************:9088/container/information/messageRelease
 
# 钉钉的配置
wxOrDingtalk: 0   #1 代表钉钉部署
dingtalk :
  redisTokenKey:   #钉钉assessToken key
  app_key: 
  app_secret: 
  agent_id: 
  corp_id: 
  url: http://testxtbg.trasen.cn/ts-information
  
information-workflow-no: L_00008
verifyurl:     
notify-url-header: http://127.0.0.1:9088

#是否跳过自己审批
skipSelfStep: 1
#流程日报提醒开关
dailyReminder: 1

# 自动评价天数
evaluate: 1

# 定时器
scheduling:
  businessJob:
    enabled: true
    evaluation: 0 0 3 * * ?
    #    evaluation: 0 0/2 * * * ?
    omFile: 0 0/2 * * * ?
    workReport: 0 30 18 * * ?
    #    workReport: 0 0/1 * * * ?
    unprocessedMessageAlertsdpd: 0 0/1 8-18 ? * *
    unprocessedMessageAlertsdjd: 0 0/1 8-18 ? * *
    fillFileWorkNumberInfo: 0 0/2 * * * ?
  sequenceJob:
    enabled: true
    cron: 0 0 2 * * ?


# 待派单、待接单提醒推送时间，毫秒数
unprocessedMessageAlerts:
  dpdTime: 60000
  djdTime: 60000

# 文件服务
fileServer:
  ipHost: http://**************:9088

# OM硬件
om:
  ip: **************
  host: 80
  filePassWord: user:user
  fileUrl: /usb/builtin/Recorder/

# 外部人员默认信息
externalPersonnel:
  defaultPassword: 123456
  ORG_ID: 417601884963041280
  ROLE_CODE: A6E028297D4849ABB59614534D7AB365

# 报单消息推送，是否根据排班，0否直接推送当前科室所有人，1根据排班
scheduling_enable_disable: 0

# 钉钉还是微信,微信为1，钉钉为0
thirdPartyComponents: 1

# 钉钉扫码跳转中间页面url
dingTalkUrl: http://testxtbg.trasen.cn/mobile-container/workSheet/dingTalkRouterChange/index?corpId=ding7e10f57d18013d0df2c783f7214b6d69

# 企业微信跳转、访问Url
oaUrl:
  faultEquipmentQrCodeUrl: http://testxtbg.trasen.cn/mobile-container/ts-mobile-work-order/pages/equipment-specification/index
  workReportUrl: ts-mobile-work-order/pages/work-order-daily/index
  scanQrCodeWxUrl: http://testxtbg.trasen.cn/ts-information/cp/weixin/wxOAuth2Url
  scanQrCodeUrl: ts-mobile-work-order/pages/work-order-reporting/index?fromPage=workBench&index=0
  workInfoUrl: http://testxtbg.trasen.cn/mobile-container/ts-mobile-work-order/pages/work-order-detail/index
  dpdUrl: http://testxtbg.trasen.cn/mobile-container/ts-mobile-work-order/pages/work-order-reception/index?fromPage=workBench&index=待派单
  djdUrl: http://testxtbg.trasen.cn/mobile-container/ts-mobile-work-order/pages/work-order-reception/index?fromPage=workBench&index=处理中

# 超级管理员用户
superPeople:
  id: admin
  name: admin
