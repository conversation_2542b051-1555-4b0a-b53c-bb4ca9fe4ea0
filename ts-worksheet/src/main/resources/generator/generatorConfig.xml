<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
	<properties resource="application.yml" />

	<context id="Mysql" targetRuntime="MyBatis3Simple">
		<property name="javaFileEncoding" value="UTF-8" />
		<property name="javaFormatter" value="org.mybatis.generator.api.dom.DefaultJavaFormatter" />
		<property name="xmlFormatter" value="org.mybatis.generator.api.dom.DefaultXmlFormatter" />

		<plugin type="cn.trasen.mapper.generate.plugins.SwaggerMapperPlugin">
			<property name="mappers" value="tk.mybatis.mapper.common.Mapper" />
		</plugin>

		<commentGenerator>
			<property name="suppressDate" value="true" />
			<property name="suppressAllComments" value="true" />
		</commentGenerator>
		<jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
						connectionURL="*************************************"
						userId="root"
						password="123456">
		</jdbcConnection>
<!--		<jdbcConnection driverClass="${spring.datasource.driver-class-name}"-->
<!--			connectionURL="${spring.datasource.url}" userId="${spring.datasource.username}"-->
<!--			password="${spring.datasource.password}">-->
<!--		</jdbcConnection>-->
		
		<javaTypeResolver>
            <property name="forceBigDecimals" value="false" />
        </javaTypeResolver>
		
		<javaModelGenerator targetPackage="cn.trasen.worksheet.module.entity"
			targetProject="src/main/java" />
		<sqlMapGenerator targetPackage="mapper" targetProject="src/main/resources" />
		<javaClientGenerator targetPackage="cn.trasen.worksheet.module.mapper"
			targetProject="src/main/java" type="XMLMAPPER" />
		<table tableName="ws_ws_cost"></table>
	</context>
</generatorConfiguration>