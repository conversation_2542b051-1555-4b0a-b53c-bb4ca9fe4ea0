<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.SequenceMapper">


  <insert id="createSequence">
    INSERT INTO ws_number_sequence VALUES ('ws_number_sequence', 0, 1)
  </insert>
  <select id="getSequence" resultType="java.lang.Integer">
    SELECT NEXTVAL('ws_number_sequence')
  </select>

  <delete id="deleteSequence">
    delete from ws_number_sequence
  </delete>
  
  <select id="selectSequence" resultType="java.lang.Long" parameterType="String">
  	select count(1) from ws_ws_sheet
	where work_number = #{sequence}
  </select>
</mapper>