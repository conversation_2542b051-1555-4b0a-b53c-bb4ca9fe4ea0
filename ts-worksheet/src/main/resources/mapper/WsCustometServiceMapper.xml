<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsCustometServiceMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsCustometService">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="pk_customet_service_id" jdbcType="VARCHAR" property="pkCustometServiceId"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delete_status" jdbcType="TINYINT" property="deleteStatus"/>
        <result column="play_screen" jdbcType="TINYINT" property="playScreen"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="equipment" jdbcType="VARCHAR" property="equipment"/>
        <result column="fk_user_id" jdbcType="VARCHAR" property="fkUserId"/>
    </resultMap>

    <sql id="custometServiceColums">
        pk_customet_service_id
        ,
      create_by,
      create_time,
      update_by,
      update_time,
      delete_status,
      play_screen,
      play_voice,
      phone,
      customet_service_status,
      equipment,
      fk_user_id,
      fk_user_dept_id
    </sql>
    <insert id="saveCustometService">
        insert into ws_customet_service(<include refid="custometServiceColums"/>)
        values (
        #{pkCustometServiceId},
        #{createBy},
        #{createTime},
        #{updateBy},
        #{updateTime},
        #{deleteStatus},
        #{playScreen},
        #{playVoice},
        #{phone},
        #{custometServiceStatus},
        #{equipment},
        #{fkUserId},
        #{fkUserDeptId}
        )
    </insert>
    <update id="updateCustometService">
        update ws_customet_service
        set update_by       = #{updateBy},
            update_time     = #{updateTime},
            delete_status   = #{deleteStatus},
            play_screen     = #{playScreen},
            play_voice     = #{playVoice},
            phone           = #{phone},
        customet_service_status          = #{custometServiceStatus},
            equipment       = #{equipment},
            fk_user_dept_id = #{fkUserDeptId}
        where pk_customet_service_id = #{pkCustometServiceId}
    </update>
    <update id="updateOther">
        update ws_customet_service
        set update_by       = #{updateBy},
            update_time     = #{updateTime},
        customet_service_status = 0
        where phone = #{phone} and play_screen = 1 and customet_service_status = 1 and delete_status = 0
    </update>

    <select id="selectOneById" resultType="cn.trasen.worksheet.module.entity.WsCustometService">
        select
        <include refid="custometServiceColums"/>
        from ws_customet_service
        where pk_customet_service_id = #{pkCustometServiceId} and delete_status = 0
    </select>

    <select id="selectListByDeptId" resultType="cn.trasen.worksheet.module.entity.WsCustometService">
        select
        <include refid="custometServiceColums"/>
        from ws_customet_service
        where fk_user_dept_id
        in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        and delete_status = 0 and customet_service_status = 1 and play_screen = 1
    </select>
    <select id="selectOneByFkUserId" resultType="cn.trasen.worksheet.module.entity.WsCustometService">
        select
        <include refid="custometServiceColums"/>
        from ws_customet_service
        where fk_user_id = #{fkUserId} and delete_status = 0
    </select>
    <select id="selectOneCustometServiceByPhone"
            resultType="cn.trasen.worksheet.module.entity.WsCustometService">
        select
        <include refid="custometServiceColums"/>
        from ws_customet_service
        where phone = #{phone} and play_screen = 1 and customet_service_status = 1
    </select>
    <select id="getOneCustometServiceStatus" resultType="cn.trasen.worksheet.module.entity.WsCustometService">
        select
        <include refid="custometServiceColums"/>
        from ws_customet_service
        where phone = #{phone} and fk_user_id != #{fkUserId} and play_screen = 1 and customet_service_status = 1 and delete_status = 0
    </select>
    <select id="selectListByFkUserId" resultType="cn.trasen.worksheet.module.entity.WsCustometService">
        select
        <include refid="custometServiceColums"/>
        from ws_customet_service
        where fk_user_id
        in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        and delete_status = 0
        <if test="null != custometServiceStatus">
            and customet_service_status = #{custometServiceStatus}
        </if>
        <if test="null != playScreen">
            and play_screen = #{playScreen}
        </if>
    </select>
</mapper>