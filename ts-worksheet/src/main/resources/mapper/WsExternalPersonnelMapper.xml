<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsExternalPersonnelMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsExternalPersonnel">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="pk_external_personnel_id" jdbcType="VARCHAR" property="pkExternalPersonnelId"/>
        <result column="institutional_affiliations" jdbcType="VARCHAR" property="institutionalAffiliations"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delete_status" jdbcType="TINYINT" property="deleteStatus"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="user_password" jdbcType="VARCHAR" property="userPassword"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="position" jdbcType="VARCHAR" property="position"/>
        <result column="job_deion" jdbcType="VARCHAR" property="jobDeion"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="belongs_dept_id" jdbcType="VARCHAR" property="belongsDeptId"/>
        <result column="belongs_dept_name" jdbcType="VARCHAR" property="belongsDeptName"/>
    </resultMap>

    <sql id="externalPersonnelColums">
        pk_external_personnel_id,
        institutional_affiliations,
        create_by,
        create_time,
        update_by,
        update_time,
        delete_status,
        user_name,
        user_password,
        phone,
        position,
        job_deion,
        status,
        belongs_dept_id,
        belongs_dept_name
    </sql>
    <insert id="insertExternalPersonnel">
        insert into ws_external_personnel
        values(
        #{pkExternalPersonnelId},
        #{institutionalAffiliations},
        #{createBy},
        #{createTime},
        #{updateBy},
        #{updateTime},
        #{deleteStatus},
        #{userName},
        #{userPassword},
        #{phone},
        #{position},
        #{jobDeion},
        #{status},
        #{belongsDeptId},
        #{belongsDeptName}
        )


    </insert>
    <update id="updateExternalPersonnel">
        update ws_external_personnel set
        institutional_affiliations = #{institutionalAffiliations},
        update_by = #{updateBy},
        update_time = #{updateTime},
        delete_status = #{deleteStatus},
        user_name = #{userName},
        user_password = #{userPassword},
        phone = #{phone},
        position = #{position},
        job_deion = #{jobDeion},
        status = #{status},
        belongs_dept_id = #{belongsDeptId},
        belongs_dept_name = #{belongsDeptName}
        where pk_external_personnel_id = #{pkExternalPersonnelId}
    </update>

    <select id="selectOneById" resultType="cn.trasen.worksheet.module.entity.WsExternalPersonnel">
        select
        <include refid="externalPersonnelColums"/>
        from ws_external_personnel
        where pk_external_personnel_id = #{pkExternalPersonnelId}
    </select>

    <select id="selectOneByIds" resultType="cn.trasen.worksheet.module.entity.WsExternalPersonnel">
        select
        <include refid="externalPersonnelColums"/>
        from ws_external_personnel
        where pk_external_personnel_id in
        (
        <foreach collection="list" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>

    <select id="selectOneByPhone" resultType="cn.trasen.worksheet.module.entity.WsExternalPersonnel">
        select
        <include refid="externalPersonnelColums"/>
        from ws_external_personnel
        where phone = #{phone}
    </select>


    <select id="selectPageList"
            resultType="cn.trasen.worksheet.module.dto.outVo.ExternalPersonnelPageListOutVo">
        select
        <include refid="externalPersonnelColums"/>
        from ws_external_personnel
        where delete_status = 0
        <if test="null != fuzzy and ''!= fuzzy">
            and (institutional_affiliations like concat('%',#{fuzzy},'%')
            or user_name like concat('%',#{fuzzy},'%'))
        </if>
        <if test="null != deptId and ''!= deptId">
            and belongs_dept_id = #{deptId}
        </if>
    </select>

    <select id="selectAllList"
            resultType="cn.trasen.worksheet.module.dto.outVo.ExternalPersonnelPageListOutVo">
        select
        <include refid="externalPersonnelColums"/>
        from ws_external_personnel
        where delete_status = 0
        <if test="null != fuzzy and ''!= fuzzy">
            and (institutional_affiliations like concat('%',#{fuzzy},'%')
            or user_name like concat('%',#{fuzzy},'%'))
        </if>
        <if test="null != deptId and ''!= deptId">
            and belongs_dept_id = #{deptId}
        </if>
    </select>

    <select id="selectAllInstitutionalAffiliations" resultType="java.util.Map">
        SELECT DISTINCT institutional_affiliations FROM ws_external_personnel
        where delete_status = 0
        <if test="null != institutionalAffiliations and ''!=institutionalAffiliations">
           and institutional_affiliations like concat('%',#{institutionalAffiliations},'%')
        </if>
    </select>

    <select id="selectAllPosition" resultType="java.util.Map">
        SELECT DISTINCT position FROM ws_external_personnel
        where delete_status = 0
        <if test="null != position and ''!=position">
            and position like concat('%',#{position},'%')
        </if>
    </select>
    <select id="selectAllListByDeptId" resultType="cn.trasen.worksheet.module.entity.WsExternalPersonnel">
        select
        <include refid="externalPersonnelColums"/>
        from ws_external_personnel
        where delete_status = 0 and belongs_dept_id = #{deptId} and status =1

    </select>

</mapper>