<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsFaultCommonMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsFaultCommon">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="pk_fault_common_id" jdbcType="VARCHAR" property="pkFaultCommonId" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delete_status" jdbcType="TINYINT" property="deleteStatus" />
    <result column="falut_deion" jdbcType="VARCHAR" property="falutDeion" />
    <result column="fk_user_id" jdbcType="VARCHAR" property="fkUserId" />
  </resultMap>

  <sql id="faultCommonColums">
    pk_fault_common_id,
    create_by,
    create_time,
    update_by,
    update_time,
    delete_status,
    falut_deion,
    fk_user_id
  </sql>
    <insert id="insertFaultCommn">
        insert into ws_fault_common (<include refid="faultCommonColums" />)
        values(
            #{pkFaultCommonId},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime},
            #{deleteStatus},
            #{falutDeion},
            #{fkUserId}
        )
    </insert>
    <update id="updateById">
    update ws_fault_common
        set
            update_by = #{updateBy},
            update_time = #{updateTime},
            delete_status = #{deleteStatus},
            falut_deion = #{falutDeion}
    where pk_fault_common_id = #{pkFaultCommonId}

  </update>
  <delete id="deleteById">
    update ws_fault_common
        set
            delete_status = 1
    where pk_fault_common_id = #{pkFaultCommonId}
  </delete>
    <delete id="deleteAllByIds">
        update ws_fault_common
        set
        delete_status = 1
        where pk_fault_common_id in
        (
        <foreach collection="list" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )

    </delete>


    <select id="selectOneById" resultType="cn.trasen.worksheet.module.entity.WsFaultCommon">
    select
        <include refid="faultCommonColums" />
        from ws_fault_common
        where pk_fault_common_id = #{id}
  </select>
  <select id="selectFaultCommonAllList" resultType="cn.trasen.worksheet.module.dto.outVo.FaultCommonOutVo">
    select
        <include refid="faultCommonColums" />
        from ws_fault_common
        where fk_user_id = #{fkUserId} and delete_status = 0
        <if test="null != falutDeion and '' != falutDeion">
            and falut_deion like concat('%',#{falutDeion},'%')
        </if>
        order by create_time desc

  </select>
    <select id="selectFaultCommonPageList" resultType="cn.trasen.worksheet.module.dto.outVo.FaultCommonOutVo">
        select
        <include refid="faultCommonColums" />
        from ws_fault_common
        where delete_status = 0 and fk_user_id = #{fkUserId}
        <if test="null != falutDeion and '' != falutDeion">
            and falut_deion like concat('%',#{falutDeion},'%')
        </if>
        order by create_time desc
    </select>

</mapper>