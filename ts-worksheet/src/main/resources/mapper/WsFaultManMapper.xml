<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsFaultManMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsFaultMan">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="fk_fault_type_id" jdbcType="VARCHAR" property="fkFaultTypeId"/>
        <result column="fk_user_id" jdbcType="VARCHAR" property="fkUserId"/>
        <result column="fk_user_name" jdbcType="VARCHAR" property="fkUserName"/>
        <result column="fk_user_dept_id" jdbcType="VARCHAR" property="fkUserDeptId"/>
        <result column="fk_user_dept_name" jdbcType="VARCHAR" property="fkUserDeptName"/>
    </resultMap>
    <sql id="faultManColums">
        fk_fault_type_id,
        fk_user_id,
        fk_user_name,
        fk_user_dept_id,
        fk_user_dept_name
    </sql>

    <insert id="insertBatch">
      INSERT INTO ws_fault_man
      ( <include refid="faultManColums" />)
      VALUES
      <foreach collection="list" item="item" separator=",">
        (
        #{item.fkFaultTypeId},
        #{item.fkUserId},
        #{item.fkUserName},
        #{item.fkUserDeptId},
        #{item.fkUserDeptName}
        )
      </foreach>
    </insert>
    <delete id="deleteFaultMan">
        delete from ws_fault_man where fk_fault_type_id = #{fkFaultTypeId}
    </delete>

</mapper>