<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsHospitalDistrictMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsHospitalDistrict">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="pk_hospital_district_id" jdbcType="VARCHAR" property="pkHospitalDistrictId" />
    <result column="hospital_district_name" jdbcType="VARCHAR" property="hospitalDistrictName" />
    <result column="hospital_district_status" jdbcType="VARCHAR" property="hospitalDistrictStatus" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delete_status" jdbcType="VARCHAR" property="deleteStatus" />
  </resultMap>
</mapper>