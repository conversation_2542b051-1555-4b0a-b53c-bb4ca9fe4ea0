<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsKnowledgeBaseMapper">
    <resultMap id="WsKnowledgeBaseMap" type="cn.trasen.worksheet.module.entity.WsKnowledgeBase">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="pk_knowledge_base_id" jdbcType="VARCHAR" property="pkKnowledgeBaseId"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_by_name" jdbcType="VARCHAR" property="createByName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_by_name" jdbcType="VARCHAR" property="updateByName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delete_status" jdbcType="TINYINT" property="deleteStatus"/>
        <result column="fk_knowledge_type_id" jdbcType="VARCHAR" property="fkKnowledgeTypeId"/>
        <result column="knowledge_title" jdbcType="VARCHAR" property="knowledgeTitle"/>
        <result column="recommended_work_hours" jdbcType="TINYINT" property="recommendedWorkHours"/>
        <result column="knowledge_status" jdbcType="TINYINT" property="knowledgeStatus"/>
        <result column="review_time" jdbcType="TIMESTAMP" property="reviewTime"/>
        <result column="back_reason" jdbcType="VARCHAR" property="backReason"/>
        <result column="fk_user_id" jdbcType="VARCHAR" property="fkUserId"/>
        <result column="fk_user_name" jdbcType="VARCHAR" property="fkUserName"/>
        <result column="fk_user_dept_id" jdbcType="VARCHAR" property="fkUserDeptId"/>
        <result column="fk_user_dept_name" jdbcType="VARCHAR" property="fkUserDeptName"/>
        <result column="contribution_time" jdbcType="TIMESTAMP" property="contributionTime"/>
        <result column="remove_time" jdbcType="TIMESTAMP" property="removeTime"/>
        <result column="remove_reason" jdbcType="VARCHAR" property="removeReason"/>
        <result column="useful_numbers" jdbcType="TINYINT" property="usefulNumbers"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="knowledge_content" jdbcType="LONGVARCHAR" property="knowledgeContent"/>
    </resultMap>

    <sql id="KnowledgeBaseColums">
        pk_knowledge_base_id,create_by,create_by_name,create_time,
        update_by,update_by_name,update_time,fk_knowledge_type_id,
        knowledge_title,recommended_work_hours,knowledge_content,
        knowledge_status,review_time,back_reason,fk_user_name,fk_user_dept_id,fk_user_dept_name,
        contribution_time,remove_time,remove_reason,remark
    </sql>
    <insert id="insertknowledgeBase">
        insert into ws_knowledge_base
        values (#{pkKnowledgeBaseId},
                #{createBy},
                #{createByName},
                #{createTime},
                #{updateBy},
                #{updateByName},
                #{updateTime},
                #{deleteStatus},
                #{fkKnowledgeTypeId},
                #{knowledgeTitle},
                #{recommendedWorkHours},
                #{knowledgeContent},
                #{knowledgeStatus},
                #{reviewTime},
                #{backReason},
                #{fkUserId},
                #{fkUserName},
                #{fkUserDeptId},
                #{fkUserDeptName},
                #{contributionTime},
                #{removeTime},
                #{removeReason},
                0,
                #{remark})
    </insert>


    <update id="updateknowledgeBase" parameterType="cn.trasen.worksheet.module.entity.WsKnowledgeBase">
        update ws_knowledge_base
        set update_by             = #{updateBy},
            update_by_name=#{updateByName},
            update_time=#{updateTime},
            delete_status=#{deleteStatus},
            fk_knowledge_type_id=#{fkKnowledgeTypeId},
            knowledge_title=#{knowledgeTitle},
            recommended_work_hours=#{recommendedWorkHours},
            knowledge_content=#{knowledgeContent},
            knowledge_status=#{knowledgeStatus},
            review_time=#{reviewTime},
            back_reason=#{backReason},
            contribution_time=#{contributionTime},
            remove_time=#{removeTime},
            remove_reason=#{removeReason},
            remark=#{remark}
        where pk_knowledge_base_id = #{pkKnowledgeBaseId}

    </update>

    <select id="selectOneById" resultMap="WsKnowledgeBaseMap">
        select
        a.delete_status,
        fk_user_id,
        <include refid="KnowledgeBaseColums"/>,
        IFNULL((select count(1) from ws_knowledge_like b where a.pk_knowledge_base_id = b.fk_knowledge_base_id and
        b.delete_status = 0 and fk_user_id = #{userId}),0) islike,
        IFNULL(( SELECT count( 1 ) FROM ws_knowledge_like b WHERE a.pk_knowledge_base_id = b.fk_knowledge_base_id and
        b.delete_status = 0 GROUP BY b.fk_knowledge_base_id ),0) useful_numbers
        from ws_knowledge_base a
        where a.delete_status = 0 and pk_knowledge_base_id = #{pkKnowledgeBaseId}
    </select>
    <select id="selectListByUserId" resultMap="WsKnowledgeBaseMap">
        select
        delete_status,
        fk_user_id,
        <include refid="KnowledgeBaseColums"/>
        from ws_knowledge_base
        where pk_knowledge_base_id = #{pkKnowledgeBaseId}
    </select>
    <select id="selectOneKnowledgeBase" parameterType="cn.trasen.worksheet.module.entity.WsKnowledgeBase"
            resultType="cn.trasen.worksheet.module.entity.WsKnowledgeBase">
        select
        fk_user_id,
        delete_status,
        <include refid="KnowledgeBaseColums"/>
        from ws_knowledge_base
        where delete_status = 0
        <if test="null != fkKnowledgeTypeId and '' != fkKnowledgeTypeId">
            and fk_knowledge_type_id = #{fkKnowledgeTypeId}
        </if>
        <if test="null != knowledgeTitle and '' != knowledgeTitle">
            and knowledge_title = #{knowledgeTitle}
        </if>
    </select>

    <select id="selectPageList" resultType="cn.trasen.worksheet.module.dto.outVo.KnowledgeBasePageOutVo">
        select
        a.fk_user_id,
        a.delete_status,
        <include refid="KnowledgeBaseColums"/>,
        b.category_name fkKnowledgeTypeName,
        IFNULL((select count(1) from ws_knowledge_like b where a.pk_knowledge_base_id = b.fk_knowledge_base_id and
        delete_status = 0 and fk_user_id = #{userId}),0) islike,
        IFNULL(( SELECT count( 1 ) FROM ws_knowledge_like b WHERE a.pk_knowledge_base_id = b.fk_knowledge_base_id and
        b.delete_status = 0 GROUP BY b.fk_knowledge_base_id ),0) useful_numbers
        from ws_knowledge_base a
        join (select pk_knowledge_type_id,category_name from ws_knowledge_type) b on a.fk_knowledge_type_id =
        b.pk_knowledge_type_id
        where a.delete_status = 0
        <choose>
            <!-- 4为列表展示，知识库页签-->
            <when test=' 4 !=knowledgeStatus'>
                and knowledge_status = #{knowledgeStatus}
            </when>
            <otherwise>
                and knowledge_status = 2
            </otherwise>
        </choose>
        <if test="null != fkUserId and '' != fkUserId">
            and a.fk_user_id = #{fkUserId}
        </if>
        <if test="null != fkKnowledgeTypeId and '' != fkKnowledgeTypeId">
            and a.fk_knowledge_type_id in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        </if>
        <if test="null != knowledgeTitle and '' != knowledgeTitle">
            and knowledge_title like concat('%',#{knowledgeTitle},'%')
        </if>
    </select>
    <select id="getMyKnowledgeBaseCount" resultType="java.util.Map">
        select
            (SELECT count(1) FROM ws_knowledge_base where knowledge_status != 0 and fk_user_id = #{fk_user_id} ) ytj,
            (SELECT count(1) FROM ws_knowledge_base where knowledge_status = 1 and fk_user_id = #{fk_user_id} ) sh
        from dual
    </select>
    <select id="getKnowledgeBaseCountByDate" resultType="java.util.Map">
        SELECT
        <!--DATE_FORMAT( contribution_time, '%Y-%m-%d' ) date, -->
        <choose>
            <when test=' 0 == dayOrMonthType'>
                DATE_FORMAT( contribution_time, '%Y-%m-%d' ) date,
            </when>
            <when test=' 1 == dayOrMonthType'>
                DATE_FORMAT( contribution_time, '%Y-%m' ) date,
            </when>
            <otherwise>
                DATE_FORMAT( contribution_time, '%Y' ) date,
            </otherwise>
        </choose>
        count( 1 ) counts
        FROM
        ws_knowledge_base
        WHERE
        knowledge_status = 2
        <choose>
        	<when test="_databaseId=='kingbase'">
		        <if test="null != beginTime and '' != beginTime">
		            AND contribution_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND contribution_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</when>
        	<otherwise>
		        <if test="null != beginTime and '' != beginTime">
		            AND contribution_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND contribution_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</otherwise>
        </choose>
        GROUP BY
        <choose>
            <when test=' 0 == dayOrMonthType'>
                DATE_FORMAT( contribution_time, '%Y-%m-%d' )
            </when>
            <when test=' 1 == dayOrMonthType'>
                DATE_FORMAT( contribution_time, '%Y-%m' )
            </when>
            <otherwise>
                DATE_FORMAT( contribution_time, '%Y' )
            </otherwise>
        </choose>
    </select>

    <select id="getLevelOneKnowledgeBaseTypeDatas" resultType="java.util.Map">
        SELECT a.pk_knowledge_type_id pkKnowledgeTypeId,
        category_name categoryName, count( b.pk_knowledge_base_id ) counts
        FROM  ws_knowledge_type a
        LEFT JOIN ws_knowledge_base b ON a.pk_knowledge_type_id = b.fk_knowledge_type_id
        AND b.knowledge_status = 2
        <choose>
        	<when test="_databaseId=='kingbase'">
		        <if test="null != beginTime and '' != beginTime">
		            AND b.contribution_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND b.contribution_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</when>
        	<otherwise>
		        <if test="null != beginTime and '' != beginTime">
		            AND b.contribution_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND b.contribution_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</otherwise>
        </choose>
        WHERE
        a.delete_status = 0
        AND a.parent_id IS NULL
        GROUP BY
        a.pk_knowledge_type_id
    </select>
	
    <select id="getKnowledgeBaseSubmitTopDatas" resultType="java.util.Map">
        SELECT
        b.fk_user_dept_name fkUserDeptName,
        b.fk_user_name fkUserName,
        b.total
        FROM
        (
        SELECT
        a.fk_user_dept_name,
        a.fk_user_name,
        a.total AS total,
        a.contribution_time contributionTime,
        (
        SELECT
        COUNT( DISTINCT b.total )
        FROM
        (
        SELECT
        max( a.fk_user_dept_name ) fk_user_dept_name,
        max( a.fk_user_name ) fk_user_name,
        count( 1 ) total
        FROM
        ws_knowledge_base a
        WHERE
        knowledge_status NOT IN ( 0,-1 )
        <choose>
        	<when test="_databaseId=='kingbase'">
		        <if test="null != beginTime and '' != beginTime">
		            AND a.contribution_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND a.contribution_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</when>
        	<otherwise>
		        <if test="null != beginTime and '' != beginTime">
		            AND a.contribution_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND a.contribution_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</otherwise>
        </choose>
        GROUP BY
        a.fk_user_id
        ) b
        WHERE
        b.total >= a.total
        ) AS Rankbak
        FROM
        (
        SELECT
        max( a.fk_user_dept_name ) fk_user_dept_name,
        max( a.fk_user_name ) fk_user_name,
        count( 1 ) total,
        max( contribution_time ) contribution_time
        FROM
        ws_knowledge_base a
        WHERE
        knowledge_status NOT IN ( 0,-1 )
        <choose>
        	<when test="_databaseId=='kingbase'">
		        <if test="null != beginTime and '' != beginTime">
		            AND a.contribution_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND a.contribution_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</when>
        	<otherwise>
		        <if test="null != beginTime and '' != beginTime">
		            AND a.contribution_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND a.contribution_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</otherwise>
        </choose>
        GROUP BY
        a.fk_user_id
        ) a
        ) b
        ORDER BY
        b.total DESC,
        b.contributionTime

    </select>

    <select id="selectKnowledgeAllList" resultType="cn.trasen.worksheet.module.dto.outVo.KnowledgeTypeTreeOutVo">
        select
        pk_knowledge_type_id id,
        pk_knowledge_type_id code,
        full_path fullPath,
        parent_id pid,
        category_name name,
        count(b.fk_knowledge_type_id) count
        from ws_knowledge_type a
        left join ws_knowledge_base b on a.pk_knowledge_type_id = b.fk_knowledge_type_id and b.knowledge_status = 2 and
        b.delete_status = 0
        where a.delete_status = 0
        <if test="null != categoryName and '' != categoryName">
            and category_name like concat('%',#{categoryName},'%')
        </if>
        <if test="null != knowledgeStatus and '' != knowledgeStatus">
            and a.knowledge_status = #{knowledgeStatus}
        </if>
        <choose>
        	<when test="_databaseId=='kingbase'">
		        <if test="null != beginTime and '' != beginTime">
		            AND b.contribution_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND b.contribution_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</when>
        	<otherwise>
		        <if test="null != beginTime and '' != beginTime">
		            AND b.contribution_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND b.contribution_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</otherwise>
        </choose>
        GROUP BY a.pk_knowledge_type_id
    </select>

    <select id="selectCountsGroupStatus" resultType="java.util.Map">
        select (select count(1)
        from ws_knowledge_base
        where knowledge_status = '2' and delete_status = 0
        <if test="fkKnowledgeTypeId != null and fkKnowledgeTypeId != ''">
            and fk_knowledge_type_id in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        </if>
        <if test="knowledgeTitle != null and knowledgeTitle != ''">
            and knowledge_title like concat('%',#{knowledgeTitle},'%')
        </if>
        ) zskCounts,
        (select count(1) from ws_knowledge_base where knowledge_status = '1' and delete_status = 0
        <if test="fkKnowledgeTypeId != null and fkKnowledgeTypeId != ''">
            and fk_knowledge_type_id in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        </if>
        <if test="knowledgeTitle != null and knowledgeTitle != ''">
            and knowledge_title like concat('%',#{knowledgeTitle},'%')
        </if>
        <if test="admin == null or admin == ''">
            and fk_user_id = #{fkUserId}
        </if>) shzCounts,

        (select count(1) from ws_knowledge_base where knowledge_status = '-1' and delete_status = 0
        <if test="fkKnowledgeTypeId != null and fkKnowledgeTypeId != ''">
            and fk_knowledge_type_id in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        </if>
        <if test="knowledgeTitle != null and knowledgeTitle != ''">
            and knowledge_title like concat('%',#{knowledgeTitle},'%')
        </if>
        <if test="admin == null or admin == ''">
            and fk_user_id = #{fkUserId}
        </if>) wtgCounts,

        (select count(1) from ws_knowledge_base where knowledge_status = '3' and delete_status = 0
        <if test="fkKnowledgeTypeId != null and fkKnowledgeTypeId != ''">
            and fk_knowledge_type_id in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        </if>
        <if test="knowledgeTitle != null and knowledgeTitle != ''">
            and knowledge_title like concat('%',#{knowledgeTitle},'%')
        </if>
        <if test="admin == null or admin == ''">
            and fk_user_id = #{fkUserId}
        </if>) yycCounts,

        (select count(1) from ws_knowledge_base where knowledge_status = '0' and delete_status = 0
        <if test="fkKnowledgeTypeId != null and fkKnowledgeTypeId != ''">
            and fk_knowledge_type_id in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        </if>
        <if test="knowledgeTitle != null and knowledgeTitle != ''">
            and knowledge_title like concat('%',#{knowledgeTitle},'%')
        </if>
        AND fk_user_id = #{fkUserId}
        ) cgxCounts
        from dual
    </select>
    <select id="knowledgeLikeRank"
            resultType="cn.trasen.worksheet.module.dto.outVo.KnowledgeBaseMobileInfoOutVo">
        select
               a.review_time,
               a.pk_knowledge_base_id,
               a.knowledge_title,
               c.full_path fkKnowledgeTypeName,
               a.fk_user_dept_name,
               a.fk_user_name,
               a.recommended_work_hours,
               count(1) usefulNumbers,
               contribution_time
        from ws_knowledge_base a
            left join ws_knowledge_like b on a.pk_knowledge_base_id = b.fk_knowledge_base_id
            left join ws_knowledge_type c on a.fk_knowledge_type_id = c.pk_knowledge_type_id
        where b.fk_knowledge_base_id is not null
        and a.knowledge_status = 2
          and b.delete_status = 0
        <if test="null != fkUserDeptId and ''!=fkUserDeptId">
            and a.fk_user_dept_id = #{fkUserDeptId}
        </if>
        <choose>
        	<when test="_databaseId=='kingbase'">
		        <if test="null != beginTime and '' != beginTime">
		            AND a.contribution_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND a.contribution_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</when>
        	<otherwise>
		        <if test="null != beginTime and '' != beginTime">
		            AND a.contribution_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND a.contribution_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</otherwise>
        </choose>
        group by a.pk_knowledge_base_id
        ORDER BY count(1) DESC,contribution_time DESC
        <if test="null != limit">
            LIMIT #{limit}
        </if>
    </select>

</mapper>