<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsWsAssistMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsWsAssist">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="PK_WS_ASSIST_ID" jdbcType="VARCHAR" property="pkWsAssistId" />
    <result column="WORK_NUMBER" jdbcType="VARCHAR" property="workNumber" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DELETE_STATUS" jdbcType="SMALLINT" property="deleteStatus" />
    <result column="FK_ASSIST_USER_ID" jdbcType="VARCHAR" property="fkAssistUserId" />
    <result column="FK_ASSIST_USER_NAME" jdbcType="VARCHAR" property="fkAssistUserName" />
    <result column="FK_ASSIST_USER__DEPT_ID" jdbcType="VARCHAR" property="fkAssistUserDeptId" />
    <result column="FK_ASSIST_USER__DEPT_NAME" jdbcType="VARCHAR" property="fkAssistUserDeptName" />
    <result column="ASSIST" jdbcType="SMALLINT" property="assist" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
  </resultMap>


</mapper>