<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsWsBackMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsWsBack">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="pk_ws_back_id" jdbcType="VARCHAR" property="pkWsBackId"/>
        <result column="work_number" jdbcType="VARCHAR" property="workNubmer"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delete_status" jdbcType="TINYINT" property="deleteStatus"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="back_reason" jdbcType="VARCHAR" property="backReason"/>
        <result column="fk_user_name" jdbcType="VARCHAR" property="fkUserName"/>
        <result column="fk_user_dept_name" jdbcType="VARCHAR" property="fkUserDeptName"/>
    </resultMap>

    <sql id="BackColums">
        pk_ws_back_id,
        work_number,
        create_by,
        create_time,
        update_by,
        update_time,
        delete_status,
        remark,
        back_reason,
        fk_user_name,
        fk_user_dept_name
    </sql>
    <insert id="insertBack">
        insert into ws_ws_back
        values(
        #{pkWsBackId},
        #{workNubmer},
        #{createBy},
        #{createTime},
        #{updateBy},
        #{updateTime},
        #{deleteStatus},
        #{remark},
        #{backReason},
        #{fkUserName},
        #{fkUserDeptName},
        #{type}
        )
    </insert>

</mapper>