<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsWsCostMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsWsCost">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="pk_ws_cost_id" jdbcType="VARCHAR" property="pkWsCostId"/>
        <result column="work_number" jdbcType="VARCHAR" property="workNumber"/>
        <result column="money" jdbcType="INTEGER" property="money"/>
        <result column="cost_deion" jdbcType="VARCHAR" property="costDeion"/>
        <result column="cost_time" jdbcType="TIMESTAMP" property="costTime"/>
        <result column="fill_user" jdbcType="VARCHAR" property="fillUser"/>
        <result column="fill_user_id" jdbcType="VARCHAR" property="fillUserId"/>
        <result column="fill_dept_id" jdbcType="VARCHAR" property="fillDeptId"/>
        <result column="fill_dept_name" jdbcType="VARCHAR" property="fillDeptName"/>
        <result column="cost_status" jdbcType="INTEGER" property="costStatus"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delete_status" jdbcType="INTEGER" property="deleteStatus"/>
    </resultMap>
    <sql id="wsCostColums">
        a.pk_ws_cost_id,
        a.work_number,
        a.money,
        a.cost_deion,
        a.cost_time,
        a.fill_user,
        a.fill_user_id,
        a.fill_dept_id,
        a.fill_dept_name,
        a.cost_status,
        a.files,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.delete_status,
        a.file_count
    </sql>
    <select id="getPageList" resultType="cn.trasen.worksheet.module.dto.outVo.WsWsCostPageListOutVo">
        SELECT
        <include refid="wsCostColums"/>,
        c.dept_id businessDeptId,
        c.dept_name businessDeptName
        FROM
        WS_WS_COST A
        LEFT JOIN ws_ws_sheet B ON A.work_number = B.work_number
        LEFT JOIN ws_om_meau C ON B.business_dept_id = C.dept_id
        WHERE
        C.delete_status = 0 and a.delete_status = 0
        <if test="null != businessDeptId and ''!=businessDeptId ">
            AND b.business_dept_id = #{businessDeptId}
        </if>
        <if test="null != workNumber and ''!=workNumber ">
            AND a.work_number = #{workNumber}
        </if>
        <if test=" null != costBeiginTime ">
            AND DATE_FORMAT(a.cost_time,'%Y-%m-%d') &gt;= DATE_FORMAT(#{costBeiginTime},'%Y-%m-%d')
        </if>
        <if test=" null != costEndTime ">
            AND DATE_FORMAT(a.cost_time,'%Y-%m-%d') &lt; DATE_FORMAT(#{costEndTime},'%Y-%m-%d')
        </if>
        <if test="null != fillDeptId and ''!=fillDeptId ">
            AND a.fill_dept_id = #{fillDeptId}
        </if>
        <if test="null != fillUserId and ''!=fillUserId ">
            AND a.fill_user_id = #{fillUserId}
        </if>
        <if test=" null != createBeiginTime ">
            AND DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d') &gt;= DATE_FORMAT(#{createBeiginTime},'%Y-%m-%d')
        </if>
        <if test=" null != createEndTime ">
            AND DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d') &lt; DATE_FORMAT(#{createEndTime},'%Y-%m-%d')
        </if>
    </select>
    <select id="wsCostSum" resultType="java.lang.Float">
        SELECT
        sum( money )
        FROM
        WS_WS_COST A
        LEFT JOIN ws_ws_sheet B ON A.work_number = B.work_number
        LEFT JOIN ws_om_meau C ON B.business_dept_id = C.dept_id
        WHERE
        C.delete_status = 0 and a.delete_status = 0
        <if test="null != businessDeptId and ''!=businessDeptId ">
            AND b.business_dept_id = #{businessDeptId}
        </if>
        <if test="null != workNumber and ''!=workNumber ">
            AND a.work_number = #{workNumber}
        </if>
        <if test=" null != costBeiginTime ">
            AND DATE_FORMAT(a.cost_time,'%Y-%m-%d') &gt;= DATE_FORMAT(#{costBeiginTime},'%Y-%m-%d')
        </if>
        <if test=" null != costEndTime ">
            AND DATE_FORMAT(a.cost_time,'%Y-%m-%d') &lt; DATE_FORMAT(#{costEndTime},'%Y-%m-%d')
        </if>
        <if test="null != fillDeptId and ''!=fillDeptId ">
            AND a.fill_dept_id = #{fillDeptId}
        </if>
        <if test="null != fillUserId and ''!=fillUserId ">
            AND a.fill_user_id = #{fillUserId}
        </if>
        <if test=" null != createBeiginTime ">
            AND DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d') &gt;= DATE_FORMAT(#{createBeiginTime},'%Y-%m-%d')
        </if>
        <if test=" null != createEndTime ">
            AND DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d') &lt; DATE_FORMAT(#{createEndTime},'%Y-%m-%d')
        </if>


    </select>
</mapper>