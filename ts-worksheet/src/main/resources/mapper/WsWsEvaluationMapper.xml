<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsWsEvaluationMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsWsEvaluation">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="pk_ws_evaluation_id" jdbcType="VARCHAR" property="pkWsEvaluationId"/>
        <result column="work_number" jdbcType="VARCHAR" property="workNumber"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_by_name" jdbcType="VARCHAR" property="updateByName"/>
        <result column="create_by_name" jdbcType="VARCHAR" property="createByName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delete_status" jdbcType="TINYINT" property="deleteStatus"/>
        <result column="process_speed" jdbcType="TINYINT" property="processSpeed"/>
        <result column="service_attituude" jdbcType="TINYINT" property="serviceAttituude"/>
        <result column="technical_level" jdbcType="TINYINT" property="technicalLevel"/>
    </resultMap>


    <insert id="insertBatch">
        INSERT INTO ws_ws_evaluation
        (
        pk_ws_evaluation_id,
        work_number,
        delete_status,
        create_time,
        process_speed,
        service_attituude,
        technical_level
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.pkWsEvaluationId},
            #{item.workNumber},
            #{item.deleteStatus},
            #{item.createTime},
            #{item.processSpeed},
            #{item.serviceAttituude},
            #{item.technicalLevel}
            )
        </foreach>
    </insert>

    <select id="getComprehensiveScoreOfWorkOrder" resultType="java.util.Map">
        select
        IFNULL(avgScore,0) avgScore,
        IFNULL(avgProcessSpeed,0) avgProcessSpeed,
        IFNULL(avgServiceAttituude,0) avgServiceAttituude,
        IFNULL(avgTechnicalLevel,0) avgTechnicalLevel
        from
        (select ROUND(avg(socre),2) avgScore from (SELECT SUM(process_speed+service_attituude+technical_level)/3 socre
        FROM ws_ws_evaluation e
        join ws_ws_sheet f on e.work_number = f.work_number
        where f.delete_status = 0
        <if test=" null != fkDeptId and '' != fkDeptId">
            and f.business_dept_id = #{fkDeptId}
        </if>
        <choose>
        	<when test="_databaseId=='kingbase'">
		        <if test="null != beginTime and '' != beginTime">
		            AND f.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND f.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</when>
        	<otherwise>
		        <if test="null != beginTime and '' != beginTime">
		            AND f.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND f.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</otherwise>
        </choose>
        <if test="null != fkUserId and ''!=fkUserId">
            and f.fk_user_id = #{fkUserId}
        </if>
        GROUP BY pk_ws_evaluation_id) a) b,
        (SELECT ROUND(avg(process_speed),2) avgProcessSpeed,ROUND(avg(service_attituude),2)
        avgServiceAttituude,ROUND(avg(technical_level),2) avgTechnicalLevel FROM ws_ws_evaluation e
        join ws_ws_sheet f on e.work_number = f.work_number
        where f.delete_status = 0
        <if test=" null != fkDeptId and '' != fkDeptId">
            and f.business_dept_id = #{fkDeptId}
        </if>
        <choose>
        	<when test="_databaseId=='kingbase'">
		        <if test="null != beginTime and '' != beginTime">
		            AND f.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND f.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</when>
        	<otherwise>
		        <if test="null != beginTime and '' != beginTime">
		            AND f.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND f.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</otherwise>
        </choose>
        <if test="null != fkUserId and ''!=fkUserId">
            and f.fk_user_id = #{fkUserId}
        </if>
        ) c
    </select>
  
    <select id="getEvaluationAverageScore" resultType="java.util.Map">
        SELECT
        <choose>
            <when test=' 0 == dayOrMonthType'>
                DATE_FORMAT( create_time, '%Y-%m-%d' ) date,
            </when>
            <when test=' 1 == dayOrMonthType'>
                DATE_FORMAT( create_time, '%Y-%m' ) date,
            </when>
            <otherwise>
                DATE_FORMAT( create_time, '%Y' ) date,
            </otherwise>
        </choose>
        <!-- DATE_FORMAT( create_time, '%Y-%m-%d' ) date,-->
        ROUND(avg( process_speed ),2) avgProcessSpeed,
        ROUND(avg( service_attituude ),2) avgServiceAttituude,
        ROUND(avg( technical_level ),2) avgTechnicalLevel,
        ROUND(avg( sum ),2) avgSum
        FROM
        ( SELECT process_speed, service_attituude, technical_level,( process_speed + service_attituude + technical_level
        ) / 3 sum,
        a.create_time
        FROM ws_ws_evaluation a join ws_ws_sheet b on a.work_number = b.work_number
        WHERE b.delete_status = 0
        <if test=" null != fkUserDeptId and '' != fkUserDeptId">
            and b.business_dept_id = #{fkUserDeptId}
        </if>
        <choose>
        	<when test="_databaseId=='kingbase'">
		        <if test="null != beginTime and '' != beginTime">
		            AND b.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND b.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</when>
        	<otherwise>
		        <if test="null != beginTime and '' != beginTime">
		            AND b.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND b.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</otherwise>
        </choose>
        ) a
        GROUP BY
        <choose>
            <when test=' 0 == dayOrMonthType'>
                DATE_FORMAT( create_time, '%Y-%m-%d' )
            </when>
            <when test=' 1 == dayOrMonthType'>
                DATE_FORMAT( create_time, '%Y-%m' )
            </when>
            <otherwise>
                DATE_FORMAT( create_time, '%Y' )
            </otherwise>
        </choose>
    </select>

    <select id="getEvaluationLevel" resultType="java.util.Map">
        SELECT
        sum( CASE WHEN davg = 5 THEN 1 ELSE 0 END ) fcmy,
        sum( CASE WHEN davg &lt; 5 AND davg &gt;= 4 THEN 1 ELSE 0 END ) my,
        sum( CASE WHEN davg &lt; 4 AND davg &gt;= 3 THEN 1 ELSE 0 END ) yb,
        sum( CASE WHEN davg &lt; 3 AND davg &gt;= 2 THEN 1 ELSE 0 END ) bmy,
        sum( CASE WHEN davg &lt; 2 THEN 1 ELSE 0 END ) hbmy
        FROM
        ( SELECT
        <choose>
            <when test=' 1 == evaluationType'>
                avg( process_speed )
            </when>
            <when test=' 2 == evaluationType'>
                avg( service_attituude )
            </when>
            <when test=' 3 == evaluationType'>
                avg( technical_level )
            </when>
            <otherwise>
                sum( process_speed + service_attituude + technical_level )/ 3
            </otherwise>
        </choose>
        davg FROM ws_ws_evaluation a join ws_ws_sheet b on a.work_number = b.work_number
        WHERE
        b.delete_status = 0
        <if test=" null != fkUserDeptId and '' != fkUserDeptId">
            and b.business_dept_id = #{fkUserDeptId}
        </if>
        <choose>
        	<when test="_databaseId=='kingbase'">
		        <if test="null != beginTime and '' != beginTime">
		            AND b.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND b.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</when>
        	<otherwise>
		        <if test="null != beginTime and '' != beginTime">
		            AND b.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND b.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</otherwise>
        </choose>
        GROUP BY pk_ws_evaluation_id ) a
    </select>

    <select id="getDeptEvaluationAverageScore" resultType="java.util.Map">
        SELECT
        c.dept_name fkUserDeptName,
        ROUND( avg(
        <choose>
            <when test=' 1 == evaluationType'>
                a.process_speed
            </when>
            <when test=' 2 == evaluationType'>
                a.service_attituude
            </when>
            <when test=' 3 == evaluationType'>
                a.technical_level
            </when>
            <otherwise>
                a.sum
            </otherwise>
        </choose>
        ), 2 ) avg
        FROM
        ( SELECT work_number, process_speed, service_attituude, technical_level, sum( process_speed + service_attituude
        + technical_level )/ 3 sum 
        FROM ws_ws_evaluation 
        GROUP BY work_number,process_speed,service_attituude,technical_level ) a
        JOIN ws_ws_sheet b ON a.work_number = b.work_number
        join ws_om_meau c on b.business_dept_id = c.dept_id
        where b.delete_status = 0
        <if test=" null != fkUserDeptId and ''!= fkUserDeptId">
            and b.business_dept_id = #{fkUserDeptId}
        </if>
        <choose>
        	<when test="_databaseId=='kingbase'">
		        <if test="null != beginTime and '' != beginTime">
		            AND b.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND b.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</when>
        	<otherwise>
		        <if test="null != beginTime and '' != beginTime">
		            AND b.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
		        <if test="null != endTime and '' != endTime">
		            AND b.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		        </if>
        	</otherwise>
        </choose>
        GROUP BY b.business_dept_id,c.dept_name 
        ORDER BY avg DESC
    </select>

    <select id="getUserEvaluationAverageScore"
            resultType="cn.trasen.worksheet.module.dto.outVo.WsEvaluationTopOutVo">
        SELECT
               ROUND( avg(( process_speed + service_attituude + technical_level )/ 3
                          ),
                      2
                   ) avgSocre,
               b.fk_user_name,
               b.fk_user_dept_name
        FROM
            ws_ws_evaluation a
                JOIN ws_ws_sheet b ON a.work_number = b.work_number
        <where>
            <if test=" null != list ">
                b.business_dept_id in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
            </if>
            <choose>
            	<when test="_databaseId=='kingbase'">
		            <if test="null != beginTime and '' != beginTime">
		                AND b.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		            </if>
		            <if test="null != endTime and '' != endTime">
		                AND b.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		            </if>
            	</when>
            	<otherwise>
		            <if test="null != beginTime and '' != beginTime">
		                AND b.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
		            </if>
		            <if test="null != endTime and '' != endTime">
		                AND b.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
		            </if>
            	</otherwise>
            </choose>
        </where>
        GROUP BY
            b.fk_user_id
        order by avgSocre desc
            <if test=' null!= limit '>
                LIMIT #{limit}
            </if>
    </select>

    <select id="getEvaluationGroupByDeptAverageScore" resultType="java.util.Map">

        SELECT deptAvg, dept_name deptName FROM ( SELECT ROUND( avg(( process_speed + service_attituude + technical_level )/ 3
        ),
        1
        ) deptAvg,
        business_dept_id
        FROM
        ws_ws_evaluation a
        JOIN (
        SELECT
        a.work_number,
        a.business_dept_id
        FROM
        ws_ws_sheet a
        WHERE delete_status = 0
        <choose>
        	<when test="_databaseId=='kingbase'">
		        AND create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' ) AND create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
        	</when>
        	<otherwise>
		        AND create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' ) AND create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
        	</otherwise>
        </choose>
         UNION
        SELECT
        a.work_number,
        business_dept_id
        FROM
        ws_ws_sheet a
        LEFT JOIN ws_ws_task b ON a.work_number = b.work_number
        WHERE  TAKE_REMARK LIKE '工单流转%'
        <choose>
        	<when test="_databaseId=='kingbase'">
		        AND b.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' ) AND b.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
        	</when>
        	<otherwise>
		        AND b.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' ) AND b.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
        	</otherwise>
        </choose>
        AND a.delete_status = 0
        ) b ON a.work_number = b.work_number
        GROUP BY
        b.business_dept_id
        ) a
        JOIN ws_om_meau b ON a.business_dept_id = b.dept_id
        WHERE
        delete_status = 0

    </select>
</mapper>