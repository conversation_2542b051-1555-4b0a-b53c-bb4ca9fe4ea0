<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsWsLogMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsWsLog">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="pk_ws_log_id" jdbcType="VARCHAR" property="pkWsLogId" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delete_status" jdbcType="TINYINT" property="deleteStatus" />
    <result column="log_task" jdbcType="VARCHAR" property="logTask" />
    <result column="log_describe" jdbcType="VARCHAR" property="logDescribe" />
    <result column="work_number" jdbcType="VARCHAR" property="workNumber" />
  </resultMap>
</mapper>