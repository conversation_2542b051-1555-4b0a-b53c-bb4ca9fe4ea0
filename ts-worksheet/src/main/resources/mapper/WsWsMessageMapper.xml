<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsWsMessageMapper">

    <sql id="messagesColums">
        pk_ws_message_id
        ,
        work_number,
        work_status,
        message_type,
        message_title,
        message_content,
        fk_user_id_rev,
        create_by,
        create_time,
        update_by,
        update_time,
        delete_status,
        remark,
        fk_user_name_rev,
        is_read
    </sql>

    <insert id="insertMessage">
        insert into ws_ws_message
        values (#{pkWsMessageId},
                #{workNumber},
                #{workStatus},
                #{messageType},
                #{messageTitle},
                #{messageContent},
                #{fkUserIdRev},
                #{createBy},
                #{createTime},
                #{updateBy},
                #{updateTime},
                #{deleteStatus},
                #{remark},
                #{fkUserNameRev},
                #{isRead})
    </insert>
    <update id="updateMessage">
        update ws_ws_message
        set is_read = 1
        where pk_ws_message_id = #{pkWsMessageId}
    </update>
    <update id="updateMessageAllByFkUserId">
        update ws_ws_message
        set is_read = 1
        where fk_user_id_rev = #{fkUserId}
    </update>

    <select id="selectMessagePageList" resultType="cn.trasen.worksheet.module.dto.outVo.WsWsMessageListOutVo">
        select
        <include refid="messagesColums"/>
        from ws_ws_message
        where fk_user_id_rev = #{fkUserId} and is_read = #{isRead}
    </select>
    <select id="selectOneById" resultType="cn.trasen.worksheet.module.entity.WsWsMessage">
        select
        <include refid="messagesColums"/>
        from ws_ws_message
        where pk_ws_message_id = #{pkWsMessageId}
    </select>
    <select id="selectMessageAllList" resultType="cn.trasen.worksheet.module.dto.outVo.WsWsMessageListOutVo">
        select
        <include refid="messagesColums"/>
        from ws_ws_message
        where fk_user_id_rev = #{fkUserId} and is_read = #{isRead}
        order by create_time desc
    </select>
    <select id="selectOneDpdDjdByWorkNumber" resultType="java.util.Map">
        SELECT
            work_number workNumber,
            DATE_FORMAT(max( create_time ),'%Y-%m-%d %H:%i:%s') createTime
        FROM
            `ws_ws_message`
        WHERE
            work_number in (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
          AND work_status = #{workStatus}
        GROUP BY
            work_number
    </select>
    
    <select id="getSchedule" parameterType="String" resultType="cn.trasen.homs.bean.base.EmployeeResp">
    	SELECT
			t3.employee_no
		FROM
			ts_base_oa.hrms_scheduling_manage t1
		LEFT JOIN ts_base_oa.hrms_scheduling_frequency t2 ON t2.frequency_id = t1.frequency_id
		LEFT JOIN ts_base_oa.cust_emp_base t3 ON t1.employee_id = t3.employee_id
		WHERE
			t1.is_deleted = 'N'
			AND DATE_FORMAT(t1.scheduling_date,'%Y-%m-%d')&gt;= #{startDate}
			AND DATE_FORMAT(t1.scheduling_date,'%Y-%m-%d')&lt;= #{endDate} 
			AND t3.org_id = #{deptId}
     </select>
</mapper>