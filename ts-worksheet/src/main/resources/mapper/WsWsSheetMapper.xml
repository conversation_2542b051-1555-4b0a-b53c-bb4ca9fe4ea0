<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsWsSheetMapper">
	<resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.dto.outVo.WsWsSheetListOutVo">
		<!-- WARNING - @mbg.generated -->
		<result column="pk_ws_sheet_id" jdbcType="VARCHAR" property="pkWsSheetId" />
		<result column="work_number" jdbcType="VARCHAR" property="workNumber" />
		<result column="work_hours" jdbcType="VARCHAR" property="workHours" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="repair_man_dept_id" jdbcType="VARCHAR" property="repairManDeptId" />
		<result column="repair_man_dept_name" jdbcType="VARCHAR" property="repairManDeptName" />
		<result column="repair_dept_address" jdbcType="VARCHAR" property="repairDeptAddress" />
		<result column="repair_man_id" jdbcType="VARCHAR" property="repairManId" />
		<result column="repair_man_name" jdbcType="VARCHAR" property="repairManName" />
		<result column="repair_phone" jdbcType="VARCHAR" property="repairPhone" />
		<result column="fk_fault_type_id" jdbcType="VARCHAR" property="fkFaultTypeId" />
		<result column="fk_fault_type_name" jdbcType="VARCHAR" property="fkFaultTypeName" />
		<result column="fault_equipment_name" jdbcType="VARCHAR" property="faultEquipmentName" />
		<result column="fk_fault_equipment_id" jdbcType="VARCHAR" property="fkFaultEquipmentId" />
		<result column="repair_type" jdbcType="TINYINT" property="repairType" />
		<result column="fault_emergency" jdbcType="TINYINT" property="faultEmergency" />
		<result column="fault_affect_scope" jdbcType="TINYINT" property="faultAffectScope" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="fk_user_dept_id" jdbcType="VARCHAR" property="fkUserDeptId" />
		<result column="fk_user_dept_name" jdbcType="VARCHAR" property="fkUserDeptName" />
		<result column="fault_deion" jdbcType="VARCHAR" property="faultDeion" />
		<result column="fk_user_id" jdbcType="VARCHAR" property="fkUserId" />
		<result column="fk_user_name" jdbcType="VARCHAR" property="fkUserName" />
		<result column="create_by" jdbcType="VARCHAR" property="createBy" />
		<result column="create_by_name" jdbcType="VARCHAR" property="createByName" />
		<result column="required_completion_time" jdbcType="TIMESTAMP" property="requiredCompletionTime" />
		<result column="haten_count" jdbcType="TINYINT" property="hatenCount" />
		<result column="work_status" jdbcType="VARCHAR" property="workStatus" />
		<result column="pk_ws_task_id" jdbcType="VARCHAR" property="pkWsTaskId" />
		<result column="assist" jdbcType="TINYINT" property="assist" />
		<!--一对多，方式一 -->
		<collection property="wsFileOutVoList" column="work_number"
			ofType="cn.trasen.worksheet.module.dto.outVo.WsFileOutVo" javaType="ArrayList"
			select="cn.trasen.worksheet.module.mapper.WsFileFileMapper.selectAllList" />

	</resultMap>
	<resultMap id="BaseResultMap1" type="cn.trasen.worksheet.module.dto.outVo.WsWsSheetListOutVo">
		<!-- WARNING - @mbg.generated -->
		<result column="pk_ws_sheet_id" jdbcType="VARCHAR" property="pkWsSheetId" />
		<result column="work_number" jdbcType="VARCHAR" property="workNumber" />
		<result column="work_hours" jdbcType="VARCHAR" property="workHours" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="repair_man_dept_id" jdbcType="VARCHAR" property="repairManDeptId" />
		<result column="repair_man_dept_name" jdbcType="VARCHAR" property="repairManDeptName" />
		<result column="repair_dept_address" jdbcType="VARCHAR" property="repairDeptAddress" />
		<result column="repair_man_id" jdbcType="VARCHAR" property="repairManId" />
		<result column="repair_man_name" jdbcType="VARCHAR" property="repairManName" />
		<result column="repair_phone" jdbcType="VARCHAR" property="repairPhone" />
		<result column="fk_fault_type_id" jdbcType="VARCHAR" property="fkFaultTypeId" />
		<result column="fk_fault_type_name" jdbcType="VARCHAR" property="fkFaultTypeName" />
		<result column="fault_equipment_name" jdbcType="VARCHAR" property="faultEquipmentName" />
		<result column="fk_fault_equipment_id" jdbcType="VARCHAR" property="fkFaultEquipmentId" />
		<result column="repair_type" jdbcType="TINYINT" property="repairType" />
		<result column="fault_emergency" jdbcType="TINYINT" property="faultEmergency" />
		<result column="fault_affect_scope" jdbcType="TINYINT" property="faultAffectScope" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="fk_user_dept_id" jdbcType="VARCHAR" property="fkUserDeptId" />
		<result column="fk_user_dept_name" jdbcType="VARCHAR" property="fkUserDeptName" />
		<result column="fault_deion" jdbcType="VARCHAR" property="faultDeion" />
		<result column="fk_user_id" jdbcType="VARCHAR" property="fkUserId" />
		<result column="fk_user_name" jdbcType="VARCHAR" property="fkUserName" />
		<result column="create_by" jdbcType="VARCHAR" property="createBy" />
		<result column="create_by_name" jdbcType="VARCHAR" property="createByName" />
		<result column="required_completion_time" jdbcType="TIMESTAMP" property="requiredCompletionTime" />
		<result column="haten_count" jdbcType="TINYINT" property="hatenCount" />
		<result column="work_status" jdbcType="VARCHAR" property="workStatus" />
		<result column="pk_ws_task_id" jdbcType="VARCHAR" property="pkWsTaskId" />
		<result column="assist" jdbcType="TINYINT" property="assist" />
	</resultMap>
	<sql id="wsSheetColum">
		CREATE_BY,
		CREATE_BY_NAME,
		create_by_dept_id,
		CREATE_TIME,
		UPDATE_BY,
		UPDATE_BY_NAME,
		UPDATE_TIME,
		DELETE_STATUS,
		repair_man_dept_id,
		repair_man_dept_name,
		REPAIR_MAN_ID,
		REPAIR_MAN_NAME,
		FK_USER_ID,
		FK_USER_NAME,
		fk_user_dept_id,
		fk_user_dept_name,
		PK_WS_SHEET_ID,
		work_number,
		WORK_STATUS,
		REPAIR_DEPT_ADDRESS,
		REPAIR_PHONE,
		FK_FAULT_TYPE_ID,
		FAULT_EQUIPMENT_NAME,
		FK_FAULT_EQUIPMENT_ID,
		fault_deion,
		REPAIR_TYPE,
		FAULT_EMERGENCY,
		FAULT_AFFECT_SCOPE,
		REQUIRED_COMPLETION_TIME,
		ACTUAL_COMPLETION_TIME,
		REMARK,
		WORK_HOURS,
		fk_user_phone,
		work_sheet_type,
		business_dept_id,
		fk_hospital_district_id,
		l_businessId,
		workflow_inst_id,
		workflow_no,
		end_work_status
	</sql>

	<insert id="insertWsSheet">
		INSERT INTO ws_ws_sheet (
		<include refid="wsSheetColum" />
		)
		values (
		#{createBy},
		#{createByName},
		#{createByDeptId},
		#{createTime},
		#{updateBy},
		#{updateByName},
		#{updateTime},
		#{deleteStatus},
		#{repairManDeptId},
		#{repairManDeptName},
		#{repairManId},
		#{repairManName},
		#{fkUserId},
		#{fkUserName},
		#{fkUserDeptId},
		#{fkUserDeptName},
		#{pkWsSheetId},
		#{workNumber},
		#{workStatus},
		#{repairDeptAddress},
		#{repairPhone},
		#{fkFaultTypeId},
		#{faultEquipmentName},
		#{fkFaultEquipmentId},
		#{faultDeion},
		#{repairType},
		#{faultEmergency},
		#{faultAffectScope},
		#{requiredCompletionTime},
		#{actualCompletionTime},
		#{remark},
		#{workHours},
		#{fkUserPhone},
		#{workSheetType},
		#{businessDeptId},
		#{fkHospitalDistrictId},
		#{lBusinessId},
		#{workflowInstId},
		#{workflowNo},
		#{endWorkStatus}
		)
	</insert>
	<update id="updateWsSheetByWorkNumber">
		update ws_ws_sheet
		set UPDATE_BY=#{updateBy},
		UPDATE_BY_NAME=#{updateByName},
		UPDATE_TIME=#{updateTime},
		DELETE_STATUS=#{deleteStatus},
		repair_man_dept_id=#{repairManDeptId},
		repair_man_dept_name=#{repairManDeptName},
		REPAIR_MAN_ID=#{repairManId},
		REPAIR_MAN_NAME=#{repairManName},
		FK_USER_ID=#{fkUserId},
		FK_USER_NAME=#{fkUserName},
		fk_user_dept_id=#{fkUserDeptId},
		fk_user_dept_name=#{fkUserDeptName},
		WORK_STATUS=#{workStatus},
		REPAIR_DEPT_ADDRESS=#{repairDeptAddress},
		REPAIR_PHONE=#{repairPhone},
		fk_fault_type_id=#{fkFaultTypeId},
		FAULT_EQUIPMENT_NAME=#{faultEquipmentName},
		fk_fault_equipment_id=#{fkFaultEquipmentId},
		fault_deion=#{faultDeion},
		REPAIR_TYPE=#{repairType},
		FAULT_EMERGENCY=#{faultEmergency},
		FAULT_AFFECT_SCOPE=#{faultAffectScope},
		REQUIRED_COMPLETION_TIME=#{requiredCompletionTime},
		ACTUAL_COMPLETION_TIME=#{actualCompletionTime},
		REMARK=#{remark},
		WORK_HOURS=#{workHours},
		fk_user_phone=#{fkUserPhone},
		business_dept_id = #{businessDeptId},
		fk_hospital_district_id = #{fkHospitalDistrictId},
		end_WORK_STATUS=#{endWorkStatus}
		where work_number = #{workNumber}

	</update>
	<update id="updateBatch" parameterType="java.util.List">
		<foreach collection="list" index="index" item="item" separator=";">
			UPDATE ws_ws_sheet
			set work_status = #{item.workStatus},
			update_time = #{item.updateTime},
			actual_completion_time=#{item.actualCompletionTime,jdbcType=DATE}
			WHERE work_number = #{item.workNumber}
		</foreach>
	</update>

	<select id="selectOneWsSheet" resultType="cn.trasen.worksheet.module.entity.WsWsSheet" parameterType="String">
		select
		<include refid="wsSheetColum" />
		,IFNULL(e.haten_count, 0) haten_count
		from ws_ws_sheet a
		LEFT JOIN (
		SELECT
		count( b.work_number ) haten_count,
		a.work_number workNumber
		FROM
		ws_ws_sheet a
		LEFT JOIN ws_ws_hasten b ON a.work_number = b.work_number
		AND A.WORK_NUMBER = #{workNumber}
		GROUP BY
		a.work_number
		) E ON a.work_number = E.workNumber
		where WORK_NUMBER = #{workNumber}
		limit 1
	</select>

	<select id="selectOneWsSheetByBusinessId" resultType="cn.trasen.worksheet.module.entity.WsWsSheet">
		select
		<include refid="wsSheetColum" />
		,IFNULL(e.haten_count, 0) haten_count
		from ws_ws_sheet a
		LEFT JOIN (
		SELECT
		count( b.work_number ) haten_count,
		a.work_number workNumber
		FROM
		ws_ws_sheet a
		LEFT JOIN ws_ws_hasten b ON a.work_number = b.work_number
		AND A.WORK_NUMBER = #{workNumber}
		GROUP BY
		a.work_number
		) E ON a.work_number = E.workNumber
		where a.l_businessId = #{businessId}
	</select>

	<select id="selectListWsSheetByFkUserId" resultType="cn.trasen.worksheet.module.entity.WsWsSheet">
		select
		<include refid="wsSheetColum" />
		from ws_ws_sheet
		where fk_user_id = #{fkUserId}
	</select>

	<select id="selectOneWsWsSheetOutVo" resultType="cn.trasen.worksheet.module.dto.outVo.WsWsSheetOutVo">
		select a.CREATE_BY,
		a.CREATE_BY_NAME,
		a.CREATE_TIME,
		a.UPDATE_BY,
		a.UPDATE_BY_NAME,
		a.UPDATE_TIME,
		a.DELETE_STATUS,
		repair_man_dept_id,
		repair_man_dept_name,
		REPAIR_MAN_ID,
		REPAIR_MAN_NAME,
		FK_USER_ID,
		FK_USER_NAME,
		fk_user_dept_id,
		fk_user_dept_name,
		PK_WS_SHEET_ID,
		work_number,
		WORK_STATUS,
		REPAIR_DEPT_ADDRESS,
		REPAIR_PHONE,
		FK_FAULT_TYPE_ID,
		FAULT_EQUIPMENT_NAME,
		FK_FAULT_EQUIPMENT_ID,
		fault_deion,
		REPAIR_TYPE,
		FAULT_EMERGENCY,
		FAULT_AFFECT_SCOPE,
		REQUIRED_COMPLETION_TIME,
		ACTUAL_COMPLETION_TIME,
		REMARK,
		WORK_HOURS,
		b.full_path fkFaultTypeName,
		c.dept_id businessDeptId,
		c.dept_name businessDeptName
		from ws_ws_sheet a
		left join ws_fault_type b on a.fk_fault_type_id = b.pk_fault_type_id
		left join ws_om_meau c on a.business_dept_id = c.dept_id
		where WORK_NUMBER = #{workNumber} and c.delete_status=0
	</select>

	<select id="selectOneWsSheetByTaskId" resultType="cn.trasen.worksheet.module.entity.WsWsSheet">
		select
		<include refid="wsSheetColum" />
		from ws_ws_sheet
		where WORK_NUMBER = ( select work_number from ws_ws_task where pk_ws_task_id = #{taskId})
	</select>

	<select id="selectWsSheetListByWorkStatus" resultType="cn.trasen.worksheet.module.entity.WsWsSheet">
		select
		<include refid="wsSheetColum" />
		from ws_ws_sheet
		where <!-- delete_status = 0 and -->
		work_status = #{workStatus}
	</select>

	<select id="getWorkSheetPageList"
		parameterType="cn.trasen.worksheet.module.dto.inputVo.WsWsSheetListSelectInputVo" resultMap="BaseResultMap1">
		SELECT
		a.l_businessId,
		a.workflow_inst_id,
		a.workflow_no,
		a.work_sheet_type,
		a.pk_ws_sheet_id,
		a.work_number,
		a.work_hours,
		a.create_time,
		a.repair_man_dept_id,
		a.repair_man_dept_name,
		a.repair_man_id,
		a.repair_man_name,
		a.create_by,
		a.create_by_name,
		a.create_by_name terminationByName,
		a.repair_phone,
		a.fault_deion,
		a.fk_user_id,
		a.fk_user_name,
		a.fk_user_phone,
		b.fk_user_id userId,
		a.required_completion_time,
		IFNULL( c.haten_count, 0 ) haten_count,
		a.work_status,
		b.pk_ws_task_id,
		b.assist,
		(select create_time from ws_ws_task bb where bb.work_number = a.work_number and task_name = '-2' limit 1) as receiveTime,
		b.create_time suspendedTime,
		b.create_time confirmTime,
		b.create_time terminationTime,
		a.repair_dept_address,
		a.fk_fault_type_id,
		f1.full_path fk_fault_type_name,
		a.fault_equipment_name,
		a.fk_fault_equipment_id,
		a.repair_type,
		a.fault_emergency,
		a.fault_affect_scope,
		a.remark,
		a.fk_user_dept_id,
		a.fk_user_dept_name,
		g.wcsj actual_completion_time,
		b.take_remark suspendedRemark,
		b.take_remark terminationRemark,
		v.remark backRemark,
		v.fk_user_name backUserName,
		v.fk_user_dept_name backDeptName,
		q.assist_name,
		q.assist_id,
		l.no_pass_counts,
		xx.create_by_name sendPeopleName,
		xx.create_time sendTime,
		case when k.assist = 1 and k.work_hours != 0 then 1 else 0 end assistFlag,
		IFNULL(m.theAssistFlag,0) theAssistFlag,
		h.dept_id businessDeptId,
		h.dept_name businessDeptName,
		a.fk_Hospital_District_Id,
		pk.hospital_district_name
		FROM
		ws_ws_sheet A
		JOIN ws_ws_task B ON a.work_number = b.WORK_NUMBER and a.work_status = b.work_status
		<!-- JOIN ws_ws_task B ON a.work_number = b.WORK_NUMBER and a.work_status = b.work_status -->

		<choose>
			<!-- 服务台列表根据在办节点id最大一条过滤，一笔业务仅一条 -->
			<when
				test=" null != workStatus and ('10' == workStatus or '11' == workStatus or '12' == workStatus or '13' == workStatus)">

				JOIN (
				SELECT
				b.pk_ws_task_id,
				b.assist,
				b.work_number,
				b.fk_user_id,
				b.complete
				FROM
				( SELECT MAX( PK_WS_TASK_ID ) PK_WS_TASK_ID FROM ws_ws_task
				<choose>
					<!-- 12为服务台派单 -->
					<when test="'12' == workStatus ">
						where complete = 0 and assist = 0 AND WORK_STATUS in('1')
					</when>
					<!-- 10为服务台处理中 -->
					<when test="'10' == workStatus ">
						where complete = 0 and assist = 0 AND WORK_STATUS
						<choose>
							<when test="null != workStatusGList">
								in (
								<foreach collection="workStatusGList" index="index" item="item" separator=",">#{item}</foreach>
								)
							</when>
							<otherwise>
								in('2','3','4','7')
							</otherwise>
						</choose>
						<!--not in('1','5','6','8') -->
					</when>
					<!-- 11为服务台已完成 -->
					<when test="'11' == workStatus ">
						where assist = 0 and WORK_STATUS in('5','6','8')
					</when>
					<!-- 13为服务台全部 -->
					<otherwise>
						where assist = 0
					</otherwise>
				</choose>
				GROUP BY work_number ) a
				JOIN ws_ws_task b ON a.PK_WS_TASK_ID = b.PK_WS_TASK_ID
				) d ON d.pk_ws_task_id = b.pk_ws_task_id
			</when>
			<!-- 我的工单列表根据当前登录人过滤，一笔业务仅展示一条 -->
			<otherwise>
				join (
				SELECT
				<!-- max( a.PK_WS_TASK_ID ) PK_WS_TASK_ID -->
				 a.pk_ws_task_id,a.work_number
				FROM
				(
				SELECT
				MAX(PK_WS_TASK_ID) PK_WS_TASK_ID,
				a.WORK_NUMBER
				FROM
				ws_ws_sheet a
				JOIN ws_ws_task b ON a.WORK_NUMBER = b.WORK_NUMBER
				WHERE
				a.work_status = b.work_status
				<!-- 数据权限处理 -->
				<choose>
					<!-- 根据数据权限展示科室所有业务 -->
					<when test="null != admin and false != admin">
						and (b.fk_user_dept_id in
						(
						<foreach collection="fkDeptIdList" index="index" item="item" separator=",">#{item}</foreach>
						)
						or a.repair_man_dept_id in
						(
						<foreach collection="fkDeptIdList" index="index" item="item" separator=",">#{item}</foreach>
						)
						)
					</when>
					<!-- 展示本人业务 -->
					<otherwise>
						<choose>
							<when test="null != fkId and null != repairId and '' != fkId and '' != repairId">
								and (b.fk_user_id = #{fkId} or a.repair_man_id = #{repairId})
							</when>
							<when test="null != repairId and '' != repairId">
								and a.repair_man_id = #{repairId}
							</when>
							<when test="null != fkId and '' != fkId ">
								and b.fk_user_id = #{fkId}
							</when>
						</choose>
					</otherwise>
				</choose>

				<choose>
					<!-- 已完成、已终止特殊情况查询COMPLETE为1的数据（即该办理完成） -->
					<when test=" null != workStatus and ('6' == workStatus or '8' == workStatus)">
						AND a.actual_completion_time IS NOT NULL
						GROUP BY work_number
					</when>
					<otherwise>
						<!-- AND COMPLETE = 0 --> AND ASSIST = 0
						GROUP BY work_number
						<if test="null != fkId and '' != fkId">
							UNION ALL
							SELECT
							max(b.PK_WS_TASK_ID) PK_WS_TASK_ID,
							a.WORK_NUMBER
							FROM
							ws_ws_sheet a
							JOIN ws_ws_task b ON a.WORK_NUMBER = b.WORK_NUMBER
							JOIN (select max(PK_WS_TASK_ID) PK_WS_TASK_ID from ws_ws_task where
							work_number NOT IN (
							SELECT DISTINCT
							B.WORK_NUMBER
							FROM
							ws_ws_sheet A
							LEFT JOIN ws_ws_task B ON a.work_number = B.WORK_NUMBER
							WHERE
							B.ASSIST = '1'
							AND B.create_time &lt; ( SELECT IFNULL( max( create_time ), '2000-01-01' ) FROM ws_ws_task WHERE take_remark LIKE
							'工单流转%' AND WORK_NUMBER = a.work_number )
							AND b.fk_user_id = #{fkId}
							)
							AND ASSIST = 1
							AND COMPLETE = 0
							and fk_user_id = #{fkId}
							GROUP BY work_number) c ON b.PK_WS_TASK_ID = c.PK_WS_TASK_ID
							WHERE
							a.work_status = b.work_status
							<!-- 数据权限处理 -->
							<choose>
								<!-- 根据数据权限展示科室或跨科室所有业务 -->
								<when test="null != admin and false != admin">
									and b.fk_user_dept_id in (
									<foreach collection="fkDeptIdList" index="index" item="item" separator=",">#{item}</foreach>
									)
								</when>
								<!-- 展示本人业务 -->
								<otherwise>
									<choose>
										<when test="null != fkId and null != repairId and '' != fkId and '' != repairId">
											and (b.fk_user_id = #{fkId} or a.repair_man_id = #{repairId})
										</when>
										<when test="null != repairId and '' != repairId">
											and a.repair_man_id = #{repairId}
										</when>
										<when test="null != fkId and '' != fkId ">
											and b.fk_user_id = #{fkId}
										</when>
									</choose>
									<!-- and b.fk_user_id = #{fkUserId} -->
								</otherwise>
							</choose>
							AND ASSIST = 1 AND COMPLETE = 0 Group by a.Work_number
						</if>
					</otherwise>
				</choose>

				<!-- 个人工单全部列表 已完成、已终止数据 -->
				<if test="'0' == workStatus">
					UNION ALL
					SELECT
					b.PK_WS_TASK_ID,
					a.WORK_NUMBER
					FROM
					ws_ws_sheet a
					JOIN ws_ws_task b ON a.WORK_NUMBER = b.WORK_NUMBER
					JOIN (select max(PK_WS_TASK_ID) PK_WS_TASK_ID from ws_ws_task GROUP BY work_number) c ON
					b.PK_WS_TASK_ID = c.PK_WS_TASK_ID
					WHERE
					a.work_status = b.work_status
					<choose>
						<!-- 根据数据权限展示科室所有业务 -->
						<when test="null != admin and false != admin">
							and ( a.repair_man_dept_id in
							(
							<foreach collection="repairDeptIdList" index="index" item="item" separator=",">#{item}</foreach>
							)
							OR b.fk_user_dept_id in
							(
							<foreach collection="fkDeptIdList" index="index" item="item" separator=",">#{item}</foreach>
							)
							)
						</when>
						<!-- 展示本人业务 -->
						<otherwise>
							<choose>
								<when test="null != fkId and null != repairId and '' != fkId and '' != repairId">
									and (b.fk_user_id = #{fkId} or a.repair_man_id = #{repairId})
								</when>
								<when test="null != repairId and '' != repairId">
									and a.repair_man_id = #{repairId}
								</when>
								<when test="null != fkId and '' != fkId ">
									and b.fk_user_id = #{fkId}
								</when>
							</choose>

						</otherwise>
					</choose>
					AND complete = '1'
					and a.work_status in('6','8')
				</if>
				) a
				) f ON b.PK_WS_TASK_ID = f.PK_WS_TASK_ID
			</otherwise>
		</choose>
		LEFT JOIN (
		SELECT
		count( b.work_number ) haten_count, a.work_number
		FROM ws_ws_sheet a
		LEFT JOIN ws_ws_hasten b ON a.work_number = b.work_number
		GROUP BY a.work_number
		) c ON a.work_number = c.WORK_NUMBER
		LEFT JOIN ws_fault_type f1 ON a.fk_fault_type_id = f1.pk_fault_type_id
		LEFT JOIN (
		SELECT
		WORK_NUMBER,
		GROUP_CONCAT( CONCAT( a.fk_user_dept_name, '-', a.fk_user_name ) SEPARATOR ',' ) assist_name,
		GROUP_CONCAT(a.fk_user_id) assist_id
		FROM
		(
		SELECT DISTINCT B.WORK_NUMBER, B.fk_user_dept_name, B.fk_user_name,B.fk_user_id FROM ws_ws_sheet A LEFT JOIN ws_ws_task B ON
		a.work_number = B.WORK_NUMBER
		WHERE B.ASSIST = '1'
		and B.create_time > (SELECT IFNULL(max(create_time),'2000-01-01') FROM ws_ws_task where take_remark LIKE '工单流转%' and
		WORK_NUMBER = a.work_number )
		) a
		GROUP BY WORK_NUMBER ) q ON a.work_number = q.WORK_NUMBER
		LEFT JOIN (
		SELECT
		a.no_pass_counts,
		a.WORK_NUMBER,
		b.fk_user_name,
		b.fk_user_dept_name,
		b.back_reason
		FROM
		( SELECT count( 1 ) no_pass_counts, work_number FROM ws_ws_back WHERE type = 2 GROUP BY work_number ) a
		JOIN (
		SELECT
		fk_user_name,
		fk_user_dept_name,
		back_reason,
		a.WORK_NUMBER
		FROM
		ws_ws_back a
		JOIN ( SELECT max( create_time ) create_time, work_number FROM ws_ws_back WHERE type = 2 GROUP BY work_number )
		b ON a.create_time = b.create_time
		AND a.work_number = b.work_number
		) b ON a.work_number = b.WORK_NUMBER
		) l ON
		a.work_number =
		l.WORK_NUMBER
		left join (SELECT
		fk_user_name,
		fk_user_dept_name,
		remark,
		a.WORK_NUMBER
		FROM
		ws_ws_back a
		JOIN ( SELECT max( create_time ) create_time, work_number FROM ws_ws_back WHERE type = 1 GROUP BY work_number )
		b ON a.create_time = b.create_time
		AND a.work_number = b.work_number) v ON a.work_number =
		v.WORK_NUMBER
		left join (
		SELECT
		c.WORK_NUMBER,
		c.ASSIST,
		c.work_hours
		FROM
		(
		SELECT
		a.WORK_NUMBER,
		a.ASSIST,
		a.work_hours,
		a.create_time,
		count( 1 ) AS rankbak
		FROM
		ws_ws_task a
		LEFT JOIN ws_ws_task b ON a.WORK_NUMBER = b.WORK_NUMBER
		AND a.create_time &lt;= b.create_time
		<choose>
			<when test="_databaseId=='kingbase'">
				where a.create_time > add_months(CURRENT_DATE, -12)
			</when>
			<otherwise>
				where a.create_time > DATE_SUB(NOW(), INTERVAL 1 YEAR)
			</otherwise>
		</choose>
		GROUP BY a.WORK_NUMBER,a.ASSIST,a.work_hours,a.create_time ) AS c
		WHERE
		c.rankbak = 1
		) k
		ON a.work_number =
		k.WORK_NUMBER
		left join (

		SELECT
		c.WORK_NUMBER,
		CASE

		WHEN ( d.create_time IS NULL OR c.create_time > d.create_time ) THEN
		1 ELSE 0
		END theAssistFlag
		FROM
		(
		SELECT
		a.WORK_NUMBER,
		a.ASSIST,
		a.work_hours,
		a.create_time,
		count( 1 ) AS rankbak
		FROM
		( SELECT * FROM ws_ws_task WHERE fk_user_id = #{theUserId} AND work_hours != 0 AND ASSIST = 1 ) a
		LEFT JOIN ( SELECT * FROM ws_ws_task WHERE fk_user_id = #{theUserId} AND work_hours != 0 AND ASSIST = 1 ) b ON
		a.WORK_NUMBER = b.WORK_NUMBER

		AND a.create_time &lt;= b.create_time
		<choose>
			<when test="_databaseId=='kingbase'">
				where a.create_time > add_months(CURRENT_DATE, -12)
			</when>
			<otherwise>
				where a.create_time > DATE_SUB(NOW(), INTERVAL 1 YEAR)
			</otherwise>
		</choose>
		GROUP BY a.WORK_NUMBER,a.ASSIST,a.work_hours,a.create_time
		) c
		LEFT JOIN (select max(create_time) create_time,work_number from ws_ws_back GROUP BY work_number) d ON c.WORK_NUMBER =
		d.work_number
		WHERE
		c.rankbak = 1
		) m on a.WORK_NUMBER = m.WORK_NUMBER
		LEFT JOIN (SELECT max(create_time) wcsj, work_number
		FROM ws_ws_task
		WHERE work_status = '4'
		and assist = 0 GROUP BY work_number
		) g ON a.WORK_NUMBER = g.WORK_NUMBER
		LEFT JOIN (select * from ws_om_meau where delete_status = 0) h ON a.business_dept_id = h.dept_id
		LEFT JOIN (
		select a.create_by_name,a.work_number,a.create_time from ws_ws_task a
		join (select max(CREATE_TIME) CREATE_TIME,work_number from ws_ws_task where task_name = '-5' GROUP BY
		work_number) b
		on a.WORK_NUMBER = b.WORK_NUMBER and a.CREATE_TIME = b.CREATE_TIME
		) xx ON xx.work_number = a.work_number
		LEFT JOIN (SELECT hospital_district_name,pk_hospital_district_id FROM ws_hospital_district WHERE delete_status = 0 ) pk ON
		a.fk_hospital_district_id = pk.pk_hospital_district_id
		<where>
			a.delete_status = 0
			<!-- 服务台根据数据权限过滤数据 -->
			<if
				test=" null!=workStatus and ('10' == workStatus or '11' == workStatus or '12' == workStatus or '13' == workStatus)">
				and 1=1 and a.business_dept_id in (
				<foreach collection="fkDeptIdList" index="index" item="item" separator=",">#{item}</foreach>
				)
			</if>
			<if test=" null != fuzzy and '' != fuzzy">
				AND (a.work_number = #{fuzzy} or a.repair_man_name = #{fuzzy} or a.fault_deion like
				concat('%',#{fuzzy},'%'))
			</if>
			<if test=" null != beginTime ">
				AND DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d') &gt;= DATE_FORMAT(#{beginTime},'%Y-%m-%d')
			</if>
			<if test=" null != endTime ">
				AND DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d') &lt; DATE_FORMAT(#{endTime},'%Y-%m-%d')
			</if>
			<if test="null != workStatus and '10'.toString() != workStatus and '11'.toString() != workStatus 
					and '12'.toString() != workStatus and '13'.toString() != workStatus">
				<choose>
					<when test="'0'.toString() == workStatus">
					</when>
					<when test="'9'.toString() == workStatus">
						AND b.complete = 1
					</when>
					<otherwise>
						<choose>
							<when test="null != mobileType and '' != mobileType">
								<choose>
									<when test="'1'.toString() == mobileType">
										AND a.WORK_STATUS in ('1','2')
									</when>
									<when test="'2'.toString() == mobileType">
										AND a.WORK_STATUS in ('3','4','5','7')
									</when>
									<when test="'3'.toString() == mobileType">
										AND a.WORK_STATUS in ('6','8')
									</when>
								</choose>
							</when>
							<otherwise>
								<!-- <if test="&quot;8&quot; == workStatus">
									AND b.complete = 0
								</if> -->
								<if test="null == workStatusValue or '' == workStatusValue">
									AND a.WORK_STATUS = #{workStatus}
								</if>
							</otherwise>
						</choose>
					</otherwise>
				</choose>
			</if>
			<if test="null != completeStatus and completeStatus != ''">
				AND a.WORK_STATUS = #{completeStatus}
			</if>
			<if test="null != workStatusValue and '' != workStatusValue ">
				AND a.WORK_STATUS in (
				<foreach collection="workStatusValueList" index="index" item="item" separator=",">#{item}</foreach>
				)
			</if>
			<if test="null != createTime ">
				AND DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d') = DATE_FORMAT(#{createTime},'%Y-%m-%d')
			</if>

			<if test="null != beginRequiredCompletionTime ">
				AND DATE_FORMAT(a.required_completion_time,'%Y-%m-%d') &gt;=
				DATE_FORMAT(#{beginRequiredCompletionTime},'%Y-%m-%d')
			</if>
			<if test="null != endRequiredCompletionTime ">
				AND DATE_FORMAT(a.required_completion_time,'%Y-%m-%d') &lt;
				DATE_FORMAT(#{endRequiredCompletionTime},'%Y-%m-%d')
			</if>

			<if test="null != beginAcknowledgingTime ">
				AND DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d') &gt;= DATE_FORMAT(#{beginAcknowledgingTime},'%Y-%m-%d')
			</if>
			<if test="null != endAcknowledgingTime ">
				AND DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d') &lt; DATE_FORMAT(#{endAcknowledgingTime},'%Y-%m-%d')
			</if>

			<if test="null != beginActualCompletionTime ">
				AND DATE_FORMAT(a.actual_completion_time,'%Y-%m-%d') &gt;=
				DATE_FORMAT(#{beginActualCompletionTime},'%Y-%m-%d')
			</if>
			<if test="null != endActualCompletionTime ">
				AND DATE_FORMAT(a.actual_completion_time,'%Y-%m-%d') &lt;
				DATE_FORMAT(#{endActualCompletionTime},'%Y-%m-%d')
			</if>

			<if test="null != beginTerminationOfTime ">
				AND DATE_FORMAT(a.actual_completion_time,'%Y-%m-%d') &gt;=
				DATE_FORMAT(#{beginTerminationOfTime},'%Y-%m-%d')
			</if>
			<if test="null != endTerminationOfTime ">
				AND DATE_FORMAT(a.actual_completion_time,'%Y-%m-%d') &lt;
				DATE_FORMAT(#{endTerminationOfTime},'%Y-%m-%d')
			</if>

			<if test="null != repairManDeptId and '' != repairManDeptId">
				AND a.repair_man_dept_id = #{repairManDeptId}
			</if>
			<if test="null != fkDeptId and '' != fkDeptId">
				AND a.fk_user_dept_id = #{fkDeptId}
			</if>
			<if test="null != faultTypeId and '' != faultTypeId">
				AND a.FK_FAULT_TYPE_ID = #{faultTypeId}
			</if>
			<if test="null != faultEmergency and '' != faultEmergency">
				AND a.fault_emergency = #{faultEmergency} + 0
			</if>
			<if test="null != repairType and 0 != repairType">
				AND a.repair_type = #{repairType} + 0
			</if>
			<if test="null != faultAffectScope and 0 != faultAffectScope">
				AND a.fault_affect_scope = #{faultAffectScope} + 0
			</if>
			<if test="null != faultDeion and '' != faultDeion">
				AND a.fault_deion LIKE concat('%',#{faultDeion},'%')
			</if>
			<if test="null != workNumber and '' != workNumber">
				AND a.work_number LIKE concat('%',#{workNumber},'%')
			</if>
			<if test="null != repairManId and '' != repairManId">
				AND a.repair_man_id = #{repairManId}
			</if>
			<if test="null != userId and '' != userId">
				AND a.fk_user_id = #{userId}
			</if>
		</where>
	</select>

	<select id="selectOneWsSheetInfo" resultType="cn.trasen.worksheet.module.dto.outVo.WsWsSheetInfoOutVo">
		SELECT a.pk_ws_sheet_id,
		a.work_number,
		ll.PK_WS_TASK_ID,
		a.work_status,
		a.repair_man_id,
		a.repair_man_name,
		a.repair_man_dept_id,
		a.repair_man_dept_name,
		a.repair_dept_address,
		a.repair_phone,
		a.fk_fault_type_id,
		f.full_path fkFaultType,
		a.fault_equipment_name,
		a.fk_fault_equipment_id,
		a.fault_deion,
		a.fault_emergency,
		a.fault_affect_scope,
		a.required_completion_time,
		a.repair_type,
		a.create_by_name,
		a.create_time,
		a.fk_user_id,
		<!-- a.fk_user_name, -->
		CONCAT(a.fk_user_name,'-',a.fk_user_phone) as fk_user_name,
		a.remark,
		k.wcsj actualCompletionTime,
		ROUND(IFNULL(b.WORK_HOURS, 0), 2) work_hours,
		k.rev_time,
		k.confirm_time,
		d.assist,
		d.assist_id,
		IFNULL(e.haten_count, 0) haten_count,
		h.dept_id businessDeptId,
		h.dept_name businessDeptName,
		xx.create_by_name dispatcher,
		xx.create_time dispatchTime,
		a.fk_hospital_district_id fkHospitalDistrictId,
		dd.hospital_district_name hospitalDistrictName,
		fe.equipment_remark as equipmentRemark
		FROM ws_ws_sheet a
		LEFT JOIN (SELECT WORK_NUMBER, SUM(WORK_HOURS) WORK_HOURS
		FROM ws_ws_task
		WHERE WORK_NUMBER = #{workNumber}
		GROUP BY WORK_NUMBER) b ON a.WORK_NUMBER = b.WORK_NUMBER
		LEFT JOIN (SELECT WORK_NUMBER,
		GROUP_CONCAT(CONCAT(a.fk_user_dept_name, '-', a.fk_user_name) SEPARATOR ',') assist,
		GROUP_CONCAT(CONCAT(a.fk_user_id) SEPARATOR ',') assist_id
		FROM (SELECT DISTINCT WORK_NUMBER, fk_user_dept_name, fk_user_name, fk_user_id
		FROM ws_ws_task
		WHERE WORK_NUMBER = #{workNumber}
		AND ASSIST = '1'
		and create_time > (SELECT IFNULL(max(create_time),'2000-01-01') FROM ws_ws_task where take_remark LIKE '工单流转%' and
		WORK_NUMBER = #{workNumber} )) a
		GROUP BY WORK_NUMBER) d ON a.WORK_NUMBER = d.WORK_NUMBER
		LEFT JOIN (
		SELECT count(b.work_number) haten_count,a.work_number
		FROM ws_ws_sheet a LEFT JOIN ws_ws_hasten b ON a.work_number = b.work_number AND A.WORK_NUMBER = #{workNumber}
		GROUP BY a.work_number
		) E ON a.work_number = E.WORK_NUMBER
		LEFT JOIN ws_fault_type f ON a.fk_fault_type_id = f.pk_fault_type_id
		LEFT JOIN (select * from ws_om_meau where delete_status = 0) h ON a.business_dept_id = h.dept_id
		LEFT JOIN (
		SELECT confirm_time,
		wcsj,
		IFNULL(rev_time,claim_time) rev_time,
		#{workNumber} work_number
		FROM (SELECT max(create_time) confirm_time
		FROM ws_ws_task
		WHERE work_status IN ('5','6')
		AND work_number = #{workNumber}) a,
		(SELECT max(create_time) wcsj
		FROM ws_ws_task
		WHERE WORK_NUMBER = #{workNumber}
		AND work_status = '4') b,
		(SELECT max(create_time) rev_time
		FROM ws_ws_task
		WHERE work_number = #{workNumber}
		AND task_name = '-2'
		<!-- AND work_status = '3' AND assist = 0 AND work_hours != 0 -->
		) c,
		( SELECT max( create_time ) claim_time FROM ws_ws_task WHERE work_number = #{workNumber} AND task_name = '-20' ) d
		) k ON a.work_number = k.work_number
		LEFT JOIN (
		SELECT DISTINCT
		a.pk_ws_task_id,
		a.work_number
		FROM
		(
		SELECT
		PK_WS_TASK_ID,
		a.WORK_NUMBER
		FROM
		ws_ws_sheet a
		JOIN ws_ws_task b ON a.WORK_NUMBER = b.WORK_NUMBER
		WHERE
		a.work_status = b.work_status
		<choose>

			<when test="null != repairManId and '' != repairManId">
				and a.repair_man_id = #{repairManId}
			</when>
			<when test="null != fkUserId and '' != fkUserId ">
				and b.fk_user_id = #{fkUserId}
			</when>
		</choose>
		AND a.work_number = #{workNumber}
		AND COMPLETE = 0
		AND ASSIST = 0
		<if test="null != fkUserId and '' != fkUserId ">
			UNION ALL
			SELECT
			max( b.PK_WS_TASK_ID ) PK_WS_TASK_ID, a.WORK_NUMBER
			FROM ws_ws_sheet a
			JOIN ws_ws_task b ON a.WORK_NUMBER = b.WORK_NUMBER
			JOIN ( SELECT max( PK_WS_TASK_ID ) PK_WS_TASK_ID FROM ws_ws_task WHERE ASSIST = 1 AND COMPLETE = 0
			and fk_user_id = #{fkUserId}
			AND work_number = #{workNumber} GROUP BY work_number ) c ON b.PK_WS_TASK_ID = c.PK_WS_TASK_ID
			WHERE
			a.work_status = b.work_status
			and b.fk_user_id = #{fkUserId}
			AND a.work_number = #{workNumber}
			AND ASSIST = 1
			AND COMPLETE = 0
		</if>
		UNION ALL
		SELECT
		b.PK_WS_TASK_ID,
		a.WORK_NUMBER
		FROM
		ws_ws_sheet a
		JOIN ws_ws_task b ON a.WORK_NUMBER = b.WORK_NUMBER
		JOIN ( SELECT max( PK_WS_TASK_ID ) PK_WS_TASK_ID FROM ws_ws_task GROUP BY work_number ) c ON b.PK_WS_TASK_ID =
		c.PK_WS_TASK_ID
		WHERE
		a.work_status = b.work_status
		<choose>
			<when test="null != repairManId and '' != repairManId">
				and a.repair_man_id = #{repairManId}
			</when>
			<when test="null != fkUserId and '' != fkUserId ">
				and b.fk_user_id = #{fkUserId}
			</when>
		</choose>
		AND a.work_number = #{workNumber}
		AND complete = '1'
		AND a.work_status IN ( '6', '8' )
		) a where a.PK_WS_TASK_ID is not null
		) ll ON a.work_number = ll.work_number
		LEFT JOIN (
		select a.create_by_name,a.work_number,a.create_time from ws_ws_task a
		join (select max(CREATE_TIME) CREATE_TIME,work_number from ws_ws_task where task_name = '-5' and work_status = 2 GROUP BY
		work_number) b
		on a.WORK_NUMBER = b.WORK_NUMBER and a.CREATE_TIME = b.CREATE_TIME
		JOIN ws_ws_sheet c on a.WORK_NUMBER = c.WORK_NUMBER and c.work_status != '1'
		) xx ON xx.work_number = a.work_number
		left join ws_hospital_district dd on a.fk_hospital_district_id = dd.pk_hospital_district_id
		LEFT JOIN ws_fault_equipment fe ON a .fk_fault_equipment_id= fe.pk_fault_equipment_id
		where a.WORK_NUMBER = #{workNumber}
		limit 1
		<!-- and h.delete_status = 0 -->
	</select>

	<select id="selectCountGroupByWorkNumber" resultType="java.util.Map">
		SELECT
		SUM( counts ) counts,
		workStatus
		FROM
		(
		SELECT
		count( DISTINCT A.work_number ) counts,
		'1' workStatus
		FROM
		ws_ws_sheet a JOIN ws_ws_task b ON a.work_number = b.work_number and a.delete_status = 0
		WHERE
		a.REPAIR_MAN_ID = #{fkUserId}
		AND a.WORK_STATUS = '1' and a.delete_status = 0
		UNION ALL
		SELECT
		count( DISTINCT A.work_number ) counts,
		a.work_Status workStatus
		FROM
		ws_ws_sheet a JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE
		<if test=' 4 == type'>
			a.FK_USER_ID = #{fkUserId} AND
		</if>
		<if test='5 == type'>
			a.REPAIR_MAN_ID = #{fkUserId} AND
		</if>
		a.WORK_STATUS NOT IN ( '1','3') and a.delete_status = 0
		GROUP BY
		a.WORK_STATUS
		<!-- 服务台已完成页签数量 -->
		UNION ALL
		SELECT
		count( DISTINCT A.work_number ) counts,
		'11' workStatus
		FROM
		ws_ws_sheet a JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE
		a.WORK_STATUS IN ( '5', '6', '8' ) and a.delete_status = 0
		AND a.business_dept_id in (
		<foreach collection="fkDeptId" index="index" item="item" separator=",">#{item}</foreach>
		)
		<!-- 服务台处理中页签数量 -->
		UNION ALL
		SELECT
		count( DISTINCT A.work_number ) counts,
		'10' workStatus
		FROM
		ws_ws_sheet a JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE
		a.delete_status = 0
		and
		a.WORK_STATUS NOT IN ( '1','5', '6', '8' )
		AND a.business_dept_id in (
		<foreach collection="fkDeptId" index="index" item="item" separator=",">#{item}</foreach>
		)

		<if test="null != workNumber and '' != workNumber">
			AND a.work_number LIKE concat('%',#{workNumber},'%')
		</if>
		<if test="null != faultDeion and '' != faultDeion">
			AND a.fault_deion LIKE concat('%',#{faultDeion},'%')
		</if>
		<if test=" null != beginTime ">
			AND DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d') &gt;= DATE_FORMAT(#{beginTime},'%Y-%m-%d')
		</if>
		<if test=" null != endTime ">
			AND DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d') &lt; DATE_FORMAT(#{endTime},'%Y-%m-%d')
		</if>
		<if test="null != workStatusG ">
			AND a.WORK_STATUS in (
			<foreach collection="workStatusG" index="index" item="item" separator=",">#{item}</foreach>
			)
		</if>
		<if test="null != repairManDeptId and '' != repairManDeptId">
			AND a.repair_man_dept_id = #{repairManDeptId}
		</if>
		<if test="null != userId and '' != userId">
			AND a.fk_user_id = #{userId}
		</if>
		<if test="null != repairManId and '' != repairManId">
			AND a.repair_man_id = #{repairManId}
		</if>
		<if test=" null != beginRequiredCompletionTime ">
			AND DATE_FORMAT(a.required_completion_time,'%Y-%m-%d') &gt;=
			DATE_FORMAT(#{beginRequiredCompletionTime},'%Y-%m-%d')
		</if>
		<if test=" null != endRequiredCompletionTime ">
			AND DATE_FORMAT(a.required_completion_time,'%Y-%m-%d') &lt;
			DATE_FORMAT(#{endRequiredCompletionTime},'%Y-%m-%d')
		</if>

		<!-- 服务台待派单页签数量 -->
		UNION ALL
		SELECT
		count( DISTINCT A.work_number ) counts,
		'15' workStatus
		FROM
		ws_ws_sheet a JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE
		a.WORK_STATUS IN ( '1' ) and a.delete_status = 0
		AND a.business_dept_id in (
		<foreach collection="fkDeptId" index="index" item="item" separator=",">#{item}</foreach>
		)
		UNION ALL
		SELECT
		count( DISTINCT a.work_number ) counts,
		'3' workStatus
		FROM
		(
		<if test=' 4 == type'>
			SELECT
			a.work_number
			FROM
			ws_ws_sheet a
			JOIN ws_ws_task b ON a.work_number = b.work_number
			WHERE
			a.work_status = '3'
			AND b.delete_status = 0
			AND b.complete = 0
			AND b.assist = 1
			AND b.fk_user_id = #{fkUserId}
			AND B.create_time &gt; ( SELECT IFNULL( max( create_time ), '2000-01-01' ) FROM ws_ws_task WHERE take_remark LIKE
			'工单流转%' AND WORK_NUMBER = a.work_number )
			UNION ALL
		</if>
		SELECT
		a.work_number
		FROM
		ws_ws_sheet a JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE
		<if test=' 4 == type'>
			a.FK_USER_ID = #{fkUserId} AND
		</if>
		<if test='5 == type'>
			a.REPAIR_MAN_ID = #{fkUserId} AND
		</if>
		a.WORK_STATUS = '3' and a.delete_status = 0
		) a
		) a
		GROUP BY
		workStatus
	</select>
	<select id="getTookPartPageList"
		parameterType="cn.trasen.worksheet.module.dto.inputVo.WsWsSheetListSelectInputVo"
		resultType="cn.trasen.worksheet.module.dto.outVo.WsWsSheetListOutVo">
		SELECT
		a.pk_ws_sheet_id,
		a.work_number,
		a.work_hours,
		a.create_time,
		a.repair_man_dept_id,
		a.repair_man_dept_name,
		a.repair_man_id,
		a.repair_man_name,
		a.create_by,
		a.create_by_name,
		a.repair_phone,
		a.fault_deion,
		a.fk_user_id,
		a.fk_user_name,
		a.required_completion_time,
		IFNULL( c.haten_count, 0 ) haten_count ,
		a.work_status,
		'' pk_ws_task_id,
		'' assist,
		<!--b.pk_ws_task_id, b.assist, -->
		a.repair_dept_address,
		a.fk_fault_type_id,
		a.fault_equipment_name,
		a.fk_fault_equipment_id,
		a.repair_type,
		a.fault_emergency,
		a.fault_affect_scope,
		a.remark,
		a.fk_user_dept_id,
		a.fk_user_dept_name,
		f.full_path fk_fault_type_name,
		p.putIntoWorkHours,
		j.dept_id businessDeptId,
		j.dept_name businessDeptName
		FROM
		ws_ws_sheet a
		JOIN (
		SELECT
		DISTINCT
		a.WORK_NUMBER
		FROM
		ws_ws_task a
		JOIN ( SELECT max( PK_WS_TASK_ID ) TASK_ID FROM ws_ws_task WHERE FK_USER_ID = '366609915005460480' AND WORK_HOURS != 0 AND
		delete_status = 0 GROUP BY work_number
		union all
		SELECT max( PK_WS_TASK_ID ) TASK_ID FROM ws_ws_sheet a join ws_ws_task b WHERE a.create_by ='366609915005460480' AND
		a.delete_status = 0 AND b.delete_status = 0
		GROUP BY b.work_number
		) b ON a.PK_WS_TASK_ID = b.TASK_ID


		<!-- SELECT -->
		<!-- <include refid="cn.trasen.worksheet.module.mapper.WsWsTaskMapper.wsTaskColum"/> -->
		<!-- FROM -->
		<!-- ws_ws_task a -->
		<!-- JOIN ( SELECT max( PK_WS_TASK_ID ) TASK_ID FROM ws_ws_task WHERE FK_USER_ID = #{fkUserId} AND -->
		<!-- WORK_HOURS != 0 AND delete_status = 0 GROUP BY work_number) b ON a.PK_WS_TASK_ID = b.TASK_ID -->
		) b ON a.WORK_NUMBER = b.WORK_NUMBER
		LEFT JOIN (
		SELECT
		count( b.work_number ) haten_count,
		a.work_number
		FROM
		ws_ws_sheet a
		LEFT JOIN ws_ws_hasten b ON a.work_number = b.work_number
		GROUP BY
		a.work_number
		) c ON a.work_number = c.WORK_NUMBER
		LEFT JOIN ws_fault_type f ON a.fk_fault_type_id = f.pk_fault_type_id
		left join (select work_number,sum(work_hours) putIntoWorkHours from ws_ws_task where fk_user_id = #{fkUserId}
		and work_hours!=0 GROUP BY work_number) p
		ON a.work_number = p.work_number
		join ws_om_meau j on a.business_dept_id = j.dept_id
		WHERE
		a.delete_status = 0 and a.work_status!=3 AND j.delete_status = 0
		<if test="null != workNumber and '' != workNumber">
			AND a.work_number LIKE concat('%',#{workNumber},'%')
		</if>
		<if test="null != workStatusValue and '' != workStatusValue">
			AND a.work_Status in (
			<foreach collection="workStatusValueList" index="index" item="item" separator=",">#{item}</foreach>
			)
		</if>
		<if test="null != repairManId and '' != repairManId">
			AND a.repair_man_id = #{repairManId}
		</if>
		<if test=" null != fuzzy and '' != fuzzy">
			AND (a.work_number = #{fuzzy} or a.fk_user_name = #{fuzzy} or a.fault_deion like '%' || #{fuzzy} || '%')
		</if>
		<if test=" null != beginTime ">
			AND DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d') &gt; #{beginTime}
		</if>
		<if test=" null != endTime ">
			AND DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d') &lt; #{endTime}
		</if>
		<if test="null != repairManDeptId and '' != repairManDeptId">
			AND a.repair_man_dept_id = #{repairManDeptId}
		</if>
		<if test="null != faultTypeId and '' != faultTypeId">
			AND a.FK_FAULT_TYPE_ID = #{faultTypeId}
		</if>
		<if test="null != faultEmergency and 0 != faultEmergency">
			AND a.fault_emergency = #{faultEmergency}
		</if>
		<if test="null != faultDeion and 0 != faultDeion">
			AND a.fault_deion LIKE concat('%',#{faultDeion},'%')
		</if>
	</select>
	<select id="getServiceDeskStaffStatisticalIndicators" resultType="java.util.Map">
		select ifnull((select count(1) from ws_ws_sheet where work_status = '1' and business_dept_id in (
		<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>
		)),
		0) dpd,
		ifnull((select count(1)
		from ws_customet_log a
		left join ws_ws_sheet b on a.work_number = b.work_number

		where a.call_type = 0
		and b.pk_ws_sheet_id is null
		AND a.business_dept_id in (
		<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>
		)), 0) wj,
		ifnull((select count(1)
		from ws_customet_log a

		where (a.call_work_status = 2 OR a.work_number IS NOT NULL)
		AND a.business_dept_id in (
		<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>
		)
		and DATE_FORMAT(a.create_time, 'yyyy-mm-dd') = DATE_FORMAT(now(), 'yyyy-mm-dd')), 0) dayyj,
		ifnull((select count(1)
		from ws_customet_log a

		where call_work_status in (5, 2)
		AND a.business_dept_id in (
		<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>
		)
		and DATE_FORMAT(a.create_time, 'yyyy-mm-dd') = DATE_FORMAT(now(), 'yyyy-mm-dd')
		), 0) dayhb,
		ifnull((select count(1)
		from ws_ws_sheet a
		where work_sheet_type = 1
		AND a.create_by_dept_id in (
		<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>
		)
		AND DATE_FORMAT(create_time, 'yyyy-mm-dd') = DATE_FORMAT(now(), 'yyyy-mm-dd')
		), 0) dayfwtjd,
		ifnull((select count(DISTINCT work_number)
		from ws_ws_task
		where DATE_FORMAT(create_time, 'yyyy-mm-dd') = DATE_FORMAT(now(), 'yyyy-mm-dd')
		and create_by_dept_id
		in (
		<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>
		)
		and work_status = '2'), 0) daypd,
		ifnull((select count(1)
		from ws_ws_sheet a
		join ws_customet_log b on a.work_number = b.work_number
		where DATE_FORMAT(a.create_time, 'yyyy-mm-dd') = DATE_FORMAT(now(), 'yyyy-mm-dd')
		and a.create_by_dept_id in (
		<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>
		)
		and b.call_work_status = 4), 0)
		daydhyjj,
		ifnull((select count(1)
		from ws_ws_sheet
		where DATE_FORMAT(create_time, 'yyyy-mm-dd') = DATE_FORMAT(now(), 'yyyy-mm-dd')
		and business_dept_id in (
		<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>
		)
		), 0) dayztd,
		ifnull((select count(1)
		from ws_ws_sheet
		where DATE_FORMAT(actual_completion_time, 'yyyy-mm-dd') = DATE_FORMAT(now(), 'yyyy-mm-dd')
		and business_dept_id in (
		<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>
		)
		), 0) dayzbj,
		ifnull((select count(1)
		from ws_ws_sheet
		where work_status IN ('1', '2', '3', '7')
		and business_dept_id in (
		<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>
		)), 0) wbj
		from dual
	</select>

	<select id="getAbnormalWorkSheetStatisCounts" resultType="java.util.Map">
		SELECT
		IFNULL(( SELECT count( 1 ) FROM ws_ws_sheet WHERE datediff( now(), required_completion_time ) &gt;= -3
		AND work_status IN ( '2', '3', '7')
		<choose>
			<when test=' 4 == type or 5 == type '>
				and fk_user_id = #{fkUserId}
			</when>
			<when test=' 6 == type '>
				and (fk_user_id = #{fkUserId} or repair_man_id = #{fkUserId})
			</when>
			<when test='null != type and ""!=type and 2 == type '>
				and fk_user_dept_id = #{fkUserDeptId} and fk_user_id not in(select pk_external_personnel_id from
				ws_external_personnel)
			</when>
			<when test='null != type and ""!=type and 3 == type '>
				and fk_user_id in(select pk_external_personnel_id from ws_external_personnel)
			</when>
		</choose>
		),0) cq,
		IFNULL((
		SELECT
		count( DISTINCT a.work_number )
		FROM
		ws_ws_sheet a
		JOIN ws_ws_hasten b ON a.work_number = b.work_number
		WHERE
		a.DELETE_STATUS = 0
		AND a.work_status IN ( '2', '3', '7' )
		<choose>
			<when test=' 4 == type or 5 == type '>
				and a.fk_user_id = #{fkUserId}
			</when>
			<when test=' 6 == type '>
				and (a.fk_user_id = #{fkUserId} or a.repair_man_id = #{fkUserId})
			</when>
			<when test='null != type and ""!=type and 2 == type '>
				and a.fk_user_dept_id = #{fkUserDeptId} and a.fk_user_id not in(select pk_external_personnel_id from
				ws_external_personnel)
			</when>
			<when test='null != type and ""!=type and 3 == type '>
				and a.fk_user_id in(select pk_external_personnel_id from ws_external_personnel)
			</when>
		</choose>
		),0) cb,
		IFNULL((
		SELECT
		count( 1 )
		FROM
		ws_ws_sheet a
		JOIN (select ( sum( b.process_speed + b.service_attituude + b.technical_level ) / 3 ) avg,work_number from
		ws_ws_evaluation b
		GROUP BY work_number) b ON a.work_number = b.work_number
		where a.delete_status = 0 and b.avg &lt; 3
		<choose>
			<when test=' 4 == type or 5 == type '>
				and a.fk_user_id = #{fkUserId}
			</when>
			<when test=' 6 == type '>
				and (a.fk_user_id = #{fkUserId} or a.repair_man_id = #{fkUserId})
			</when>
			<when test='null != type and ""!=type and 2 == type '>
				and a.fk_user_dept_id = #{fkUserDeptId} and a.fk_user_id not in(select pk_external_personnel_id from
				ws_external_personnel)
			</when>
			<when test='null != type and ""!=type and 3 == type '>
				and a.fk_user_id in(select pk_external_personnel_id from ws_external_personnel)
			</when>
		</choose>
		),0) cp,
		IFNULL((
		SELECT
		count( 1 )
		FROM
		ws_ws_task
		WHERE
		(( work_status = '7' AND complete = '0' )
		OR work_status = '8' )
		AND DATE_FORMAT( create_time, '%Y-%M-%D' ) = DATE_FORMAT( NOW(), '%Y-%M-%D' )
		<choose>
			<when test=' 4 == type or 5 == type '>
				and fk_user_id = #{fkUserId}
			</when>
			<when test='null != type and ""!=type and 2 == type '>
				and fk_user_dept_id = #{fkUserDeptId} and fk_user_id not in(select pk_external_personnel_id from
				ws_external_personnel)
			</when>
			<when test='null != type and ""!=type and 3 == type '>
				and fk_user_id in(select pk_external_personnel_id from ws_external_personnel)
			</when>
		</choose>
		),0) zzzz,
		IFNULL(( SELECT count( DISTINCT a.work_number ) FROM ws_ws_sheet a JOIN ws_ws_back b ON a.work_number =
		b.work_number
		where a.delete_status = 0 and a.work_status not in('4','5','6','8') and b.type = 2
		<choose>
			<when test=' 4 == type or 5 == type '>
				and a.fk_user_id = #{fkUserId}
			</when>
			<when test=' 6 == type '>
				and (a.fk_user_id = #{fkUserId} or a.repair_man_id = #{fkUserId})
			</when>
			<when test='null != type and ""!=type and 2 == type '>
				and a.fk_user_dept_id = #{fkUserDeptId} and a.fk_user_id not in(select pk_external_personnel_id from
				ws_external_personnel)
			</when>
			<when test='null != type and ""!=type and 3 == type '>
				and a.fk_user_id in(select pk_external_personnel_id from ws_external_personnel)
			</when>
		</choose>
		),0) dh
		,(
		select count( DISTINCT a.work_number) assist from ws_ws_sheet a join ws_ws_task b on a.work_number = b.work_number
		where a.work_status = b.work_status and b.assist = 1 and b.complete = 0
		and B.create_time > ( SELECT IFNULL( max( create_time ), '2000-01-01' ) FROM ws_ws_task WHERE take_remark LIKE '工单流转%'
		AND WORK_NUMBER = a.work_number )
		<choose>
			<when test=' 4 == type '>
				and b.fk_user_id = #{fkUserId}
			</when>
			<when test=' 6 == type '>
				and (b.fk_user_id = #{fkUserId} or a.repair_man_id = #{fkUserId})
			</when>
			<when test='2 == type '>
				and b.fk_user_dept_id = #{fkUserDeptId} and b.fk_user_id not in(select pk_external_personnel_id from
				ws_external_personnel)
			</when>
			<when test='null != type and ""!=type and 3 == type '>
				and b.fk_user_id in(select pk_external_personnel_id from ws_external_personnel)
			</when>
		</choose>
		) assist,
		IFNULL((
		SELECT
		count( 1 )
		FROM
		ws_ws_sheet a
		JOIN ws_ws_task b ON a.work_number = b.work_number and a.work_status = b.work_status
		WHERE
		( a.work_status = '7' AND b.complete = '0' )
		AND DATE_FORMAT( b.create_time, '%Y-%M-%D' ) = DATE_FORMAT( NOW(), '%Y-%M-%D' )
		<choose>
			<when test=' 4 == type '>
				and b.fk_user_id = #{fkUserId}
			</when>
			<when test=' 5 == type '>
				and a.repair_man_id = #{fkUserId}
			</when>
			<when test=' 6 == type '>
				and (b.fk_user_id = #{fkUserId} or a.repair_man_id = #{fkUserId})
			</when>
		</choose>
		),
		0
		) zt,
		IFNULL((
		SELECT
		count( 1 )
		FROM
		ws_ws_sheet a
		JOIN ws_ws_task b ON a.work_number = b.work_number and a.work_status = b.work_status
		WHERE
		a.work_status = '8'
		AND DATE_FORMAT( b.create_time, '%Y-%M-%D' ) = DATE_FORMAT( NOW(), '%Y-%M-%D' )
		<choose>
			<when test=' 4 == type '>
				and a.fk_user_id = #{fkUserId}
			</when>
			<when test=' 5 == type '>
				and a.repair_man_id = #{fkUserId}
			</when>
			<when test=' 6 == type '>
				and (a.fk_user_id = #{fkUserId} or a.repair_man_id = #{fkUserId})
			</when>
		</choose>
		),
		0
		) zz

		FROM
		DUAL
	</select>

	<select id="getExceedTimeWorkSheets" resultType="cn.trasen.worksheet.module.dto.outVo.WsWorkSheetHomeListOutVo">
		SELECT
		work_status,
		a.WORK_NUMBER,
		datediff( now(), required_completion_time ) cqDay,
		required_completion_time,
		fault_deion,
		repair_man_dept_name,
		repair_man_name,
		create_time,
		b.jdsj taskCreateTime,
		c.take_remark,
		a.fk_user_id,
		a.fk_user_name,
		a.fk_user_dept_name
		FROM
		ws_ws_sheet a
		LEFT JOIN ( SELECT WORK_NUMBER, max( create_time ) jdsj FROM ws_ws_task WHERE work_status = '3' GROUP BY
		WORK_NUMBER ) b ON a.WORK_NUMBER = b.WORK_NUMBER
		LEFT JOIN ( SELECT WORK_NUMBER,take_remark FROM ws_ws_task WHERE work_status = '7' and complete = 0 ) c ON
		a.WORK_NUMBER = c.WORK_NUMBER
		WHERE
		datediff( now(), required_completion_time ) &gt;= -3
		AND work_status IN ( '2', '3', '7')
		<choose>
			<when test='null != type and ""!=type and 4 == type '>
				and a.fk_user_id = #{fkUserId}
			</when>
			<when test=' 6 == type '>
				and (a.fk_user_id = #{fkUserId} or a.repair_man_id = #{fkUserId})
			</when>
			<when test='null != type and ""!=type and 2 == type '>
				and a.fk_user_dept_id = #{fkUserDeptId} and a.fk_user_id not in(select pk_external_personnel_id from
				ws_external_personnel)
			</when>
			<when test='null != type and ""!=type and 3 == type '>
				and a.fk_user_id not in(select pk_external_personnel_id from ws_external_personnel)
			</when>
		</choose>


	</select>

	<select id="getHastenWorkSheets" resultType="cn.trasen.worksheet.module.dto.outVo.WsWorkSheetHomeListOutVo">
		SELECT
		b.work_number,
		b.fk_user_id,
		b.fk_user_name,
		fk_user_dept_name,
		b.required_completion_time,
		b.fault_deion,
		c.gxsj taskCreateTime,
		a.cbsj operatingTime,
		a.counts
		FROM
		( SELECT max( create_time ) cbsj, count( 1 ) counts, work_number FROM ws_ws_hasten GROUP BY work_number ) a
		JOIN ws_ws_sheet b ON a.work_number = b.work_number
		JOIN ( SELECT work_number, max( create_time ) gxsj FROM ws_ws_task GROUP BY WORK_NUMBER ) c ON a.work_number =
		c.work_number
		where work_status IN ( '2', '3', '7' )
		<choose>
			<when test='null != type and ""!=type and 4 == type '>
				and b.fk_user_id = #{fkUserId}
			</when>
			<when test=' 6 == type '>
				and (b.fk_user_id = #{fkUserId} or b.repair_man_id = #{fkUserId})
			</when>
			<when test='null != type and ""!=type and 2 == type '>
				and b.fk_user_dept_id = #{fkUserDeptId} and b.fk_user_id not in(select pk_external_personnel_id from
				ws_external_personnel)
			</when>
			<when test='null != type and ""!=type and 3 == type '>
				and b.fk_user_id not in(select pk_external_personnel_id from ws_external_personnel)
			</when>
		</choose>

	</select>

	<select id="getSuspendTerminateSheets"
		resultType="cn.trasen.worksheet.module.dto.outVo.WsWorkSheetHomeListOutVo">
		SELECT
		a.work_number,
		a.work_status,
		a.fk_user_name,
		a.fk_user_id,
		a.fk_user_dept_name,
		a.required_completion_time,
		a.fault_deion,
		a.repair_man_dept_name,
		a.repair_man_name,
		a.create_time,
		b.take_remark
		FROM
		ws_ws_sheet a
		LEFT JOIN (
		SELECT
		WORK_NUMBER,
		take_remark,
		create_time,
		work_status
		FROM
		ws_ws_task

		<where>
			work_status = #{workStatus} AND complete = #{complete}
		</where>
		<!-- ( work_status = '7' AND complete = 0 ) OR ( work_status = 8 AND complete = 1 ) -->
		) b ON a.WORK_NUMBER = b.WORK_NUMBER and a.work_status = b.work_status
		WHERE
		a.work_status = #{workStatus}
		<!-- work_status IN ( '7', '8' ) -->
		AND DATE_FORMAT( b.create_time, '%Y-%M-%D' ) = DATE_FORMAT( NOW(), '%Y-%M-%D' )
		<choose>
			<when test='null != type and ""!=type and 4 == type '>
				and a.fk_user_id = #{fkUserId}
			</when>
			<when test='null != type and ""!=type and 2 == type '>
				and a.fk_user_dept_id = #{fkUserDeptId} and a.fk_user_id not in(select pk_external_personnel_id from
				ws_external_personnel)
			</when>
			<when test='null != type and ""!=type and 3 == type '>
				and a.fk_user_id not in(select pk_external_personnel_id from ws_external_personnel)
			</when>
		</choose>
	</select>

	<select id="getBadReviewSheets" resultType="cn.trasen.worksheet.module.dto.outVo.WsWorkSheetHomeListOutVo">
		SELECT
		ROUND( sum( b.process_speed + b.service_attituude + b.technical_level ) / 3, 2 ) comprehensiveScore,
		a.fk_user_name,
		a.fk_user_id,
		a.fk_user_dept_name,
		a.actual_completion_time,
		a.fault_deion,
		a.repair_man_name,
		a.repair_man_dept_name,
		a.repair_phone,
		b.create_time operatingTime,
		a.work_status,
		a.work_number
		FROM
		ws_ws_sheet a
		JOIN ws_ws_evaluation b ON a.work_number = b.work_number
		WHERE
		a.delete_status = 0
		<choose>
			<when test='null != type and ""!=type and 4 == type '>
				and a.fk_user_id = #{fkUserId}
			</when>
			<when test=' 6 == type '>
				and (a.fk_user_id = #{fkUserId} or a.repair_man_id = #{fkUserId})
			</when>
			<when test='null != type and ""!=type and 2 == type '>
				and a.fk_user_dept_id = #{fkUserDeptId} and a.fk_user_id not in(select pk_external_personnel_id from
				ws_external_personnel)
			</when>
			<when test='null != type and ""!=type and 3 == type '>
				and a.fk_user_id not in(select pk_external_personnel_id from ws_external_personnel)
			</when>
		</choose>
		GROUP BY
		a.work_number
		HAVING
		( sum( b.process_speed + b.service_attituude + b.technical_level ) / 3 ) &lt; 3
	</select>

	<select id="getBackSheets" resultType="cn.trasen.worksheet.module.dto.outVo.WsWorkSheetHomeListOutVo">
		SELECT
		b.counts,
		a.fk_user_id,
		a.fk_user_name,
		a.fk_user_dept_name,
		a.actual_completion_time,
		a.fault_deion,
		a.repair_man_name,
		a.repair_man_dept_name,
		a.create_time,
		b.back_reason takeRemark,
		a.work_status,
		a.work_number
		FROM
		ws_ws_sheet a
		JOIN (
		SELECT
		a.counts,
		a.create_time,
		a.work_number,
		b.back_reason
		FROM
		( SELECT count( 1 ) counts, max( create_time ) create_time, work_number, max( pk_ws_back_id ) pk_ws_back_id FROM
		ws_ws_back where type = 2 GROUP BY work_number ) a
		JOIN ws_ws_back b ON a.pk_ws_back_id = b.pk_ws_back_id
		) b ON a.work_number = b.work_number
		WHERE
		a.delete_status = 0 and a.work_status not in('4','5','6','8')
		<choose>
			<when test='null != type and ""!=type and 4 == type '>
				and a.fk_user_id = #{fkUserId}
			</when>
			<when test=' 6 == type '>
				and (a.fk_user_id = #{fkUserId} or a.repair_man_id = #{fkUserId})
			</when>
			<when test='null != type and ""!=type and 2 == type '>
				and a.fk_user_dept_id = #{fkUserDeptId} and a.fk_user_id not in(select pk_external_personnel_id from
				ws_external_personnel)
			</when>
			<when test='null != type and ""!=type and 3 == type '>
				and a.fk_user_id not in(select pk_external_personnel_id from ws_external_personnel)
			</when>
		</choose>

	</select>

	<select id="getCountGroupByWorkStatus" resultType="java.util.Map">
		SELECT a.work_status, count( 1 ) counts
		FROM (
		SELECT a.work_status,a.work_number
		FROM ws_ws_sheet a
		WHERE delete_status = 0
		<if test="null != type and 0 != type">
			<choose>
				<when test='5 == type'>
					and repair_man_id = #{fkUserId}
				</when>
				<when test='4 == type'>
					and fk_user_id = #{fkUserId}
				</when>
				<when test='6 == type'>
					and (fk_user_id = #{fkUserId} or repair_man_id = #{fkUserId})
				</when>
				<when test='2 == type'>
					and business_dept_id = #{fkUserDeptId}
				</when>
			</choose>
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test=" null != beginTime and ''!= beginTime">
					and to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= b.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= b.create_time
				</if>
			</when>
			<otherwise>
				<if test=" null != beginTime and ''!= beginTime">
					and str_to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= create_time
				</if>
			</otherwise>
		</choose>
		UNION
		SELECT a.work_status, a.work_number
		FROM ws_ws_sheet a
		LEFT JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE TAKE_REMARK LIKE '工单流转%'
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test=" null != beginTime and ''!= beginTime">
					and to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= b.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= b.create_time
				</if>
			</when>
			<otherwise>
				<if test=" null != beginTime and ''!= beginTime">
					and str_to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= b.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= b.create_time
				</if>
			</otherwise>
		</choose>
		and a.delete_status = 0
		<if test="null != type and 0 != type">
			<choose>
				<when test='5 == type'>
					and a.repair_man_id = #{fkUserId}
				</when>
				<when test='4 == type'>
					and a.fk_user_id = #{fkUserId}
				</when>
				<when test='6 == type'>
					and (a.fk_user_id = #{fkUserId} or a.repair_man_id = #{fkUserId})
				</when>
				<when test='2 == type'>
					and a.business_dept_id = #{fkUserDeptId}
				</when>
			</choose>
		</if>
		) a
		GROUP BY a.work_status
	</select>

	<select id="getWorkOrderProcessing" resultType="java.util.Map">
		SELECT a.date, a.tdcounts,IFNULL(b.bjcounts,0) bjcounts
		FROM
		( SELECT count( DISTINCT k.work_number ) tdcounts
		<choose>
			<when test=' 0 == dayOrMonthType'>
				,DATE_FORMAT( create_time, '%Y-%m-%d' ) date
			</when>
			<when test=' 1 == dayOrMonthType'>
				,DATE_FORMAT( create_time, '%Y-%m' ) date
			</when>
			<otherwise>
				,DATE_FORMAT( create_time, '%Y' ) date
			</otherwise>
		</choose>
		from (
		SELECT
		a.work_number,
		max( a.create_time ) create_time
		FROM
		(
		select a.work_number,
		a.create_time
		FROM
		ws_ws_sheet a
		WHERE
		delete_status = 0
		<if test="null != fkUserDeptId and ''!= fkUserDeptId">
			and business_dept_id = #{fkUserDeptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test=" null != beginTime and ''!= beginTime">
					and to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= create_time
				</if>
			</when>
			<otherwise>
				<if test=" null != beginTime and ''!= beginTime">
					and str_to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= create_time
				</if>
			</otherwise>
		</choose>
		UNION
		SELECT a.work_number, b.create_time
		FROM ws_ws_sheet a
		LEFT JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE TAKE_REMARK LIKE '工单流转%'
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test=" null != beginTime and ''!= beginTime">
					and to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= b.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= b.create_time
				</if>
			</when>
			<otherwise>
				<if test=" null != beginTime and ''!= beginTime">
					and str_to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= b.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= b.create_time
				</if>
			</otherwise>
		</choose>
		<if test="null != fkUserDeptId and ''!= fkUserDeptId">
			and a.business_dept_id = #{fkUserDeptId}
		</if>
		) a
		GROUP BY work_number
		) k GROUP BY
		<choose>
			<when test=' 0 == dayOrMonthType'>
				DATE_FORMAT( create_time, '%Y-%m-%d' )
			</when>
			<when test=' 1 == dayOrMonthType'>
				DATE_FORMAT( create_time, '%Y-%m' )
			</when>
			<otherwise>
				DATE_FORMAT( create_time, '%Y' )
			</otherwise>
		</choose>
		) a
		LEFT JOIN (
		SELECT
		count( DISTINCT work_number ) bjcounts
		<choose>
			<when test=' 0 == dayOrMonthType'>
				,DATE_FORMAT( actual_completion_time, '%Y-%m-%d' ) date
			</when>
			<when test=' 1 == dayOrMonthType'>
				,DATE_FORMAT( actual_completion_time, '%Y-%m' ) date
			</when>
			<otherwise>
				,DATE_FORMAT( actual_completion_time, '%Y' ) date
			</otherwise>
		</choose>
		FROM ws_ws_sheet
		WHERE delete_status = 0 and work_status in ('5','6','8')
		<if test="null != fkUserDeptId and ''!= fkUserDeptId">
			and business_dept_id = #{fkUserDeptId}
		</if>
		AND actual_completion_time IS NOT NULL
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test=" null != beginTime and ''!= beginTime">
					and to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= actual_completion_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= actual_completion_time
				</if>
			</when>
			<otherwise>
				<if test=" null != beginTime and ''!= beginTime">
					and str_to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= actual_completion_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= actual_completion_time
				</if>
			</otherwise>
		</choose>
		GROUP BY
		<choose>
			<when test=' 0 == dayOrMonthType'>
				DATE_FORMAT( actual_completion_time, '%Y-%m-%d' )
			</when>
			<when test=' 1 == dayOrMonthType'>
				DATE_FORMAT( actual_completion_time, '%Y-%m' )
			</when>
			<otherwise>
				DATE_FORMAT( actual_completion_time, '%Y' )
			</otherwise>
		</choose>
		) b ON a.date = b.date

	</select>

	<select id="getWorkOrderUnfinished" resultType="java.lang.Integer">
		SELECT
		count( DISTINCT work_number ) UnfinishedNumber
		FROM ws_ws_sheet
		WHERE delete_status = 0 and work_status in ('1','2','3','7')
		<if test="null != fkUserDeptId and ''!= fkUserDeptId">
			and business_dept_id = #{fkUserDeptId}
		</if>
	</select>
	
	<select id="getWorkByTypeDatas" resultType="java.util.Map">
		SELECT C.${statusType} name, count( DISTINCT C.work_number ) value
		FROM (SELECT A.*
		FROM ws_ws_sheet A
		WHERE delete_status = 0
		<if test="null != fkUserDeptId and '' != fkUserDeptId">
			and business_dept_id = #{fkUserDeptId}
		</if>
		<if test="null != fkUserId and '' != fkUserId">
			and (repair_man_id = #{fkUserId} or fk_user_id = #{fkUserId})
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test=" null != beginTime and ''!= beginTime">
					and to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= create_time
				</if>
			</when>
			<otherwise>
				<if test=" null != beginTime and ''!= beginTime">
					and str_to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= create_time
				</if>
			</otherwise>
		</choose>
		UNION
		SELECT A.*
		FROM ws_ws_sheet a
		LEFT JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE TAKE_REMARK LIKE '工单流转%'
		<if test="null != fkUserDeptId and '' != fkUserDeptId">
			and A.business_dept_id = #{fkUserDeptId}
		</if>
		<if test="null != fkUserId and '' != fkUserId">
			and (a.repair_man_id = #{fkUserId} or a.fk_user_id = #{fkUserId})
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test=" null != beginTime and ''!= beginTime">
					and to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= b.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= b.create_time
				</if>
			</when>
			<otherwise>
				<if test=" null != beginTime and ''!= beginTime">
					and str_to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= b.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= b.create_time
				</if>
			</otherwise>
		</choose>
		) C
		GROUP BY C.${statusType}
	</select>

	<select id="getDeptReceiveWorkSheetDatas" resultType="java.util.Map">
		SELECT
		fk_user_dept_name,
		SUM(IF(work_status='1',counts,0)) as dispatch,
		SUM(IF(work_status='2',counts,0)) as waiting,
		SUM(IF(work_status='3',counts,0)) as processing,
		SUM(IF(work_status='4',counts,0)) as acceptance,
		SUM(IF(work_status='5',counts,0)) as evaluate,
		SUM(IF(work_status='6',counts,0)) as completed,
		SUM(IF(work_status='7',counts,0)) as suspended,
		SUM(IF(work_status='8',counts,0)) as terminat,
		sum(counts) sum
		FROM
		(select dept_name fk_user_dept_name,work_status,count(1) counts from (
		SELECT A.*
		FROM ws_ws_sheet A
		WHERE
		delete_status = 0
		<if test=" null != fkUserDeptId and ''!= fkUserDeptId">
			and a.business_dept_id = #{fkUserDeptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test=" null != beginTime and ''!= beginTime">
					and to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= a.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= a.create_time
				</if>
			</when>
			<otherwise>
				<if test=" null != beginTime and ''!= beginTime">
					and str_to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= a.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= a.create_time
				</if>
			</otherwise>
		</choose>
		UNION
		SELECT A.*
		FROM ws_ws_sheet a
		LEFT JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE TAKE_REMARK LIKE '工单流转%'
		<if test=" null != fkUserDeptId and ''!= fkUserDeptId">
			and a.business_dept_id = #{fkUserDeptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test=" null != beginTime and ''!= beginTime">
					and to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= b.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= b.create_time
				</if>
			</when>
			<otherwise>
				<if test=" null != beginTime and ''!= beginTime">
					and str_to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= b.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= b.create_time
				</if>
			</otherwise>
		</choose>
		) a
		join ws_om_meau b on a.business_dept_id = b.dept_id
		where
		<!-- a.fk_user_id is not null and -->
		b.DELETE_STATUS = 0
		<!-- and a.work_status != '1' -->
		GROUP BY a.business_dept_id,a.work_status, b.dept_name) a
		GROUP BY fk_user_dept_name
	</select>

	<select id="getDeptUserReceiveWorkSheetDatas" resultType="java.util.Map">
		SELECT
		fk_user_dept_name ,fk_user_name ,(select employee_no from ts_base_oa.cust_emp_base where employee_id=fk_user_id) fk_user_id,
		SUM(IF(work_status='2',counts,0)) as waiting,
		SUM(IF(work_status='3',counts,0)) as processing,
		SUM(IF(work_status='4',counts,0)) as acceptance,
		SUM(IF(work_status='5',counts,0)) as evaluate,
		SUM(IF(work_status='6',counts,0)) as completed,
		SUM(IF(work_status='7',counts,0)) as suspended,
		SUM(IF(work_status='8',counts,0)) as terminat,
		sum(counts) sum
		FROM
		(select dept_name fk_user_dept_name,ifnull(fk_user_id,'') fk_user_id,ifnull(fk_user_name,'') fk_user_name,
		work_status,count(1) counts from (
		SELECT A.*
		FROM ws_ws_sheet A
		WHERE
		delete_status = 0
		<if test=" null != fkUserDeptId and ''!= fkUserDeptId">
			and a.business_dept_id = #{fkUserDeptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test=" null != beginTime and ''!= beginTime">
					and to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= a.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= a.create_time
				</if>
			</when>
			<otherwise>
				<if test=" null != beginTime and ''!= beginTime">
					and str_to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= a.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= a.create_time
				</if>
			</otherwise>
		</choose>
		UNION
		SELECT
		A.*
		FROM
		ws_ws_sheet a
		LEFT JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE
		TAKE_REMARK LIKE '工单流转%'
		<if test=" null != fkUserDeptId and ''!= fkUserDeptId">
			and a.business_dept_id = #{fkUserDeptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test=" null != beginTime and ''!= beginTime">
					and to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= b.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= b.create_time
				</if>
			</when>
			<otherwise>
				<if test=" null != beginTime and ''!= beginTime">
					and str_to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= b.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= b.create_time
				</if>
			</otherwise>
		</choose>
		) a
		join ws_om_meau b on a.business_dept_id = b.dept_id
		where
		<!-- a.fk_user_id is not null and -->
		b.DELETE_STATUS = 0
		and a.work_status != '1'
		GROUP BY a.business_dept_id,fk_user_name,fk_user_id,a.work_status, b.dept_name) a
		GROUP BY fk_user_dept_name,fk_user_name,fk_user_id

	</select>

	<select id="getFaultEquipment" resultType="java.util.Map">
		SELECT
		equipment_name equipmentName,
		count( b.work_number ) total,
		sum(case when b.work_status IN ( '5', '6', '8' ) then 1 else 0 end) wsTotal
		FROM
		ws_fault_equipment a
		left JOIN ws_ws_sheet b ON a.pk_fault_equipment_id = b.fk_fault_equipment_id
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND b.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND b.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND b.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND b.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		WHERE
		a.delete_status = 0
		GROUP BY
		a.pk_fault_equipment_id
	</select>

	<select id="getDeptCountTopDatas" resultType="java.util.Map">
		SELECT
		max( repair_man_dept_name ) name,
		count( 1 ) total,
		sum(case when work_status IN ( '5', '6', '8' ) then 1 else 0 end) wsTotal,
		sum(case when work_status not IN ( '5', '6','8' ) then 1 else 0 end) wwcTotal
		FROM
		(SELECT A.*
		FROM ws_ws_sheet A
		WHERE
		delete_status = 0
		<if test="null != deptId and '' != deptId">
			and business_dept_id =#{deptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		UNION
		SELECT
		A.*
		FROM
		ws_ws_sheet a
		LEFT JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE
		TAKE_REMARK LIKE '工单流转%'
		<if test="null != deptId and '' != deptId">
			and a.business_dept_id =#{deptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND b.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND b.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND b.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND b.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		) C GROUP BY repair_man_dept_id
	</select>

	<select id="getLevelOneFaultTypeDatas" resultType="java.util.Map">
		SELECT
		category_name categoryName,
		count( b.work_number ) total,
		sum(case when b.work_status in('5','6','8') then 1 else 0 end) wcTotal
		FROM
		ws_fault_type a
		left JOIN ws_ws_sheet b ON a.pk_fault_type_id = b.fk_fault_type_id
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND b.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND b.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND b.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND b.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		WHERE
		parent_id IS NULL and a.delete_status = 0
		GROUP BY
		a.pk_fault_type_id

	</select>

	<select id="getKnowledgeLikeCountTopDatas" resultType="java.util.Map">
		select
		b.fk_user_dept_name fkUserDeptName,
		b.fk_user_name fkUserName,
		b.total,
		( select count(1) FROM
		ws_knowledge_base a
		JOIN ws_knowledge_like b ON a.pk_knowledge_base_id = b.fk_knowledge_base_id
		WHERE
		knowledge_status = 2 ) sum
		from (
		select
		a.fk_user_dept_id,
		a.fk_user_dept_name,
		a.fk_user_name,
		a.total as total,
		a.contribution_time contributionTime,
		(select
		COUNT(DISTINCT b.total)
		from (
		SELECT
		max( a.fk_user_dept_name ) fk_user_dept_name,
		max( a.fk_user_name ) fk_user_name,
		count( 1 ) total
		FROM ws_knowledge_base a
		JOIN ws_knowledge_like b ON a.pk_knowledge_base_id = b.fk_knowledge_base_id
		WHERE knowledge_status not in (0,-1)
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		GROUP BY a.fk_user_id
		) b where b.total &gt;= a.total
		) as rankbak
		from ( SELECT
		max( a.fk_user_dept_id ) fk_user_dept_id,
		max( a.fk_user_dept_name ) fk_user_dept_name,
		max( a.fk_user_name ) fk_user_name,
		count( 1 ) total,
		max(contribution_time) contribution_time
		FROM ws_knowledge_base a
		JOIN ws_knowledge_like b ON a.pk_knowledge_base_id = b.fk_knowledge_base_id
		WHERE knowledge_status not in (0,-1)
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>

			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		GROUP BY a.fk_user_id
		) a
		) b
		<where>
			<if test="null != deptId and '' != deptId">
				b.fk_user_dept_id =#{deptId}
			</if>
		</where>
		order by b.total DESC,b.contributionTime
	</select>

	<select id="getKeyDataIndicatorsOfWorkOrder" resultType="java.util.Map">
		<!-- select -->
		<!-- <if test="null != type and '' != type"> -->
		<!-- &lt;!&ndash; , &ndash;&gt; -->
		<!-- (select count(1) -->
		<!-- from ws_ws_sheet a -->
		<!-- where -->
		<!-- business_dept_id = #{fkUserDeptId} and -->
		<!-- DATE_FORMAT( CREATE_TIME, '%Y-%m-%d' ) = DATE_FORMAT( now(), '%Y-%m-%d' ) -->
		<!-- ) daydeptyjd, -->
		<!-- ( -->
		<!-- select sum(counts) from -->
		<!-- ( -->
		<!-- select count(1) counts -->
		<!-- from ws_ws_sheet a join ws_ws_task b on a.work_number = b.work_number and a.work_status = b.work_status -->
		<!-- where DATE_FORMAT( b.create_time, '%Y-%m-%d' ) = DATE_FORMAT( now(), '%Y-%m-%d' ) -->
		<!-- and a.work_status = '4' -->
		<!-- AND business_dept_id = #{fkUserDeptId} -->
		<!-- &lt;!&ndash; union all -->
		<!-- select count(1) counts -->
		<!-- from ws_ws_sheet -->
		<!-- where DATE_FORMAT(actual_completion_time, '%Y-%m-%d' ) = DATE_FORMAT( now(), '%Y-%m-%d' ) -->
		<!-- AND business_dept_id = #{fkUserDeptId} &ndash;&gt; -->
		<!-- ) f -->
		<!-- )daydeptywc, -->
		<!-- ( select count(1) from ws_ws_sheet a -->
		<!-- where a.work_status in('1','2') -->
		<!-- AND business_dept_id = #{fkUserDeptId} ) daydeptdjd, -->
		<!-- (select count(1) from ws_ws_sheet a -->
		<!-- where work_status = '3' -->
		<!-- AND a.business_dept_id = #{fkUserDeptId} -->
		<!-- ) daydeptjxz -->
		<!-- <choose> -->
		<!-- <when test="null != faultType and '' != faultType"> -->
		<!-- ,(select IFNULL(ROUND((select count(1) from ws_ws_sheet WHERE work_status = '3' and business_dept_id -->
		<!-- = #{fkUserDeptId} )/count(DISTINCT -->
		<!-- c.fk_user_id),1),0) avgWork from ws_ws_sheet a -->
		<!-- join ws_fault_type b on a.fk_fault_type_id = b.pk_fault_type_id -->
		<!-- join ws_fault_man c on b.pk_fault_type_id = c.fk_fault_type_id -->
		<!-- where a.business_dept_id = #{fkUserDeptId}) avgWork -->
		<!-- </when> -->
		<!-- <otherwise> -->
		<!-- ,(select ROUND( -->
		<!-- count(1)/(#{count} + ( SELECT count( 1 ) FROM ws_external_personnel WHERE belongs_dept_id = -->
		<!-- #{fkUserDeptId} AND STATUS = 1 )) -->
		<!-- ,1) avgWork from ws_ws_sheet where -->
		<!-- work_status = '3' AND -->
		<!-- business_dept_id =#{fkUserDeptId}) -->
		<!-- avgWork -->
		<!-- </otherwise> -->
		<!-- </choose> -->
		<!-- </if> -->
		<!-- from dual -->
	</select>
	<select id="getHotTrend" resultType="java.util.Map">
		select
		a.date,
		a.td,
		IFNULL(b.dhtd,0) dhtd,
		IFNULL(c.bj,0) bj
		from
		(select
		DATE_FORMAT( create_time, '%Y-%m' ) date,
		count(1) td
		from ws_ws_sheet
		where delete_status = 0
		<if test="null != type and '' != type">
			and business_dept_id = #{fkUserDeptId}
		</if>
		GROUP BY DATE_FORMAT( create_time, '%Y-%m' )
		) a
		left join (
		select
		DATE_FORMAT( create_time, '%Y-%m' ) date,
		count(1) dhtd
		from ws_ws_sheet
		where repair_type = 1
		<if test="null != type and '' != type">
			and business_dept_id = #{fkUserDeptId}
		</if>
		GROUP BY DATE_FORMAT( create_time, '%Y-%m' )
		) b on a.date = b.date
		left join (
		select
		DATE_FORMAT( actual_completion_time, '%Y-%m' ) date,
		count(1) bj
		from ws_ws_sheet
		where actual_completion_time is not null
		<if test="null != type and '' != type">
			and business_dept_id = #{fkUserDeptId}
		</if>
		GROUP BY DATE_FORMAT( actual_completion_time, '%Y-%m' )
		) c on a.date = c.date
	</select>
	<select id="getProcessTheWorkOrder" resultType="java.util.Map">
		SELECT
		<choose>
			<when test="null != type and ''!=type">
				fk_user_name name,
			</when>
			<otherwise>
				fk_user_dept_name name,
			</otherwise>
		</choose>
		fault_deion
		FROM ws_ws_sheet
		WHERE work_status = '3'
		<if test="null != type and ''!=type">
			and business_dept_id = #{fkUserDeptId}
		</if>
		ORDER BY create_time
	</select>

	<select id="getDeptWorkSheetCount" resultType="java.util.Map">
		SELECT
		<choose>
			<when test="null != type and ''!=type">
				max( fk_user_name ) name,
			</when>
			<otherwise>
				max( fk_user_dept_name ) name,
			</otherwise>
		</choose>
		sum(case when work_status IN ( '2', '3', '7' ) then 1 else 0 end ) wwc,
		sum(case when work_status IN ( '4','5', '6', '8' ) then 1 else 0 end ) ywc
		FROM
		ws_ws_sheet
		WHERE delete_status = 0
		<if test="null != type and ''!=type">
			and fk_user_dept_id = #{fkUserDeptId}
		</if>
		GROUP BY
		<choose>
			<when test="null != type and ''!=type">
				fk_user_id
			</when>
			<otherwise>
				fk_user_dept_id
			</otherwise>
		</choose>
	</select>

	<select id="getDepartmentWorkOrderQuality" resultType="cn.trasen.worksheet.module.dto.outVo.WsEvaluationOutVo">
		select a.avgScore, c.fcmy, c.my, c.yb, c.bmy, c.hbmy
		from ( SELECT ROUND( avg( davg ), 2 ) avgScore
		FROM ( SELECT
		sum( process_speed + service_attituude + technical_level )/ 3 davg
		FROM ws_ws_evaluation a
		join ws_ws_sheet b on a.work_number = b.work_number
		where a.delete_status = 0
		<if test="null != fkUserDeptId and ''!=fkUserDeptId">
			and b.fk_user_dept_id = #{fkUserDeptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		GROUP BY pk_ws_evaluation_id ) a
		) a,

		( select
		sum(case when davg = 5 then 1 else 0 end) fcmy,
		sum(case when davg &lt; 5 and davg &gt;= 4 then 1 else 0 end) my,
		sum(case when davg &lt; 4 and davg &gt;= 3 then 1 else 0 end) yb,
		sum(case when davg &lt; 3 and davg &gt;= 2 then 1 else 0 end) bmy,
		sum(case when davg &lt; 2 then 1 else 0 end) hbmy
		from ( SELECT
		sum( process_speed + service_attituude + technical_level )/ 3 davg
		FROM ws_ws_evaluation a
		join ws_ws_sheet b on a.work_number = b.work_number
		where a.delete_status = 0
		<if test="null != fkUserDeptId and ''!=fkUserDeptId">
			and b.fk_user_dept_id = #{fkUserDeptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		GROUP BY pk_ws_evaluation_id
		) a
		) c
	</select>

	<select id="getDeptQualityOfService" resultType="java.util.Map">
		SELECT
		b.dept_name fkUserDeptName,
		ROUND( sum( a.work_hours )/ count( 1 ), 2 ) avgWorkHours
		FROM
		ws_ws_sheet a join ws_om_meau b on a.business_dept_id = b.dept_id
		WHERE
		a.actual_completion_time IS NOT NULL and b.DELETE_STATUS = 0
		<if test=" null != fkUserDeptId and ''!= fkUserDeptId">
			and a.business_dept_id = #{fkUserDeptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		GROUP BY a.business_dept_id,b.dept_name
	</select>

	<select id="getQualityOfPersonnelService" resultType="java.util.Map">
		select a.*,IFNULL(b.wwc,0) wwc,IFNULL(b.wwczb,0) wwczb from (
		SELECT
		a.fk_user_id fkUserId,
		a.fk_user_name fkUserName,
		a.fk_user_dept_name fkUserDeptName,
		count( 1 ) bjl,
		ROUND( avg( sum ), 2 ) avgSum,
		ROUND( avg( process_speed ), 2 ) avgProcessSpeed,
		ROUND( avg( service_attituude ), 2 ) avgServiceAttituude,
		ROUND( avg( technical_level ), 2 ) avgTechnicalLevel,
		ROUND( sum( work_hours )/ count( 1 ), 2 ) avgWorkHours,
		ROUND( (( c.tg / ( c.tg + IFNULL(d.wtg,0) )) * 100 ), 2 ) tgl
		FROM ws_ws_sheet a
		JOIN ( SELECT work_number, process_speed, service_attituude, technical_level, sum( process_speed +
				service_attituude + technical_level ) / 3 sum 
			FROM ws_ws_evaluation GROUP BY work_number, process_speed, service_attituude, technical_level ) b 
		ON a.work_number = b.work_number
		join ( SELECT b.fk_user_id, count( 1 ) tg
			FROM ws_ws_sheet a
			JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE a.actual_completion_time IS NOT NULL
		and b.complete = 1
		AND b.assist = 0
		AND b.task_name = '-10'
		<if test=" null != fkUserDeptId and ''!= fkUserDeptId">
			and a.business_dept_id = #{fkUserDeptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		GROUP BY b.fk_user_id) c on a.fk_user_id = c.fk_user_id
		LEFT join (
			select	a.fk_user_id, count( 1 ) wtg
			from ws_ws_sheet a 
			join ws_ws_back b on a.work_number = b.work_number
			where b.type = '2'
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		GROUP BY a.fk_user_id) d on a.fk_user_id = d.fk_user_id
		WHERE actual_completion_time IS NOT NULL
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		<if test="null != fkUserId and '' != fkUserId">
			AND a.fk_user_id = #{fkUserId}
		</if>
		<if test="null != fkUserName and '' != fkUserName">
			AND a.fk_user_name = #{fkUserName}
		</if>
		<if test="null != fkUserDeptId and '' != fkUserDeptId">
			AND a.fk_user_dept_id = #{fkUserDeptId}
		</if>
			GROUP BY a.fk_user_id,a.fk_user_name,a.fk_user_dept_name,c.tg,d.wtg 
		) a
		left join (
			SELECT
			a.fk_user_id fkUserId,
			a.fk_user_name fkUserName,
			sum( CASE WHEN work_status IN ( '1', '2', '3', '4', '7' ) THEN 1 ELSE 0 END ) wwc,
			ROUND(sum( CASE WHEN work_status IN ( '1', '2', '3', '4', '7' ) THEN 1 ELSE 0 END ) /count( 1 ) *100,2) wwczb
			FROM ws_ws_sheet a
			WHERE	delete_status = 0 AND fk_user_id IS NOT NULL AND fk_user_id != ''
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		<if test="null != fkUserId and '' != fkUserId">
			AND a.fk_user_id = #{fkUserId}
		</if>
		<if test="null != fkUserName and '' != fkUserName">
			AND a.fk_user_name = #{fkUserName}
		</if>
		<if test="null != fkUserDeptId and '' != fkUserDeptId">
			AND a.fk_user_dept_id = #{fkUserDeptId}
		</if>
		GROUP BY fk_user_id, a.fk_user_name
		) b ON a.fkUserId = b.fkUserId
	</select>

	<select id="selectListWsSheetByfaultEquipmentId"
		resultType="cn.trasen.worksheet.module.dto.outVo.WsWsSheetInfoByFaultEquipmentOutVo">
		select
		<include refid="wsSheetColum" />
		from ws_ws_sheet
		where fk_fault_equipment_id = #{faultEquipmentId}
	</select>
	<select id="getCreateWorkSheetPageList" resultType="cn.trasen.worksheet.module.dto.outVo.WsWsSheetListOutVo">
		select
		<include refid="wsSheetColum" />
		,b.full_path fkFaultTypeName
		from ws_ws_sheet a
		left join (select pk_fault_type_id,full_path from ws_fault_type) b on a.fk_fault_type_id = b.pk_fault_type_id
		where delete_status = 0
		<if test="null != repairManDeptId and ''!=repairManDeptId">
			and repair_man_dept_id = #{repairManDeptId}
		</if>
		<if test="null != faultDeion and ''!=faultDeion">
			and fault_deion like concat('%',#{faultDeion},'%')
		</if>


	</select>
	<select id="getAssistWorkOrder" resultType="cn.trasen.worksheet.module.dto.outVo.WsWorkSheetHomeListOutVo">
		SELECT
		IFNULL( m.theAssistFlag, 0 ) theAssistFlag,
		b.work_number,
		b.required_completion_time,
		a.create_time assist_time,
		b.fault_deion,
		b.repair_man_name,
		b.repair_man_dept_name,
		b.create_time,
		a.take_remark,
		a.fk_user_id,
		b.fk_user_name,
		b.fk_user_dept_name
		FROM ws_ws_task a
		JOIN ws_ws_sheet b ON a.work_number = b.work_number
		LEFT JOIN (
		SELECT
		c.WORK_NUMBER,
		CASE

		WHEN ( d.create_time IS NULL OR c.create_time > d.create_time ) THEN
		1 ELSE 0
		END theAssistFlag
		FROM
		(
		SELECT
		a.WORK_NUMBER,
		a.ASSIST,
		a.work_hours,
		a.create_time,
		count( 1 ) AS rankbak
		FROM
		( SELECT * FROM ws_ws_task WHERE work_hours != 0 AND ASSIST = 1
		<if test=' 2 == type'>
			and fk_user_dept_id = #{fkUserDeptId}
		</if>
		<if test=' 4 == type or 6 == type'>
			and fk_user_id = #{fkUserId}
		</if>
		) a
		LEFT JOIN ( SELECT * FROM ws_ws_task WHERE work_hours != 0 AND ASSIST = 1
		<if test=' 2 == type'>
			and fk_user_dept_id = #{fkUserDeptId}
		</if>
		<if test=' 4 == type or 6 == type'>
			and fk_user_id = #{fkUserId}
		</if>
		) b ON a.WORK_NUMBER = b.WORK_NUMBER
		AND a.create_time &lt;= b.create_time
		GROUP BY a.WORK_NUMBER, a.ASSIST , a.work_hours,a.create_time
		) c
		LEFT JOIN (select max(create_time) create_time,work_number from ws_ws_back GROUP BY work_number) d ON c.WORK_NUMBER =
		d.work_number
		WHERE
		c.rankbak = 1
		) m ON a.WORK_NUMBER = m.WORK_NUMBER

		WHERE
		a.create_time &gt; ( SELECT IFNULL( max( create_time ), '2000-01-01' ) FROM ws_ws_task WHERE take_remark LIKE '工单流转%' AND
		WORK_NUMBER = b.work_number )
		AND a.complete = 0
		and a.work_status = b.work_status
		AND a.assist = 1
		<if test=' 2 == type'>
			and a.fk_user_dept_id = #{fkUserDeptId}
		</if>
		<if test=' 4 == type or 6 == type'>
			and a.fk_user_id = #{fkUserId}
		</if>
		GROUP BY b.work_number, m.theAssistFlag,b.required_completion_time,
		a.create_time ,b.fault_deion,b.repair_man_name,b.repair_man_dept_name,b.create_time,
		a.take_remark,a.fk_user_id,b.fk_user_name,b.fk_user_dept_name
	</select>
	<select id="getAllWorkOrderTemporalInterval" resultType="java.util.Map">
		select (select count(DISTINCT DATE_FORMAT(create_time, '%Y-%m-%d')) from ws_ws_sheet) days,
		(select count(DISTINCT DATE_FORMAT(create_time, '%Y-%m')) from ws_ws_sheet) months
		from dual
	</select>
	<select id="getKnowledgeBaseSubmitTopDatas" resultType="java.util.Map">
	</select>
	<select id="selectAllList" resultType="cn.trasen.worksheet.module.entity.WsWsSheet">
		select
		<include refid="wsSheetColum" />
		from (SELECT A.*
		FROM ws_ws_sheet A
		WHERE
		delete_status = 0
		<if test="null != deptId and ''!=deptId">
			and business_dept_id = #{deptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		UNION
		SELECT
		A.*
		FROM
		ws_ws_sheet a
		LEFT JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE
		TAKE_REMARK LIKE '工单流转%'
		<if test="null != deptId and ''!=deptId">
			and business_dept_id = #{deptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND b.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND b.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND b.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND b.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		) C
	</select>

	<select id="getApplicantWorkSheetPageList" resultType="cn.trasen.worksheet.module.dto.outVo.WsWsSheetListOutVo">
		select b.pk_ws_task_id,
		a.CREATE_BY,
		a.CREATE_BY_NAME,
		a.create_by_dept_id,
		a.CREATE_TIME,
		a.UPDATE_BY,
		a.UPDATE_BY_NAME,
		a.UPDATE_TIME,
		a.DELETE_STATUS,
		a.repair_man_dept_id,
		a.repair_man_dept_name,
		a.REPAIR_MAN_ID,
		a.REPAIR_MAN_NAME,
		a.FK_USER_ID,
		a.FK_USER_NAME,
		a.fk_user_dept_id,
		a.fk_user_dept_name,
		a.PK_WS_SHEET_ID,
		a.work_number,
		a.WORK_STATUS,
		a.REPAIR_DEPT_ADDRESS,
		a.REPAIR_PHONE,
		a.FK_FAULT_TYPE_ID,
		a.FAULT_EQUIPMENT_NAME,
		a.FK_FAULT_EQUIPMENT_ID,
		a.fault_deion,
		a.REPAIR_TYPE,
		a.FAULT_EMERGENCY,
		a.FAULT_AFFECT_SCOPE,
		a.REQUIRED_COMPLETION_TIME,
		a.ACTUAL_COMPLETION_TIME,
		a.REMARK,
		a.WORK_HOURS,
		a.fk_user_phone,
		a.work_sheet_type,
		a.business_dept_id
		from ws_ws_sheet a
		join ws_ws_task b on a.work_number = b.work_number
		where repair_man_id = #{fkUserId}
		and a.work_status = b.work_status
		and b.assist = 0
		and complete = 0
	</select>
	<select id="mobileWorkSheetListBusCounts" resultType="java.util.Map">
		SELECT
		( SELECT count( 1 ) FROM ws_ws_sheet a JOIN ws_ws_task b ON a.work_number = b.work_number
		where a.repair_man_id = #{fkUserId}

		) td,
		( SELECT count( 1 ) FROM ws_ws_sheet a JOIN ws_ws_task b ON a.work_number = b.work_number WHERE a.work_status = '6'

		and repair_man_id = #{fkUserId}

		) wc,
		( SELECT count( 1 ) FROM ws_ws_sheet a JOIN ws_ws_task b ON a.work_number = b.work_number WHERE a.work_status = '8'

		and repair_man_id = #{fkUserId}

		) zz,
		( SELECT count( 1 ) FROM ws_ws_sheet a JOIN ws_ws_task b ON a.work_number = b.work_number WHERE a.work_status IN ( '1',
		'2' )

		and repair_man_id = #{fkUserId}

		) wks,
		( SELECT count( 1 ) FROM ws_ws_sheet a JOIN ws_ws_task b ON a.work_number = b.work_number WHERE a.work_status in ( '3',
		'4', '5', '7' )

		and repair_man_id = #{fkUserId}

		)
		jxz
		FROM
		DUAL

	</select>
	<select id="selectMobileInfo" resultType="cn.trasen.worksheet.module.dto.outVo.WsWsSheetMobileInfoOutVo">

		SELECT sum,
		ROUND(avg, 2) avg,
		ROUND( process_speed, 2 ) process_speed,
		ROUND( service_attituude, 2 ) service_attituude,
		ROUND( technical_level, 2 ) technical_level,
		ROUND( avgWorkHours, 2 ) avgWorkHours
		FROM
		(
		SELECT
		count(1) sum
		FROM
		ws_ws_sheet a
		JOIN (
		SELECT DISTINCT
		work_number
		FROM
		ws_ws_task
		WHERE
		fk_user_id = #{fkUserId}
		AND task_name = '-2'
		) b ON a.work_number = b.work_number
		WHERE
		a.fk_user_id = #{fkUserId} ) a,
		(
		SELECT
		avg( sum ) avg,
		avg( process_speed ) process_speed,
		avg( service_attituude ) service_attituude,
		avg( technical_level ) technical_level
		FROM
		(
		SELECT
		a.WORK_NUMBER,
		process_speed,
		service_attituude,
		technical_level,
		( process_speed + service_attituude + technical_level )/ 3 sum
		FROM
		ws_ws_evaluation a
		JOIN ws_ws_sheet b ON a.WORK_NUMBER = b.WORK_NUMBER
		WHERE
		b.fk_user_id = #{fkUserId}
		GROUP BY
		a.WORK_NUMBER
		) a
		) b,
		(
		SELECT
		sum( work_hours ) / count( 1 ) avgWorkHours
		FROM
		ws_ws_sheet
		WHERE
		fk_user_id = #{fkUserId}
		AND work_status IN ( '5', '6' )) c

	</select>
	<select id="mobileWorkbenchWorkSheetBusCounts" resultType="java.util.Map">
		SELECT (SELECT count(1)
		FROM ws_ws_sheet a JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE a.work_status = '2'
		and (a.repair_man_id = #{fkUserId} or a.fk_user_id = #{fkUserId})) djd,
		(
		SELECT count(1)
		FROM ws_ws_sheet a JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE a.work_status IN ('3', '7')
		and (a.repair_man_id = #{fkUserId} or a.fk_user_id = #{fkUserId})) zb,
		(
		SELECT count(1)
		FROM ws_ws_sheet a JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE a.work_status IN ('4')
		and (a.repair_man_id = #{fkUserId} or a.fk_user_id = #{fkUserId})) dys,
		(
		SELECT count(1)
		FROM ws_ws_sheet a JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE a.work_status IN ('5')
		and (a.repair_man_id = #{fkUserId} or a.fk_user_id = #{fkUserId})) dpj
		FROM DUAL
	</select>
	<select id="passRateAndAvgWorkHoursAndAvgScore" resultType="java.util.Map">
		select
		(
		SELECT
		ROUND( IFNULL((( c.tg / ( c.tg + IFNULL( d.wtg, 0 ) )) * 100 ),0), 2 ) tgl
		FROM
		(
		SELECT
		count( 1 ) tg
		FROM
		ws_ws_sheet a
		JOIN ws_ws_task b ON a.work_number = b.work_number
		WHERE
		a.actual_completion_time IS NOT NULL
		AND b.complete = 1
		AND b.assist = 0
		AND b.task_name = '-10'
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		) c, (
		SELECT
		count( 1 ) wtg
		FROM
		ws_ws_sheet a
		JOIN ws_ws_back b ON a.work_number = b.work_number
		WHERE
		b.type = '2'
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND a.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND a.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		) d
		) passRate,
		IFNULL((select ROUND(sum(work_hours)/count(1),2) from ws_ws_sheet
		where work_status in('4','5','6')
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		),0) avgWorkHours,
		IFNULL((select ROUND(avg((process_speed+service_attituude+technical_level)/3),2) from ws_ws_sheet a join ws_ws_evaluation b on
		a.WORK_NUMBER = b.WORK_NUMBER
		<where>
			<choose>
				<when test="_databaseId=='kingbase'">
					<if test="null != beginTime and '' != beginTime">
						AND a.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
					</if>
					<if test="null != endTime and '' != endTime">
						AND a.create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
					</if>
				</when>
				<otherwise>
					<if test="null != beginTime and '' != beginTime">
						AND a.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
					</if>
					<if test="null != endTime and '' != endTime">
						AND a.create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
					</if>
				</otherwise>
			</choose>
		</where>
		),0) avgScore
		from dual
	</select>

	<select id="wsSheetScreenPageList" resultType="cn.trasen.worksheet.module.dto.outVo.WsWsSheetScreenListOutVo">
		select repair_man_dept_name,fault_deion,fault_emergency,work_status,fk_user_name,create_time,b.dept_name
		from ws_ws_sheet a
		left join (select dept_name,dept_id from ws_om_meau where delete_status = 0) b
		on a.business_dept_id = b.dept_id
		where work_status in('1','2','3','7')
		<if test="null != fkDeptId and ''!=fkDeptId">
			and business_dept_id = #{fkDeptId}
		</if>
		order by create_time desc
	</select>

	<select id="wsSheetDistributionScreenPageList" resultType="java.util.Map">
		SELECT
		fk_user_id,
		fk_user_name,
		count( 1 ) zs,
		sum(case when work_status in('5','6') then 1 else 0 end ) wcs
		FROM
		ws_ws_sheet
		where fk_user_name is not null and delete_status = 0
		<if test="null != fkDeptId and ''!=fkDeptId">
			and business_dept_id = #{fkDeptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' ) AND create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d
					%H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' ) AND create_time &lt; STR_TO_DATE( #{endTime},
					'%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
		GROUP BY
		fk_user_id, fk_user_name

	</select>

	<select id="noSendOrders" resultType="java.lang.Integer">
		select count(1) from ws_ws_sheet
		where fk_user_name is null and delete_status = 0
		<if test="null != fkDeptId and ''!=fkDeptId">
			and business_dept_id = #{fkDeptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test="null != beginTime and '' != beginTime">
					AND create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND create_time &lt; TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</when>
			<otherwise>
				<if test="null != beginTime and '' != beginTime">
					AND create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
				<if test="null != endTime and '' != endTime">
					AND create_time &lt; STR_TO_DATE( #{endTime}, '%Y-%m-%d %H:%i:%s' )
				</if>
			</otherwise>
		</choose>
	</select>

	<select id="workSheetremind" resultType="java.util.Map">
		SELECT
		IFNULL(( SELECT count( 1 ) FROM ws_ws_sheet WHERE work_status IN ( '2', '4', '5' ) AND fk_user_id = #{fkUserId} ), 0 ) normal,
		IFNULL(( SELECT count( 1 ) FROM ws_ws_sheet WHERE work_status IN ( '7', '8' ) AND fk_user_id = #{fkUserId} ), 0 )
		abnormal
		FROM
		DUAL
	</select>
	<select id="peopleProcessCount" resultType="cn.trasen.worksheet.module.dto.outVo.WsSheetPeopleInfoOutVo">

		SELECT
		fk_user_id userId,
		count( 1 ) processCount
		FROM
		ws_ws_sheet
		WHERE
		fk_user_id IN (
		<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>
		)
		AND work_status in('2','3') and delete_status = 0
		GROUP BY
		fk_user_id

	</select>
	<select id="unprocessedMessageAlertsDpdDjd" resultType="java.util.Map">
		SELECT
		a.WORK_NUMBER workNumber,
		DATE_FORMAT( max( b.create_time ), '%Y-%m-%d %H:%i:%s' ) createTime,
		a.repair_man_dept_name repairManDeptName
		FROM
		ws_ws_sheet a
		JOIN ws_ws_task b ON a.work_number = b.work_number
		AND a.work_status = b.work_status
		WHERE
		a.work_status = #{workStatus} and a.business_dept_id = #{deptId} and a.delete_status = 0
		AND b.task_name IN (
		<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>
		)
		GROUP BY
		a.WORK_NUMBER
	</select>
	<select id="getDayGroupByDept" resultType="java.util.Map">
		SELECT
		A.deptName,
		A.count,
		B.sumCount
		FROM
		(
		SELECT
		COUNT( DISTINCT work_number ) count,
		dept_name deptName,
		business_dept_id
		FROM
		(
		SELECT
		a.work_number,
		a.business_dept_id,
		B.dept_name
		FROM
		ws_ws_sheet a
		JOIN ws_om_meau B ON A.business_dept_id = B.dept_id
		WHERE a.delete_status = 0 AND b.delete_status = 0
		<choose>
			<when test="_databaseId=='kingbase'">
				AND a.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' ) AND a.create_time &lt; TO_DATE( #{endTime},
				'%Y-%m-%d %H:%i:%s' )
			</when>
			<otherwise>
				AND a.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' ) AND a.create_time &lt; STR_TO_DATE( #{endTime},
				'%Y-%m-%d %H:%i:%s' )
			</otherwise>
		</choose>
		UNION
		SELECT
		a.work_number,
		a.business_dept_id,
		C.dept_name
		FROM
		ws_ws_sheet a
		LEFT JOIN ws_ws_task b ON a.work_number = b.work_number
		JOIN ws_om_meau C ON A.business_dept_id = C.dept_id
		WHERE TAKE_REMARK LIKE '工单流转%' AND C.delete_status = 0
		<choose>
			<when test="_databaseId=='kingbase'">
				AND b.create_time &gt; TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' ) AND b.create_time &lt; TO_DATE( #{endTime},
				'%Y-%m-%d %H:%i:%s' )
			</when>
			<otherwise>
				AND b.create_time &gt; STR_TO_DATE( #{beginTime}, '%Y-%m-%d %H:%i:%s' ) AND b.create_time &lt; STR_TO_DATE( #{endTime},
				'%Y-%m-%d %H:%i:%s' )
			</otherwise>
		</choose>
		AND a.delete_status = 0
		) A
		GROUP BY
		business_dept_id
		) a
		JOIN ( SELECT count( 1 ) sumCount, business_dept_id FROM ws_ws_sheet GROUP BY business_dept_id ) b ON A.business_dept_id =
		B.business_dept_id
		order by B.sumCount desc,A.count desc
	</select>

	<select id="fkUserWorkStatusCount" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM ws_ws_sheet
		WHERE work_status = #{workStatus} and delete_status = 0
		<if test=" null != fkUserId and '' !=fkUserId ">
			AND fk_user_id = #{fkUserId}
		</if>
		<if test=" null != fkDeptId and '' !=fkDeptId ">
			AND business_dept_id = #{fkDeptId}
		</if>
	</select>
	<select id="selectSheetSendAgingOutVo"
		resultType="cn.trasen.worksheet.module.dto.outVo.WsWorkSheetSendAgingOutVo">
		SELECT
		d.employee_no employeeNo,
		a.create_by_name employeeName,
		count( a.work_number ) dispatchNumber,
		min(
		TIMESTAMPDIFF( SECOND, c.create_time, a.create_time )) dispatchShortestTime,
		max(
		TIMESTAMPDIFF( SECOND, c.create_time, a.create_time )) dispatchLongestTime,
		CEILING(
		sum(
		TIMESTAMPDIFF( SECOND, c.create_time, a.create_time ))/ count( a.work_number )) dispatchAvgtTime,
		(
		SELECT
		CEILING(
		sum(
		TIMESTAMPDIFF( SECOND, c.create_time, a.create_time ))/ count( a.work_number ))
		FROM
		ws_ws_task a
		JOIN ( SELECT max( CREATE_TIME ) CREATE_TIME, work_number FROM ws_ws_task WHERE task_name = '-5' AND work_status = 2 GROUP
		BY work_number ) b ON a.WORK_NUMBER = b.WORK_NUMBER
		AND a.CREATE_TIME = b.CREATE_TIME
		JOIN ws_ws_sheet c ON a.work_number = c.work_number
		WHERE
		a.work_status != '1'
		<if test="null != fkUserDeptId and '' != fkUserDeptId">
			and c.business_dept_id = #{fkUserDeptId}
		</if>
		) as dispatchTime,
		(
		CEILING(
		sum(
		TIMESTAMPDIFF( SECOND, c.create_time, a.create_time ))/ count( a.work_number )) - (
		SELECT
		CEILING(
		sum(
		TIMESTAMPDIFF( SECOND, c.create_time, a.create_time ))/ count( a.work_number ))
		FROM
		ws_ws_task a
		JOIN ( SELECT max( CREATE_TIME ) CREATE_TIME, work_number FROM ws_ws_task WHERE task_name = '-5' AND work_status = 2 GROUP
		BY work_number ) b ON a.WORK_NUMBER = b.WORK_NUMBER
		AND a.CREATE_TIME = b.CREATE_TIME
		JOIN ws_ws_sheet c ON a.work_number = c.work_number
		WHERE
		a.work_status != '1'
		<if test="null != fkUserDeptId and '' != fkUserDeptId">
			and c.business_dept_id = #{fkUserDeptId}
		</if>
		)) dispatchTimeDifference
		FROM
		ws_ws_task a
		JOIN ( SELECT max( CREATE_TIME ) CREATE_TIME, work_number FROM ws_ws_task WHERE task_name = '-5' AND work_status = 2 GROUP
		BY work_number ) b ON a.WORK_NUMBER = b.WORK_NUMBER
		AND a.CREATE_TIME = b.CREATE_TIME
		JOIN ws_ws_sheet c ON a.work_number = c.work_number
		LEFT JOIN ts_base_oa.cust_emp_base d ON d.employee_id = a.create_by
		WHERE
		a.work_status != '1' and a.delete_status = 0
		<if test="null != fkUserDeptId and '' != fkUserDeptId">
			and c.business_dept_id = #{fkUserDeptId}
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test=" null != beginTime and ''!= beginTime">
					and to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= a.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= a.create_time
				</if>
			</when>
			<otherwise>
				<if test=" null != beginTime and ''!= beginTime">
					and str_to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= a.create_time
				</if>
				<if test=" null != endTime and ''!= endTime">
					and str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= a.create_time
				</if>
			</otherwise>
		</choose>
		GROUP BY a.create_by,d.employee_no,a.create_by_name
	</select>

	<select id="selectSheetOderAgingOutVo"
		resultType="cn.trasen.worksheet.module.dto.outVo.WsWorkSheetOderAgingOutVo">
		SELECT
		d.employee_no employeeNo,
		a.create_by_name employeeName,
		count( a.work_number ) orderNumber,
		min(
		TIMESTAMPDIFF( SECOND, c.create_time, a.create_time )) orderShortestTime,
		max(
		TIMESTAMPDIFF( SECOND, c.create_time, a.create_time )) orderLongestTime,
		CEILING(
		sum(
		TIMESTAMPDIFF( SECOND, c.create_time, a.create_time ))/ count( a.work_number )) orderAvgtTime,
		e.zsl,
		IFNULL(f.djdnum,0) djdnum,
		IFNULL(f.csnum,0) csnum,
		IFNULL(ROUND(IFNULL(f.csnum,0)/IFNULL(f.djdnum,0)*100,2),0) csl
		FROM
		ws_ws_task a
		JOIN ( SELECT max( CREATE_TIME ) CREATE_TIME, work_number FROM ws_ws_task WHERE task_name = '-2' GROUP BY work_number ) b
		ON a.WORK_NUMBER = b.WORK_NUMBER
		AND a.CREATE_TIME = b.CREATE_TIME
		JOIN ws_ws_sheet m ON a.WORK_NUMBER = m.WORK_NUMBER
		LEFT JOIN ts_base_oa.cust_emp_base d ON d.employee_id = a.create_by
		JOIN (
		(
		SELECT
		a.create_time,
		a.work_number
		FROM
		ws_ws_task a
		JOIN ( SELECT max( CREATE_TIME ) CREATE_TIME, work_number FROM ws_ws_task WHERE task_name = '-5' AND work_status = 2 GROUP
		BY work_number ) b ON a.WORK_NUMBER = b.WORK_NUMBER
		AND a.CREATE_TIME = b.CREATE_TIME
		)) c ON a.WORK_NUMBER = c.WORK_NUMBER
		left join (SELECT
		a.create_by,
		ROUND(sum(
		case when
		IFNULL((select request_receiving_time from ws_sys_config where delete_status = 0),'exist') = 'exist'
		then 1
		when
		TIMESTAMPDIFF( SECOND, c.create_time, a.create_time ) &lt; (select request_receiving_time from ws_sys_config where delete_status =
		0)*60*60
		then 1
		else 0
		end)/count(a.work_number) * 100,2) zsl
		FROM
		ws_ws_task a
		JOIN ( SELECT max( CREATE_TIME ) CREATE_TIME, work_number FROM ws_ws_task WHERE task_name = '-2' GROUP BY work_number ) b
		ON a.WORK_NUMBER = b.WORK_NUMBER
		AND a.CREATE_TIME = b.CREATE_TIME
		JOIN (
		(
		SELECT
		a.create_time,
		a.work_number
		FROM
		ws_ws_task a
		JOIN ( SELECT max( CREATE_TIME ) CREATE_TIME, work_number FROM ws_ws_task WHERE task_name = '-5' AND work_status = 2 GROUP
		BY work_number ) b ON a.WORK_NUMBER = b.WORK_NUMBER
		AND a.CREATE_TIME = b.CREATE_TIME
		)) c ON a.WORK_NUMBER = c.WORK_NUMBER
		GROUP BY a.create_by) e on a.create_by = e.create_by
		left join (
		SELECT
		count( 1 ) djdnum,
		fk_user_id,
		sum(case when
		IFNULL((select request_receiving_time from ws_sys_config where delete_status = 0),'exist') = 'exist'
		then 0
		when
		TIMESTAMPDIFF( SECOND, c.create_time, now() ) > (select request_receiving_time from ws_sys_config where delete_status = 0)*60*60
		then 1
		else 0
		end) csnum
		FROM
		ws_ws_sheet a
		JOIN (
		(
		SELECT
		a.create_time,
		a.work_number
		FROM
		ws_ws_task a
		JOIN ( SELECT max( CREATE_TIME ) CREATE_TIME, work_number FROM ws_ws_task WHERE task_name = '-5' AND work_status = 2 GROUP
		BY work_number ) b ON a.WORK_NUMBER = b.WORK_NUMBER
		AND a.CREATE_TIME = b.CREATE_TIME
		)) c ON a.WORK_NUMBER = c.WORK_NUMBER
		WHERE
		work_status = '2'
		GROUP BY
		fk_user_id
		)f on a.create_by = f.fk_user_id
		<where>
			<if test="null != fkUserDeptId and '' != fkUserDeptId">
				and m.fk_user_dept_id = #{fkUserDeptId}
			</if>
			<choose>
				<when test="_databaseId=='kingbase'">
					<if test=" null != beginTime and ''!= beginTime">
						and to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= a.create_time
					</if>
					<if test=" null != endTime and ''!= endTime">
						and to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= a.create_time
					</if>
				</when>
				<otherwise>
					<if test=" null != beginTime and ''!= beginTime">
						and str_to_date(#{beginTime}, '%Y-%m-%d %H:%i:%s') &lt;= a.create_time
					</if>
					<if test=" null != endTime and ''!= endTime">
						and str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s') &gt;= a.create_time
					</if>
				</otherwise>
			</choose>
		</where>
		GROUP BY a.create_by,d.employee_no,a.create_by_name,e.zsl,f.djdnum,f.csnum
	</select>

	<select id="getQualityOfPersonnelServiceWwc" resultType="java.util.Map"></select>
	
	<update id="updateByWorkNumber">
		update ws_ws_sheet
		set  fk_fault_type_id=#{fkFaultTypeId}
		where work_number = #{workNumber}

	</update>
</mapper>
