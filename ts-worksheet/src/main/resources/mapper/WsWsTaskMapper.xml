<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsWsTaskMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsWsTask">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="pk_ws_task_id" jdbcType="VARCHAR" property="pkWsTaskId"/>
        <result column="work_number" jdbcType="VARCHAR" property="workNumber"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_by_name" jdbcType="VARCHAR" property="createByName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_by_name" jdbcType="VARCHAR" property="updateByName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delete_status" jdbcType="TINYINT" property="deleteStatus"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="take_remark" jdbcType="VARCHAR" property="takeRemark"/>
        <result column="fk_former_user_id" jdbcType="VARCHAR" property="fkFormerUserId"/>
        <result column="fk_former_user_name" jdbcType="VARCHAR" property="fkFormerUserName"/>
        <result column="work_hours" jdbcType="TINYINT" property="workHours"/>
        <result column="complete" jdbcType="TINYINT" property="complete"/>
        <result column="assist" jdbcType="TINYINT" property="assist"/>
        <result column="fk_user_id" jdbcType="VARCHAR" property="fkUserId"/>
        <result column="fk_user_name" jdbcType="VARCHAR" property="fkUserName"/>
        <result column="work_status" jdbcType="VARCHAR" property="workStatus"/>
        <result column="fk_user_dept_id" jdbcType="VARCHAR" property="fkUserDeptId"/>
        <result column="fk_user_dept_name" jdbcType="VARCHAR" property="fkUserDeptName"/>
    </resultMap>

    <sql id="wsTaskColum">
        pk_ws_task_id
        ,
        work_number,
        create_by,
        create_by_name,
        create_time,
        update_by,
        update_by_name,
        update_time,
        delete_status,
        task_name,
        take_remark,
        fk_former_user_id,
        fk_former_user_name,
        work_hours,
        complete,
        assist,
        fk_user_id,
        fk_user_name,
        work_status,
        fk_user_dept_id,
        fk_user_dept_name,
        fk_user_phone,
        create_by_dept_id
    </sql>

    <update id="updateWsTaskById">
        update ws_ws_task
        set UPDATE_BY           = #{updateBy},
            UPDATE_BY_NAME      = #{updateByName},
            UPDATE_TIME         = #{updateTime},
            DELETE_STATUS       = #{deleteStatus},
            FK_USER_ID          = #{fkUserId},
            FK_USER_NAME        = #{fkUserName},
            fk_user_dept_id     = #{fkUserDeptId},
            fk_user_dept_name   = #{fkUserDeptName},
            fk_former_user_id   = #{fkFormerUserId},
            fk_former_user_name = #{fkFormerUserName},
            task_name           = #{taskName},
            work_status         = #{workStatus},
            take_remark         = #{takeRemark},
            complete            = #{complete},
            work_hours          = #{workHours},
            fk_user_phone       = #{fkUserPhone}
        where pk_ws_task_id = #{pkWsTaskId}
    </update>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            UPDATE ws_ws_task
            set work_status = #{item.workStatus},
            update_time = #{item.updateTime},
            complete = #{item.complete}
            WHERE work_number = #{item.workNumber} and complete = 0
        </foreach>
    </update>

    <update id="updateTaskTerminated">
        UPDATE ws_ws_task
        SET complete = 1
        WHERE work_number = #{workNumber}
          and complete = 0
    </update>

    <update id="updateTaskStop">
        UPDATE ws_ws_task
        SET delete_status = 1
        WHERE work_number = #{workNumber}
          and assist = 1
    </update>

    <update id="updateTaskOpen">
        UPDATE ws_ws_task
        SET delete_status = 0
        WHERE work_number = #{workNumber}
          and assist = 1
          and delete_status = 1
    </update>

    <select id="selectOneWsTask" resultType="cn.trasen.worksheet.module.entity.WsWsTask">
        select
        <include refid="wsTaskColum"/>
        from ws_ws_task
        where WORK_NUMBER = #{workNumber} and COMPLETE = '0'
    </select>
    <select id="selectOneWsTaskInfo" resultType="cn.trasen.worksheet.module.dto.outVo.WsWsTaskInfoOutVo">
        SELECT create_by_name,
               create_time,
               task_name,
               take_remark,
               pk_ws_task_id
        FROM ws_ws_task
        WHERE WORK_NUMBER = #{workNumber}
        <if test="null != taskName and '' != taskName">
            and task_name = #{taskName}
        </if>
        ORDER BY CREATE_TIME DESC
    </select>
    <select id="selectOneWsTaskByuser" resultType="cn.trasen.worksheet.module.entity.WsWsTask">
        select
        <include refid="wsTaskColum"/>
        from ws_ws_task
        where WORK_NUMBER = #{workNumber} and COMPLETE = '0' and FK_USER_ID = #{fkUserId}
    </select>
    <select id="selectOneWsTaskById" resultType="cn.trasen.worksheet.module.entity.WsWsTask">
        select
        <include refid="wsTaskColum"/>
        from ws_ws_task
        where pk_ws_task_id = #{id}
    </select>

    <insert id="insertBatch">
        INSERT INTO ws_ws_task
        (
        pk_ws_task_id,
        work_number,
        create_by,
        create_by_name,
        create_time,
        update_by,
        update_by_name,
        update_time,
        delete_status,
        task_name,
        take_remark,
        complete,
        assist,
        fk_user_id,
        fk_user_name,
        work_status,
        fk_user_dept_id,
        fk_user_dept_name,
        work_hours,
        fk_user_phone,
        create_by_dept_id
        )
        VALUES
        <foreach collection="list" item="task" separator=",">
            (
            #{task.pkWsTaskId},
            #{task.workNumber},
            #{task.createBy},
            #{task.createByName},
            #{task.createTime},
            #{task.updateBy},
            #{task.updateByName},
            #{task.updateTime},
            #{task.deleteStatus},
            #{task.taskName},
            #{task.takeRemark},
            #{task.complete},
            #{task.assist},
            #{task.fkUserId},
            #{task.fkUserName},
            #{task.workStatus},
            #{task.fkUserDeptId},
            #{task.fkUserDeptName},
            #{task.workHours},
            #{task.fkUserPhone},
            #{task.createByDeptId}
            )
        </foreach>
    </insert>
    <insert id="insertWsTask">

        INSERT INTO ws_ws_task (<include refid="wsTaskColum"/>)
        values (
        #{pkWsTaskId},
        #{workNumber},
        #{createBy},
        #{createByName},
        #{createTime},
        #{updateBy},
        #{updateByName},
        #{updateTime},
        #{deleteStatus},
        #{taskName},
        #{takeRemark},
        #{fkFormerUserId},
        #{fkFormerUserName},
        #{workHours},
        #{complete},
        #{assist},
        #{fkUserId},
        #{fkUserName},
        #{workStatus},
        #{fkUserDeptId},
        #{fkUserDeptName},
        #{fkUserPhone},
        #{createByDeptId}
        )

    </insert>

    <select id="assistanceToCompleted" resultType="java.lang.Integer">

        SELECT count(1)
        FROM ws_ws_task a
                 JOIN ws_ws_task b ON a.WORK_NUMBER = b.WORK_NUMBER
        WHERE a.PK_WS_TASK_ID = #{taskId}
          AND b.ASSIST = 1
          AND b.COMPLETE = 0
    </select>

    <select id="selectAssisListByWorkNumber" resultType="cn.trasen.worksheet.module.entity.WsWsTask">
        SELECT
        <include refid="wsTaskColum"/>
        FROM
        ws_ws_task a
        where work_number = #{workNumber} AND ASSIST = 1
    </select>
    <select id="selectOneMaxTimeTaskByWorkNumber"
            resultType="cn.trasen.worksheet.module.entity.WsWsTask">
        select
            a.pk_ws_task_id
                ,
            a.work_number,
            a.create_by,
            a.create_by_name,
            a.create_time,
            a.update_by,
            a.update_by_name,
            a.update_time,
            a.delete_status,
            a.task_name,
            a.take_remark,
            a.fk_former_user_id,
            a.fk_former_user_name,
            a.work_hours,
            a.complete,
            a.assist,
            a.fk_user_id,
            a.fk_user_name,
            a.work_status,
            a.fk_user_dept_id,
            a.fk_user_dept_name,
            a.fk_user_phone
        from ws_ws_task a
        join ws_ws_sheet b on a.work_number = b.work_number and a.work_status = b.work_status
        where a.assist = 0 and a.complete = 0 and a.work_number = #{workNumber}
        order by a.update_time desc limit 1
    </select>
    <select id="selectAllTaskWorkHoursList"
            resultType="cn.trasen.worksheet.module.dto.outVo.WsTaskWorkHoursListOutVo">
        select update_time create_time,fk_user_id,fk_user_name,fk_user_dept_id,fk_user_dept_name,work_hours from ws_ws_task where work_number = #{workNumber} and work_hours != 0
        order by update_time desc
    </select>
    <select id="selectSubmitKnowledgeBaseInfo"
            resultType="cn.trasen.worksheet.module.dto.outVo.WsWsTaskInfoOutVo">
        select
        <include refid="wsTaskColum" />
        from ws_ws_task where work_number = #{workNumber}  and assist = 0 and work_status = '4' ORDER BY create_time desc
            limit 1

    </select>
    <select id="lastTaskInfoIsComplete" resultType="java.lang.Integer">
        select complete from ws_ws_task where pk_ws_task_id = #{pkWsTaskId}
    </select>
    <select id="selectAssistIdByWorkNumber" resultType="java.lang.String">
        SELECT DISTINCT FK_USER_ID FROM WS_WS_TASK
        WHERE ASSIST = 1 AND work_number = #{workNumber}
    </select>
</mapper>